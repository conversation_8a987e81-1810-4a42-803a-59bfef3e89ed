# UPDevice组件概况
UPDevice组件是介于App和uSDK之间的一个中间件，其将底层uSDK的接口及逻辑进行封装，然后以自身接口的方式提供给上层App使用。组件相关问题联系方式: <<EMAIL>>
## 组件当前版本: 6.3.21

-### 6.3.21
-2021-11-02
-1.低功耗唤醒接口和属性开发

-### 6.3.20
-2021-10-25
-1.修改onControlState上报时机，需要比onDeviceInfoChange早

-### 6.3.14
-2021-10-16
-1.快连3.0，修复存放设备id的错误,补充快连设备扫描日志


-### 6.3.12
-2021-10-15
-1.快连3.0，详情页跳转需要productCode参数

-### 6.3.11
-2021-10-15
-1.合并离在线功能到发版分支

-### 6.3.10
-2021-10-13
-1.修改daemon中获取设备对象的逻辑

-### 6.3.9
-2021-10-12
-1.新增connectDevice和disConnectDevice的action

-### 6.3.8
-2021-10-12
-1.优化连接和断开设备监听逻辑

-### 6.3.7
-2021-10-12
-1.修复快连3.0代码问题

-### 6.3.5
-2021-10-11
-1.修改BindDeviceWithoutWifi接口和updateRouterInfo接口进度返回值

-### 6.3.4
-2021-10-08
-1.添加快连3.0相关接口

-### 6.2.11
-2021-9-9
集成离线在线分离功能

-### 6.2.8
-2021-8-18
-1.merge feature_resource分支，updevice新增attachResource和detachResource方法
-### 6.2.7
-2021-8-5
-1.解决偶现usdk启动后不会prepare设备的问题
-
-### 6.2.5
-2021-7-30
-1.适配逻辑引擎初始化配置文件新接口
-
-
-### 6.2.4
-2021-7-26
-1.bugfix:ZHIJIAAPP-28851
-
-### 6.2.3
-2021-7-16
-1.bugfix:ZHIJIAAPP-28668
-
-### 6.2.2
-2021-7-16
-1.合入设备离线点位埋点代码
-### 6.2.1
-2021-7-15
-1.合入OTA升级与灯组功能的相关代码
-
-### 6.1.9
-2021-7-9
-1.适配usdk 8.6.0
-
-### 6.1.8
+### 6.1.6
 2021-7-8
-1.修改CreateDeviceGroup接口返回值
-
-### 6.1.7
-2021-7-8
-1.适配逻辑引擎初始化配置文件新接口
-
-### 6.1.5

#### 依赖的uSDK版本: >=5.0.1

uSDK相关问题请参见uSDK开发手册：uSDK4.0_Phone_iOS开发手册.pdf。

## 版本发布记录:
### 6.1.6
2021-7-8
1.未解码资源订阅和解订阅接口，封装updeviceBase接口，支持重构后的UpDevicePlugin C层代码调用
2021-6-21
1.修改设备型号变化后，prepare会失败的问题

### 6.1.0
2021-5-26
1.适配usdk8.4.0新增是否为组设备接口

### 6.0.1
1.修改头文件引用

### 5.1.0
2021-4-7
1.适配usdk8.4.0 设备组相关接口封装

### 5.0.0
2021-3-18
1.增加订阅资源新接口attachResourceWithDecode，实现新的资源回调代理接口

### 4.10.6
1.修改头文件引用

### 4.10.5
1.updevice删除updevice_api库依赖,updevice_api库合入到updevice

### 4.10.4
1.修改依赖的resource库版本,指定为源码引用版本

### 4.10.3
1.切换源码依赖

### 4.10.2
1.适配逻辑引擎新增解析接口

### 4.9.3
2021-3-16
1.增加非网器设备

### 4.9.0
2021-2-20
1.增加获取网络类型接口
### 4.8.10

2020-11-20

-  bugfix:ZHIJIAAPP-16909；

### 4.8.9
1.增加设置设备数据源方法
### 4.8.7

2020-09-27

-  基于最新构建系统出tag；

### 4.8.6

2020-08-22

-  destroyDevice时清空device对象的监听者；

### 4.8.5

2020-08-21

-  回退将processAttributes和processCautions中的逻辑处理放到子线程；

### 4.8.4

2020-08-21

-  删除processDeviceInfo中无用的log；

### 4.8.3

2020-08-20

-  将processAttributes和processCautions中的逻辑处理放到子线程；

### 4.8.2

2020-08-17

-  注销后发送设备列表变化通知；

### 4.8.1

2020-08-07

-  智慧家接口优化，添加getEngineAttributeList，getEngineCautionList；

### 4.8.0

2020-08-07

- 新增获取网络质量接口 getNetworkQuality；

### 4.7.4
1.bugFix:ZHIJIAAPP-9457；

### 4.7.3
1.UpDeviceManager 添加 moveDeviceToQueueHead方法；

### 4.7.1
1.适配基础组建framework；

### 4.7.0
1.增加configState接口；

### 4.6.6
1.切换framework；

### 4.5.18
1.bugFix:ZHIJIAAPP-4681；

### 4.5.17
1.UpDeviceManager新增接口：isBound，getDeviceBindInfo；

### 4.5.16
1.新增接口：inFocus，outFocus；

### 4.5.15
1.bugfix: <82004>【概率性发生】摄像头在线，ios设备卡片页显示离线状态（Android正常。

### 4.5.14
1.FOTA错误码透传给容器。

### 4.5.13
1.去掉UPDeviceDaemon多余log。

### 4.5.0
1.增加子设备列表相关接口实现。

### 4.4.5
1.重构小循环升级isModuleNeedOTA方法。

### 4.4.1
1.增加蓝牙接口

### 4.4.0
1.增加小循环固件升级接口

### 4.2.1
1.恢复FOTA接口

### 4.1.2
1. usdk中开始设备底板固件的升级接口更新。

### 4.1.1
1.增加FOTA接口

### 4.0.18
1.适配UPResouce2.1.0接口

### 4.0.6
1. 修复UPResource新版本接口适配后出现的发送两次配置文件更新请求的问题。

### 4.0.3
1. 完善日志打印逻辑。

### 4.0.2
1. 删除UPDeviceLog等日志相关类，适配并采用updevice_api中的日志类代替其相关逻辑。

### 4.0.0
1. UPDevice整体功能及逻辑重构。

### 3.0.11
1.依赖uSDK版本改为>=5.0.1

### 3.0.10
1.添加链路打点subsys

### 3.0.9
1.softap配置接口链路打点中ipm参数隐藏password

### 3.0.8
1.合并3.0.6和3.0.7版本修改.

### 3.0.7
1.smartlink配合接口添加csTraceNode关联

### 3.0.6
1. 适配uSDK 5.0.1版本。


### 3.0.5
1.链路打点ssid公开
2.smartlink配置接口使用uSDK新接口，增加cs打点关联
### 3.0.4
1.添加uSDK临时文件，紧急修复设备状态不上报问题
### 3.0.3
1.获取绑定key添加失败回调
### 3.0.2
1.softAP配置命令改为进入后台不计时
### 3.0.1
1.链路打点添加ipm
### 3.0.0
1.依赖的uSDK版本:5.0.0
2.获取绑定key接口修改，添加timeoutInterval字段
3.UPTrace -addSendRequestTraceNodeWithSubSys接口添加返回值
4.UPTrace -addReceiveResponseTraceNodeWithSubSys接口添加relatedCSNode字段
### 2.6.1
1.依赖的uSDK版本:4.4.04-20180411
### 2.6.0
1. 修改uSDK.framework的引入方式，将原有方式改为以Cocoa Pods库依赖的方式。
### 2.5.9
1.修改因多typeId配置接口更改，导致链路打点错误问题。

### 2.5.8
1. 升级uSDK版本至4.4.04_2018032016，封装并增加了新的配置接口方法。

### 2.5.7
1.获取绑定key失败重试逻辑由重试3次，改为隔2s一直重试

### 2.5.6
1. UPDevice类增加typeName属性，表示该设备大类的名称。

### 2.5.5
1. 合并feature_2.5.0到master后整理发tag

### 2.5.4
1. 修复因uSDK设备属性状态变化接口（-device:didUpdateValueForAttributes:）方法名修改，导致设备状态变化不触发问题。影响版本2.5.1-2.5.3

### 2.5.3
1. 替换uSDK正式包4.4.03_2018013118

### 2.5.2
1. 发布内测版本

### 2.5.1-SNAPSHOT
1. 替换uSDK测试版本4.4.02_2018012315
2. softAP获取配置信息/配置接口重试逻辑修改
3. UPTrace添加两个点位枚举

### 2.5.0-SNAPSHOT
1. 替换uSDK测试版本4.4.02_2018010818
2. 升级uSDK新配置接口
3. 获取配置信息/发送配置信息/获取绑定key 失败加入重试
4. utraceId uSpanId获取方式改变

### 2.4.0
1.  一拖多设备子设备命令下发逻辑修改。修复无法对单个子设备下发命令的BUG。
2. UPDevice类增加针对子设备下发命令的接口，详见其UPDevice中头文件定义。
3. UPDeviceDelegate中接口方法逻辑修改，增加子设备变化的上报逻辑。

### 2.3.3
1. 命令下发时，去除原有针对组命令名的转换逻辑，组命令名称在下发时直接透传，不做任何处理。

### 2.3.2
1. 增加可设置deviceID和typeID的设备配置接口configDeviceWithMode:typeID:deviceID:security:userId:ssid:pwd:timeoutInterval:success:failure:，原有配置接口不推荐使用。

### 2.3.1
1. 更新uSDK至4.4.01_2017102717版本。

### 2.3.0
1. 修复JS设备报警状态解析逻辑不正确的问题。
2. 增加子设备类UPSubDevice，即其相关逻辑。完成对一拖多复杂设备的支持。

### 2.2.19
1. UPTrace修改bName：configDeviceBySoftapWithConfigInfo

### 2.2.18
1. 修复部分JS设备的报警状态解析不正确的问题。

### 2.2.17
1. 增加对一拖多复杂设备的功能支持。

### 2.2.16
1. UPTrace中打点参数cfg改为number类型

### 2.2.14
1. 替换uSDK 4.3.01发布包

### 2.2.13
1. 配置接口增加uId，以支持全链路监控日志打点
2. 配置接口实现、获取绑定信息全链路监控日志增加uId、dId信息
3. UPTrace逻辑优化

### 2.2.11
1. 升级uSDK测试版本4.3.01 (build 2017091418)


### 2.2.9
1. 升级uSDK测试版本4.3.01 (build 2017090715)

### 2.2.7
1. 升级uSDK测试版本4.3.01 (build 2017090518)

### 2.2.5
1. 升级uSDK测试版本4.3.01 (build 2017090514)
2. UPTrace修改字段错误和uTraceId生成时机。

### 2.2.1
1. 升级并更新uSDK测试版本至4.3.01。
2. 添加日志采集管理类。
3. UPDevice调用uSDK配置相关位置添加日志采集。
4. 此版本为调试版本，非正式版本。

### 2.1.10
1. 升级并更新uSDK版本至4.2.01。

### 2.1.9
1. 删除refoundDevice接口。
2. 绑定配置接口回调加入UPDevice对象。

### 2.1.8
1. 修复一个崩溃的Bug。

### 2.1.7
1. 更新uSDK版本至4.1.02。该版本修复海尔智能音响播放音乐进度来回跳动的问题和洗衣机累计用水量不正确的问题。

### 2.1.6
1. 修复JS设备在初次上线时，如果处于报警状态，其报警解析逻辑不正确的问题。

### 2.1.5
1. 修复因settGroupAttr中属性数组为空数组时，导致JS执行语句语法错误的问题。

### 2.1.4
1. 更新正式发布的稳定的uSDK4.1.01版本的包。

### 2.1.3
1. 更新最新的uSDK4.1.01版本的包。

### 2.1.2
1. 4.1.01版的uSDK中发现存在扫地机器人、洗衣机等部分设备不上线的问题，故将uSDK版本再还原回较稳定的4.0.01版本。

### 2.1.1
1. 替换原有存在BUG的uSDK包。

### 2.1.0
1. 升级uSDK版本至4.1.01版。
2. 采用新规则修正组命令名转换逻辑，即:普通0~9数字串为十进制，以0x开头的字符串进行16进制识别，其他字符串不做处理。

### 2.0.10
1. 修复JS引擎中解析组命令格式错误导致命令执行失败的问题。
2. 添加组命令名转16进制的逻辑。
3. 优化日志打印逻辑，以方便调试。

### 2.0.9
1. 调整UPDeviceCenter类的设备发现和移除逻辑。
2. 修复JS引擎在向JS设备下发命令时对于布尔型和数字型的属性值，未做字符串下发的BUG.
3. 完善JS引擎在执行JS代码时的日志，以及JS向native上报的设备信息日志。

### 2.0.8
1. 修复App从后台重新进入前台时，JS设备状态无法上报的Bug.
2. 修改UPDevice向JS上报的设备信息数据结构。增加表示设备连接状态的connection字段。
3. 将UPDevice中与JSAPI相关的数据上报接口方法单独封装，并开放出来。

### 2.0.7
1. 修复JS设备调用setAttr接口后，设备属性不刷新的问题，以及设置属性接口执行结果没有回调的Bug.

### 2.0.6
1. 增加已订阅设备，再次通过配置入网上线的判断逻辑和接口。
2. 报警模型增加code属性。
3. 调整JS设备命令下发逻辑以及属性变化上报逻辑。

### 2.0.5
1. 修复上报到JS的设备状态数据，JS无法正常解析的问题。
2. 修改解析从JS获取的设备属性字段的逻辑。

### 2.0.4
1. 修改并完善JS引擎模块逻辑，简化JS设备使用接口及方法。
2. BUG修复。

### 2.0.3
1. 将原先已经移除的UPCloudDevice相关类及逻辑，优化之后，再添加到UPDevice组件中。其仅包含从云平台获取的设备相关信息。
2. JS引擎模块逻辑调整优化。
3. BUG修复。

### 2.0.2
1. 修改UPJavaScriptDevice初始化方法修改。
2. UPDeviceEngineDelegate接口方法修改。
3. UPDeviceCenter中增加获得设备绑定信息的接口。

### 2.0.1
1. BUG修复。
2. UPDeviceAttribute增加初始化方法及类代码结构分类。
3. 部分类注释完善。

### 2.0.0
1. 移除UPDevice组件对UPCloudDevice的依赖，采用UPDeviceInformation替代相关设备信息。
2. 升级uSDK版本至4.0.01,并适配对应接口。
3. 对uSDK接口进行完全封装。
4. 增加UPJavaScriptDevice对象及JS引擎相关逻辑。
5. 扩充UPDeviceCenter类的接口及功能，移除UPuSDK类，原有接口及功能用UPDeviceCenter中代替。
6. 移除UPDeviceFactory类，采用UPDeviceFactoryDelegate进行替代。

### 1.4.3
1. 代码目录结构调整。
2. 修复重复绑定，设备状态不刷新的bug。

### 1.4.2
1. 增加用户云链接网关端口常量。

### 1.4.1
1. 优化代码结构及完善接口注释。

### 1.4.0
1. 替换修复了类名冲突问题的uSDK.framework。
2. 完善UPDevice组件注释及优化代码结构。

### 1.3.1
1. 完成uSDK_3.3.01版适配。

### 1.3.0
1. 适配uSDK3.3.01版的接口。

### 1.2.3
1. 修复blzId缺失的问题。

### 1.2.2
1. 更新uSDK至2.3.09_2.3.09_20160616093518573版。

### 1.2.1
1. 更新uSDKFramework至2.3.09_2.3.09。

### 1.2.0
1. 增加UPDevice报警上报的delegate方法。
