/** 初始化定义，web调用方式
** @param {string} 设备型号，Haier家电设备标准型号名称
** @param {string} 设备MAC，格式如：110ABF34234C
** @param {function} 设备订阅回调函数,只有设备真实状态发生改变时被回调，deviceAttrs为设备所有属性的Json集合
** @return device对象，该设备对象内对外只提供该文档内3个方法，getAttr, setAttr, setGroupAttr；当传入model值与当前js对应的model不一致时，抛出运行时异常
**/
var EP = new DeviceAPI.createDevice(model, macAddr, function(deviceAttrs){
	//订阅回调函数
});

/** 设备属性查询方法,根据传入deviceAttr名称数组中各自对应deviceAttr的属性值
** @param {[string]} deviceAttr设备属性名称的数组
** @return {object}，JSON字符串，包含查询结果以及被查询属性相关信息
**/
EP.getAttr([deviceAttr]);

EP.getAttr(["currentTemperature"]);
/*/return Object
{
	"retCode" : "00000",
	"retInfo" : "操作成功",
	"data" : [{
		"name" : "currentTemperature",
		"value" : "46",
    “class” : "integer"
		"logic" : {
			"readable" : true,
			"writeable" : true,
			"range" : {
				"type" : "step",
				"minValue" : 20,
				"maxValue" : 70,
				"step" : 1
			  }
		  }
	}]
}
*/
EP.getAttr(["resn1Time","resn1Temperature","resn1RunningStatus"]);
/*return Object
{
	"retCode" : "00000",
	"retInfo" : "操作成功",
	"data" : [
    {
		"name" : "resn1Time",
		"value" : "12:00",
    “class” : "string"
  		"logic" : {
			"readable" : true,
			"writeable" : true,
      //字符串无range时，不需要填写
		  }
	}},{
		{
		"name" : "resn1Temperature",
		"value" : "46",
    “class” : "integer"
		"logic" : {
			"readable" : true,
			"writeable" : true,
			"range" : {
				"type" : "step",
				"minValue" : 20,
				"maxValue" : 70,
				"step" : 1
			  }
		  }
	}},{
		resn1RunningStatus : {
		"name" : "resn1RunningStatus",
		"value" : false,
    “class” : "boolean"
		"logic" : {
			"readable" : true,
			"writeable" : true,
      "range" : {
				"type" : "enum",
				"list" : [true, false]
			  }

		}
	}}]
}
*/

/** 设备操作 **/
/** 设置设备的状态属性，每次虚拟set一个deviceAttr，不实际下发设备命令
** @param {object} attr 设备属性信息，包括包括属性名称和属性值
** @param {function} successCallback 成功回调函数，deviceAttrs为受该次函数调用影响到的设备属性集合
** @param {function} errorCallback 失败回调函数，返回错误信息，错误信息中只包含retInfo, retData
**/
EP.setAttr(deviceAttr, successCallback, errorCallback);

EP.setAttr([{'onOffStatus':true}],function(deviceAttrs){},function(){});


/** 组命令指令,每次虚拟set一个组命令，不实际下发设备命令
** @param {object} groupName 组命令名称，与ID文档组命令名称一致
** @param {object} attrArray 设备状态信息，包括key和value
** @param {function} successCallback 成功回调函数，deviceAttrs为受该次函数调用影响到的设备属性集合
** @param {function} errorCallback 失败回调函数，返回错误信息，错误信息中只包含retInfo, retData，以及当前出错的deviceAttr名称
**/
EP.setGroupAttr(groupName, attrArray, successCallback, errorCallback);

EP.setGroupAttr('000001',[{'resn1Temperature':'43'},{'resn1RunningStatus':true},{'resn1Time':'19:00'}],function(){},function(){});

/** 设备操作指令，将前一次操作函数调用与本次函数调用间所有的虚拟set指令进行实际下发，并清除所有set记录
** @param {function} successCallback 成功回调函数，返回操作成功
** @param {function} errorCallback 失败回调函数，返回错误信息，错误信息中只包含retInfo, retData，以及当前出错的deviceAttr名称
**/
EP.operation(successCallback, errorCallback)

/** 设备虚拟操作运算，将前一次操作函数调用与本次函数调用间所有的虚拟set指令进行运算得出需要下发的命令集合
** @param {boolean} isClean 虚拟运算是否清除所有set操作记录，true：清除， false：不清除
** @return {object}，JSON字符串，实际下发给设备的多个6位ID码集合或者属性操作集合
**/
EP.calculate(isClean)
