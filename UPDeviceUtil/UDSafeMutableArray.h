//
//  UpSafeMutableArray.h
//  UPDevice
//
//  Created by osiris on 2020/9/16.
//  Copyright © 2020 海尔优家智能科技（北京）有限公司. All rights reserved.
//

#import <Foundation/Foundation.h>

NS_ASSUME_NONNULL_BEGIN

@interface UDSafeMutableArray <__covariant T> : NSObject

@property(nonatomic, copy, readonly) NSArray<T> *array;
@property (nonatomic, assign, readonly) NSInteger count;

- (void)addObject:(T)anObject;

- (void)insertObject:(T)anObject atIndex:(NSUInteger)index;

- (void)exchangeObjectAtIndex:(NSUInteger)idx1 withObjectAtIndex:(NSUInteger)idx2;

- (void)removeObject:(T)obj;

- (void)removeLastObject;

- (void)removeObjectAtIndex:(NSUInteger)index;

- (void)replaceObjectAtIndex:(NSUInteger)index withObject:(T)anObject;

- (nullable T)objectAtIndex:(NSUInteger)index;

- (BOOL)containsObject:(T)obj;

- (NSUInteger)indexOfObject:(T)obj;

- (nullable T)firstObject;

- (nullable T)lastObject;

- (nullable id)objectAtIndexedSubscript:(NSUInteger)index;

- (void)removeAllObjects;

- (void)setObject:(T)obj atIndexedSubscript:(NSUInteger)index;

@end
NS_ASSUME_NONNULL_END
