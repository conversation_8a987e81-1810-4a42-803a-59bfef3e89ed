//
//  UpSafeMutableArray.m
//  UPDevice
//
//  Created by osiris on 2020/9/16.
//  Copyright © 2020 海尔优家智能科技（北京）有限公司. All rights reserved.
//

#import "UDSafeMutableArray.h"

@interface UDSafeMutableArray <__covariant T>
()

    @property(nonatomic, strong) NSMutableArray<T> *safeArray;
@property (nonatomic, strong) dispatch_queue_t readWriteQuene;

@end

@implementation UDSafeMutableArray
- (instancetype)init
{
    self = [super init];
    if (self) {
        _safeArray = [NSMutableArray array];
        _readWriteQuene = dispatch_queue_create("com.haier.updevice.array", DISPATCH_QUEUE_CONCURRENT);
    }
    return self;
}

- (NSArray *)array
{
    __block typeof(_safeArray) item = nil;
    dispatch_sync(self.readWriteQuene, ^{
      item = [self.safeArray copy];
    });
    return item;
}

- (void)removeAllObjects
{
    dispatch_barrier_async(self.readWriteQuene, ^{
      [self.safeArray removeAllObjects];
    });
}

- (NSInteger)count
{
    __block NSInteger item = 0;
    dispatch_sync(self.readWriteQuene, ^{
      item = self.safeArray.count;
    });
    return item;
}

- (void)addObject:(id)anObject
{
    dispatch_barrier_async(self.readWriteQuene, ^{
      [self.safeArray addObject:anObject];
    });
}

- (void)insertObject:(id)anObject atIndex:(NSUInteger)index
{
    dispatch_barrier_async(self.readWriteQuene, ^{
      [self.safeArray insertObject:anObject atIndex:index];
    });
}

- (void)exchangeObjectAtIndex:(NSUInteger)idx1 withObjectAtIndex:(NSUInteger)idx2
{
    dispatch_barrier_async(self.readWriteQuene, ^{
      [self.safeArray exchangeObjectAtIndex:idx1 withObjectAtIndex:idx2];
    });
}

- (void)removeObject:(id)obj
{
    dispatch_barrier_async(self.readWriteQuene, ^{
      [self.safeArray removeObject:obj];
    });
}

- (void)removeLastObject
{
    dispatch_barrier_async(self.readWriteQuene, ^{
      [self.safeArray removeLastObject];
    });
}

- (void)removeObjectAtIndex:(NSUInteger)index
{
    dispatch_barrier_async(self.readWriteQuene, ^{
      [self.safeArray removeObjectAtIndex:index];
    });
}

- (void)replaceObjectAtIndex:(NSUInteger)index withObject:(id)anObject
{
    dispatch_barrier_async(self.readWriteQuene, ^{
      [self.safeArray replaceObjectAtIndex:index withObject:anObject];
    });
}

- (nullable id)objectAtIndex:(NSUInteger)index
{
    __block id item = nil;
    dispatch_sync(self.readWriteQuene, ^{
      if (index <= self.safeArray.count - 1) {
          item = [self.safeArray objectAtIndex:index];
      }
    });
    return item;
}

- (BOOL)containsObject:(id)obj
{
    __block BOOL result = NO;
    dispatch_sync(self.readWriteQuene, ^{
      result = [self.safeArray containsObject:obj];
    });
    return result;
}

- (NSUInteger)indexOfObject:(id)obj
{
    __block NSUInteger index = 0;
    dispatch_sync(self.readWriteQuene, ^{
      index = [_safeArray indexOfObject:obj];
    });
    return index;
}

- (nullable id)firstObject
{
    __block id item = nil;
    dispatch_sync(self.readWriteQuene, ^{
      if (self.safeArray.count > 0) {
          item = [self.safeArray objectAtIndex:0];
      }
    });
    return item;
}

- (nullable id)lastObject
{
    __block id item = nil;
    dispatch_sync(self.readWriteQuene, ^{
      NSUInteger size = self.safeArray.count;
      if (size > 0) {
          item = self.safeArray[size - 1];
      }
    });
    return item;
}


- (nullable id)objectAtIndexedSubscript:(NSUInteger)index
{
    __block id item = nil;
    dispatch_sync(self.readWriteQuene, ^{
      if (index <= self.safeArray.count - 1) {
          item = self.safeArray[index];
      }
    });
    return item;
}

- (void)setObject:(id)obj atIndexedSubscript:(NSUInteger)index
{
    dispatch_barrier_async(self.readWriteQuene, ^{
      self.safeArray[index] = obj;
    });
}

@end
