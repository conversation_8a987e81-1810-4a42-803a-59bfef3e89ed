//
//  UpDeviceResourceInstallTool.m
//  UPDevice
//
//  Created by 王杰 on 2022/12/14.
//  Copyright © 2022 海尔优家智能科技（北京）有限公司. All rights reserved.
//

#import "UpDeviceGetDeviceResourceTool.h"
#import "UPDeviceLog.h"
@interface UpDeviceGetDeviceResourceTool () <UpResourceSelector, UPResourceCallback>
/**
 Description
 */
@property (nonatomic, strong) UPResourceManager *resourceManager;
@property (nonatomic, strong) UPResourceDeviceCondition *condition;
@property (nonatomic, copy) void (^completeion)(UpDeviceResult *result);
@end
@implementation UpDeviceGetDeviceResourceTool

- (instancetype)initWithResoureManager:(UPResourceManager *)resouceManager condition:(UPResourceDeviceCondition *)condition;
{
    if (self = [super init]) {
        self.resourceManager = resouceManager;
        self.condition = condition;
    }
    return self;
}

- (void)getResourceWithCompleteion:(void (^)(UpDeviceResult *_Nonnull))completeion
{
    self.completeion = completeion;
    if (!self.resourceManager || !self.condition) {
        UpDeviceResult *result = [UpDeviceResult UpDeviceResult:ErrorCode_FAILURE extraData:nil];
        result.extraInfo = @"resourceManager or condition  is null";
        self.completeion ? self.completeion(result) : nil;
        return;
    }
    [self.resourceManager getDeviceResource:self.condition selector:self callback:self listener:nil];
}

#pragma mark UPResourceCallback
- (void)onResult:(BOOL)success message:(NSString *)message resourceInfo:(UPResourceInfo *)info
{
    UPDeviceLogDebug(@"[%s][%d]getDeviceResource on Result prodNo:%@ success:%d  message:%@", __PRETTY_FUNCTION__, __LINE__, self.condition.prodNo, success, message);
    UpDeviceResultErrorCode code;
    if (success) {
        code = ErrorCode_SUCCESS;
    }
    else {
        code = ErrorCode_FAILURE;
    }
    UpDeviceResult *result = [UpDeviceResult UpDeviceResult:code extraData:info];
    result.extraInfo = message;
    self.completeion ? self.completeion(result) : nil;
}


#pragma mark UpResourceSelector
- (nonnull UPResourceInfo *)selectFrom:(nonnull NSArray<UPResourceInfo *> *)infoList
{
    __block UPResourceInfo *info = nil;
    [infoList enumerateObjectsUsingBlock:^(UPResourceInfo *_Nonnull obj, NSUInteger idx, BOOL *_Nonnull stop) {
      if (obj.type == self.condition.resourceType) {
          info = obj;
          *stop = YES;
      }
    }];
    return info;
}

@end
