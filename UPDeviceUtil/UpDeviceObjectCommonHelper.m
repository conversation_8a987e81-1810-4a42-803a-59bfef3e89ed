//
//  UpDeviceObjectCommonHelper.m
//  UPDeviceUtil
//
//  Created by 王杰 on 2022/4/14.
//  Copyright © 2022 海尔优家智能科技（北京）有限公司. All rights reserved.
//

#import "UpDeviceObjectCommonHelper.h"

BOOL UPDevice_isValidString(NSString *str)
{
    if (![str isKindOfClass:[NSString class]]) {
        return NO;
    }
    if (str == nil || str == NULL) {
        return NO;
    }
    if ([str length] == 0) {
        return NO;
    }
    return YES;
};

BOOL UPDevice_isValidOutSpaceString(NSString *str)
{

    if (![str isKindOfClass:[NSString class]]) {
        return NO;
    }
    if (str == nil || str == NULL) {
        return NO;
    }
    if ([[str stringByTrimmingCharactersInSet:[NSCharacterSet whitespaceCharacterSet]] length] == 0) {
        return NO;
    }
    return YES;
};

BOOL UPDevice_isValidDictionary(NSDictionary *dict)
{
    if (![dict isKindOfClass:[NSDictionary class]] || dict.allValues.count == 0) {
        return NO;
    }
    else {
        return YES;
    }
}
