//
//  UpSafeMutableDictionary.m
//  UPDevice
//
//  Created by osiris on 2020/9/16.
//  Copyright © 2020 海尔优家智能科技（北京）有限公司. All rights reserved.
//

#import "UDSafeMutableDictionary.h"

@interface UDSafeMutableDictionary < __covariant K : id <NSCopying>
, __covariant V > ()

                      @property(nonatomic, strong) NSMutableDictionary<K, V> *dictionary;
@property (nonatomic, strong) dispatch_queue_t readWriteQuene;

@end

@implementation UDSafeMutableDictionary

- (instancetype)init
{
    self = [super init];
    if (self) {
        _dictionary = [NSMutableDictionary dictionary];
        _readWriteQuene = dispatch_queue_create("com.haier.updevice.dictionary", DISPATCH_QUEUE_CONCURRENT);
    }
    return self;
}

- (void)removeObjectForKey:(id)aKey
{
    dispatch_barrier_async(self.readWriteQuene, ^{
      [self.dictionary removeObjectForKey:aKey];
    });
}

- (void)setObject:(id)anObject forKey:(id)aKey
{
    dispatch_barrier_async(self.readWriteQuene, ^{
      [self.dictionary setObject:anObject forKey:aKey];
    });
}

- (nullable id)objectForKey:(id)aKey
{
    __block id item = nil;
    dispatch_sync(self.readWriteQuene, ^{
      item = [self.dictionary objectForKey:aKey];
    });
    return item;
}

- (NSArray<id> *)allKeys
{
    __block NSArray<id> *keys;
    dispatch_sync(self.readWriteQuene, ^{
      keys = [self.dictionary allKeys];
    });
    return keys;
}

- (NSArray<id> *)allValues
{
    __block NSArray<id> *values;
    dispatch_sync(self.readWriteQuene, ^{
      values = [self.dictionary allValues];
    });
    return values;
}

- (id)objectForKeyedSubscript:(id<NSCopying>)key
{
    __block id value = nil;
    dispatch_sync(self.readWriteQuene, ^{
      value = self.dictionary[key];
    });
    return value;
}

- (void)setObject:(id)obj forKeyedSubscript:(id<NSCopying>)key
{
    dispatch_barrier_async(self.readWriteQuene, ^{
      self.dictionary[key] = obj;
    });
}

@end
