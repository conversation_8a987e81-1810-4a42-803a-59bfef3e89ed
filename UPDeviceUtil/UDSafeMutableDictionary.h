//
//  UpSafeMutableDictionary.h
//  UPDevice
//
//  Created by osiris on 2020/9/16.
//  Copyright © 2020 海尔优家智能科技（北京）有限公司. All rights reserved.
//

#import <Foundation/Foundation.h>

NS_ASSUME_NONNULL_BEGIN

@interface UDSafeMutableDictionary < __covariant K : id <NSCopying>
, __covariant V > : NSObject

                    -
                    (void)removeObjectForKey : (K)aKey;

- (void)setObject:(V)anObject forKey:(K)aKey;

- (nullable V)objectForKey:(K)aKey;

- (NSArray<K> *)allKeys;

- (NSArray<V> *)allValues;

- (V)objectForKeyedSubscript:(K)key;
- (void)setObject:(V)obj forKeyedSubscript:(K)key;

@end

NS_ASSUME_NONNULL_END
