//
//  UPDeviceUtilTests.m
//  UPDeviceUtilTests
//
//  Created by 闫达 on 2021/4/12.
//  Copyright © 2021 海尔优家智能科技（北京）有限公司. All rights reserved.
//

#import <XCTest/XCTest.h>

@interface UPDeviceUtilTests : XCTestCase

@end

@implementation UPDeviceUtilTests

- (void)setUp
{
    // Put setup code here. This method is called before the invocation of each test method in the class.
}

- (void)tearDown
{
    // Put teardown code here. This method is called after the invocation of each test method in the class.
}

- (void)testExample
{
    // This is an example of a functional test case.
    // Use XCTAssert and related functions to verify your tests produce the correct results.
}

- (void)testPerformanceExample
{
    // This is an example of a performance test case.
    [self measureBlock:^{
        // Put the code you want to measure the time of here.
    }];
}

@end
