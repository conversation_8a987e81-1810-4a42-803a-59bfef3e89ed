PODS:
  - AFNetworking (4.0.1):
    - AFNetworking/NSURLSession (= 4.0.1)
    - AFNetworking/Reachability (= 4.0.1)
    - AFNetworking/Security (= 4.0.1)
    - AFNetworking/Serialization (= 4.0.1)
    - AFNetworking/UIKit (= 4.0.1)
  - AFNetworking/NSURLSession (4.0.1):
    - AFNetworking/Reachability
    - AFNetworking/Security
    - AFNetworking/Serialization
  - AFNetworking/Reachability (4.0.1)
  - AFNetworking/Security (4.0.1)
  - AFNetworking/Serialization (4.0.1)
  - AFNetworking/UIKit (4.0.1):
    - AFNetworking/NSURLSession
  - Aspects (1.4.1)
  - AWFileHash (0.2.0)
  - Cucumberish (1.4.0)
  - FMDB (2.7.12):
    - FMDB/standard (= 2.7.12)
  - FMDB/Core (2.7.12)
  - FMDB/SQLCipher (2.7.12):
    - FMDB/Core
    - SQLCipher (~> 4.6)
  - FMDB/standard (2.7.12):
    - FMDB/Core
  - Godzip (1.0.0)
  - LogicEngine (999.999.999.2024022301)
  - MJExtension (3.2.1)
  - OCHamcrest (7.1.2)
  - OCMock (3.8.1)
  - Protobuf (3.17.0)
  - Reachability (3.7.6)
  - Realm (10.28.3):
    - Realm/Headers (= 10.28.3)
  - Realm/Headers (10.28.3)
  - SQLCipher (4.7.0):
    - SQLCipher/standard (= 4.7.0)
  - SQLCipher/common (4.7.0)
  - SQLCipher/standard (4.7.0):
    - SQLCipher/common
  - uAnalytics (3.8.2)
  - UHMasonry (1.1.2.2023060801)
  - UHWebImage (3.8.7.2024012901):
    - UHWebImage/Core (= 3.8.7.2024012901)
  - UHWebImage/Core (3.8.7.2024012901)
  - UPCore (3.6.1.2024110701):
    - UPCore/CoreHive (= 3.6.1.2024110701)
    - UPCore/LaunchTime (= 3.6.1.2024110701)
    - UPCore/MRC (= 3.6.1.2024110701)
    - UPCore/others (= 3.6.1.2024110701)
    - UPCore/toggles (= 3.6.1.2024110701)
    - UPCore/UPContext (= 3.6.1.2024110701)
  - UPCore/CoreHive (3.6.1.2024110701):
    - UPCore/LaunchTime
    - UPCore/UPContext
    - UpTrace/UpTrace
    - UpTrace/UpTraceCore
  - UPCore/LaunchTime (3.6.1.2024110701):
    - YYModel
  - UPCore/MRC (3.6.1.2024110701)
  - UPCore/others (3.6.1.2024110701)
  - UPCore/toggles (3.6.1.2024110701)
  - UPCore/UPContext (3.6.1.2024110701):
    - UPStorage (>= 1.4.13)
    - YYModel
  - uplog (*******.2023032102):
    - AFNetworking (>= 4.0.1)
    - Protobuf (= 3.17.0)
    - ZipArchive (>= 1.4.0)
  - upnetwork (********24082701):
    - AFNetworking (= 4.0.1)
    - MJExtension (>= ********)
    - uplog (>= 1.1.8)
    - upnetwork/DynamicSign (= ********24082701)
    - upnetwork/Headers (= ********24082701)
    - upnetwork/HTTPDns (= ********24082701)
    - upnetwork/Manager (= ********24082701)
    - upnetwork/Request (= ********24082701)
    - upnetwork/Settings (= ********24082701)
    - upnetwork/Utils (= ********24082701)
  - upnetwork/DynamicSign (********24082701):
    - AFNetworking (= 4.0.1)
    - MJExtension (>= ********)
    - uplog (>= 1.1.8)
    - upnetwork/DynamicSign/Private (= ********24082701)
  - upnetwork/DynamicSign/Private (********24082701):
    - AFNetworking (= 4.0.1)
    - MJExtension (>= ********)
    - uplog (>= 1.1.8)
  - upnetwork/Headers (********24082701):
    - AFNetworking (= 4.0.1)
    - MJExtension (>= ********)
    - uplog (>= 1.1.8)
    - upnetwork/Headers/Private (= ********24082701)
  - upnetwork/Headers/Private (********24082701):
    - AFNetworking (= 4.0.1)
    - MJExtension (>= ********)
    - uplog (>= 1.1.8)
  - upnetwork/HTTPDns (********24082701):
    - AFNetworking (= 4.0.1)
    - MJExtension (>= ********)
    - uplog (>= 1.1.8)
  - upnetwork/Manager (********24082701):
    - AFNetworking (= 4.0.1)
    - MJExtension (>= ********)
    - uplog (>= 1.1.8)
    - upnetwork/Manager/Private (= ********24082701)
  - upnetwork/Manager/Private (********24082701):
    - AFNetworking (= 4.0.1)
    - MJExtension (>= ********)
    - uplog (>= 1.1.8)
  - upnetwork/Request (********24082701):
    - AFNetworking (= 4.0.1)
    - MJExtension (>= ********)
    - uplog (>= 1.1.8)
    - upnetwork/Request/Private (= ********24082701)
  - upnetwork/Request/Private (********24082701):
    - AFNetworking (= 4.0.1)
    - MJExtension (>= ********)
    - uplog (>= 1.1.8)
  - upnetwork/Settings (********24082701):
    - AFNetworking (= 4.0.1)
    - MJExtension (>= ********)
    - uplog (>= 1.1.8)
    - upnetwork/Settings/Private (= ********24082701)
  - upnetwork/Settings/Private (********24082701):
    - AFNetworking (= 4.0.1)
    - MJExtension (>= ********)
    - uplog (>= 1.1.8)
  - upnetwork/Utils (********24082701):
    - AFNetworking (= 4.0.1)
    - MJExtension (>= ********)
    - uplog (>= 1.1.8)
    - upnetwork/Utils/Private (= ********24082701)
  - upnetwork/Utils/Private (********24082701):
    - AFNetworking (= 4.0.1)
    - MJExtension (>= ********)
    - uplog (>= 1.1.8)
  - UPResource (2.26.2.2025031801):
    - AWFileHash (>= 0.1.0)
    - FMDB (>= 2.7.5)
    - UHWebImage
    - UPCore/toggles (>= 3.5.12.2024032301)
    - uplog (>= 1.1.8)
    - upnetwork (>= 4.0.0)
    - UPResource/UPRes (= 2.26.2.2025031801)
    - UPResource/UPResDelegateIMP (= 2.26.2.2025031801)
    - UPTools/ModuleLanguage
    - UPTools/Others
    - ZipArchive (>= 1.4.0)
  - UPResource/UPRes (2.26.2.2025031801):
    - AWFileHash (>= 0.1.0)
    - FMDB (>= 2.7.5)
    - UHWebImage
    - UPCore/toggles (>= 3.5.12.2024032301)
    - uplog (>= 1.1.8)
    - upnetwork (>= 4.0.0)
    - UPTools/ModuleLanguage
    - UPTools/Others
    - ZipArchive (>= 1.4.0)
  - UPResource/UPResDelegateIMP (2.26.2.2025031801):
    - AWFileHash (>= 0.1.0)
    - FMDB (>= 2.7.5)
    - UHWebImage
    - UPCore/toggles (>= 3.5.12.2024032301)
    - uplog (>= 1.1.8)
    - upnetwork (>= 4.0.0)
    - UPTools/ModuleLanguage
    - UPTools/Others
    - ZipArchive (>= 1.4.0)
  - UPSafeColletion (1.0.0.2023112301)
  - UPStorage (********.2021120601):
    - FMDB/SQLCipher (>= 2.7.5)
    - uplog (>= 1.1.12)
    - UPStorage/Common (= ********.2021120601)
    - UPStorage/DataChange (= ********.2021120601)
    - UPStorage/Manager (= ********.2021120601)
    - UPStorage/Private (= ********.2021120601)
    - UPStorage/Public (= ********.2021120601)
    - UPStorage/Storage (= ********.2021120601)
    - UPStorage/UPStorageUtil (= ********.2021120601)
  - UPStorage/Common (********.2021120601):
    - FMDB/SQLCipher (>= 2.7.5)
    - uplog (>= 1.1.12)
  - UPStorage/DataChange (********.2021120601):
    - FMDB/SQLCipher (>= 2.7.5)
    - uplog (>= 1.1.12)
  - UPStorage/Manager (********.2021120601):
    - FMDB/SQLCipher (>= 2.7.5)
    - uplog (>= 1.1.12)
  - UPStorage/Private (********.2021120601):
    - FMDB/SQLCipher (>= 2.7.5)
    - uplog (>= 1.1.12)
  - UPStorage/Public (********.2021120601):
    - FMDB/SQLCipher (>= 2.7.5)
    - uplog (>= 1.1.12)
  - UPStorage/Storage (********.2021120601):
    - FMDB/SQLCipher (>= 2.7.5)
    - uplog (>= 1.1.12)
  - UPStorage/UPStorageUtil (********.2021120601):
    - FMDB/SQLCipher (>= 2.7.5)
    - uplog (>= 1.1.12)
  - UPTools/ModuleLanguage (999.999.999.2025031001):
    - AFNetworking (>= 3.1.0)
    - UHMasonry (>= 1.1.1)
  - UPTools/Others (999.999.999.2025031001):
    - AFNetworking (>= 3.1.0)
    - Aspects (>= 1.0.0)
    - uAnalytics (>= 3.2.0)
    - UHMasonry (>= 1.1.1)
    - UPCore/UPContext
    - uplog
    - YYCategories
  - UpTrace (*******.2023032201):
    - Godzip (= 1.0.0)
    - MJExtension (>= 3.2.1)
    - Realm (= 10.28.3)
    - uplog (>= 1.4.0)
    - upnetwork (>= 4.0.6)
    - UpTrace/UpTrace (= *******.2023032201)
    - UpTrace/UpTraceCore (= *******.2023032201)
  - UpTrace/UpTrace (*******.2023032201):
    - Godzip (= 1.0.0)
    - MJExtension (>= 3.2.1)
    - Realm (= 10.28.3)
    - uplog (>= 1.4.0)
    - upnetwork (>= 4.0.6)
  - UpTrace/UpTraceCore (*******.2023032201):
    - Godzip (= 1.0.0)
    - MJExtension (>= 3.2.1)
    - Realm (= 10.28.3)
    - uplog (>= 1.4.0)
    - upnetwork (>= 4.0.6)
  - upuserdomain (3.31.0.2025041801):
    - MJExtension (= 3.2.1)
    - uplog (>= 1.1.8)
    - upnetwork (>= 3.2.1)
    - upuserdomain/upuserdomain (= 3.31.0.2025041801)
    - upuserdomain/UserDomainAPIs (= 3.31.0.2025041801)
    - upuserdomain/UserDomainDataSource (= 3.31.0.2025041801)
  - upuserdomain/upuserdomain (3.31.0.2025041801):
    - MJExtension (= 3.2.1)
    - uplog (>= 1.1.8)
    - upnetwork (>= 3.2.1)
  - upuserdomain/UserDomainAPIs (3.31.0.2025041801):
    - MJExtension (= 3.2.1)
    - uplog (>= 1.1.8)
    - upnetwork (>= 3.2.1)
  - upuserdomain/UserDomainDataSource (3.31.0.2025041801):
    - MJExtension (= 3.2.1)
    - uplog (>= 1.1.8)
    - upnetwork (>= 3.2.1)
  - UPVDN (*******.2023032102):
    - uplog (>= 1.1.2)
    - UPVDN/Back (= *******.2023032102)
    - UPVDN/Categorys (= *******.2023032102)
    - UPVDN/Launcher (= *******.2023032102)
    - UPVDN/Page (= *******.2023032102)
    - UPVDN/Patch (= *******.2023032102)
    - UPVDN/ResultListener (= *******.2023032102)
    - UPVDN/Utils (= *******.2023032102)
    - UPVDN/VDNManager (= *******.2023032102)
    - UPVDN/Vdns (= *******.2023032102)
    - UPVDN/VirtualDomain (= *******.2023032102)
  - UPVDN/Back (*******.2023032102):
    - uplog (>= 1.1.2)
  - UPVDN/Categorys (*******.2023032102):
    - uplog (>= 1.1.2)
  - UPVDN/Launcher (*******.2023032102):
    - uplog (>= 1.1.2)
    - UPVDN/Launcher/Native (= *******.2023032102)
  - UPVDN/Launcher/Native (*******.2023032102):
    - uplog (>= 1.1.2)
  - UPVDN/Page (*******.2023032102):
    - uplog (>= 1.1.2)
  - UPVDN/Patch (*******.2023032102):
    - uplog (>= 1.1.2)
  - UPVDN/ResultListener (*******.2023032102):
    - uplog (>= 1.1.2)
  - UPVDN/Utils (*******.2023032102):
    - uplog (>= 1.1.2)
  - UPVDN/VDNManager (*******.2023032102):
    - uplog (>= 1.1.2)
  - UPVDN/Vdns (*******.2023032102):
    - uplog (>= 1.1.2)
  - UPVDN/VirtualDomain (*******.2023032102):
    - uplog (>= 1.1.2)
  - uSDK (10.9.0.2025070501):
    - uSDKCommon (>= 1.5.0.Beta)
  - uSDKCommon (1.6.0)
  - YYCategories (1.0.4):
    - YYCategories/no-arc (= 1.0.4)
  - YYCategories/no-arc (1.0.4)
  - YYModel (1.0.4)
  - ZipArchive (1.4.0)

DEPENDENCIES:
  - Cucumberish (= 1.4.0)
  - LogicEngine (= 999.999.999.2024022301)
  - OCHamcrest (~> 7.1.2)
  - OCMock (= 3.8.1)
  - Reachability (>= 3.2.0)
  - UPCore (= 3.6.1.2024110701)
  - uplog (= *******.2023032102)
  - UPResource (= 2.26.2.2025031801)
  - UPSafeColletion (= 1.0.0.2023112301)
  - UPStorage (= ********.2021120601)
  - UpTrace (= *******.2023032201)
  - upuserdomain (= 3.31.0.2025041801)
  - UPVDN (= *******.2023032102)
  - uSDK (= 10.9.0.2025070501)

SPEC REPOS:
  https://git.haier.net/uplus/shell/cocoapods/Specs.git:
    - LogicEngine
    - uAnalytics
    - UHMasonry
    - UHWebImage
    - UPCore
    - uplog
    - upnetwork
    - UPResource
    - UPSafeColletion
    - UPStorage
    - UPTools
    - UpTrace
    - upuserdomain
    - UPVDN
    - uSDK
    - uSDKCommon
  trunk:
    - AFNetworking
    - Aspects
    - AWFileHash
    - Cucumberish
    - FMDB
    - Godzip
    - MJExtension
    - OCHamcrest
    - OCMock
    - Protobuf
    - Reachability
    - Realm
    - SQLCipher
    - YYCategories
    - YYModel
    - ZipArchive

SPEC CHECKSUMS:
  AFNetworking: 3bd23d814e976cd148d7d44c3ab78017b744cd58
  Aspects: 7595ba96a6727a58ebcbfc954497fc5d2fdde546
  AWFileHash: 5b0d1f458a1b8d4de636f12f2d71039494899521
  Cucumberish: 6cbd0c1f50306b369acebfe7d9f514c9c287d26c
  FMDB: 728731dd336af3936ce00f91d9d8495f5718a0e6
  Godzip: 4ee041500d1b0d56aa2415af3d99b932bfdec007
  LogicEngine: 06371129f4ff7a892a3dbb9658a05d492d818687
  MJExtension: 635f2c663dcb1bf76fa4b715b2570a5710aec545
  OCHamcrest: b284c9592c28c1e4025a8542e67ea41a635d0d73
  OCMock: 29f6e52085b4e7d9b075cbf03ed7c3112f82f934
  Protobuf: 7327d4444215b5f18e560a97f879ff5503c4581c
  Reachability: fd0ecd23705e2599e4cceeb943222ae02296cbc6
  Realm: 64e66568d981de2496f81ecab2e1372b27dc7d58
  SQLCipher: ba9d0076041ed767c5bd3d3f77098318d04a403c
  uAnalytics: 5e5ec2958ebd8d1e253cd8b54d5ee76285cc6eab
  UHMasonry: 3be12fd6fbbb52fad07a26b4e0a8c667c9fd24f4
  UHWebImage: 2950f4cf024b5c8c631719b8f6e2e462da3521ea
  UPCore: 66c6a651ab8e6a812d3696694310bd4eb761ce78
  uplog: 281c08ec5f1b24b24130d0f12ed74784618f7a3c
  upnetwork: fb56ef7af2848a925e9c191d4526503aa4675b1f
  UPResource: 48a91131002eb6ba2d5969c0b2235809338232fd
  UPSafeColletion: 9db70b7d16fc9f66034c6a09c5e14f3960f94a9a
  UPStorage: 98d060d48c95cbf1084290f680a27820593ff781
  UPTools: c0ace31809f2f1ca9f7dd755a20684f907643edc
  UpTrace: ccc0f47df4f3b86959b319d371209b10f59d23a4
  upuserdomain: 41ddc158e67dcbb62888340676fe4ae39a63b8d0
  UPVDN: cf8f1246c0af89bae2f5b7191880a177f1319d8e
  uSDK: f64409aafc6569f817d2af1eec4f06e7f1b81c38
  uSDKCommon: d489f55eeb41c756fb105b97c006cd47cb75196d
  YYCategories: 6bcd4314c6661a561410dce4a793379ebd306abd
  YYModel: 2a7fdd96aaa4b86a824e26d0c517de8928c04b30
  ZipArchive: e25a4373192673e3229ac8d6e9f64a3e5713c966

PODFILE CHECKSUM: 74d00bc02a1528aa7e729ec5453f35bf718a4146

COCOAPODS: 1.12.1
