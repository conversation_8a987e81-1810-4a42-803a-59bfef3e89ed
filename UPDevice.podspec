Pod::Spec.new do |s|
  s.name             = "UPDevice"
  s.version          = "10.4.0.**********"
  s.author           = { "YueLei" => "<EMAIL>" }
  s.license          = 'MIT'
  s.summary          = "iOS UPDevice library"
  s.homepage = 'www.example.com'
  s.platform       = :ios, '12.0'
  s.requires_arc  = true
  s.frameworks = 'Foundation'
  s.user_target_xcconfig = { 'OTHER_LDFLAGS' => '-lc++' }
  s.module_name = 'UPDevice'
  s.source           = { :git => "https://git.haier.net/uplus/ios/UpDevice_Ios.git", :tag => s.version.to_s}
  s.subspec 'UPDevice' do |updeviceSpec|
      updeviceSpec.subspec 'compat' do |compat|
          compat.subspec 'api' do |api|
              api.source_files = 'UPDevice/compat/api/*.{h,m}'
          end
          compat.source_files = 'UPDevice/compat/*.{h,m}'
      end
      updeviceSpec.subspec 'dummy' do |dummy|
          dummy.source_files = 'UPDevice/dummy/*.{h,m}'
      end
      updeviceSpec.subspec 'wrapper' do |wrapper|
          wrapper.source_files = 'UPDevice/wrapper/*.{h,m}'
      end
      updeviceSpec.source_files = 'UPDevice/*.{h,m}'
  end
  s.subspec 'updevice-non-network' do |nonNetwork|
      nonNetwork.source_files = 'UPDevice/updevice-non-network/*.{h,m}'
  end
  s.subspec 'updevice-logic-engine' do |engine|
    engine.source_files = 'UPDevice/updevice-logic-engine/*.{h,m}'
  end
  
  s.subspec 'updevice-non-network' do |nonnetwork|
    nonnetwork.source_files = 'UPDevice/updevice-non-network/*.{h,m}'
  end
  s.subspec 'updevice-aggregate' do |aggregate|
    aggregate.source_files = 'UPDevice/updevice-aggregate/*.{h,m}'
  end
  s.subspec 'utils' do |engine|
    engine.source_files = 'UPDevice/util/*.{h,m}'
  end
  s.subspec 'voicebox' do |voicebox|
    voicebox.source_files = 'UPDevice/voicebox/*.{h,m}'
  end
  s.subspec 'wash' do |wash|
    wash.source_files = 'UPDevice/wash/*.{h,m}'
  end
  s.subspec 'gio' do |gio|
    gio.source_files = 'UPDevice/gio/*.{h,m}'
  end
  s.subspec 'model' do |model|
    model.source_files = 'UPDevice/model/*.{h,m}'
  end
  s.subspec 'updevice-toolkit-usdk' do |toolkitusdk|
      toolkitusdk.subspec 'usdk' do |usdk|
          usdk.subspec 'action' do |action|
              action.source_files = 'UPDevice/updevice-toolkit-usdk/usdk/action/*.{h,m}'
          end
          usdk.source_files = 'UPDevice/updevice-toolkit-usdk/usdk/*.{h,m}'
      end
  end
  s.subspec 'UPDeviceUtil' do |ss|
    ss.source_files = 'UPDeviceUtil/*.{h,m}','UPDeviceUtil/**/*.{h,m}'
  end
  s.subspec 'updevice_api' do |api|
    api.source_files = 'updevice_api/*.{h,m}','updevice_api/**/*.{h,m}'
  end
  s.resources = ['doc/readme.md']
  s.dependency "UPResource",">=2.13.0"
  s.dependency "LogicEngine",">=2.1.11"
  s.dependency "uSDK",">= 5.0.1"
  s.dependency "uplog",">= 1.1.12"
  s.dependency "UPStorage",">= 1.4.0"
  s.dependency "Reachability",">= 3.2.0"
  s.dependency "upuserdomain",">= 3.26.0"
  s.dependency "UPCore",">= 3.6.1"
  s.dependency "UPSafeColletion",">= 1.0.0"
end
