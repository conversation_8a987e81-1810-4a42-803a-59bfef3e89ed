//
//  DeviceManagerSteps.m
//  UPDeviceTests
//
//  Created by <PERSON><PERSON><PERSON><PERSON> on 2020/12/24.
//  Copyright © 2020 海尔优家智能科技（北京）有限公司. All rights reserved.
//

#import "DeviceManagerSteps.h"
#import <Cucumberish/Cucumberish.h>
#import <OCMock/OCMock.h>
#import "UpDeviceBroker.h"
#import "UpDeviceToolkit.h"
#import "UpDeviceManager.h"
#import "WifiDeviceToolkitImpl.h"
#import "StepsUtils.h"
#import "UpDeviceDataSourceWrapper.h"
#import "UpDeviceCreator.h"
#import "UpDeviceFactory.h"
#import "UpDeviceFilters.h"
#import "UpDeviceFilter.h"
#import "UpDevice.h"
#import "UpDeviceBase.h"
#import "DefaultDeviceBroker.h"
#import "FakeDelegateListener.h"
#import "CustomFilterProvider.h"
#import "FakeUpDeviceBase.h"
#import "UpDeviceHelper.h"
#import "FakeDelegateFactory.h"
#import "DeviceManagerDetectListener.h"
#import "UpDeviceInjection.h"
#import "FakeUSDKDevice.h"

typedef void (^CompleteBlock)(UpDeviceResult *result);
@interface DeviceManagerSteps ()
@property (nonatomic, strong) UpDeviceManager *deviceManager;
@property (nonatomic, strong) DefaultDeviceBroker *defaultbroker;
@property (nonatomic, strong) id<UpDeviceDataSource> deviceSource;
@property (nonatomic, strong) WifiDeviceToolkitImpl *toolkit;
@property (nonatomic, strong) id<UpDeviceFactory> factory;
@property (nonatomic, strong) id<UpDevice> device;
@property (nonatomic, strong) id<UpDeviceFilter> deviceFilter;
@property (nonatomic, strong) NSMutableArray<DeviceInfo *> *deviceInfoLists;
@property (nonatomic, strong) NSMutableDictionary *gatewayParams;
@property (nonatomic, strong) NSMutableDictionary<NSString *, id<Listener>> *listenerMap;
@property (nonatomic, strong) NSMutableArray *filters;
@property (nonatomic, strong) UpDeviceResult *deviceResult;
@property (nonatomic, strong) UpDeviceResult *attachResult;
@property (nonatomic, assign) BOOL isPrepareToolkitSuccess;
@property (nonatomic, assign) BOOL isReleaseToolkitSuccess;
@property (nonatomic, strong) NSArray<DeviceInfo *> *deviceList;
@property (nonatomic, strong) NSArray<NSString *> *movedPrepareQueue;
@property (nonatomic, strong) NSMutableArray<NSString *> *quickLauncherDevList;
@property (nonatomic, strong) uSDKDeviceManager *mUSDKDeviceManager;
@property (nonatomic, strong) NSMutableArray<NSString *> *usdkDevices;
@end

@implementation DeviceManagerSteps
- (void)defineStepsAndHocks
{
    before(^(CCIScenarioDefinition *scenario) {
      self.deviceManager = nil;
      self.defaultbroker = nil;
      self.deviceSource = nil;
      self.toolkit = nil;
      self.factory = nil;
      self.device = nil;
      self.deviceResult = nil;
      self.attachResult = nil;
      self.deviceInfoLists = [NSMutableArray array];
      self.listenerMap = [NSMutableDictionary dictionary];
      self.filters = [NSMutableArray array];
      self.isPrepareToolkitSuccess = NO;
      self.isReleaseToolkitSuccess = NO;
      self.deviceList = [NSArray array];
      self.quickLauncherDevList = [NSMutableArray array];
      self.movedPrepareQueue = [NSArray array];
      self.mUSDKDeviceManager = nil;
      self.usdkDevices = [NSMutableArray array];
    });

    Given(@"^初始化设备管理器$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      self.mUSDKDeviceManager = OCMClassMock([uSDKDeviceManager class]);
      [OCMStub([self.mUSDKDeviceManager getDeviceWithID:[OCMArg any]]) andCall:@selector(fakeDeviceWithID:) onObject:self];
      self.toolkit = OCMPartialMock([WifiDeviceToolkitImpl WifiDeviceToolkitImpl:OCMClassMock([uSDKManager class]) DeviceManager:self.mUSDKDeviceManager]);
      UpDeviceDataSourceWrapper *deviceDataSourceWrapper = [[UpDeviceDataSourceWrapper alloc] init];
      id<UpDeviceDataSource> deviceSource = OCMProtocolMock(@protocol(UpDeviceDataSource));
      self.deviceSource = deviceSource;
      [deviceDataSourceWrapper setDataSource:deviceSource];
      self.factory = OCMPartialMock([[FakeDelegateFactory alloc] init]);
      self.defaultbroker = [[DefaultDeviceBroker alloc] initWithKit:self.toolkit];
      [[UpDeviceInjection getInstance] initDeviceManagerWithWifi:self.toolkit dataSource:self.deviceSource];
      self.deviceManager = [UpDeviceInjection getInstance].deviceManager;
      [self.deviceManager appendDeviceFactory:self.factory];
      [StepsUtils sharedInstance].mDefaultDeviceBroker = self.defaultbroker;

    });

    Given(@"^设备数据源的更新设备列表接口返回数据如下:$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      self.deviceList = getDeviceListFromExpectUserInfo(userInfo);
      //备注:加ignoringNonObjectArgs()表示参数可以是YES或NO,默认是YES，为NO时不会回调
      [OCMStub([self.deviceSource getDeviceList:[OCMArg any] finishBlock:[OCMArg any]])
              .ignoringNonObjectArgs() andDo:^(NSInvocation *invocation) {
        [invocation retainArguments];
        void *finishBlock;
        [invocation getArgument:&finishBlock atIndex:3];
        CompleteBlock block = (__bridge CompleteBlock)finishBlock;
        UpDeviceResult *deviceResult = [[UpDeviceResult alloc] init];
        deviceResult.extraData = self.deviceList;
        deviceResult.errorCode = ErrorCode_FAILURE;
        if (self.deviceList.count > 0) {
            deviceResult.errorCode = ErrorCode_SUCCESS;
        }
        block(deviceResult);
      }];
    });
    Given(@"^设备数据源的更新设备列表接口返回\"([^\"]*)\"条设备数据,设备id从\"([^\"]*)\"到\"([^\"]*)\"递增,设备数据如下:$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      NSString *number = args[0];
      NSArray<DeviceInfo *> *deviceList = getDeviceListFromExpectUserInfoWithTotalNumber(userInfo, [number intValue]);
      self.deviceList = deviceList;
    });

    Given(@"^设备代理的接入网关参数为\"([^\"]*)\"$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      NSString *result = args[0];
      NSMutableDictionary *params = [self getStringObjectDict:result];
      self.gatewayParams = params;
      [self.defaultbroker setGatewayParams:params];
    });

    Given(@"^Toolkit的连接工具接口的执行结果为\"([^\"]*)\"$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      NSString *result = args[0];
      UpDeviceResult *deviceResult = [[UpDeviceResult alloc] init];
      deviceResult.errorCode = ErrorCode_FAILURE;
      if ([result isEqualToString:@"成功"]) {
          deviceResult.errorCode = ErrorCode_SUCCESS;
      };
      [OCMStub([self.toolkit attachToolkit:[OCMArg any] detectListener:[OCMArg any] finishBlock:[OCMArg any]]) andDo:^(NSInvocation *invocation) {
        [invocation retainArguments];
        void *finishBlock;
        [invocation getArgument:&finishBlock atIndex:4];
        CompleteBlock block = (__bridge CompleteBlock)finishBlock;
        block(deviceResult);
      }];
    });

    Given(@"^Toolkit的释放工具接口的执行结果为\"([^\"]*)\"$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      NSString *result = args[0];
      UpDeviceResult *deviceResult = [[UpDeviceResult alloc] init];
      deviceResult.errorCode = ErrorCode_FAILURE;
      if ([result isEqualToString:@"成功"]) {
          deviceResult.errorCode = ErrorCode_SUCCESS;
      };
      [OCMStub([self.toolkit detachToolkit:[OCMArg any]]) andDo:^(NSInvocation *invocation) {
        [invocation retainArguments];
        void *finishBlock;
        [invocation getArgument:&finishBlock atIndex:2];
        CompleteBlock block = (__bridge CompleteBlock)finishBlock;
        block(deviceResult);
      }];
    });

    When(@"^调用设备管理器的准备Toolkit接口$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      [self.deviceManager prepare:^(UpDeviceResult *result) {
        self.isPrepareToolkitSuccess = result.isSuccessful;
      }];
    });

    When(@"^调用设备管理器的释放Toolkit接口$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      [self.deviceManager release:^(UpDeviceResult *result) {
        self.isReleaseToolkitSuccess = result.isSuccessful;
      }];
    });

    Then(@"^设备管理器的准备Toolkit接口回调结果为\"([^\"]*)\"$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      NSString *result = args[0];
      CCIAssert(self.isPrepareToolkitSuccess == [result isEqualToString:@"成功"], @"设备管理器的准备设备工具接口回调结果与实际不一致");
    });

    Then(@"^设备管理器的释放Toolkit接口回调结果为\"([^\"]*)\"$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      NSString *result = args[0];
      CCIAssert(self.isReleaseToolkitSuccess == [result isEqualToString:@"成功"], @"设备管理器的释放设备工具接口回调结果与实际不一致");
    });

    Then(@"^Toolkit的准备工具接口被调用\"([^\"]*)\"次$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      NSString *invocationTimes = args[0];
      NSInteger expectedInvocationTimes = invocationTimes.integerValue;
      OCMVerify(times(expectedInvocationTimes), [self.toolkit attachToolkit:[OCMArg any] detectListener:[OCMArg any] finishBlock:[OCMArg any]]);
    });

    Then(@"^Toolkit的释放工具接口被调用\"([^\"]*)\"次$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      NSString *invocationTimes = args[0];
      NSInteger expectedInvocationTimes = invocationTimes.integerValue;
      OCMVerify(times(expectedInvocationTimes), [self.toolkit detachToolkit:([OCMArg any])]);
    });

    Given(@"^Toolkit的更新设备列表接口的执行结果为\"([^\"]*)\"$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      NSString *result = args[0];
      UpDeviceResult *deviceResult = [[UpDeviceResult alloc] init];
      deviceResult.errorCode = ErrorCode_FAILURE;
      if ([result isEqualToString:@"成功"]) {
          deviceResult.errorCode = ErrorCode_SUCCESS;
      };
      [OCMStub([self.toolkit refreshUsdkDeviceList:[OCMArg any]]) andDo:^(NSInvocation *invocation) {
        [invocation retainArguments];
        void *finishBlock;
        [invocation getArgument:&finishBlock atIndex:2];
        CompleteBlock block = (__bridge CompleteBlock)finishBlock;
        block(deviceResult);
      }];
    });

    Given(@"^设备数据源的更新设备列表接口执行结果为\"([^\"]*)\"$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      NSString *result = args[0];
      UpDeviceResult *deviceResult = [[UpDeviceResult alloc] init];
      deviceResult.errorCode = ErrorCode_FAILURE;
      if ([result isEqualToString:@"成功"]) {
          deviceResult.errorCode = ErrorCode_SUCCESS;
      };
      [OCMStub([self.deviceSource getDeviceList:[OCMArg any] finishBlock:[OCMArg any]])
              .ignoringNonObjectArgs() andDo:^(NSInvocation *invocation) {
        [invocation retainArguments];
        void *finishBlock;
        [invocation getArgument:&finishBlock atIndex:3];
        CompleteBlock block = (__bridge CompleteBlock)finishBlock;
        block(deviceResult);
      }];
    });

    Given(@"^Toolkit的连接用户接入网关的执行结果为\"([^\"]*)\"$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      NSString *result = args[0];
      UpDeviceResult *deviceResult = [[UpDeviceResult alloc] init];
      deviceResult.errorCode = ErrorCode_FAILURE;
      if ([result isEqualToString:@"成功"]) {
          deviceResult.errorCode = ErrorCode_SUCCESS;
      };
      [OCMStub([self.toolkit connectRemoteDevices:[OCMArg any] params:[OCMArg any] finishBlock:[OCMArg any]]) andDo:^(NSInvocation *invocation) {
        [invocation retainArguments];
        void *finishBlock;
        [invocation getArgument:&finishBlock atIndex:4];
        CompleteBlock block = (__bridge CompleteBlock)finishBlock;
        block(deviceResult);
      }];
    });

    When(@"^订阅者\"([^\"]*)\"订阅设备列表变化,立即通知标志为\"([^\"]*)\"$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      NSString *user = args[0];
      NSString *flag = args[1];
      BOOL isImmediate = YES;
      if ([flag isEqualToString:@"FALSE"]) {
          isImmediate = NO;
      }
      id<Listener> kListener = [[FakeDelegateListener alloc] init];
      [self.listenerMap setObject:kListener forKey:user];
      [self.deviceManager attach:kListener immediate:isImmediate];
    });

    Then(@"^订阅者\"([^\"]*)\"收到设备列表变化通知\"([^\"]*)\"次,变化的设备列表如下:$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      NSString *user = args[0];
      NSString *invocationTimes = args[1];
      NSInteger expectedInvocationTimes = invocationTimes.integerValue;
      FakeDelegateListener *kListener = [self.listenerMap objectForKey:user];
      NSArray<DeviceInfo *> *deviceList = getDeviceListFromExpectUserInfo(userInfo);
      NSArray *kParameters = [kListener getParametersOfSelector:@selector(onDeviceListChange:)];
      NSArray<DeviceInfo *> *lisDeviceList = kParameters[0];
      NSUInteger actualTimes = [kListener getInvocationTimesOfSelector:@selector(onDeviceListChange:)];
      CCIAssert(actualTimes == expectedInvocationTimes, @"收到设备列表变化通知调用:%ld,实际为:%ld", expectedInvocationTimes, actualTimes);
      CCIAssert(isEqualDeviceList(deviceList, lisDeviceList), @"更新后的设备列表与实际不一致");
    });

    When(@"^用户更新设备列表,立即更新标志为\"([^\"]*)\"$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      NSString *flag = args[0];
      BOOL isImmediately = NO;
      if ([flag isEqualToString:@"TRUE"]) {
          isImmediately = YES;
      }
      [self.deviceManager updateDeviceList:isImmediately
                               finishBlock:^(UpDeviceResult *result) {
                                 NSArray *extraArray = (NSArray *)result.extraData;
                                 NSMutableArray *array = [NSMutableArray array];
                                 for (id<UpDevice> info in extraArray) {
                                     [array addObject:info.getInfo];
                                 }
                                 self.deviceInfoLists = array;
                                 self.deviceResult = result;
                               }];

    });

    Then(@"^Toolkit的更新设备列表接口被调用\"([^\"]*)\"次$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      NSString *invocationTimes = args[0];
      NSInteger expectedInvocationTimes = invocationTimes.integerValue;
      OCMVerify(times(expectedInvocationTimes), [self.toolkit refreshUsdkDeviceList:[OCMArg any]]);
    });

    Then(@"^设备数据源的更新设备列表接口被调用\"([^\"]*)\"次,参数立即更新为\"([^\"]*)\"$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      NSString *invocationTimes = args[0];
      NSInteger expectedInvocationTimes = invocationTimes.integerValue;
      NSString *flag = args[1];
      BOOL isImmediate = NO;
      if ([flag isEqualToString:@"TRUE"]) {
          isImmediate = YES;
      }
      OCMVerify(times(expectedInvocationTimes), [self.deviceSource getDeviceList:isImmediate finishBlock:[OCMArg any]]);
    });

    Then(@"^设备工厂的创建设备接口被调用\"([^\"]*)\"次,参数设备列表如下:$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      NSString *invocationTimes = args[0];
      NSInteger expectedInvocationTimes = invocationTimes.integerValue;
      NSArray<DeviceInfo *> *deviceList = getDeviceListFromExpectUserInfo(userInfo);
      OCMVerify(times(expectedInvocationTimes), [self.factory create:[OCMArg any] deviceInfo:[OCMArg any] broker:[OCMArg any] factory:[OCMArg any]]);
      CCIAssert(isEqualDeviceList(deviceList, self.deviceInfoLists), @"执行结果与实际不一致");
    });

    Then(@"^Toolkit的连接用户接入网关接口被调用\"([^\"]*)\"次,参数网关信息为\"([^\"]*)\"$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      NSString *invocationTimes = args[0];
      NSString *paramMsg = args[1];
      NSInteger expectedInvocationTimes = invocationTimes.integerValue;
      NSMutableDictionary *params = [self getStringObjectDict:paramMsg];
      OCMVerify(times(expectedInvocationTimes), [self.toolkit connectRemoteDevices:[OCMArg any] params:[OCMArg any] finishBlock:[OCMArg any]]);
      NSMutableDictionary *dict = self.gatewayParams;
      CCIAssert([params isEqual:dict], @"设备信息与实际不一致");
    });

    Then(@"^使用者收到更新设备列表接口的结果为\"([^\"]*)\",更新后的设备列表如下:$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      NSString *result = args[0];
      NSArray<DeviceInfo *> *deviceinfo = getDeviceListFromExpectUserInfo(userInfo);
      CCIAssert([result isEqualToString:@"成功"] == self.deviceResult.isSuccessful, @"执行结果与实际不一致");
      CCIAssert(isEqualDeviceList(deviceinfo, self.deviceInfoLists), @"执行结果与实际不一致");
    });

    When(@"^等待\"([^\"]*)\"秒$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      NSTimeInterval time = [args[0] floatValue];
      XCTestExpectation *expectation = [[XCTestExpectation alloc] initWithDescription:@"等待秒的接口"];
      [XCTWaiter waitForExpectations:@[ expectation ] timeout:time enforceOrder:YES];
    });

    Then(@"^调用设备管理器的获取设备接口,协议为\"([^\"]*)\",设备id为\"([^\"]*)\",获取的设备信息如下:$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      NSString *protocolStr = args[0];
      NSString *deviceId = args[1];
      id<UpDeviceInfo> deviceinfo = convertUpDeviceFromStepTableData(userInfo);
      self.device = [self.deviceManager getDevice:protocolStr deviceId:deviceId];
      CCIAssert(isEqualDevice(self.device.getInfo, deviceinfo), @"设备信息与实际不一致");
    });

    Then(@"^设备管理器的获取设备接口返回的设备信息如下:$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      id<UpDeviceInfo> deviceinfo = convertUpDeviceFromStepTableData(userInfo);
      CCIAssert(isEqualDevice(self.device.getInfo, deviceinfo), @"设备管理器的获取设备接口返回的设备信息与实际不一致");
    });

    Then(@"^调用设备管理器的获取设备列表接口,参数过滤器为\"([^\"]*)\",获取的设备列表如下:$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      NSString *filterStr = args[0];
      id<UpDeviceFilter> deviceFilter;
      if ([filterStr isEqualToString:@"CompositeFilter"]) {
          deviceFilter = [CompositeFilter CompositeFilter:self.filters];
      }
      else if ([filterStr isEqualToString:@"StringFilter"]) {
          deviceFilter = self.filters.firstObject;
      }
      else if ([filterStr isEqualToString:@"NotNullFilter"]) {
          deviceFilter = [NotNullFilter NotNullFilter:nil];
      }
      NSArray<id<UpDevice>> *deviceListFilter = [self.deviceManager getDeviceList:deviceFilter];
      NSMutableArray *array = [NSMutableArray array];
      for (id<UpDevice> info in deviceListFilter) {
          [array addObject:info.getInfo];
      }
      NSArray<DeviceInfo *> *deviceList = getDeviceListFromExpectUserInfo(userInfo);
      CCIAssert(isEqualDeviceList(array, deviceList), @"设备管理器的获取设备列表接口返回的设备信息与实际不一致");
    });

    Given(@"^设置过滤器为\"([^\"]*)\",参数过滤集合为\"([^\"]*)\",过滤条件为\"([^\"]*)\"$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      NSString *filter = args[0];
      NSString *filterSet = args[1];
      NSString *filterType = args[2];
      NSArray *colletion = [self getStringObjectArray:filterSet];
      if ([filter isEqualToString:@"StringFilter"]) {
          CustomFilterProvider *provider = [[CustomFilterProvider alloc] init];
          provider.filterType = filterType;
          StringFilter *filter = [StringFilter StringFilter:[colletion mutableCopy] provider:provider];
          [self.filters addObject:filter];
      }
    });

    Then(@"^调用设备管理器的获取所有设备列表接口,返回的设备列表如下:$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      NSArray<DeviceInfo *> *deviceList = getDeviceListFromExpectUserInfo(userInfo);
      NSArray<id<UpDevice>> *deviceAllListArray = [self.deviceManager getDeviceList];
      NSMutableArray *array = [NSMutableArray array];
      for (id<UpDevice> info in deviceAllListArray) {
          [array addObject:info.getInfo];
      }
      CCIAssert(isEqualDeviceList(array, deviceList), @"设备管理器的获取所有设备列表接口返回的设备列表与实际不一致");
    });

    When(@"^调用设备管理器的清除设备列表接口$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      [self.deviceManager clearDeviceList];
    });

    When(@"^调用设备管理器的连接用户接入网关接口$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      [self.deviceManager connectRemoteDevices];
    });

    Then(@"^Toolkit的断开用户网关接口被调用\"([^\"]*)\"次$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      NSString *invocationTimes = args[0];
      NSInteger expectedInvocationTimes = invocationTimes.integerValue;
      OCMVerify(times(expectedInvocationTimes), [self.toolkit disconnectRemoteDevices:[OCMArg any]]);
    });

    Given(@"^Toolkit的断开用户接入网关的执行结果为\"([^\"]*)\"$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      NSString *result = args[0];
      UpDeviceResult *deviceResult = [[UpDeviceResult alloc] init];
      deviceResult.errorCode = ErrorCode_FAILURE;
      if ([result isEqualToString:@"成功"]) {
          deviceResult.errorCode = ErrorCode_SUCCESS;
      };
      [OCMStub([self.toolkit disconnectRemoteDevices:[OCMArg any]]) andDo:^(NSInvocation *invocation) {
        [invocation retainArguments];
        void *finishBlock;
        [invocation getArgument:&finishBlock atIndex:2];
        CompleteBlock block = (__bridge CompleteBlock)finishBlock;
        block(deviceResult);
      }];
    });

    When(@"^调用设备管理器的断开用户接入网关接口$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      [self.deviceManager disconnectRemoteDevices];
    });

    When(@"^订阅者\"([^\"]*)\"取消订阅设备列表变化$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      NSString *user = args[0];
      id<Listener> kListener = [self.listenerMap objectForKey:user];
      [self.deviceManager detach:kListener];
      [self.listenerMap removeObjectForKey:user];
    });

    Given(@"^Toolkit的连接设备接口等待\"([^\"]*)\"秒返回执行结果为\"([^\"]*)\"$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      NSTimeInterval time = [args[0] floatValue];
      NSString *result = args[1];
      [self kAttachDeviceWithTime:time result:result];
    });

    Given(@"^Toolkit的连接设备接口的执行结果为\"([^\"]*)\"$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      NSString *result = args[0];
      [self kAttachDeviceWithTime:0 result:result];
    });
    Then(@"^Toolkit的连接设备接口被调用\"([^\"]*)\"次,参数设备Id为\"([^\"]*)\"$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      NSString *invocationTimes = args[0];
      NSInteger expectedInvocationTimes = invocationTimes.integerValue;
      NSString *paramStr = args[1];
      OCMVerify(times(expectedInvocationTimes), [self.toolkit attachDevice:[OCMArg any] reportListener:[OCMArg any] finishBlock:[OCMArg any]]);
      NSMutableArray *deviceIdArray = [NSMutableArray array];
      for (DeviceInfo *info in self.deviceInfoLists) {
          [deviceIdArray addObject:info.deviceId];
      }
      NSString *componentsDeviceId = [deviceIdArray componentsJoinedByString:@","];
      CCIAssert([componentsDeviceId isEqualToString:paramStr], @"参数设备Id与实际不一致");
    });

    Given(@"^Toolkit的释放设备接口的执行结果为\"([^\"]*)\"$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      NSString *result = args[0];
      UpDeviceResult *deviceResult = [[UpDeviceResult alloc] init];
      deviceResult.errorCode = ErrorCode_FAILURE;
      if ([result isEqualToString:@"成功"]) {
          deviceResult.errorCode = ErrorCode_SUCCESS;
      };
      [OCMStub([[StepsUtils sharedInstance].mDefaultDeviceBroker.getToolkit detachDevice:[OCMArg any] finishBlock:[OCMArg any]]) andDo:^(NSInvocation *invocation) {
        [invocation retainArguments];
        void *finishBlock;
        [invocation getArgument:&finishBlock atIndex:3];
        CompleteBlock block = (__bridge CompleteBlock)finishBlock;
        block(deviceResult);
      }];
    });

    Then(@"^Toolkit的释放设备接口被调用\"([^\"]*)\"次,参数设备Id为\"([^\"]*)\"$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      NSString *invocationTimes = args[0];
      NSString *paramStr = args[1];
      NSInteger expectedInvocationTimes = invocationTimes.integerValue;
      OCMVerify(times(expectedInvocationTimes), [self.toolkit detachDevice:[OCMArg any] finishBlock:[OCMArg any]]);
      NSMutableArray *deviceIdArray = [NSMutableArray array];
      for (DeviceInfo *info in self.deviceInfoLists) {
          [deviceIdArray addObject:info.deviceId];
      }
      NSString *componentsDeviceId = [deviceIdArray componentsJoinedByString:@","];
      CCIAssert([componentsDeviceId isEqualToString:paramStr], @"参数设备Id与实际不一致");
    });

    Then(@"^刷新的设备列表的设备连接状态如下:$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      NSArray *list = getTableDataListFromExpectUserInfo(userInfo);
      for (int i = 0; i < list.count; i++) {
          NSArray *extraArray = (NSArray *)self.deviceResult.extraData;
          NSArray *deviceList = list[i];
          id<UpDevice> device = (id<UpDevice>)extraArray[i];
          UpDeviceState realState = [device getState];
          UpDeviceState expectState = [self getDeviceState:deviceList[2]];
          if (realState == UpDeviceState_PREPARING) {
              realState = UpDeviceState_RELEASED;
          }
          CCIAssert(realState == expectState, @"设备连接状态与实际不一致");
          CCIAssert([device.getInfo.protocol isEqualToString:deviceList[0]], @"设备协议与实际不一致");
          CCIAssert([device.getInfo.deviceId isEqualToString:deviceList[1]], @"设备deviceId与实际不一致");
      }
    });

    Then(@"^协议为\"([^\"]*)\",设备id为\"([^\"]*)\"的设备连接状态为\"([^\"]*)\"$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      NSString *protocolStr = args[0];
      NSString *deviceIdStr = args[1];
      NSString *stateStr = args[2];
      UpDeviceState expectState = [self getDeviceState:stateStr];
      id<UpDevice> kDevice = [self.deviceManager getDevice:protocolStr deviceId:deviceIdStr];
      CCIAssert(expectState == kDevice.getState, @"设备连接状态与实际不一致");
    });

    When(@"^设置协议为\"([^\"]*)\",设备id为\"([^\"]*)\"的设备为父设备$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      NSString *protocolStr = args[0];
      NSString *deviceIdStr = args[1];
      id<UpDevice> kDevice = [self.deviceManager getDevice:protocolStr deviceId:deviceIdStr];
      FakeUpDeviceBase *fakeDeviceBase = (FakeUpDeviceBase *)kDevice;
      [StepsUtils sharedInstance].mUpDeviceInfo = kDevice.getInfo;
      if ([StepsUtils sharedInstance].mUpDeviceList == nil) {
          [StepsUtils sharedInstance].mUpDeviceList = [NSMutableDictionary dictionaryWithCapacity:0];
      }
      [[StepsUtils sharedInstance].mUpDeviceList setValue:fakeDeviceBase forKey:kDevice.getInfo.deviceId];
    });

    When(@"调用优先准备设备接口,协议为\"([^\"]*)\",设备id为\"([^\"]*)\"$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      NSString *deviceIdStr = args[1];
      [self.deviceManager moveDeviceToQueueHead:deviceIdStr];
    });

    When(@"^设备列表变化代理方法被执行", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      DeviceManagerDetectListener *managerDetectListener = [self.deviceManager valueForKeyPath:@"managerDetectListener"];
      [managerDetectListener onDeviceListChanged];
    });

    Given(@"快捷启动的主设备id为\"([^\"]*)\",附件设备id为\"([^\"]*)\"$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      NSMutableArray *devIds = [NSMutableArray array];
      NSString *masterDevId = args[0];
      NSString *attachmentDevIdsStr = args[1];
      NSArray *attachmentDevIds = [attachmentDevIdsStr componentsSeparatedByString:@","];
      [devIds addObject:masterDevId];
      [devIds addObjectsFromArray:attachmentDevIds];
      [self.quickLauncherDevList addObjectsFromArray:devIds];
    });

    When(@"调用优先准备设备接口,协议为\"([^\"]*)\",设备为快捷启动的主设备和附件设备", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      [self.deviceManager moveDevicesToQueueHead:self.quickLauncherDevList];
      self.movedPrepareQueue = [self.deviceManager getPrepareQueue];
    });

    Then(@"^设备准备队列的顺序为\"([^\"]*)\"$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      NSString *protocolStr = @"haier-usdk";
      NSString *attachmentDevIds = args[0];
      NSArray *devIds = [attachmentDevIds componentsSeparatedByString:@","];
      for (int i = 0; i < devIds.count; i++) {
          NSString *devId = devIds[i];
          if (![self.quickLauncherDevList containsObject:devId]) {
              continue;
          }
          id<UpDevice> kDevice = [self.deviceManager getDevice:protocolStr deviceId:devId];
          NSString *uniqueId = kDevice.uniqueId;
          NSString *movedPreapreQueueDeviceUniqueId;
          if (i < self.movedPrepareQueue.count) {
              movedPreapreQueueDeviceUniqueId = self.movedPrepareQueue[i];
          }
          CCIAssert([uniqueId isEqualToString:movedPreapreQueueDeviceUniqueId], @"设备在准备队列中的位置没有被移动");
      }
    });

    When(@"^将如下设备加入当前家庭设备准备列表:$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      NSArray *list = getTableDataListFromExpectUserInfo(userInfo);
      NSArray *devicelist = list[0];
      NSString *devicesStr = devicelist[0];
      NSArray<NSString *> *devices = [devicesStr componentsSeparatedByString:@","];
      NSMutableArray<NSString *> *fittingDevices = [NSMutableArray new];
      for (NSString *deviceId in devices) {
          if ([deviceId isEqualToString:@""]) {
              continue;
          }
          [fittingDevices addObject:deviceId];
      }
      if (fittingDevices.count == 0) {
          fittingDevices = nil;
      }
      [self.deviceManager setCurFamilyPriorityPrepareQueue:fittingDevices];
    });

    When(@"^uSDKDeviceManagerDelegate的didAddDevices回调接口,上报设备如下:$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      NSArray *list = getTableDataListFromExpectUserInfo(userInfo);
      NSArray *devicelist = list[0];
      NSString *devicesStr = devicelist[0];
      NSArray<NSString *> *devices = [devicesStr componentsSeparatedByString:@","];
      NSMutableArray<uSDKDevice *> *usdkdevices = [NSMutableArray new];
      for (NSString *deviceId in devices) {
          if ([deviceId isEqualToString:@""]) {
              continue;
          }
          FakeUSDKDevice *device = [[FakeUSDKDevice alloc] init];
          [device mockuSDKDeviceID:deviceId];
          [usdkdevices addObject:device];
      }
      if (usdkdevices.count == 0) {
          usdkdevices = nil;
      }
      [self.toolkit deviceManager:self.mUSDKDeviceManager didAddDevices:usdkdevices];
      for (NSString *deviceId in devices) {
          [self.usdkDevices addObject:deviceId];
      }
    });

    Then(@"^设备家庭准备列表为:$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      NSArray *list = getTableDataListFromExpectUserInfo(userInfo);
      NSArray *devicelist = list[0];
      NSString *devicesStr = devicelist[0];
      NSArray<NSString *> *expectDevices = @[];
      if (![devicesStr isEqualToString:@""]) {
          expectDevices = [devicesStr componentsSeparatedByString:@","];
      }
      NSArray<NSString *> *actualDevices = [self.deviceManager curFamilyPriorityPrepareQueue];
      NSArray *sortedArray1 = [expectDevices sortedArrayUsingSelector:@selector(compare:)];
      NSMutableArray<NSString *> *uniqueIds = [NSMutableArray new];
      for (NSString *deviceId in sortedArray1) {
          NSString *uniqueId = [NSString stringWithFormat:@"haier-usdk~>%@", deviceId];
          [uniqueIds addObject:uniqueId];
      }
      NSArray *sortedArray2 = [actualDevices sortedArrayUsingSelector:@selector(compare:)];
      CCIAssert([uniqueIds isEqualToArray:sortedArray2], @"设备家庭准备队列是否符合期望");
    });

    Then(@"^设备准备队列为:$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      NSArray<id<UpDevice>> *devices = [self.deviceManager getDeviceList];
      NSArray *list = getTableDataListFromExpectUserInfo(userInfo);
      NSArray *devicelist = list[0];
      NSString *devicesStr = devicelist[0];
      NSArray<NSString *> *expectDevices = @[];
      if (![devicesStr isEqualToString:@""]) {
          expectDevices = [devicesStr componentsSeparatedByString:@","];
      }
      for (NSString *deviceId in expectDevices) {
          for (id<UpDevice> device in devices) {
              if (device) {
                  if ([device.getInfo.deviceId isEqualToString:deviceId]) {
                      CCIAssert(device.getState != UpDeviceState_PREPARED, @"设备准备队列是否符合期望");
                  }
              }
          }
      }
    });
}

#pragma mark - private

- (uSDKDevice *)fakeDeviceWithID:(NSString *)deviceId
{
    if ([self.usdkDevices containsObject:deviceId]) {
        FakeUSDKDevice *device = [[FakeUSDKDevice alloc] init];
        [device mockuSDKDeviceID:deviceId];
        FakeUSDKDevice *kFakeUSDKDevice = OCMPartialMock(device);
        OCMStub(kFakeUSDKDevice.isSubscribed).andReturn(YES);
        return kFakeUSDKDevice;
    }
    return nil;
}

- (NSMutableDictionary *)getStringObjectDict:(NSString *)string
{
    NSMutableDictionary *dict = [NSMutableDictionary dictionary];
    NSArray *array = [string componentsSeparatedByString:@","];
    for (NSString *str in array) {
        NSArray *arr = [str componentsSeparatedByString:@"="];
        NSString *key = [NSString stringWithFormat:@"%@", arr[0]];
        NSString *value = [NSString stringWithFormat:@"%@", arr[1]];
        if ([key isEqualToString:@"usdk-user-id"]) {
            key = WifiDeviceToolkit_KEY_USERID;
        }
        [dict setObject:value forKey:key];
    }
    return dict;
}
- (NSArray *)getStringObjectArray:(NSString *)string
{
    return [string componentsSeparatedByString:@","];
}

- (UpDeviceState)getDeviceState:(NSString *)statStr
{
    UpDeviceState kExpectUpDeviceState = UpDeviceState_RELEASED;
    if ([statStr isEqualToString:@"PREPARED"]) {
        kExpectUpDeviceState = UpDeviceState_PREPARED;
    }
    else if ([statStr isEqualToString:@"RELEASED"]) {
        kExpectUpDeviceState = UpDeviceState_RELEASED;
    }
    else if ([statStr isEqualToString:@"RELEASING"]) {
        kExpectUpDeviceState = UpDeviceState_RELEASING;
    }
    else if ([statStr isEqualToString:@"RELOADING"]) {
        kExpectUpDeviceState = UpDeviceState_RELOADING;
    }
    else if ([statStr isEqualToString:@"PREPARING"]) {
        kExpectUpDeviceState = UpDeviceState_PREPARING;
    }
    return kExpectUpDeviceState;
}
- (void)kAttachDeviceWithTime:(NSTimeInterval)time result:(NSString *)result
{
    self.attachResult = [[UpDeviceResult alloc] init];
    self.attachResult.errorCode = ErrorCode_FAILURE;
    if ([result isEqualToString:@"成功"]) {
        self.attachResult.errorCode = ErrorCode_SUCCESS;
    };

    UpDeviceBase *kUpDeviceBase = [[StepsUtils sharedInstance].mUpDeviceList objectForKey:[StepsUtils sharedInstance].mUpDeviceInfo.deviceId];
    if ([kUpDeviceBase isKindOfClass:[FakeUpDeviceBase class]]) {
        [(FakeUpDeviceBase *)kUpDeviceBase mockPrepareExtApiResult:[result isEqualToString:@"成功"]];
    }

    [OCMStub([[[StepsUtils sharedInstance].mDefaultDeviceBroker getToolkit] attachDevice:[OCMArg any] reportListener:[OCMArg any] finishBlock:[OCMArg any]]) andDo:^(NSInvocation *invocation) {
      [invocation retainArguments];
      void *finishBlock;
      [invocation getArgument:&finishBlock atIndex:4];
      CompleteBlock block = (__bridge CompleteBlock)finishBlock;
      [NSThread sleepForTimeInterval:time];
      dispatch_async(dispatch_get_global_queue(0, 0), ^{
        block(self.attachResult);
      });
    }];
}

@end
