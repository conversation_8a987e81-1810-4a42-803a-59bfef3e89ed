//
//  EngineDeviceSteps.m
//  UPDeviceTests
//
//  Created by <PERSON><PERSON><PERSON><PERSON> on 2020/12/24.
//  Copyright © 2020 海尔优家智能科技（北京）有限公司. All rights reserved.
//

#import "EngineDeviceSteps.h"
#import <Cucumberish/Cucumberish.h>
#import "UPResourceManager.h"
#import <OCMock/OCMock.h>
#import "UpEngineDeviceFactory.h"
#import "UpDeviceInfo.h"
#import "DeviceBaseInfo.h"
#import "DeviceInfo.h"
#import "StepsUtils.h"
#import "UpEngineDevice.h"
#import "DefaultDeviceBroker.h"
#import "WifiDeviceToolkitImpl.h"
#import "LEDeviceAttribute.h"
#import <LogicEngine/LECommonFuncs.h>
#import "UPDeviceLog.h"
#import <LogicEngine/LECalcLogicResult.h>
#import "UpEngineDevice+ConfigSource.h"
#import "UpEngineDevice+EngineDelegate.h"
#import <LogicEngine/LEDeviceCMD.h>
#import "UpEngineReporter.h"
#import "FakeLEEngineListener.h"

#define HC_SHORTHAND
#import <OCHamcrest/OCHamcrest.h>

#import <Foundation/NSBundle.h>
#import "UPResourceErrCodes.h"
#import "UpDeviceReporter.h"
#import "InitializationSteps.h"
#import "FakeUpDeviceListener.h"
#import "FakeResourceManager.h"
typedef void (^resDeviceResListCompletion)(NSArray<UPResourceInfo *> *infoList, NSError *error);
typedef void (^ConnectRemoteDeviceResult)(UpDeviceResult *result);
typedef void (^resourceInstallCompleteion)(UPResourceInfo *info, NSError *error);
@interface EngineDeviceSteps ()

@property (nonatomic, strong) LEEngine *mLEEngine;

@property (nonatomic, strong) UpDeviceResult *mUpDeviceResult;

@property (nonatomic, strong) LEDeviceAttribute *mLEDeviceAttribute;

@property (nonatomic, copy) NSString *mResSyncReturnValue;

@property (nonatomic, assign) BOOL mCleanCache;

@property (nonatomic, assign) BOOL mEngineWaring;

@property (nonatomic, strong) NSMutableDictionary *mNotifyEventListenerList;

@property (nonatomic, strong) NSArray<LEAttribute *> *mLEAttributeList;

@property (nonatomic, strong) NSArray<LEAlarm *> *mLEAlarmList;

@property (nonatomic, strong) NSMutableArray *kLEDeviceAttributeArray;
@property (nonatomic, assign) BOOL mLE_executeCommand;
@property (nonatomic, strong) NSMutableDictionary *mLEEngineListenerDict;
@property (nonatomic, strong) NSArray *attrList;
@end

@implementation EngineDeviceSteps

- (void)defineStepsAndHocks
{
    before(^(CCIScenarioDefinition *scenario) {
      self.mLEEngine = nil;
      self.mUpDeviceResult = nil;
      self.mLEDeviceAttribute = nil;
      self.mResSyncReturnValue = nil;
      self.mCleanCache = NO;
      self.mEngineWaring = NO;
      self.mNotifyEventListenerList = nil;
      self.mNotifyEventListenerList = [NSMutableDictionary dictionaryWithCapacity:0];
      self.mLEAttributeList = nil;
      self.mLEAlarmList = nil;
      self.kLEDeviceAttributeArray = nil;
      self.mLE_executeCommand = NO;
      self.mLEEngineListenerDict = [NSMutableDictionary dictionaryWithCapacity:0];
    });

    Given(@"^逻辑引擎使用\"([^\"]*)\"$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      NSString *kParameter = args[0];
      if ([kParameter isEqualToString:@"模拟的"]) {
          LEEngine *kLEEngine = OCMClassMock([LEEngine class]);
          self.mLEEngine = kLEEngine;
          NSString *kDeviceId = [StepsUtils sharedInstance].mUpDeviceInfo.deviceId;
          [(UpEngineDevice *)[[StepsUtils sharedInstance].mUpDeviceList objectForKey:kDeviceId] setValue:kLEEngine forKeyPath:@"logicEngine"];
      }
    });

    When(@"^使用者调用逻辑引擎设备的操作命令接口,传入命令参数为\"([^\"]*)\",清除参数为\"([^\"]*)\"$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      NSString *kCMDParameter = args[0];
      NSString *kIsCleanCache = args[1];
      NSString *kDeviceId = [StepsUtils sharedInstance].mUpDeviceInfo.deviceId;
      BOOL kCleanCache = NO;
      LEDeviceAttribute *kLEDeviceAttribute = NULL;
      if (LECommon_isValidString(kCMDParameter) && ![kCMDParameter isEqualToString:@"空对象"]) {
          kLEDeviceAttribute = convertLEDeviceAttributeFromArgs(args, 0);
          self.mLEDeviceAttribute = kLEDeviceAttribute;
      }
      if ([kIsCleanCache isEqualToString:@"true"]) {
          kCleanCache = YES;
      }
      self.mCleanCache = kCleanCache;
      [(UpEngineDevice *)([[StepsUtils sharedInstance].mUpDeviceList objectForKey:kDeviceId]) operateEngineCommand:kLEDeviceAttribute
                                                                                                             clean:kCleanCache
                                                                                                        completion:^(UpDeviceResult *_Nonnull result) {
                                                                                                          self.mUpDeviceResult = result;
                                                                                                        }];
    });

    Then(@"^使用者收到逻辑引擎设备的操作命令接口的结果为\"([^\"]*)\"$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      NSString *kResult = args[0];
      CCIAssert([kResult isEqualToString:@"成功"] == self.mUpDeviceResult.isSuccessful, @"执行结果与实际不一致");
    });

    Then(@"^逻辑引擎计算命令接口被调用\"([^\"]*)\"次,命令参数为\"([^\"]*)\",清除参数为\"([^\"]*)\"$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      NSString *kInvocationTimes = args[0];
      NSString *kCMDParameter = args[1];
      NSString *kIsCleanCache = args[2];
      BOOL kCleanCache = NO;
      LEDeviceAttribute *kLEDeviceAttribute = NULL;
      NSMutableArray *kLEDeviceAttributeArray = [NSMutableArray array];
      if (LECommon_isValidString(kCMDParameter) && ![kCMDParameter isEqualToString:@"空对象"]) {
          //parameter must be the original's(self.mLEDeviceAttribute) ,if reproduced result in test failure
          if ([kCMDParameter containsString:@","]) {
              NSArray *commands = [kCMDParameter componentsSeparatedByString:@","];
              for (int i = 0; i < commands.count; i++) {
                  LEDeviceAttribute *kLEDeviceAttribute = convertLEDeviceAttributeFromArgs(commands, i);
                  [kLEDeviceAttributeArray addObject:kLEDeviceAttribute];
              }
          }
          else {
              kLEDeviceAttribute = convertLEDeviceAttributeFromArgs(args, 1);
          }
      }
      if ([kIsCleanCache isEqualToString:@"true"]) {
          kCleanCache = YES;
      }
      if (kLEDeviceAttributeArray.count > 0) {
          CCIAssert(kCleanCache == self.mCleanCache && [[StepsUtils sharedInstance] isEqualUpDeviceAttributeList:self.kLEDeviceAttributeArray expectList:kLEDeviceAttributeArray], @"执行结果与实际不一致");
      }
      else {
          CCIAssert(kCleanCache == self.mCleanCache && [[StepsUtils sharedInstance] isEqualUpDeviceAttributeList:[NSArray arrayWithObject:self.mLEDeviceAttribute] expectList:[NSArray arrayWithObject:kLEDeviceAttribute]], @"执行结果与实际不一致");
      }
      if (NULL == kLEDeviceAttribute) {
          OCMVerify(times(kInvocationTimes.integerValue), [self.mLEEngine calculate:[OCMArg any] clean:kCleanCache]);
      }
      else {
          OCMVerify(times(kInvocationTimes.integerValue), [self.mLEEngine calculate:[NSArray arrayWithObject:self.mLEDeviceAttribute] clean:kCleanCache]);
      }
    });

    Then(@"^逻辑引擎计算命令接口被调用\"([^\"]*)\"次$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      NSString *kInvocationTimes = args[0];
      BOOL kCleanCache = NO;
      NSString *kDeviceId = [StepsUtils sharedInstance].mUpDeviceInfo.deviceId;
      UpEngineDevice *kUpEngineDevice = (UpEngineDevice *)[[StepsUtils sharedInstance].mUpDeviceList objectForKey:kDeviceId];
      if (nil == kUpEngineDevice.engine && 0 == kInvocationTimes.integerValue) {
          CCIAssert(YES, @"执行结果与实际不一致");
      }
      else {
          OCMVerify(times(kInvocationTimes.integerValue), [self.mLEEngine calculate:[OCMArg isEqual:[NSArray arrayWithObject:[OCMArg isKindOfClass:[LEDeviceAttribute class]]]] clean:kCleanCache ? isTrue() : isFalse()]);
      }
    });

    Then(@"^逻辑引擎操作命令接口被调用\"([^\"]*)\"次$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      NSString *kInvocationTimes = args[0];
      NSString *kDeviceId = [StepsUtils sharedInstance].mUpDeviceInfo.deviceId;
      UpEngineDevice *kUpEngineDevice = (UpEngineDevice *)[[StepsUtils sharedInstance].mUpDeviceList objectForKey:kDeviceId];
      if (nil == kUpEngineDevice.engine && 0 == kInvocationTimes.integerValue) {
          CCIAssert(YES, @"执行结果与实际不一致");
      }
      else {
          OCMVerify(times(kInvocationTimes.integerValue), [self.mLEEngine operateCMDWithCompletion:([OCMArg any])]);
      }
    });

    Given(@"^逻辑引擎计算命令接口的执行结果为\"([^\"]*)\"$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      NSString *kResult = args[0];
      NSString *kDeviceId = [StepsUtils sharedInstance].mUpDeviceInfo.deviceId;
      BOOL isSuccessful = NO;
      if ([kResult isEqualToString:@"成功"]) {
          isSuccessful = YES;
      }
      LECalcLogicResult *kLECalcLogicResult = [LECalcLogicResult new];
      NSString *errInfo = [NSString stringWithFormat:@"设备（%@）的逻辑引擎当前状态不可执行操作！请稍后再试！", (UpEngineDevice *)([[StepsUtils sharedInstance].mUpDeviceList objectForKey:kDeviceId]).deviceId];
      kLECalcLogicResult.success = isSuccessful;
      if (!isSuccessful) {
          kLECalcLogicResult.errorDesc = errInfo;
      }
      //if not ignoringNonObjectArgs(),then the stub function is not called because the parameters is not consistent
      OCMStub([self.mLEEngine calculate:[OCMArg any] clean:YES]).ignoringNonObjectArgs().andReturn(kLECalcLogicResult);
    });

    Given(@"^逻辑引擎指令下发的执行结果为\"([^\"]*)\"$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      NSString *kResult = args[0];
      NSString *kDeviceId = [StepsUtils sharedInstance].mUpDeviceInfo.deviceId;
      BOOL isSuccessful = NO;
      int errCode = ErrorCode_FAILURE;
      NSString *errDomain = @"failure";
      if ([kResult isEqualToString:@"成功"]) {
          isSuccessful = YES;
          errCode = ErrorCode_SUCCESS;
          errDomain = @"success";
      }
      NSDictionary *userInfoError = @{ @"EngineDeviceSteps" : [NSString stringWithFormat:@"Expected Result Of CMD Operation %@", errDomain] };
      NSError *error = [[NSError alloc] initWithDomain:errDomain code:errCode userInfo:userInfoError];
      UpEngineDevice *kUpEngineDevice = (UpEngineDevice *)[[StepsUtils sharedInstance].mUpDeviceList objectForKey:kDeviceId];
      OCMStub([kUpEngineDevice.engine operateCMDWithCompletion:[OCMArg any]]).andDo(^(NSInvocation *invocation) { /* block that handles the method invocation */
                                                                                                                  [invocation retainArguments];
                                                                                                                  void *successBlockPointer;
                                                                                                                  [invocation getArgument:&successBlockPointer atIndex:2];
                                                                                                                  LECompletion successBlock = (__bridge LECompletion)successBlockPointer;
                                                                                                                  successBlock(error);
      });

    });

    Given(@"^资源管理器获取设备配置资源接口的同步返回值为\"([^\"]*)\"$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      NSString *kParameter = args[0];
      if ([kParameter isEqualToString:@"空对象"]) {
          self.mResSyncReturnValue = @"空";
      }
      else if ([kParameter isEqualToString:@"ResourceInfo"]) {
          self.mResSyncReturnValue = @"ResourceInfo";
      }
      else {
          self.mResSyncReturnValue = @"Unknown";
      }
    });

    Given(@"^资源管理器获取设备配置资源接口异步返回\"([^\"]*)\"$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      NSString *kResult = args[0];
      NSUInteger errCode = ErrorCode_FAILURE;
      NSString *errDomain = @"failure";
      NSDictionary *userInfoError = @{ @"EngineDeviceSteps" : [NSString stringWithFormat:@"Expected Result Of ResManager updateDeviceResList %@", errDomain] };
      UPResourceInfo *kAsyncDataUPResourceInfo = NULL;
      if ([kResult isEqualToString:@"不支持异常"]) {
          errCode = UPResErrorCodeServerResponseEmptyList;
          errDomain = @"NotSupport";
      }
      else if ([kResult isEqualToString:@"其他异常"]) {
          errCode = ErrorCode_FAILURE;
          errDomain = @"other";
      }
      else if ([kResult isEqualToString:@"ResourceInfo"]) {
          errCode = ErrorCode_SUCCESS;
          UPResourceInfo *kAsyncDataUPResourceInfo1 = [[UPResourceInfo alloc] init];
          kAsyncDataUPResourceInfo1.path = @"/path/to/resource/ApiCloud/cfg1@1.2.0";
          kAsyncDataUPResourceInfo1.type = UPResourceTypeDeviceConfig;
          kAsyncDataUPResourceInfo1.name = @"resC";
          kAsyncDataUPResourceInfo1.version = @"1.0.1";
          kAsyncDataUPResourceInfo = OCMPartialMock(kAsyncDataUPResourceInfo1);
          [OCMStub(kAsyncDataUPResourceInfo.active) andReturnValue:@(YES)];
      }
      else {
          errCode = ErrorCode_INVALID;
          errDomain = @"unknown";
      }
      NSError *error = [[NSError alloc] initWithDomain:errDomain code:errCode userInfo:userInfoError];
      UPResourceInfo *kUPResourceInfo = NULL;
      if ((LECommon_isValidString(self.mResSyncReturnValue) && [self.mResSyncReturnValue isEqualToString:@"ResourceInfo"])) {
          UPResourceInfo *kUPResourceInfo1 = [[UPResourceInfo alloc] init];
          kUPResourceInfo1.path = @"/path/to/resource/ApiCloud/cfg1@1.2.0";
          kUPResourceInfo1.type = UPResourceTypeDeviceConfig;
          kUPResourceInfo1.name = @"resC";
          kUPResourceInfo1.version = @"1.0.1";

          kUPResourceInfo = OCMPartialMock(kUPResourceInfo1);
          [OCMStub(kUPResourceInfo.active) andReturnValue:@(YES)];
      }
      [[StepsUtils sharedInstance].mResourceManager mockSyncResource:kUPResourceInfo asyncResouce:kAsyncDataUPResourceInfo error:error];
    });

    Given(@"^逻辑引擎准备接口的执行结果为\"([^\"]*)\"$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      NSString *kResult = args[0];
      BOOL isSuccessful = NO;
      int errCode = ErrorCode_FAILURE;
      NSString *errDomain = @"failure";
      if ([kResult isEqualToString:@"成功"]) {
          isSuccessful = YES;
          errCode = ErrorCode_SUCCESS;
          errDomain = @"success";
      }
      NSDictionary *userInfoError = @{ @"EngineDeviceSteps" : [NSString stringWithFormat:@"Expected Result Of Ext Prepare Operation %@", errDomain] };
      NSError *error = [[NSError alloc] initWithDomain:errDomain code:errCode userInfo:userInfoError];
      //use mLEEngine property in case mUpEngineDevice is not initialed
      OCMStub([self.mLEEngine startLogicEngineWithCompletion:[OCMArg any]]).andDo(^(NSInvocation *invocation) { /* block that handles the method invocation */
                                                                                                                [invocation retainArguments];
                                                                                                                void *successBlockPointer;
                                                                                                                [invocation getArgument:&successBlockPointer atIndex:2];
                                                                                                                LECompletion successBlock = (__bridge LECompletion)successBlockPointer;
                                                                                                                if (isSuccessful) {
                                                                                                                    successBlock(nil);
                                                                                                                }
                                                                                                                else {
                                                                                                                    successBlock(error);
                                                                                                                }
      });
    });

    Given(@"^生成器创建的逻辑引擎是\"([^\"]*)\"$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      NSString *kParameter = args[0];
      if ([kParameter isEqualToString:@"模拟的"]) {
          id kLEEngine = OCMClassMock([LEEngine class]);
          OCMStub([kLEEngine engineWithDeviceID:[OCMArg any] deviceDelegate:[OCMArg any] configDataSource:[OCMArg any]]).andReturn(kLEEngine);
          self.mLEEngine = kLEEngine;
      }
    });

    Given(@"^逻辑引擎释放接口的执行结果为\"([^\"]*)\"$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      NSString *kDeviceId = [StepsUtils sharedInstance].mUpDeviceInfo.deviceId;
      NSString *kResult = args[0];
      BOOL isSuccessful = NO;
      int errCode = ErrorCode_FAILURE;
      NSString *errDomain = @"failure";
      if ([kResult isEqualToString:@"成功"]) {
          isSuccessful = YES;
          errCode = 0;
          errDomain = @"success";
      }
      NSDictionary *userInfoError = @{ @"EngineDeviceSteps" : [NSString stringWithFormat:@"Expected Result Of stopLogicEngine Operation %@", errDomain] };
      NSError *error = [[NSError alloc] initWithDomain:errDomain code:errCode userInfo:userInfoError];
      UpEngineDevice *kUpEngineDevice = (UpEngineDevice *)[[StepsUtils sharedInstance].mUpDeviceList objectForKey:kDeviceId];
      OCMStub([kUpEngineDevice.engine stopLogicEngineWithCompletion:[OCMArg any]]).andDo(^(NSInvocation *invocation) { /* block that handles the method invocation */
                                                                                                                       [invocation retainArguments];
                                                                                                                       void *successBlockPointer;
                                                                                                                       [invocation getArgument:&successBlockPointer atIndex:2];
                                                                                                                       LECompletion successBlock = (__bridge LECompletion)successBlockPointer;
                                                                                                                       if (isSuccessful) {
                                                                                                                           successBlock(nil);
                                                                                                                       }
                                                                                                                       else {
                                                                                                                           successBlock(error);
                                                                                                                       }
      });
    });

    When(@"^等待\"([^\"]*)\"秒$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      NSTimeInterval time = [args[0] floatValue];
      XCTestExpectation *expectation = [[XCTestExpectation alloc] initWithDescription:@"等待秒的接口"];
      [XCTWaiter waitForExpectations:@[ expectation ] timeout:time enforceOrder:YES];
    });

    Given(@"^逻辑引擎是否准备好接口的执行结果为\"([^\"]*)\"$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      NSString *kParameter = args[0];
      int kLEEngineStatus = LEEngineStatusUnknown;
      if ([kParameter isEqualToString:@"true"]) {
          kLEEngineStatus = LEEngineStatusReady;
      }
      OCMStub([self.mLEEngine isEngineReady]).andReturn(kLEEngineStatus);
    });


    When(@"^线程\"([^\"]*)\"调用设备的准备接口$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      NSString *kParameter = args[0];
      NSString *kDeviceId = [StepsUtils sharedInstance].mUpDeviceInfo.deviceId;
      UpEngineDevice *kUpEngineDevice = (UpEngineDevice *)[[StepsUtils sharedInstance].mUpDeviceList objectForKey:kDeviceId];
      [self prepareWork];
      dispatch_async(dispatch_get_global_queue(0, 0), ^{
        NSLog(@"线程 %@ | prepare:", kParameter);
        [kUpEngineDevice prepare:^(UpDeviceResult *result) {
          NSLog(@"线程 %@ | result %ld", kParameter, (long)result.errorCode);
          self.mUpDeviceResult = result;
          self.mUpDeviceResult.extraInfo = kParameter;
        }];
      });
    });

    Then(@"^线程\"([^\"]*)\"收到设备准备的执行结果为\"([^\"]*)\"$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      NSString *kParameter = args[0];
      NSString *kResult = args[1];
      BOOL isSuccessful = NO;
      if ([kResult isEqualToString:@"成功"]) {
          isSuccessful = YES;
      }
      CCIAssert(isSuccessful == self.mUpDeviceResult.isSuccessful && [kParameter isEqualToString:self.mUpDeviceResult.extraInfo], @"执行结果与实际不一致");
    });

    Then(@"^使用者查询设备\"([^\"]*)\"状态为\"([^\"]*)\"$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      NSString *kResult = args[1];
      NSString *kDeviceId = args[0];
      UpDeviceState kExpectUpDeviceState = UpDeviceState_RELEASED;
      if ([kResult isEqualToString:@"PREPARED"]) {
          kExpectUpDeviceState = UpDeviceState_PREPARED;
      }
      UpDeviceState kUpDeviceState = UpDeviceState_RELEASED;
      if ([[[StepsUtils sharedInstance].mUpDeviceList objectForKey:kDeviceId] isReady]) {
          kUpDeviceState = UpDeviceState_PREPARED;
      }
      CCIAssert(kExpectUpDeviceState == kUpDeviceState, @"执行结果与实际不一致");
    });

    Then(@"^使用者查询设备\"([^\"]*)\"状态为\"([^\"]*)\"或者为\"([^\"]*)\"$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      NSString *kResult = args[1];
      NSString *kResult1 = args[2];
      NSString *kDeviceId = args[0];
      UpDeviceState kExpectUpDeviceState = UpDeviceState_RELEASED;
      UpDeviceState kExpectUpDeviceState1 = UpDeviceState_RELEASED;
      if ([kResult isEqualToString:@"PREPARED"]) {
          kExpectUpDeviceState = UpDeviceState_PREPARED;
      }
      if ([kResult1 isEqualToString:@"PREPARING"]) {
          kExpectUpDeviceState1 = UpDeviceState_PREPARING;
      }

      UpEngineDevice *kUpEngineDevice = (UpEngineDevice *)[[StepsUtils sharedInstance].mUpDeviceList objectForKey:kDeviceId];

      CCIAssert(kExpectUpDeviceState == kUpEngineDevice.getState || kExpectUpDeviceState1 == kUpEngineDevice.getState, @"执行结果与实际不一致");
    });

    When(@"^使用者调用设备准备接口$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      NSString *kDeviceId = [StepsUtils sharedInstance].mUpDeviceInfo.deviceId;
      [self prepareWork];
      UpEngineDevice *kUpEngineDevice = (UpEngineDevice *)[[StepsUtils sharedInstance].mUpDeviceList objectForKey:kDeviceId];
      [kUpEngineDevice prepare:^(UpDeviceResult *result) {
        NSLog(@"main thread | result %ld", (long)result.errorCode);
        self.mUpDeviceResult = result;
      }];
    });

    Then(@"^使用者收到设备准备的执行结果为\"([^\"]*)\"$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      //there have different handling logic with android for second call prepare,so test case 7008 example failure
      NSString *kResult = args[0];
      CCIAssert([kResult isEqualToString:@"成功"] == self.mUpDeviceResult.isSuccessful, @"执行结果与实际不一致");
    });

    Given(@"^逻辑引擎设备的资源管理为\"([^\"]*)\"$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      NSString *kDeviceId = [StepsUtils sharedInstance].mUpDeviceInfo.deviceId;
      NSString *kParameter = args[0];
      if ([kParameter isEqualToString:@"空对象"]) {
          UpEngineDevice *kUpEngineDevice = (UpEngineDevice *)[[StepsUtils sharedInstance].mUpDeviceList objectForKey:kDeviceId];
          [kUpEngineDevice setResManager:nil];
      }
    });

    Given(@"^Toolkit释放设备接口的执行结果为\"([^\"]*)\"$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      NSString *kParameter = args[0];
      BOOL isSuccessful = NO;
      int errCode = ErrorCode_FAILURE;
      if ([kParameter isEqualToString:@"成功"]) {
          isSuccessful = YES;
          errCode = ErrorCode_SUCCESS;
      }
      UpDeviceResult *kUpDeviceResult = [UpDeviceResult UpDeviceResult:errCode extraData:nil];
      OCMStub([[[StepsUtils sharedInstance].mDefaultDeviceBroker getToolkit] detachDevice:[OCMArg any] finishBlock:[OCMArg any]]).andDo(^(NSInvocation *invocation) { /* block that handles the method invocation */
                                                                                                                                                                      [invocation retainArguments];
                                                                                                                                                                      void *successBlockPointer;
                                                                                                                                                                      [invocation getArgument:&successBlockPointer atIndex:3];
                                                                                                                                                                      ConnectRemoteDeviceResult successBlock = (__bridge ConnectRemoteDeviceResult)successBlockPointer;
                                                                                                                                                                      successBlock(kUpDeviceResult);
      });
    });

    When(@"^使用者调用设备释放接口$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      NSString *kDeviceId = [StepsUtils sharedInstance].mUpDeviceInfo.deviceId;
      UpEngineDevice *kUpEngineDevice = (UpEngineDevice *)[[StepsUtils sharedInstance].mUpDeviceList objectForKey:kDeviceId];
      [kUpEngineDevice release:^(UpDeviceResult *result) {
        NSLog(@"EngineDeviceSteps | release: | errorCode = %ld", result.errorCode);
        self.mUpDeviceResult = result;
      }];
    });

    Then(@"^使用者收到设备释放的执行结果为\"([^\"]*)\"$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      NSString *kParameter = args[0];
      CCIAssert([kParameter isEqualToString:@"成功"] == self.mUpDeviceResult.isSuccessful, @"执行结果与实际不一致");
    });

    Given(@"^资源管理器的获取设备配置资源接口异步返回\"([^\"]*)\"路径的\"([^\"]*)\"$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      NSString *kPathParameter = args[0];
      NSString *kDataParameter = args[1];
      NSString *kDeviceId = [StepsUtils sharedInstance].mUpDeviceInfo.deviceId;
      int errCode = ErrorCode_FAILURE;
      NSString *errDomain = @"failure";
      NSString *kConfigPath = @"";
      if ([kPathParameter isEqualToString:@"有"]) {
          kConfigPath = @"/path/to/resource/ApiCloud/cfg1@1.2.0";
      }
      UPResourceInfo *kAsyncDataUPResourceInfo = NULL;
      if ([kDataParameter isEqualToString:@"ResourceInfo"]) {
          errCode = ErrorCode_SUCCESS;
          kAsyncDataUPResourceInfo = [[UPResourceInfo alloc] init];
          kAsyncDataUPResourceInfo.path = kConfigPath;
          kAsyncDataUPResourceInfo.type = UPResourceTypeDeviceConfig;
          kAsyncDataUPResourceInfo.name = @"resC";
          kAsyncDataUPResourceInfo.version = @"1.0.1";
      }
      else if ([kPathParameter isEqualToString:@"异常"]) {
          errDomain = @"other";
      }
      NSDictionary *userInfoError = @{ @"EngineDeviceSteps" : [NSString stringWithFormat:@"Expected Result Of ResManager updateDeviceResList %@", errDomain] };
      NSError *error = [[NSError alloc] initWithDomain:errDomain code:errCode userInfo:userInfoError];
      UPResourceInfo *kUPResourceInfo = NULL;
      if ((LECommon_isValidString(self.mResSyncReturnValue) && [self.mResSyncReturnValue isEqualToString:@"ResourceInfo"])) {
          kUPResourceInfo = [[UPResourceInfo alloc] init];
          kUPResourceInfo.path = kConfigPath;
          kUPResourceInfo.type = UPResourceTypeDeviceConfig;
          kUPResourceInfo.name = @"resC";
          kUPResourceInfo.version = @"1.0.1";
      }

      UpEngineDevice *kUpEngineDevice = (UpEngineDevice *)[[StepsUtils sharedInstance].mUpDeviceList objectForKey:kDeviceId];


      OCMStub([kUpEngineDevice.resManager updateDeviceResList:[OCMArg any] completion:[OCMArg any]]).andDo(^(NSInvocation *invocation) { /* block that handles the method invocation */

                                                                                                                                         [invocation retainArguments];
                                                                                                                                         void *successBlockPointer;
                                                                                                                                         [invocation getArgument:&successBlockPointer atIndex:3];
                                                                                                                                         resDeviceResListCompletion successBlock = (__bridge resDeviceResListCompletion)successBlockPointer;
                                                                                                                                         if (errCode == ErrorCode_SUCCESS) {
                                                                                                                                             successBlock([NSArray arrayWithObject:kAsyncDataUPResourceInfo], nil);
                                                                                                                                         }
                                                                                                                                         else {
                                                                                                                                             successBlock(nil, error);
                                                                                                                                         }
                                                                                                    })
          .andReturn((kUPResourceInfo != nil) ? [NSArray arrayWithObject:kUPResourceInfo] : nil);
      kUpEngineDevice = OCMPartialMock(kUpEngineDevice);
      [[StepsUtils sharedInstance].mUpDeviceList setObject:kUpEngineDevice forKey:kDeviceId];
      if ([kConfigPath isEqualToString:@""]) {

          OCMStub([kUpEngineDevice installEngineDeviceResource:[OCMArg any] withCompletion:[OCMArg any]]).andDo(^(NSInvocation *invocation) {
            [invocation retainArguments];
            void *successBlockPointer;
            [invocation getArgument:&successBlockPointer atIndex:3];
            resourceInstallCompleteion successBlock = (__bridge resourceInstallCompleteion)successBlockPointer;
            successBlock(nil, [NSError errorWithDomain:@"无路径" code:-1 userInfo:nil]);
          });
      }

    });

    When(@"^订阅者\"([^\"]*)\"向设备添加设备监听器$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      NSString *kParameter = args[0];
      NSString *kDeviceId = [StepsUtils sharedInstance].mUpDeviceInfo.deviceId;
      FakeUpDeviceListener *kFakeUpDeviceListener = [[FakeUpDeviceListener alloc] init];
      [self.mNotifyEventListenerList setValue:kFakeUpDeviceListener forKey:kParameter];
      [[[StepsUtils sharedInstance].mUpDeviceList objectForKey:kDeviceId] attach:kFakeUpDeviceListener];
    });

    Then(@"^使用者获取设备配置状态为\"([^\"]*)\"$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      NSString *kParameter = args[0];
      NSString *kDeviceId = [StepsUtils sharedInstance].mUpDeviceInfo.deviceId;
      UpEngineDevice *kUpEngineDevice = (UpEngineDevice *)[[StepsUtils sharedInstance].mUpDeviceList objectForKey:kDeviceId];
      int kExpectConfigState = UpConfigStateUnknown;
      UpConfigState kUpConfigState = kUpEngineDevice.configState;
      if ([kParameter isEqualToString:@"NOT_SUPPORT"]) {
          kExpectConfigState = UpConfigStateNotSupport;
      }
      else if ([kParameter isEqualToString:@"SUPPORT"]) {
          kExpectConfigState = UpConfigStateSupport;
      }
      else {
          kExpectConfigState = UpConfigStateUnknown;
      }
      CCIAssert(kExpectConfigState == kUpConfigState, @"执行结果与实际不一致");
    });

    Then(@"^订阅者\"([^\"]*)\"收到设备通知\"([^\"]*)\"次,事件列表如下:$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      NSString *kParameter = args[0];
      NSString *kInvocationTimes = args[1];
      FakeUpDeviceListener *kFakeUpDeviceListener = [self.mNotifyEventListenerList objectForKey:kParameter];
      NSArray<NSArray<NSString *> *> *kNotifyEventNameList = convertNotifyEventNameFromStepTableData(userInfo);
      NSMutableDictionary *kExpectSubscriberNotifyEventDictionary = [[StepsUtils sharedInstance] convertNotifyEventListToSubscriberDictionary:kNotifyEventNameList];
      NSMutableDictionary *kActualSubscriberNotifyEventDictionary = kFakeUpDeviceListener.mNotifyEventDictionary;
      //Android no EVENT_ATTACHED event
      [kActualSubscriberNotifyEventDictionary removeObjectForKey:@"EVENT_ATTACHED"];
      BOOL kCheckResult = [[StepsUtils sharedInstance] isEqualNotifyEvent:kActualSubscriberNotifyEventDictionary expect:kExpectSubscriberNotifyEventDictionary];
      if (0 == kInvocationTimes.intValue) {
          kCheckResult = YES;
      }
      CCIAssert(kCheckResult == YES, @"执行结果与实际不一致");
    });

    Given(@"^逻辑引擎重加载设备配置返回\"([^\"]*)\"$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      NSString *kResult = args[0];
      BOOL isSuccessful = NO;
      if ([kResult isEqualToString:@"成功"]) {
          isSuccessful = YES;
      }
      //there is no return value for test case 7014
      //      UpEngineDevice *kUpEngineDevice = (UpEngineDevice *)[StepsUtils sharedInstance].mUpDeviceBase;
      //      OCMStub([kUpEngineDevice.engine updateConfigFile]);
    });

    When(@"^重加载被触发$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      NSString *kDeviceId = [StepsUtils sharedInstance].mUpDeviceInfo.deviceId;
      [[[StepsUtils sharedInstance].mUpDeviceList objectForKey:kDeviceId] reload:^(UpDeviceResult *result) {
        self.mUpDeviceResult = result;
      }];
    });

    Then(@"^使用者收到重加载的执行结果为\"([^\"]*)\"$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      NSString *kResult = args[0];
      BOOL isSuccessful = NO;
      if ([kResult isEqualToString:@"成功"]) {
          isSuccessful = YES;
      }
      CCIAssert(isSuccessful == self.mUpDeviceResult.isSuccessful, @"执行结果与实际不一致");
    });

    When(@"^使用者调用指令下发接口$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      NSString *kDeviceId = [StepsUtils sharedInstance].mUpDeviceInfo.deviceId;
      UpEngineDevice *kUpEngineDevice = (UpEngineDevice *)[[StepsUtils sharedInstance].mUpDeviceList objectForKey:kDeviceId];
      [kUpEngineDevice operateEngineCommand:^(UpDeviceResult *_Nonnull result) {
        self.mUpDeviceResult = result;
      }];
    });

    Then(@"^使用者收到指令下发接口的结果为\"([^\"]*)\"$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      NSString *kResult = args[0];
      CCIAssert([kResult isEqualToString:@"成功"] == self.mUpDeviceResult.isSuccessful, @"执行结果与实际不一致");
    });

    When(@"^使用者调用获取初始化属性列表接口$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      NSString *kDeviceId = [StepsUtils sharedInstance].mUpDeviceInfo.deviceId;
      UpEngineDevice *kUpEngineDevice = (UpEngineDevice *)[[StepsUtils sharedInstance].mUpDeviceList objectForKey:kDeviceId];
      self.mLEAttributeList = [kUpEngineDevice getEngineInitAttributeList];
    });

    Then(@"^使用者获取到的属性列表如下:$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      NSArray<LEAttribute *> *kExpectLEAttributeList = convertLEAttributeFromStepTableData(userInfo);
      CCIAssert([[StepsUtils sharedInstance] isEqualLEAttributeList:self.mLEAttributeList expectList:kExpectLEAttributeList], @"执行结果与实际不一致");
    });

    Given(@"^逻辑引擎获取初始化属性列表接口返回值为:$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      NSString *kDeviceId = [StepsUtils sharedInstance].mUpDeviceInfo.deviceId;
      UpEngineDevice *kUpEngineDevice = (UpEngineDevice *)[[StepsUtils sharedInstance].mUpDeviceList objectForKey:kDeviceId];
      NSArray<LEAttribute *> *kLEAttributeList = convertLEAttributeFromStepTableData(userInfo);
      OCMStub([kUpEngineDevice.engine initialFunctionAttributes]).andReturn(kLEAttributeList);
    });

    When(@"^使用者调用获取属性列表接口$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      NSString *kDeviceId = [StepsUtils sharedInstance].mUpDeviceInfo.deviceId;
      UpEngineDevice *kUpEngineDevice = (UpEngineDevice *)[[StepsUtils sharedInstance].mUpDeviceList objectForKey:kDeviceId];
      self.mLEAttributeList = [kUpEngineDevice getEngineAttributeList];
    });

    Given(@"^逻辑引擎获取属性列表接口返回值为:$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      NSString *kDeviceId = [StepsUtils sharedInstance].mUpDeviceInfo.deviceId;
      UpEngineDevice *kUpEngineDevice = (UpEngineDevice *)[[StepsUtils sharedInstance].mUpDeviceList objectForKey:kDeviceId];
      NSArray<LEAttribute *> *kLEAttributeList = convertLEAttributeFromStepTableData(userInfo);
      OCMStub([kUpEngineDevice.engine attributes]).andReturn(kLEAttributeList);
    });

    When(@"^使用者调用获取告警列表接口$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      NSString *kDeviceId = [StepsUtils sharedInstance].mUpDeviceInfo.deviceId;
      UpEngineDevice *kUpEngineDevice = (UpEngineDevice *)[[StepsUtils sharedInstance].mUpDeviceList objectForKey:kDeviceId];
      self.mLEAlarmList = [kUpEngineDevice getEngineCautionList];
    });

    Then(@"^使用者获取到的告警列表如下:$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      NSArray<LEAlarm *> *kExpectLEAlarmList = convertLEAlarmFromStepTableData(userInfo);
      CCIAssert([[StepsUtils sharedInstance] isEqualLEAlarmList:self.mLEAlarmList expectList:kExpectLEAlarmList], @"执行结果与实际不一致");
    });

    Given(@"^逻辑引擎获取告警列表接口返回值为:$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      NSString *kDeviceId = [StepsUtils sharedInstance].mUpDeviceInfo.deviceId;
      UpEngineDevice *kUpEngineDevice = (UpEngineDevice *)[[StepsUtils sharedInstance].mUpDeviceList objectForKey:kDeviceId];
      NSArray<LEAlarm *> *kExpectLEAlarmList = convertLEAlarmFromStepTableData(userInfo);
      OCMStub([kUpEngineDevice.engine alarms]).andReturn(kExpectLEAlarmList);
    });

    When(@"^使用者调用获取属性接口，属性名为\"([^\"]*)\"$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      NSString *kParameter = args[0];
      NSString *kDeviceId = [StepsUtils sharedInstance].mUpDeviceInfo.deviceId;
      UpEngineDevice *kUpEngineDevice = (UpEngineDevice *)[[StepsUtils sharedInstance].mUpDeviceList objectForKey:kDeviceId];
      LEAttribute *kLEAttribute = [kUpEngineDevice getEngineAttributeByName:kParameter];
      if (kLEAttribute) {
          self.mLEAttributeList = [NSMutableArray arrayWithObject:kLEAttribute];
      }
    });

    Then(@"^使用者获取到的属性值如下:$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      NSArray<LEAttribute *> *kExpectLEAttributeList = convertLEAttributeFromStepTableData(userInfo);
      CCIAssert([[StepsUtils sharedInstance] isEqualLEAttributeList:self.mLEAttributeList expectList:kExpectLEAttributeList], @"执行结果与实际不一致");
    });

    Given(@"^逻辑引擎有属性名为\"([^\"]*)\"的属性值:$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      NSString *kParameter = args[0];
      NSString *kDeviceId = [StepsUtils sharedInstance].mUpDeviceInfo.deviceId;
      UpEngineDevice *kUpEngineDevice = (UpEngineDevice *)[[StepsUtils sharedInstance].mUpDeviceList objectForKey:kDeviceId];
      NSArray<LEAttribute *> *kExpectLEAttributeList = convertLEAttributeFromStepTableData(userInfo);
      LEAttribute *kLEAttribute = kExpectLEAttributeList[0];
      OCMStub([kUpEngineDevice.engine getAttributeByName:kParameter]).andReturn(kLEAttribute);
    });

    When(@"^使用者调用获取设备告警状态接口$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      NSString *kDeviceId = [StepsUtils sharedInstance].mUpDeviceInfo.deviceId;
      UpEngineDevice *kUpEngineDevice = (UpEngineDevice *)[[StepsUtils sharedInstance].mUpDeviceList objectForKey:kDeviceId];
      self.mEngineWaring = [kUpEngineDevice isEngineWarning];
    });

    Then(@"^使用者获取到的设备告警状态为\"([^\"]*)\"$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      NSString *kParameter = args[0];
      CCIAssert([kParameter isEqualToString:@"true"] == self.mEngineWaring, @"执行结果与实际不一致");
    });

    Given(@"^逻辑引擎的告警状态为\"([^\"]*)\"$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      NSString *kDeviceId = [StepsUtils sharedInstance].mUpDeviceInfo.deviceId;
      UpEngineDevice *kUpEngineDevice = (UpEngineDevice *)[[StepsUtils sharedInstance].mUpDeviceList objectForKey:kDeviceId];
      NSString *kParameter = args[0];
      NSArray<LEAlarm *> *alrms;
      if ([kParameter isEqualToString:@"true"]) {
          alrms = @[ [LEAlarm new] ];
      }
      OCMStub([kUpEngineDevice.engine alarms]).andReturn(alrms);
    });

    When(@"^使用者调用重置逻辑引擎设备接口$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      NSString *kDeviceId = [StepsUtils sharedInstance].mUpDeviceInfo.deviceId;
      UpEngineDevice *kUpEngineDevice = (UpEngineDevice *)[[StepsUtils sharedInstance].mUpDeviceList objectForKey:kDeviceId];
      [kUpEngineDevice resetLogicEngineConfigState];
    });

    Then(@"^使用者获取到的初始化属性列表如下:$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      NSArray<LEAttribute *> *kExpectLEAttributeList = convertLEAttributeFromStepTableData(userInfo);
      CCIAssert([[StepsUtils sharedInstance] isEqualLEAttributeList:self.mLEAttributeList expectList:kExpectLEAttributeList], @"执行结果与实际不一致");
    });
    Given(@"^逻辑引擎操作命令接口的执行结果为\"([^\"]*)\"$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      NSString *kResult = args[0];
      NSString *kDeviceId = [StepsUtils sharedInstance].mUpDeviceInfo.deviceId;
      BOOL isSuccessful = NO;
      int errCode = ErrorCode_FAILURE;
      NSString *errDomain = @"failure";
      if ([kResult isEqualToString:@"成功"]) {
          isSuccessful = YES;
          errCode = ErrorCode_SUCCESS;
          errDomain = @"success";
      }
      NSDictionary *userInfoError = @{ @"EngineDeviceSteps" : [NSString stringWithFormat:@"Expected Result Of CMD Operation %@", errDomain] };
      NSError *error = [[NSError alloc] initWithDomain:errDomain code:errCode userInfo:userInfoError];
      UpEngineDevice *kUpEngineDevice = (UpEngineDevice *)[[StepsUtils sharedInstance].mUpDeviceList objectForKey:kDeviceId];
      OCMStub([kUpEngineDevice.engine operateCMDWithCompletion:[OCMArg any]]).andDo(^(NSInvocation *invocation) { /* block that handles the method invocation */
                                                                                                                  [invocation retainArguments];
                                                                                                                  void *successBlockPointer;
                                                                                                                  [invocation getArgument:&successBlockPointer atIndex:2];
                                                                                                                  LECompletion successBlock = (__bridge LECompletion)successBlockPointer;
                                                                                                                  successBlock(error);
      });
    });

    When(@"使用者调用逻辑引擎设备的计算命令接口,传入命令参数为\"([^\"]*)\",清除参数为\"([^\"]*)\"$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      NSString *kCMDParameter = args[0];
      NSString *kIsCleanCache = args[1];
      NSString *kDeviceId = [StepsUtils sharedInstance].mUpDeviceInfo.deviceId;
      BOOL kCleanCache = NO;
      NSMutableArray *kLEDeviceAttributeArray = [NSMutableArray array];
      if (LECommon_isValidString(kCMDParameter) && ![kCMDParameter isEqualToString:@"空对象"]) {
          NSArray *commands = [kCMDParameter componentsSeparatedByString:@","];
          for (int i = 0; i < commands.count; i++) {
              LEDeviceAttribute *kLEDeviceAttribute = convertLEDeviceAttributeFromArgs(commands, i);
              [kLEDeviceAttributeArray addObject:kLEDeviceAttribute];
          }
      }
      else {
          kLEDeviceAttributeArray = nil;
      }
      self.kLEDeviceAttributeArray = kLEDeviceAttributeArray;
      if ([kIsCleanCache isEqualToString:@"true"]) {
          kCleanCache = YES;
      }
      self.mCleanCache = kCleanCache;
      [(UpEngineDevice *)([[StepsUtils sharedInstance].mUpDeviceList objectForKey:kDeviceId]) calculateEngineCommands:kLEDeviceAttributeArray
                                                                                                                clean:kCleanCache
                                                                                                           completion:^(UpDeviceResult *_Nonnull result) {
                                                                                                             self.mUpDeviceResult = result;
                                                                                                           }];

    });

    Then(@"使用者收到逻辑引擎设备的计算命令接口的结果为\"([^\"]*)\"$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      NSString *kResult = args[0];
      CCIAssert([kResult isEqualToString:@"成功"] == self.mUpDeviceResult.isSuccessful, @"执行结果与实际不一致");
    });

    When(@"^调用设备命令接口,参数deviceId为\"([^\"]*)\",command的组命令名为\"([^\"]*)\",下发命令为\"([^\"]*)\"$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      NSString *kDeviceId = args[0];
      NSString *kGroupCommand = args[1];
      NSString *kCommandParameter = args[2];
      NSDictionary *kAttributes = convertCMDMapFromArgs(kCommandParameter);
      if ([kDeviceId isEqualToString:@"空对象"]) {
          kDeviceId = nil;
      }
      if ([kGroupCommand isEqualToString:@"空对象"]) {
          kGroupCommand = nil;
      }
      UpEngineDevice *kUpEngineDevice = (UpEngineDevice *)[[StepsUtils sharedInstance].mUpDeviceList objectForKey:kDeviceId];
      LEDeviceCMD *kDeviceCMD = [[LEDeviceCMD alloc] initDeviceCMDWithGroupName:kGroupCommand attributes:kAttributes timeout:0];
      if (kDeviceCMD.attributes.count == 0) {
          kDeviceCMD = nil;
      }
      [kUpEngineDevice LE_executeCommandWithDeviceID:kDeviceId
                                             command:kDeviceCMD
                                          completion:^(NSError *error) {
                                            if (!error) {
                                                self.mLE_executeCommand = YES;
                                            }
                                          }];
    });

    Then(@"执行设备命令接口的结果为\"([^\"]*)\"$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      NSString *kResult = args[0];
      CCIAssert([kResult isEqualToString:@"Success"] == self.mLE_executeCommand, @"执行结果与实际不一致");
    });

    Given(@"设备工具的executeDeviceCommand接口的结果为\"([^\"]*)\"$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      NSString *kResult = args[0];
      BOOL isSuccessful = NO;
      int errCode = ErrorCode_FAILURE;
      if ([kResult isEqualToString:@"Success"]) {
          isSuccessful = YES;
          errCode = ErrorCode_SUCCESS;
      }
      UpDeviceResult *kUpDeviceResult = [UpDeviceResult UpDeviceResult:errCode extraData:nil];
      [OCMStub([[[StepsUtils sharedInstance].mDefaultDeviceBroker getToolkit] executeDeviceCommand:[OCMArg any] command:[OCMArg any] timeout:0 finishBlock:[OCMArg any]]) andDo:^(NSInvocation *invocation) {
        [invocation retainArguments];
        void *successBlockPointer;
        [invocation getArgument:&successBlockPointer atIndex:5];
        ConnectRemoteDeviceResult successBlock = (__bridge ConnectRemoteDeviceResult)successBlockPointer;
        successBlock(kUpDeviceResult);
      }];
    });

    Then(@"设备工具的executeDeviceCommand接口被调用\"([^\"]*)\"次$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      NSString *kInvocationTimes = args[0];
      DeviceCommand *kExpectDeviceCommand = convertCommandFromStepTableData(userInfo);
      NSMutableArray *kExpectAttributeList = [NSMutableArray arrayWithCapacity:0];
      [kExpectDeviceCommand.attributes enumerateKeysAndObjectsUsingBlock:^(NSString *_Nonnull key, NSString *_Nonnull obj, BOOL *_Nonnull stop) {
        uSDKArgument *kuSDKArgument = [[uSDKArgument alloc] init];
        kuSDKArgument.name = key;
        kuSDKArgument.value = obj;
        [kExpectAttributeList addObject:kuSDKArgument];
      }];
      OCMVerify(times(kInvocationTimes.intValue), [[[StepsUtils sharedInstance].mDefaultDeviceBroker getToolkit] executeDeviceCommand:[OCMArg any] command:[OCMArg any] timeout:0 finishBlock:[OCMArg any]]);
    });

    //7045-7048
    When(@"^订阅者\"([^\"]*)\"订阅逻辑引擎设备变化通知$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      NSString *userParameter = args[0];
      NSString *kDeviceId = [StepsUtils sharedInstance].mUpDeviceInfo.deviceId;
      FakeLEEngineListener *kFakeListener = [[FakeLEEngineListener alloc] init];
      [self.mLEEngineListenerDict setValue:kFakeListener forKey:userParameter];
      UpEngineDevice *kUpEngineDevice = (UpEngineDevice *)[[StepsUtils sharedInstance].mUpDeviceList objectForKey:kDeviceId];
      [kUpEngineDevice attachEngine:self.mLEEngineListenerDict[userParameter]];
    });
    When(@"^逻辑引擎设备发生变化,事件为\"([^\"]*)\"$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      NSString *kParameter = args[0];
      NSUInteger event = [self getEvent:kParameter];
      NSString *kDeviceId = [StepsUtils sharedInstance].mUpDeviceInfo.deviceId;
      UpEngineDevice *kUpEngineDevice = (UpEngineDevice *)[[StepsUtils sharedInstance].mUpDeviceList objectForKey:kDeviceId];
      UpEngineReporter *engineReporter = [kUpEngineDevice valueForKeyPath:@"engineReporter"];
      [engineReporter notifyLogicEngine:self.mLEEngine event:event];
    });
    When(@"^订阅者\"([^\"]*)\"取消订阅逻辑引擎设备变化通知$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      NSString *userParameter = args[0];
      NSString *kDeviceId = [StepsUtils sharedInstance].mUpDeviceInfo.deviceId;
      UpEngineDevice *kUpEngineDevice = (UpEngineDevice *)[[StepsUtils sharedInstance].mUpDeviceList objectForKey:kDeviceId];
      [kUpEngineDevice detachEngine:self.mLEEngineListenerDict[userParameter]];
    });
    Then(@"^订阅者\"([^\"]*)\"监听逻辑引擎设备deviceNetStatusDidChange接口被调用\"([^\"]*)\"次$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      NSString *userParameter = args[0];
      NSString *kInvocationTimes = args[1];
      FakeLEEngineListener *kFakeListener = self.mLEEngineListenerDict[userParameter];
      NSUInteger actualTimes = [kFakeListener getInvocationTimesOfSelector:@selector(engine:deviceNetStatusDidChange:)];
      CCIAssert(actualTimes == kInvocationTimes.intValue, @"执行结果与实际不一致");
    });
    Then(@"^订阅者\"([^\"]*)\"监听逻辑引擎设备deviceAttributesDidChange接口被调用\"([^\"]*)\"次$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      NSString *userParameter = args[0];
      NSString *kInvocationTimes = args[1];
      FakeLEEngineListener *kFakeListener = self.mLEEngineListenerDict[userParameter];
      NSUInteger actualTimes = [kFakeListener getInvocationTimesOfSelector:@selector(engine:deviceAttributesDidChange:)];
      CCIAssert(actualTimes == kInvocationTimes.intValue, @"执行结果与实际不一致");
    });
    Then(@"^订阅者\"([^\"]*)\"监听逻辑引擎设备deviceAlarmsDidChange接口被调用\"([^\"]*)\"次$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      NSString *userParameter = args[0];
      NSString *kInvocationTimes = args[1];
      FakeLEEngineListener *kFakeListener = self.mLEEngineListenerDict[userParameter];
      NSUInteger actualTimes = [kFakeListener getInvocationTimesOfSelector:@selector(engine:deviceAlarmsDidChange:)];
      CCIAssert(actualTimes == kInvocationTimes.intValue, @"执行结果与实际不一致");
    });
}

#pragma mark - private
- (NSUInteger)getEvent:(NSString *)eventStr
{
    NSUInteger event = UpEngineEvent_EVENT_STATE_CHANGE;
    if ([eventStr isEqualToString:@"EVENT_ATTRIBUTES_CHANGE"]) {
        event = UpEngineEvent_EVENT_ATTRIBUTES_CHANGE;
    }
    else if ([eventStr isEqualToString:@"EVENT_DEVICE_CAUTION"]) {
        event = UpEngineEvent_EVENT_DEVICE_CAUTION;
    }
    else if ([eventStr isEqualToString:@"EVENT_CONNECTION_CHANGE"]) {
        event = UpEngineEvent_EVENT_CONNECTION_CHANGE;
    }
    else if ([eventStr isEqualToString:@"EVENT_STATE_CHANGE"]) {
        event = UpEngineEvent_EVENT_STATE_CHANGE;
    }
    return event;
}
- (void)prepareWork
{
    //synchronize function
    UpDeviceResult *kUpDeviceResultForBaseInfo = [UpDeviceResult UpDeviceResult:0 extraData:[StepsUtils sharedInstance].mUpDeviceInfo];
    OCMStub([[[StepsUtils sharedInstance].mDefaultDeviceBroker getToolkit] getDeviceBaseInfo:[OCMArg any] finishBlock:[OCMArg any]]).andDo(^(NSInvocation *invocation) { /* block that handles the method invocation */
                                                                                                                                                                         [invocation retainArguments];
                                                                                                                                                                         void *successBlockPointer;
                                                                                                                                                                         [invocation getArgument:&successBlockPointer atIndex:3];
                                                                                                                                                                         ConnectRemoteDeviceResult successBlock = (__bridge ConnectRemoteDeviceResult)successBlockPointer;
                                                                                                                                                                         successBlock(kUpDeviceResultForBaseInfo);
    });
    UpDeviceResult *kUpDeviceResult = [UpDeviceResult UpDeviceResult:0 extraData:[NSMutableArray arrayWithObject:[StepsUtils sharedInstance].mUpDeviceInfo]];
    OCMStub([[[StepsUtils sharedInstance].mDefaultDeviceBroker getToolkit] getSubDevBaseInfoList:[OCMArg any] finishBlock:[OCMArg any]]).andDo(^(NSInvocation *invocation) { /* block that handles the method invocation */
                                                                                                                                                                             [invocation retainArguments];
                                                                                                                                                                             void *successBlockPointer;
                                                                                                                                                                             [invocation getArgument:&successBlockPointer atIndex:3];
                                                                                                                                                                             ConnectRemoteDeviceResult successBlock = (__bridge ConnectRemoteDeviceResult)successBlockPointer;
                                                                                                                                                                             successBlock(kUpDeviceResult);
    });
}
@end
