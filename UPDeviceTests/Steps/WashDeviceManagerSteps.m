//
//  WashDeviceManagerSteps.m
//  UPDeviceTests
//
//  Created by 王杰 on 2023/2/1.
//  Copyright © 2023 海尔优家智能科技（北京）有限公司. All rights reserved.
//

#import "WashDeviceManagerSteps.h"
#import <UPStorage/UPStorage+Observer.h>
#import "UpWashAdapterApi.h"
#import "FakeWashdapterApi.h"
#import "UpWashDeviceManager.h"
#import "UpWashModel.h"
#import "UpDeviceInjection.h"
#import <OCMock/OCMock.h>
#import <Cucumberish/Cucumberish.h>
#import "StepsUtils.h"

@interface WashDeviceManagerSteps ()
@property (nonatomic, strong) Class mockApiClass;
@property (nonatomic, copy) NSString *storageKey;
@property (nonatomic, copy) NSString *washStorageJson;
@property (nonatomic, copy) NSString *washStorageJsonStringError;
@property (nonatomic, copy) NSString *washStorageJsonArrayError;
@property (nonatomic, copy) NSString *washStorageJsonEmptyDict;
@property (nonatomic, strong) NSDictionary *washDeviceModelDict;
@property (nonatomic, strong) NSDictionary *washDeviceModelEmptyDict;
@property (nonatomic, strong) NSDictionary *washServerConfig;
@property (nonatomic, strong) NSDictionary *washServerConfigEmptySettings;
@property (nonatomic, strong) NSDictionary *washServerConfigNoSettings;

@property (nonatomic, strong) UpDeviceResult *washModelResult;
@property (nonatomic, strong) FakeWashdapterApi *fakeMockApi;
@end
@implementation WashDeviceManagerSteps
- (void)defineStepsAndHocks
{
    before(^(CCIScenarioDefinition *scenario) {
      self.washStorageJson = nil;
      self.washDeviceModelDict = nil;
      self.washServerConfig = nil;
      self.washModelResult = nil;
      self.storageKey = nil;
      self.washStorageJsonStringError = nil;
      self.washStorageJsonArrayError = nil;
      self.washServerConfigEmptySettings = nil;
      self.washServerConfigNoSettings = nil;
      self.washStorageJsonEmptyDict = nil;
      self.washDeviceModelEmptyDict = nil;
    });

    after(^(CCIScenarioDefinition *scenario) {
      if (self.storageKey) {
          [UPStorage deleteNode:self.storageKey];
      }

    });

    Given(@"^获取洗衣机配置server接口使用模拟的$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      self.mockApiClass = OCMClassMock([UpWashAdapterApi class]);
      self.fakeMockApi = [[FakeWashdapterApi alloc] init];
      OCMStub([self.mockApiClass alloc]).andReturn(self.fakeMockApi);
    });

    Given(@"^数据模型\"([^\"]*)\"为:$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      if ([args[0] isEqualToString:@"washStorageJson"]) {
          self.washStorageJson = userInfo[@"DocString"];
      }
      else if ([args[0] isEqualToString:@"washDeviceModel"]) {
          self.washDeviceModelDict = jsonObjectFromEscapedString(userInfo[@"DocString"]);
      }
      else if ([args[0] isEqualToString:@"washDeviceModelEmpty"]) {
          self.washDeviceModelEmptyDict = jsonObjectFromEscapedString(userInfo[@"DocString"]);
      }
      else if ([args[0] isEqualToString:@"washServerConfig"]) {
          self.washServerConfig = jsonObjectFromEscapedString(userInfo[@"DocString"]);
      }
      else if ([args[0] isEqualToString:@"washServerConfigEmptySettings"]) {
          self.washServerConfigEmptySettings = jsonObjectFromEscapedString(userInfo[@"DocString"]);
      }
      else if ([args[0] isEqualToString:@"washServerConfigNoSettings"]) {
          self.washServerConfigNoSettings = jsonObjectFromEscapedString(userInfo[@"DocString"]);
      }
      else if ([args[0] isEqualToString:@"washStorageJsonStringError"]) {
          self.washStorageJsonStringError = userInfo[@"DocString"];
      }
      else if ([args[0] isEqualToString:@"washStorageJsonArrayError"]) {
          self.washStorageJsonArrayError = userInfo[@"DocString"];
      }
      else if ([args[0] isEqualToString:@"washStorageJsonEmptyDict"]) {
          self.washStorageJsonEmptyDict = userInfo[@"DocString"];
      }
    });

    Given(@"^storage中根据\"([^\"]*)\"查询到结果为\"([^\"]*)\"$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      NSString *typeId = args[0];
      NSString *result = args[1];
      self.storageKey = [NSString stringWithFormat:@"%@/%@", @"washRemainTimeKeys", typeId];
      if ([result isEqualToString:@"washStorageJson"]) {
          [UPStorage putStringValue:self.washStorageJson name:[NSString stringWithFormat:@"%@/%@", @"washRemainTimeKeys", typeId]];
      }
      else if ([result isEqualToString:@"空字符串"]) {
          [UPStorage putStringValue:@"" name:[NSString stringWithFormat:@"%@/%@", @"washRemainTimeKeys", typeId]];
      }
      else if ([result isEqualToString:@"空对象"]) {
      }
      else if ([result isEqualToString:@"washStorageJsonStringError"]) {
          [UPStorage putStringValue:self.washStorageJsonStringError name:[NSString stringWithFormat:@"%@/%@", @"washRemainTimeKeys", typeId]];
      }
      else if ([result isEqualToString:@"washStorageJsonArrayError"]) {
          [UPStorage putStringValue:self.washStorageJsonArrayError name:[NSString stringWithFormat:@"%@/%@", @"washRemainTimeKeys", typeId]];
      }
      else if ([result isEqualToString:@"washStorageJsonEmptyDict"]) {
          [UPStorage putStringValue:self.washStorageJsonEmptyDict name:[NSString stringWithFormat:@"%@/%@", @"washRemainTimeKeys", typeId]];
      }
    });

    Given(@"根据\"([^\"]*)\"从洗衣机server获取到的配置文件接口调用\"([^\"]*)\",接口返回结果为\"([^\"]*)\"$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      NSString *typeId = args[0];
      NSString *result = args[1];
      NSString *retData = args[2];
      self.fakeMockApi.typeId = typeId;
      if ([result isEqualToString:@"成功"]) {
          self.fakeMockApi.isSuccess = YES;
          if ([retData isEqualToString:@"washServerConfig"]) {
              self.fakeMockApi.resultDict = self.washServerConfig;
          }
          else if ([retData isEqualToString:@"washServerConfigEmptySettings"]) {
              self.fakeMockApi.resultDict = self.washServerConfigEmptySettings;
          }
          else if ([retData isEqualToString:@"washServerConfigNoSettings"]) {
              self.fakeMockApi.resultDict = self.washServerConfigNoSettings;
          }
          else if ([retData isEqualToString:@"空"]) {
              self.fakeMockApi.resultDict = nil;
          }
      }
      else if ([result isEqualToString:@"失败"]) {
          self.fakeMockApi.isSuccess = NO;
      }

    });

    When(@"^用户根据\"([^\"]*)\"调用获取洗衣机配置接口$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      NSString *typeId = args[0];
      if ([typeId isEqualToString:@"空字符串"]) {
          typeId = @"";
      }
      else if ([typeId isEqualToString:@"空对象"]) {
          typeId = nil;
      }
      [[UpWashDeviceManager getInstance] getWashDeviceModelWithTypeId:typeId
                                                          finishBlock:^(UpDeviceResult *_Nonnull result) {
                                                            self.washModelResult = result;
                                                          }];
    });

    Then(@"^用户调用获取洗衣机配置接口获取到的洗衣机配置接口结果为\"([^\"]*)\",extraData为\"([^\"]*)\"$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      NSString *result = args[0];
      NSString *retData = args[1];
      BOOL isSuccess = [result isEqualToString:@"成功"];
      CCIAssert(isSuccess == self.washModelResult.isSuccessful, @"与实际不一致");
      if (isSuccess) {
          NSDictionary *retDataDict = nil;
          if ([retData isEqualToString:@"washDeviceModel"]) {
              retDataDict = self.washDeviceModelDict;
          }
          else if ([retData isEqualToString:@"washDeviceModelEmpty"]) {
              retDataDict = self.washDeviceModelEmptyDict;
          }
          else if ([retData isEqualToString:@"空"]) {
              retDataDict = nil;
          }
          UpWashModel *washModel = self.washModelResult.extraData;
          NSDictionary *realRetData = jsonObjectFromEscapedString(washModel.JSONStringConversion);
          CCIAssert([retDataDict isEqual:realRetData], @"与实际不一致");
      }
    });


    Then(@"^用户根据\"([^\"]*)\"从storage获取到的洗衣机配置模型json为\"([^\"]*)\"$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      NSString *typeId = args[0];
      NSString *expectJson = args[1];
      if ([expectJson isEqualToString:@"washStorageJson"]) {
          expectJson = self.washStorageJson;
      }
      else if ([expectJson isEqualToString:@"空字符串"]) {
          expectJson = @"";
      }
      else if ([expectJson isEqualToString:@"空对象"]) {
          expectJson = nil;
      }
      else if ([expectJson isEqualToString:@"washStorageJsonStringError"]) {
          expectJson = self.washStorageJsonStringError;
      }
      else if ([expectJson isEqualToString:@"washStorageJsonArrayError"]) {
          expectJson = self.washStorageJsonArrayError;
          ;
      }
      NSString *realJson = [UPStorage getStringValue:[NSString stringWithFormat:@"%@/%@", @"washRemainTimeKeys", typeId] defaultValue:nil];
      BOOL realIsNull = ![realJson isKindOfClass:[NSString class]] || realJson.length == 0;
      if (realIsNull) {
          CCIAssert(realIsNull == expectJson.length == 0, @"与实际不一致");
      }
      else {
          NSDictionary *realRetData = jsonObjectFromEscapedString(realJson);
          NSDictionary *expectDict = jsonObjectFromEscapedString(expectJson);
          CCIAssert([realRetData isEqual:expectDict], @"与实际不一致");
      }


    });
}
@end
