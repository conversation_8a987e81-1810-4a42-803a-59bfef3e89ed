//
//  WifiDeviceToolkitSteps.m
//  UPDeviceTests
//
//  Created by <PERSON><PERSON><PERSON><PERSON> on 2020/12/24.
//  Copyright © 2020 海尔优家智能科技（北京）有限公司. All rights reserved.
//

#import "WifiDeviceToolkitSteps.h"
#import "WifiDeviceToolkitImpl.h"
#import <OCMock/OCMock.h>
#import <Cucumberish/Cucumberish.h>
#import "FakeUSDKDevice.h"
#import "StepsUtils.h"
#import <uSDK/uSDKDevice.h>
#import "DeviceNetWorkQualityInfo.h"
#import "DeviceCommand.h"
#import <LogicEngine/LECommonFuncs.h>
#import <MJExtension/MJExtension.h>
#import "FakeReportListener.h"
#import "WifiDeviceHelper.h"
#import "FakeDetectListener.h"
#import "UpDeviceInjection.h"

typedef void (^USDKSuccessResult)(void);

typedef void (^USDKFailureResult)(NSError *error);

@interface WifiDeviceToolkitSteps ()
@property (nonatomic, strong) WifiDeviceToolkitImpl *mWifiDeviceToolkitImpl;
@property (nonatomic, strong) uSDKManager *mUSDKManager;
@property (nonatomic, strong) uSDKDeviceManager *mUSDKDeviceManager;
@property (nonatomic, strong) UpDeviceResult *mCallbackResult;
@property (nonatomic, strong) DeviceCommand *mDeviceCommand;
@property (nonatomic, strong) NSMutableDictionary *mConnectCloudParameter;
@property (nonatomic, copy) NSString *mAppID;
@property (nonatomic, copy) NSString *mAppKey;
@property (nonatomic, assign) BOOL mIsBound;
@property (nonatomic, assign) BOOL mIsModuleNeedOta;
@property (nonatomic, strong) NSMutableDictionary *mCallbackResultMap;
@property (nonatomic, assign) DeviceNetType deviceNetType;
@property (nonatomic, strong) NSString *softwareVersion;
@property (nonatomic, strong) FakeReportListener *reportListener;
@property (nonatomic, strong) FakeDetectListener *detectListener;
@property (nonatomic, assign) BOOL isGroup;
@end

@implementation WifiDeviceToolkitSteps
- (void)defineStepsAndHocks
{
    before(^(CCIScenarioDefinition *scenario) {
      self.mUSDKManager = nil;
      self.mUSDKDeviceManager = nil;
      self.mCallbackResult = nil;
      self.mConnectCloudParameter = nil;
      self.mIsBound = NO;
      self.mAppID = nil;
      self.mAppKey = nil;
      self.mIsModuleNeedOta = NO;
      self.mWifiDeviceToolkitImpl = nil;
      self.mCallbackResultMap = nil;
      self.softwareVersion = nil;
      self.reportListener = nil;
      self.detectListener = nil;
      self.isGroup = NO;
    });

    Given(@"^初始化WifiDeviceToolkit库,uSDK管理器为\"([^\"]*)\",uSDK设备管理器为\"([^\"]*)\"$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      NSString *uSDKManagerStr = args[0];
      NSString *uSDKDeviceManagerStr = args[1];
      BOOL isFakeUSDKManager = [uSDKManagerStr isEqualToString:@"模拟的"];
      BOOL isFakeUSDKDeviceManager = [uSDKDeviceManagerStr isEqualToString:@"模拟的"];

      if (isFakeUSDKManager) {
          self.mUSDKManager = OCMClassMock([uSDKManager class]);
      }
      else {
          self.mUSDKManager = [uSDKManager defaultManager];
      }

      if (isFakeUSDKDeviceManager) {
          self.mUSDKDeviceManager = OCMClassMock([uSDKDeviceManager class]);
      }
      else {
          self.mUSDKDeviceManager = [uSDKDeviceManager defaultDeviceManager];
      }

      self.mWifiDeviceToolkitImpl = [WifiDeviceToolkitImpl WifiDeviceToolkitImpl:self.mUSDKManager DeviceManager:self.mUSDKDeviceManager];
    });

    Given(@"^uSDK设备管理器,调用getDeviceWithID方法,参数deviceID为\"([^\"]*)\",返回\"([^\"]*)\"对象$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      NSString *deviceId = args[0];
      NSString *kParameter = args[1];
      if ([kParameter isEqualToString:@"空"]) {
          [OCMStub([self.mUSDKDeviceManager getDeviceWithID:deviceId]) andReturn:([uSDKDevice new])];
          return;
      }
      self.mCallbackResultMap = [NSMutableDictionary dictionaryWithCapacity:0];
      FakeUSDKDevice *device = [[FakeUSDKDevice alloc] init];
      [device mockuSDKDeviceID:deviceId];
      FakeUSDKDevice *kFakeUSDKDevice = OCMPartialMock(device);
      [OCMStub([self.mUSDKDeviceManager getDeviceWithID:deviceId]) andReturn:(kFakeUSDKDevice)];
    });

    Given(@"^deviceID为\"([^\"]*)\"的uSDKDevice对象的subscribeResource接口的调用结果为\"([^\"]*)\"$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      NSString *kDeviceId = args[0];
      uSDKDevice *kuSDKDevice = [self.mUSDKDeviceManager getDeviceWithID:kDeviceId];
      FakeUSDKDevice *kFakeUSDKDevice = (FakeUSDKDevice *)kuSDKDevice;
      NSString *kResult = args[1];
      BOOL bResult = [kResult isEqualToString:@"Success"];
      [kFakeUSDKDevice mockSubscribeResourceResult:bResult];
    });

    When(@"^调用订阅资源接口,参数deviceId为\"([^\"]*)\", 资源名称为\"([^\"]*)\"$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      NSString *deviceId = args[0];
      if ([deviceId isEqualToString:@"空对象"]) {
          deviceId = nil;
      }
      if ([deviceId isEqualToString:@"空字符串"]) {
          deviceId = @"";
      }
      NSString *resourceName = args[1];
      if ([resourceName isEqualToString:@"空对象"]) {
          resourceName = nil;
      }
      if ([resourceName isEqualToString:@"空字符串"]) {
          resourceName = @"";
      }
      [self.mWifiDeviceToolkitImpl attachResource:deviceId
                                     resourceName:resourceName
                                      finishBlock:^(UpDeviceResult *_Nonnull result) {
                                        self.mCallbackResult = result;
                                      }];
    });

    Then(@"^订阅资源方法回调结果为\"([^\"]*)\"$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      NSString *kResult = args[0];
      [self assertResult:kResult];
    });

    Then(@"^deviceID为\"([^\"]*)\"的uSDKDevice对象的subscribeResource接口被调用\"([^\"]*)\"次,参数为\"([^\"]*)\"$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      NSString *kDeviceId = args[0];
      NSString *kInvocationTimes = args[1];
      NSString *kParameter = args[2];
      if ([kParameter isEqualToString:@""]) {
          kParameter = nil;
      }
      uSDKDevice *kuSDKDevice = [self.mUSDKDeviceManager getDeviceWithID:kDeviceId];
      FakeUSDKDevice *kFakeUSDKDevice = (FakeUSDKDevice *)kuSDKDevice;
      CCIAssert(kuSDKDevice, [NSString stringWithFormat:@"deviceId为%@的设备不存在", kDeviceId]);
      NSString *kActualParameter = [kFakeUSDKDevice getParametersOfSelector:@selector(subscribeResource:success:failure:)].firstObject;
      if (kParameter && kActualParameter) {
          CCIAssert([kActualParameter isEqualToString:kParameter], @"执行结果与实际不一致");
      }
      NSUInteger actualTimes = [kFakeUSDKDevice getInvocationTimesOfSelector:@selector(subscribeResource:success:failure:)];
      CCIAssert(actualTimes == kInvocationTimes.intValue, @"执行结果与实际不一致");
    });

    Given(@"^deviceID为\"([^\"]*)\"的uSDKDevice对象的unSubscribeResource接口的调用结果为\"([^\"]*)\"$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      NSString *kDeviceId = args[0];
      uSDKDevice *kuSDKDevice = [self.mUSDKDeviceManager getDeviceWithID:kDeviceId];
      FakeUSDKDevice *kFakeUSDKDevice = (FakeUSDKDevice *)kuSDKDevice;
      NSString *kResult = args[1];
      BOOL bResult = [kResult isEqualToString:@"Success"];
      [kFakeUSDKDevice mockUnSubscribeResourceResult:bResult];
    });

    When(@"^调用解订阅资源接口,参数deviceId为\"([^\"]*)\", 资源名称为\"([^\"]*)\"$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      NSString *deviceId = args[0];
      if ([deviceId isEqualToString:@"空对象"]) {
          deviceId = nil;
      }
      if ([deviceId isEqualToString:@"空字符串"]) {
          deviceId = @"";
      }
      NSString *resourceName = args[1];
      if ([resourceName isEqualToString:@"空对象"]) {
          resourceName = nil;
      }
      if ([resourceName isEqualToString:@"空字符串"]) {
          resourceName = @"";
      }
      [self.mWifiDeviceToolkitImpl detachResource:deviceId
                                     resourceName:resourceName
                                      finishBlock:^(UpDeviceResult *_Nonnull result) {
                                        self.mCallbackResult = result;
                                      }];
    });

    Then(@"^解订阅资源方法回调结果为\"([^\"]*)\"$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      NSString *kResult = args[0];
      [self assertResult:kResult];
    });

    Then(@"^deviceID为\"([^\"]*)\"的uSDKDevice对象的unSubscribeResource接口被调用\"([^\"]*)\"次,参数为\"([^\"]*)\"$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      NSString *kDeviceId = args[0];
      NSString *kInvocationTimes = args[1];
      NSString *kParameter = args[2];
      if ([kParameter isEqualToString:@""]) {
          kParameter = nil;
      }
      uSDKDevice *kuSDKDevice = [self.mUSDKDeviceManager getDeviceWithID:kDeviceId];
      FakeUSDKDevice *kFakeUSDKDevice = (FakeUSDKDevice *)kuSDKDevice;
      CCIAssert(kuSDKDevice, [NSString stringWithFormat:@"deviceId为%@的设备不存在", kDeviceId]);
      NSString *kActualParameter = [kFakeUSDKDevice getParametersOfSelector:@selector(unSubscribeResource:success:failure:)].firstObject;
      if (kParameter && kActualParameter) {
          CCIAssert([kActualParameter isEqualToString:kParameter], @"执行结果与实际不一致");
      }
      NSUInteger actualTimes = [kFakeUSDKDevice getInvocationTimesOfSelector:@selector(unSubscribeResource:success:failure:)];
      CCIAssert(actualTimes == kInvocationTimes.intValue, @"执行结果与实际不一致");
    });

    Given(@"^deviceID为\"([^\"]*)\"的uSDKDevice对象的writeAttributeWithName接口的结果为\"([^\"]*)\"$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      NSString *kDeviceId = args[0];
      uSDKDevice *kuSDKDevice = [self.mUSDKDeviceManager getDeviceWithID:kDeviceId];
      FakeUSDKDevice *kFakeUSDKDevice = (FakeUSDKDevice *)kuSDKDevice;
      NSString *kResult = args[1];
      BOOL bResult = [kResult isEqualToString:@"Success"];
      [kFakeUSDKDevice mockWriteAttributeWithNameResult:bResult];
    });

    Given(@"^deviceID为\"([^\"]*)\"的uSDKDevice对象的executeOperation接口的结果为\"([^\"]*)\"$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      NSString *kDeviceId = args[0];
      uSDKDevice *kuSDKDevice = [self.mUSDKDeviceManager getDeviceWithID:kDeviceId];
      FakeUSDKDevice *kFakeUSDKDevice = (FakeUSDKDevice *)kuSDKDevice;
      NSString *kResult = args[1];
      BOOL bResult = [kResult isEqualToString:@"Success"];
      [kFakeUSDKDevice mockExecuteOperationResult:bResult];
    });

    When(@"^调用执行指令接口,参数deviceId为\"([^\"]*)\",command的组命令名为\"([^\"]*)\",下发命令为\"([^\"]*)\"$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      NSString *kDeviceId = args[0];
      NSString *kGroupCommand = args[1];
      NSString *kCommandParameter = args[2];
      NSDictionary *kAttributes = convertCMDMapFromArgs(kCommandParameter);
      if ([kDeviceId isEqualToString:@"空对象"]) {
          kDeviceId = nil;
      }
      if ([kGroupCommand isEqualToString:@"空对象"]) {
          kGroupCommand = nil;
      }
      DeviceCommand *kDeviceCommand = [DeviceCommand DeviceCommand:kGroupCommand Attributes:kAttributes];
      [self.mWifiDeviceToolkitImpl executeDeviceCommand:kDeviceId
                                                command:kDeviceCommand
                                                timeout:0
                                            finishBlock:^(UpDeviceResult *result) {
                                              self.mCallbackResult = result;
                                            }];
    });

    Then(@"^执行指令接口的结果为\"([^\"]*)\"$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      NSString *kResult = args[0];
      [self assertResult:kResult];
    });

    Then(@"^deviceID为\"([^\"]*)\"的uSDKDevice对象的writeAttributeWithName接口被调用\"([^\"]*)\"次,参数为:$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      NSString *kDeviceId = args[0];
      NSString *kInvocationTimes = args[1];
      uSDKDevice *kuSDKDevice = [self.mUSDKDeviceManager getDeviceWithID:kDeviceId];
      NSArray<id<UpDeviceAttribute>> *kExpectLEDeviceAttributeList = convertLEDeviceAttributeFromStepTableData(userInfo);
      FakeUSDKDevice *kFakeUSDKDevice = (FakeUSDKDevice *)kuSDKDevice;
      NSUInteger actualTimes = [kFakeUSDKDevice getInvocationTimesOfSelector:@selector(writeAttributeWithName:value:success:failure:)];
      NSArray *kActualParameterList = [kFakeUSDKDevice getParametersOfSelector:@selector(writeAttributeWithName:value:success:failure:)];
      NSMutableArray *kLEDeviceAttributeList = [NSMutableArray arrayWithCapacity:0];
      LEDeviceAttribute *kLEDeviceAttribute = [[LEDeviceAttribute alloc] initDeviceAttributeWithName:kActualParameterList[0] value:kActualParameterList[1]];
      if (kLEDeviceAttribute) {
          [kLEDeviceAttributeList addObject:kLEDeviceAttribute];
      }
      CCIAssert(kInvocationTimes.intValue == actualTimes && [[StepsUtils sharedInstance] isEqualUpDeviceAttributeList:kLEDeviceAttributeList expectList:kExpectLEDeviceAttributeList], @"执行结果与实际不一致");
    });

    Then(@"^deviceID为\"([^\"]*)\"的uSDKDevice对象的executeOperation接口被调用\"([^\"]*)\"次,参数为:$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      NSString *kDeviceId = args[0];
      NSString *kInvocationTimes = args[1];
      uSDKDevice *kuSDKDevice = [self.mUSDKDeviceManager getDeviceWithID:kDeviceId];
      DeviceCommand *kExpectDeviceCommand = convertCommandFromStepTableData(userInfo);
      FakeUSDKDevice *kFakeUSDKDevice = (FakeUSDKDevice *)kuSDKDevice;
      NSUInteger actualTimes = [kFakeUSDKDevice getInvocationTimesOfSelector:@selector(executeOperation:args:success:failure:)];
      NSArray *kActualParameterList = [kFakeUSDKDevice getParametersOfSelector:@selector(executeOperation:args:success:failure:)];
      NSMutableArray *kExpectAttributeList = [NSMutableArray arrayWithCapacity:0];
      [kExpectDeviceCommand.attributes enumerateKeysAndObjectsUsingBlock:^(NSString *_Nonnull key, NSString *_Nonnull obj, BOOL *_Nonnull stop) {
        uSDKArgument *kuSDKArgument = [[uSDKArgument alloc] init];
        kuSDKArgument.name = key;
        kuSDKArgument.value = obj;
        [kExpectAttributeList addObject:kuSDKArgument];
      }];
      //have parameters
      if ([kExpectDeviceCommand groupName] && [kExpectDeviceCommand attributes].count > 0) {
          NSString *kExpect = [NSArray mj_keyValuesArrayWithObjectArray:kExpectAttributeList].mj_JSONString;
          NSString *kActual = [NSArray mj_keyValuesArrayWithObjectArray:kExpectAttributeList].mj_JSONString;
          CCIAssert(kInvocationTimes.intValue == actualTimes && [kActualParameterList[0] isEqualToString:kExpectDeviceCommand.groupName] && [kActual isEqualToString:kExpect], @"执行结果与实际不一致");
      }
    });


    Given(@"^uSDK设备管理器的setUserInfo接口的结果为\"([^\"]*)\"$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      NSString *kResult = args[0];
      //      NSError *kErrInfo = [[NSError alloc] initWithDomain:@"failure" code:ErrorCode_FAILURE userInfo:nil];
      OCMStub([self.mUSDKDeviceManager setUserInfo:[OCMArg any] error:[OCMArg anyObjectRef]]).andReturn([kResult isEqualToString:@"Success"] ? YES : NO);
    });

    When(@"^调用连接远程网关接口,参数为\"([^\"]*)\"$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      NSString *kParameter = args[0];
      NSMutableDictionary *kParameterDictionary = convertCMDMapFromArgs(kParameter);
      self.mConnectCloudParameter = kParameterDictionary;
      [self.mWifiDeviceToolkitImpl connectRemoteDevices:nil
                                                 params:kParameterDictionary
                                            finishBlock:^(UpDeviceResult *result) {
                                              self.mCallbackResult = result;
                                            }];
    });

    Then(@"^连接远程网关接口的回调结果为\"([^\"]*)\"$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      NSString *kResult = args[0];
      [self assertResult:kResult];
    });

    Then(@"^uSDK设备管理器的setUserInfo接口被调用\"([^\"]*)\"次,参数为:$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      NSArray<NSArray<NSString *> *> *infoList = getTableDataListFromExpectUserInfo(userInfo);
      NSArray<NSString *> *deviceInfoItemList = nil;
      if (infoList.count > 0) {
          deviceInfoItemList = infoList[0];
      }
      NSString *kInvocationTimes = args[0];
      OCMVerify(times(kInvocationTimes.intValue), [self.mUSDKDeviceManager setUserInfo:[OCMArg any] error:[OCMArg anyObjectRef]]);
      if (self.mConnectCloudParameter.count > 1) {
          if ([[self.mConnectCloudParameter allKeys] containsObject:@"usdk-access-token"] && [[self.mConnectCloudParameter allKeys] containsObject:@"usdk-access-userId"]) {
              CCIAssert([[self.mConnectCloudParameter objectForKey:@"usdk-access-token"] isEqualToString:deviceInfoItemList[0]] && [[self.mConnectCloudParameter objectForKey:@"usdk-access-userId"] isEqualToString:deviceInfoItemList[1]], @"执行结果与实际不一致");
          }
          else if ([[self.mConnectCloudParameter allKeys] containsObject:@"usdk-access-token"]) {
              CCIAssert([[self.mConnectCloudParameter objectForKey:@"usdk-access-token"] isEqualToString:deviceInfoItemList[0]], @"执行结果与实际不一致");
          }
          else if ([[self.mConnectCloudParameter allKeys] containsObject:@"usdk-access-userId"]) {
              CCIAssert([[self.mConnectCloudParameter objectForKey:@"usdk-access-userId"] isEqualToString:deviceInfoItemList[1]], @"执行结果与实际不一致");
          }
      }
    });

    When(@"^调用断开远程网关接口$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      [self.mWifiDeviceToolkitImpl disconnectRemoteDevices:^(UpDeviceResult *result) {
        self.mCallbackResult = result;
      }];
    });

    Then(@"^断开远程网关接口回调结果为\"([^\"]*)\"$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      NSString *kResult = args[0];
      [self assertResult:kResult];
    });

    Given(@"^uSDK设备管理器的refreshDeviceListWithSuccess接口的结果为\"([^\"]*)\"$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      NSString *kResult = args[0];
      NSError *kErrInfo = [[NSError alloc] initWithDomain:@"failure" code:ErrorCode_FAILURE userInfo:nil];
      OCMStub([self.mUSDKDeviceManager refreshDeviceListWithSuccess:[OCMArg any] failure:[OCMArg any]]).andDo(^(NSInvocation *invocation) { /* block that handles the method invocation */
                                                                                                                                            [invocation retainArguments];
                                                                                                                                            void *successBlockPointer;
                                                                                                                                            if ([kResult isEqualToString:@"Success"]) {
                                                                                                                                                [invocation getArgument:&successBlockPointer atIndex:2];
                                                                                                                                                USDKSuccessResult successBlock = (__bridge USDKSuccessResult)successBlockPointer;
                                                                                                                                                successBlock();
                                                                                                                                            }
                                                                                                                                            else {
                                                                                                                                                [invocation getArgument:&successBlockPointer atIndex:3];
                                                                                                                                                USDKFailureResult successBlock = (__bridge USDKFailureResult)successBlockPointer;
                                                                                                                                                successBlock(kErrInfo);
                                                                                                                                            }
      });
    });

    When(@"^调用刷新设备列表接口$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      [self.mWifiDeviceToolkitImpl refreshUsdkDeviceList:^(UpDeviceResult *result) {
        self.mCallbackResult = result;
      }];
    });

    Then(@"^刷新设备列表接口回调结果为\"([^\"]*)\"$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      NSString *kResult = args[0];
      [self assertResult:kResult];
    });

    Then(@"^uSDK设备管理器的refreshDeviceListWithSuccess接口被调用\"([^\"]*)\"次$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      NSString *kInvocationTimes = args[0];
      OCMVerify(times(kInvocationTimes.intValue), [self.mUSDKDeviceManager refreshDeviceListWithSuccess:[OCMArg any] failure:[OCMArg any]]);
    });

    Given(@"^调用uSDK设备管理器的getDeviceWithID接口,传入参数为\"([^\"]*)\",返回uSDKDevice对象的isBound的值为\"([^\"]*)\"$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      NSString *kDeviceId = args[0];
      NSString *kIsBound = args[1];
      uSDKDevice *kuSDKDevice = [self.mUSDKDeviceManager getDeviceWithID:kDeviceId];
      FakeUSDKDevice *kFakeUSDKDevice = (FakeUSDKDevice *)kuSDKDevice;
      [kFakeUSDKDevice mockIsBoundsSuccessfulResult:[kIsBound isEqualToString:@"true"]];
    });

    When(@"^调用获取是否绑定接口,参数deviceId为\"([^\"]*)\"$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      NSString *kDeviceId = args[0];
      if ([kDeviceId isEqualToString:@"空对象"]) {
          kDeviceId = nil;
      }
      self.mIsBound = [self.mWifiDeviceToolkitImpl isBound:kDeviceId];
    });

    Then(@"^获取是否绑定接口的结果为\"([^\"]*)\",回调结果中的extraData值为\"([^\"]*)\"$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      NSString *kResult = args[0];
      BOOL bResult = [kResult isEqualToString:@"true"];
      CCIAssert(bResult == self.mIsBound, [NSString stringWithFormat:@"回调结果期望不一致,期望为%d,实际为%d", bResult, self.mCallbackResult.isSuccessful]);
    });

    Given(@"^deviceID为\"([^\"]*)\"的uSDKDevice对象的\"([^\"]*)\"接口的结果为\"([^\"]*)\"$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      NSString *kDeviceId = args[0];
      NSString *kFunctionName = args[1];
      NSString *kResult = args[2];
      BOOL bResult = [kResult isEqualToString:@"Success"];
      uSDKDevice *kuSDKDevice = [self.mUSDKDeviceManager getDeviceWithID:kDeviceId];
      FakeUSDKDevice *kFakeUSDKDevice = (FakeUSDKDevice *)kuSDKDevice;
      if ([kFunctionName isEqualToString:@"disconnectWithSuccess"]) {
          [kFakeUSDKDevice mockDisconnectDeviceResult:bResult];
      }
      else if ([kFunctionName isEqualToString:@"startBoardFOTAWithSuccess"]) {
          [kFakeUSDKDevice mockStartBoardFOTAResult:bResult];
      }
      else if ([kFunctionName isEqualToString:@"fetchBLEHistoryDataSuccess"]) {
          [kFakeUSDKDevice mockFetchBLEHistoryDataResult:bResult];
      }
      else if ([kFunctionName isEqualToString:@"cancelFetchBLEHistoryDataSuccess"]) {
          [kFakeUSDKDevice mockCancelFetchBLEHistoryDataResult:bResult];
      }
    });

    When(@"^调用\"([^\"]*)\"接口,参数deviceId为\"([^\"]*)\"$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      NSString *kDeviceId = args[1];
      NSString *kFunctionName = args[0];
      if ([kDeviceId isEqualToString:@"空对象"]) {
          kDeviceId = nil;
      }
      if ([kFunctionName isEqualToString:@"断开设备"]) {
          //test case 9007 example 4 device is not exsited ,default result is successful
          [self.mWifiDeviceToolkitImpl detachDevice:kDeviceId
                                        finishBlock:^(UpDeviceResult *result) {
                                          self.mCallbackResult = result;
                                          [self.mCallbackResultMap setValue:result forKey:NSStringFromSelector((@selector(detachDevice:finishBlock:)))];
                                        }];
      }
      else if ([kFunctionName isEqualToString:@"开始Fota升级"]) {
          [self.mWifiDeviceToolkitImpl startBoardFOTA:kDeviceId
                                          finishBlock:^(UpDeviceResult *result) {
                                            self.mCallbackResult = result;
                                            [self.mCallbackResultMap setValue:result forKey:NSStringFromSelector(@selector(startBoardFOTA:finishBlock:))];
                                          }];
      }
      else if ([kFunctionName isEqualToString:@"获取蓝牙历史数据"]) {
          [self.mWifiDeviceToolkitImpl fetchBLEHistoryData:kDeviceId
                                               finishBlock:^(UpDeviceResult *result) {
                                                 self.mCallbackResult = result;
                                                 [self.mCallbackResultMap setValue:result forKey:NSStringFromSelector(@selector(fetchBLEHistoryData:finishBlock:))];
                                               }];
      }
      else if ([kFunctionName isEqualToString:@"取消获取蓝牙历史数据"]) {
          [self.mWifiDeviceToolkitImpl cancelFetchBLEHistoryData:kDeviceId
                                                     finishBlock:^(UpDeviceResult *result) {
                                                       self.mCallbackResult = result;
                                                       [self.mCallbackResultMap setValue:result forKey:NSStringFromSelector(@selector(cancelFetchBLEHistoryData:finishBlock:))];
                                                     }];
      }
    });

    Then(@"^\"([^\"]*)\"接口回调结果为\"([^\"]*)\"$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      NSString *kFunctionName = args[0];
      NSString *kResult = args[1];
      BOOL bResult = [kResult isEqualToString:@"Success"];
      UpDeviceResult *kCallbackResult = NULL;
      if ([kFunctionName isEqualToString:@"断开设备"]) {
          kCallbackResult = [self.mCallbackResultMap objectForKey:NSStringFromSelector(@selector(detachDevice:finishBlock:))];
      }
      else if ([kFunctionName isEqualToString:@"开始Fota升级"]) {
          kCallbackResult = [self.mCallbackResultMap objectForKey:NSStringFromSelector(@selector(startBoardFOTA:finishBlock:))];
      }
      else if ([kFunctionName isEqualToString:@"获取蓝牙历史数据"]) {
          kCallbackResult = [self.mCallbackResultMap objectForKey:NSStringFromSelector(@selector(fetchBLEHistoryData:finishBlock:))];
      }
      else if ([kFunctionName isEqualToString:@"取消获取蓝牙历史数据"]) {
          kCallbackResult = [self.mCallbackResultMap objectForKey:NSStringFromSelector(@selector(cancelFetchBLEHistoryData:finishBlock:))];
      }
      CCIAssert(kCallbackResult.isSuccessful == bResult, [NSString stringWithFormat:@"回调结果期望不一致,期望为%d,实际为%d", bResult, self.mCallbackResult.isSuccessful]);
    });

    Then(@"^deviceID为\"([^\"]*)\"的uSDKDevice对象的\"([^\"]*)\"接口被调用\"([^\"]*)\"次$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      NSString *kDeviceId = args[0];
      NSString *kFunctionName = args[1];
      NSString *kInvocationTimes = args[2];
      uSDKDevice *kuSDKDevice = [self.mUSDKDeviceManager getDeviceWithID:kDeviceId];
      CCIAssert(kuSDKDevice, [NSString stringWithFormat:@"deviceId为%@的设备不存在", kDeviceId]);
      FakeUSDKDevice *kFakeUSDKDevice = (FakeUSDKDevice *)kuSDKDevice;
      NSUInteger actualTimes = 0;
      if ([kFunctionName isEqualToString:@"disconnectWithSuccess"]) {
          actualTimes = [kFakeUSDKDevice getInvocationTimesOfSelector:@selector(disconnectWithSuccess:failure:)];
      }
      else if ([kFunctionName isEqualToString:@"startBoardFOTAWithSuccess"]) {
          actualTimes = [kFakeUSDKDevice getInvocationTimesOfSelector:@selector(startBoardFOTAWithSuccess:failure:)];
      }
      else if ([kFunctionName isEqualToString:@"fetchBLEHistoryDataSuccess"]) {
          actualTimes = [kFakeUSDKDevice getInvocationTimesOfSelector:@selector(fetchBLEHistoryDataSuccess:failure:)];
      }
      else if ([kFunctionName isEqualToString:@"cancelFetchBLEHistoryDataSuccess"]) {
          actualTimes = [kFakeUSDKDevice getInvocationTimesOfSelector:@selector(cancelFetchBLEHistoryDataSuccess:failure:)];
      }
      CCIAssert(actualTimes == kInvocationTimes.intValue, @"执行结果与实际不一致");
    });

    Given(@"^deviceID为\"([^\"]*)\"的uSDKDevice对象的isModuleNeedOTA,值为\"([^\"]*)\"$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      NSString *kDeviceId = args[0];
      NSString *kParameter = args[1];
      BOOL bResult = [kParameter isEqualToString:@"1"];
      uSDKDevice *kuSDKDevice = [self.mUSDKDeviceManager getDeviceWithID:kDeviceId];
      FakeUSDKDevice *kFakeUSDKDevice = (FakeUSDKDevice *)kuSDKDevice;
      [kFakeUSDKDevice mockIsModuleNeedOTASuccessfulResult:bResult];
    });

    When(@"^调用判断是否OTA升级接口,参数deviceId为\"([^\"]*)\"$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      NSString *kDeviceId = args[0];
      if ([kDeviceId isEqualToString:@"空对象"]) {
          kDeviceId = nil;
      }
      self.mIsModuleNeedOta = [self.mWifiDeviceToolkitImpl isModuleNeedOta:kDeviceId];
    });

    Then(@"^判断是否OTA升级接口的回调结果为\"([^\"]*)\",回调结果中的extraData值为\"([^\"]*)\"$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      NSString *kResult = args[0];
      BOOL bResult = [kResult isEqualToString:@"Success"];
      CCIAssert(bResult == self.mIsModuleNeedOta, @"执行结果与实际不一致");
    });

    Given(@"^uSDK管理器的startSDKWithOptions接口的结果为\"([^\"]*)\"$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      NSString *kResult = args[0];
      NSError *kErrInfo = [[NSError alloc] initWithDomain:@"failure" code:ErrorCode_FAILURE userInfo:nil];
      OCMStub([self.mUSDKManager startSDKWithOptions:[OCMArg any] success:[OCMArg any] failure:[OCMArg any]]).andDo(^(NSInvocation *invocation) { /* block that handles the method invocation */
                                                                                                                                                  [invocation retainArguments];
                                                                                                                                                  void *successBlockPointer;
                                                                                                                                                  if ([kResult isEqualToString:@"Success"]) {
                                                                                                                                                      [invocation getArgument:&successBlockPointer atIndex:3];
                                                                                                                                                      USDKSuccessResult successBlock = (__bridge USDKSuccessResult)successBlockPointer;
                                                                                                                                                      successBlock();
                                                                                                                                                  }
                                                                                                                                                  else {
                                                                                                                                                      [invocation getArgument:&successBlockPointer atIndex:4];
                                                                                                                                                      USDKFailureResult successBlock = (__bridge USDKFailureResult)successBlockPointer;
                                                                                                                                                      successBlock(kErrInfo);
                                                                                                                                                  }
      });
    });

    Given(@"^uSDK Toolkit与uSDK建立连接的结果是\"([^\"]*)\"$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      NSString *kResult = args[0];
      BOOL bResult = [kResult isEqualToString:@"YES"];
      [self.mWifiDeviceToolkitImpl setValue:[NSNumber numberWithBool:bResult] forKeyPath:@"_toolkitAttached"];
    });

    When(@"^调用连接设备工具接口$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      FakeDetectListener *detectListener = [[FakeDetectListener alloc] init];
      self.detectListener = detectListener;
      [self.mWifiDeviceToolkitImpl attachToolkit:nil
                                  detectListener:detectListener
                                     finishBlock:^(UpDeviceResult *result) {
                                       self.mCallbackResult = result;
                                     }];
    });

    Then(@"^连接设备工具接口的回调结果为\"([^\"]*)\"$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      NSString *kResult = args[0];
      [self assertResult:kResult];
    });

    Then(@"^uSDK管理器的startSDKWithOptions接口被调用\"([^\"]*)\"次,参数为:$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      NSString *kInvocationTimes = args[0];
      NSArray<NSArray<NSString *> *> *infoList = getTableDataListFromExpectUserInfo(userInfo);
      OCMVerify(times(kInvocationTimes.intValue), [self.mUSDKManager startSDKWithOptions:[OCMArg any] success:[OCMArg any] failure:[OCMArg any]]);
      NSString *kAppID = [self.mWifiDeviceToolkitImpl valueForKeyPath:@"appID"];
      NSString *kSecretKey = [self.mWifiDeviceToolkitImpl valueForKeyPath:@"secretKey"];
      NSString *kAppKey = [self.mWifiDeviceToolkitImpl valueForKeyPath:@"appKey"];
      if (infoList.count > 0) {
          NSArray<NSString *> *deviceInfoItemList = infoList[0];
          if (LECommon_isValidString(kAppID) && LECommon_isValidString(kAppKey) && LECommon_isValidString(kSecretKey) && deviceInfoItemList.count > 1) {
              CCIAssert([kAppID isEqualToString:deviceInfoItemList[0]] && [kAppKey isEqualToString:deviceInfoItemList[1]] && [kSecretKey isEqualToString:deviceInfoItemList[2]], @"执行结果与实际不一致");
          }
      }
      else {
          CCIAssert(nil == kAppID && nil == kAppKey && nil == kSecretKey, @"执行结果与实际不一致");
      }
    });

    Given(@"^uSDK管理器的stopSDK接口的结果为\"([^\"]*)\"$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      NSString *kResult = args[0];
      BOOL bResult = [kResult isEqualToString:@"Success"];
      uSDKErrorConst kUSDKErrorConst = RET_USDK_OK;
      if (!bResult) {
          kUSDKErrorConst = ERR_USDK_INVALID_PARAM;
      }
      OCMStub([self.mUSDKManager stopSDK]).andReturn(kUSDKErrorConst);
    });

    When(@"^调用断开设备工具接口$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      //assume device is attached
      //      [self.mWifiDeviceToolkitImpl setValue:[NSNumber numberWithBool:YES] forKeyPath:@"_toolkitAttached"];
      [self.mWifiDeviceToolkitImpl detachToolkit:^(UpDeviceResult *result) {
        self.mCallbackResult = result;
      }];
    });

    Then(@"^断开设备工具接口的回调结果为\"([^\"]*)\"$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      NSString *kResult = args[0];
      [self assertResult:kResult];
    });

    Then(@"^uSDK管理器的stopSDK接口被调用\"([^\"]*)\"次$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      NSString *kInvocationTimes = args[0];
      OCMVerify(times(kInvocationTimes.intValue), [self.mUSDKManager stopSDK]);
    });

    Given(@"^deviceID为\"([^\"]*)\"的uSDKDevice对象的connectNeedPropertiesWithSuccess接口的结果为\"([^\"]*)\"$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      NSString *kResult = args[1];
      NSString *kDeviceId = args[0];
      BOOL bResult = [kResult isEqualToString:@"Success"];
      uSDKDevice *kuSDKDevice = [self.mUSDKDeviceManager getDeviceWithID:kDeviceId];
      FakeUSDKDevice *kFakeUSDKDevice = (FakeUSDKDevice *)kuSDKDevice;
      [kFakeUSDKDevice mockConnectNeedPropertiesWithSuccessResult:bResult];
    });

    Given(@"^uSDK中deviceId为\"([^\"]*)\"的订阅设备结果为\"([^\"]*)\"$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      NSString *kResult = args[1];
      NSString *kDeviceId = args[0];
      BOOL bResult = [kResult isEqualToString:@"YES"];
      uSDKDevice *kuSDKDevice = [self.mUSDKDeviceManager getDeviceWithID:kDeviceId];
      FakeUSDKDevice *kFakeUSDKDevice = (FakeUSDKDevice *)kuSDKDevice;
      [kFakeUSDKDevice mockIsSubscribedSuccessfulResult:bResult];
    });

    When(@"^调用连接设备接口,参数deviceId为\"([^\"]*)\"$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      NSString *kDeviceId = args[0];
      if ([kDeviceId isEqualToString:@"空对象"]) {
          kDeviceId = nil;
      }
      FakeReportListener *listener = [[FakeReportListener alloc] init];
      self.reportListener = listener;
      [self.mWifiDeviceToolkitImpl attachDevice:kDeviceId
                                 reportListener:listener
                                    finishBlock:^(UpDeviceResult *result) {
                                      self.mCallbackResult = result;
                                    }];
    });

    Then(@"^连接设备接口的回调结果为\"([^\"]*)\"$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      NSString *kResult = args[0];
      [self assertResult:kResult];
    });

    Then(@"^deviceID为\"([^\"]*)\"的uSDKDevice对象的connectNeedPropertiesWithSuccess接口被调用\"([^\"]*)\"次$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      NSString *kDeviceId = args[0];
      NSString *kInvocationTimes = args[1];
      uSDKDevice *kuSDKDevice = [self.mUSDKDeviceManager getDeviceWithID:kDeviceId];
      CCIAssert(kuSDKDevice, [NSString stringWithFormat:@"deviceId为%@的设备不存在", kDeviceId]);
      FakeUSDKDevice *kFakeUSDKDevice = (FakeUSDKDevice *)kuSDKDevice;
      NSUInteger actualTimes = [kFakeUSDKDevice getInvocationTimesOfSelector:@selector(connectNeedPropertiesWithSuccess:failure:)];
      CCIAssert(actualTimes == kInvocationTimes.intValue, @"执行结果与实际不一致");
    });

    Given(@"^uSDK设备管理器的getDeviceList接口返回的设备列表如下:$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      NSArray<uSDKDevice *> *kList = convertUSDKDeviceListFromStepTableData(userInfo);
      if (0 == kList.count) {
          kList = nil;
      }
      OCMStub([self.mUSDKDeviceManager getDeviceList]).andReturn(kList);
    });

    When(@"^调用获取设备列表接口$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      [self.mWifiDeviceToolkitImpl getDeviceBaseInfoList:^(UpDeviceResult *result) {
        self.mCallbackResult = result;
      }];
    });

    Then(@"^获取设备列表接口的回调结果为\"([^\"]*)\"$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      NSString *kResult = args[0];
      [self assertResult:kResult];
    });

    Then(@"^uSDK设备管理器的getDeviceList接口被调用\"([^\"]*)\"次$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      NSString *kInvocationTimes = args[0];
      OCMVerify(times(kInvocationTimes.intValue), [self.mUSDKDeviceManager getDeviceList]);
    });

    Then(@"^获取设备列表接口的回调结果为\"([^\"]*)\",回调结果中的extraData值为:$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      NSString *kResult = args[0];
      BOOL bResult = [kResult isEqualToString:@"Success"];
      NSArray<DeviceInfo *> *kExpectList = getDeviceListFromExpectUserInfo(userInfo);
      CCIAssert(self.mCallbackResult.isSuccessful == bResult && isEqualDeviceList(kExpectList, self.mCallbackResult.extraData), [NSString stringWithFormat:@"回调结果期望不一致,期望为%d,实际为%d", bResult, self.mCallbackResult.isSuccessful]);
    });

    Given(@"^deviceID为\"([^\"]*)\"的uSDKDevice对象的parentDevice接口的结果为:$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      NSString *kDeviceId = args[0];
      NSArray *infoList = getTableDataListFromExpectUserInfo(userInfo);
      NSArray<NSString *> *deviceInfoItemList = infoList[0];
      uSDKDevice *kuSDKDevice = [self.mUSDKDeviceManager getDeviceWithID:kDeviceId];
      FakeUSDKDevice *kFakeUSDKDevice = (FakeUSDKDevice *)kuSDKDevice;
      if (deviceInfoItemList.count > 0 && deviceInfoItemList[0].length > 0) {
          NSString *kCommandParameter = deviceInfoItemList[0];
          NSArray *kSubList = [kCommandParameter componentsSeparatedByString:@","];
          if (kSubList.count > 0) {
              //test case 9014 parentDevice is device object not string,
              [kFakeUSDKDevice setValue:kFakeUSDKDevice forKeyPath:@"parentDevice"];
          }
      }
    });

    Given(@"^deviceID为\"([^\"]*)\"的父设备对象的getSubDeviceList接口的结果为:$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      NSString *kDeviceId = args[0];
      NSArray<uSDKDevice *> *kSubDeviceList = convertUSDKDeviceListFromStepTableData(userInfo);
      if (0 == kSubDeviceList.count) {
          kSubDeviceList = nil;
      }
      uSDKDevice *kuSDKDevice = [self.mUSDKDeviceManager getDeviceWithID:kDeviceId];
      FakeUSDKDevice *kFakeUSDKDevice = (FakeUSDKDevice *)kuSDKDevice;
      OCMStub([kFakeUSDKDevice subDeviceList]).andReturn(kSubDeviceList);
    });
    Given(@"^deviceID为\"([^\"]*)\"的uSDKDevice对象的subDeviceList接口的结果为:$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      NSString *kDeviceId = args[0];
      NSArray<uSDKDevice *> *kSubDeviceList = convertUSDKDeviceListFromStepTableData(userInfo);
      if (0 == kSubDeviceList.count) {
          kSubDeviceList = nil;
      }
      uSDKDevice *kuSDKDevice = [self.mUSDKDeviceManager getDeviceWithID:kDeviceId];
      FakeUSDKDevice *kFakeUSDKDevice = (FakeUSDKDevice *)kuSDKDevice;
      OCMStub([kFakeUSDKDevice subDeviceList]).andReturn(kSubDeviceList);
    });

    When(@"^调用获取当前设备的同级子设备列表接口,参数deviceId为\"([^\"]*)\"$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      NSString *kDeviceId = args[0];
      if ([kDeviceId isEqualToString:@"空对象"]) {
          kDeviceId = nil;
      }
      [self.mWifiDeviceToolkitImpl getSubDevListBySubDev:kDeviceId
                                             finishBlock:^(UpDeviceResult *result) {
                                               self.mCallbackResult = result;
                                             }];
    });

    Then(@"^获取子设备列表接口的回调结果为\"([^\"]*)\",回调结果的extraData值为:$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      NSArray<DeviceInfo *> *kExpectSubDeviceList = getDeviceListFromExpectUserInfo(userInfo);
      NSString *kResult = args[0];
      BOOL bResult = [kResult isEqualToString:@"Success"];
      if (bResult) {
          NSArray<DeviceInfo *> *kDeviceList = self.mCallbackResult.extraData;
          CCIAssert(self.mCallbackResult.isSuccessful == bResult && isEqualDeviceList(kExpectSubDeviceList, kDeviceList), [NSString stringWithFormat:@"回调结果期望不一致,期望为%d,实际为%d", bResult, self.mCallbackResult.isSuccessful]);
      }
      else {
          CCIAssert(self.mCallbackResult.isSuccessful == bResult, [NSString stringWithFormat:@"回调结果期望不一致,期望为%d,实际为%d", bResult, self.mCallbackResult.isSuccessful]);
      }
    });
    Then(@"^获取当前设备的子设备列表接口的回调结果为\"([^\"]*)\",回调结果的extraData值为:$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      NSArray<DeviceInfo *> *kExpectSubDeviceList = getDeviceListFromExpectUserInfo(userInfo);
      NSString *kResult = args[0];
      BOOL bResult = [kResult isEqualToString:@"Success"];
      if (bResult) {
          NSArray<DeviceInfo *> *kDeviceList = self.mCallbackResult.extraData;
          CCIAssert(self.mCallbackResult.isSuccessful == bResult && isEqualDeviceList(kExpectSubDeviceList, kDeviceList), [NSString stringWithFormat:@"回调结果期望不一致,期望为%d,实际为%d", bResult, self.mCallbackResult.isSuccessful]);
      }
      else {
          CCIAssert(self.mCallbackResult.isSuccessful == bResult, [NSString stringWithFormat:@"回调结果期望不一致,期望为%d,实际为%d", bResult, self.mCallbackResult.isSuccessful]);
      }
    });

    Then(@"^deviceID为\"([^\"]*)\"的uSDKDevice对象的parentDevice接口被调用\"([^\"]*)\"次$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      NSString *kDeviceId = args[0];
      NSString *kInvocationTimes = args[1];
      uSDKSubDevice *kuSDKDevice = (uSDKSubDevice *)[self.mUSDKDeviceManager getDeviceWithID:kDeviceId];
      CCIAssert(kuSDKDevice, [NSString stringWithFormat:@"deviceId为%@的设备不存在", kDeviceId]);
      OCMVerify(times(kInvocationTimes.intValue), [kuSDKDevice parentDevice]);
    });

    Then(@"^deviceID为\"([^\"]*)\"的父设备对象的getSubDeviceList接口被调用\"([^\"]*)\"次$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      NSString *kDeviceId = args[0];
      NSString *kInvocationTimes = args[1];
      uSDKDevice *kuSDKDevice = [self.mUSDKDeviceManager getDeviceWithID:kDeviceId];
      CCIAssert(kuSDKDevice, [NSString stringWithFormat:@"deviceId为%@的设备不存在", kDeviceId]);
      OCMVerify(times(kInvocationTimes.intValue), [kuSDKDevice subDeviceList]);
    });

    Then(@"^获取子设备列表接口的回调结果为\"([^\"]*)\"$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      NSString *kResult = args[0];
      [self assertResult:kResult];
    });

    Given(@"^deviceID为\"([^\"]*)\"的uSDKDevice对象的outFocus接口,返回值为\"([^\"]*)\"$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      NSString *kResult = args[1];
      NSString *kDeviceId = args[0];
      BOOL bResult = [kResult isEqualToString:@"true"];
      uSDKDevice *kuSDKDevice = [self.mUSDKDeviceManager getDeviceWithID:kDeviceId];
      FakeUSDKDevice *kFakeUSDKDevice = (FakeUSDKDevice *)kuSDKDevice;
      [kFakeUSDKDevice mockOutFocusSuccessfulResult:bResult];
    });

    When(@"^调用OutFocus接口,参数deviceId为\"([^\"]*)\"$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      NSString *kDeviceId = args[0];
      if ([kDeviceId isEqualToString:@"空对象"]) {
          kDeviceId = nil;
      }
      BOOL kSuccess = [self.mWifiDeviceToolkitImpl outFocus:kDeviceId];
      UpDeviceResult *kUpDeviceResult = [UpDeviceResult UpDeviceResult:ErrorCode_SUCCESS extraData:nil];
      self.mCallbackResult = kUpDeviceResult;
      self.mCallbackResult.extraData = @(kSuccess);
    });

    Then(@"^获取OutFocus接口的回调结果中的outFocus值为\"([^\"]*)\"$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      NSString *kValue = args[0];
      BOOL bValue = [kValue isEqualToString:@"true"];
      NSNumber *actualValue = self.mCallbackResult.extraData;
      CCIAssert(bValue == actualValue.boolValue, [NSString stringWithFormat:@"执行结果与实际不一致,实际为%d,期望为%d", actualValue.intValue, kValue.intValue]);
    });

    Then(@"^deviceID为\"([^\"]*)\"的uSDKDevice对象的outFocus接口被调用\"([^\"]*)\"次$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      NSString *kInvocationTimes = args[1];
      NSString *kDeviceId = args[0];
      uSDKDevice *kuSDKDevice = [self.mUSDKDeviceManager getDeviceWithID:kDeviceId];
      CCIAssert(kuSDKDevice, [NSString stringWithFormat:@"deviceId为%@的设备不存在", kDeviceId]);
      FakeUSDKDevice *kFakeUSDKDevice = (FakeUSDKDevice *)kuSDKDevice;
      NSUInteger actualTimes = [kFakeUSDKDevice getInvocationTimesOfSelector:@selector(outFocus)];
      CCIAssert(actualTimes == kInvocationTimes.intValue, @"执行结果与实际不一致");
    });

    Given(@"^deviceID为\"([^\"]*)\"的uSDKDevice对象的inFocus接口,返回值为\"([^\"]*)\"$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      NSString *kResult = args[1];
      NSString *kDeviceId = args[0];
      BOOL bResult = [kResult isEqualToString:@"true"];
      uSDKDevice *kuSDKDevice = [self.mUSDKDeviceManager getDeviceWithID:kDeviceId];
      FakeUSDKDevice *kFakeUSDKDevice = (FakeUSDKDevice *)kuSDKDevice;
      [kFakeUSDKDevice mockInFocusSuccessfulResult:bResult];
    });

    When(@"^调用InFocus接口,参数deviceId为\"([^\"]*)\"$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      NSString *kDeviceId = args[0];
      if ([kDeviceId isEqualToString:@"空对象"]) {
          kDeviceId = nil;
      }
      BOOL kSuccess = [self.mWifiDeviceToolkitImpl inFocus:kDeviceId];
      UpDeviceResult *kUpDeviceResult = [UpDeviceResult UpDeviceResult:ErrorCode_SUCCESS extraData:nil];
      self.mCallbackResult = kUpDeviceResult;
      self.mCallbackResult.extraData = @(kSuccess);
    });

    Then(@"^获取InFocus接口的回调结果中的inFocus值为\"([^\"]*)\"$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      NSString *kValue = args[0];
      BOOL bValue = [kValue isEqualToString:@"true"];
      NSNumber *actualValue = self.mCallbackResult.extraData;
      CCIAssert(bValue == actualValue.boolValue, [NSString stringWithFormat:@"执行结果与实际不一致,实际为%d,期望为%d", actualValue.intValue, kValue.intValue]);
    });

    Then(@"^deviceID为\"([^\"]*)\"的uSDKDevice对象的inFocus接口被调用\"([^\"]*)\"次$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      NSString *kInvocationTimes = args[1];
      NSString *kDeviceId = args[0];
      uSDKDevice *kuSDKDevice = [self.mUSDKDeviceManager getDeviceWithID:kDeviceId];
      CCIAssert(kuSDKDevice, [NSString stringWithFormat:@"deviceId为%@的设备不存在", kDeviceId]);
      FakeUSDKDevice *kFakeUSDKDevice = (FakeUSDKDevice *)kuSDKDevice;
      NSUInteger actualTimes = [kFakeUSDKDevice getInvocationTimesOfSelector:@selector(inFocus)];
      CCIAssert(actualTimes == kInvocationTimes.intValue, @"执行结果与实际不一致");
    });

    Given(@"^deviceID为\"([^\"]*)\"的uSDKDevice对象的attributeDict接口,返回结果为:$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      NSDictionary<NSString *, uSDKDeviceAttribute *> *kuSDKDeviceAttribute = convertUSDKDeviceAttributeFromStepTableData(userInfo);
      NSString *kDeviceId = args[0];
      uSDKDevice *kuSDKDevice = [self.mUSDKDeviceManager getDeviceWithID:kDeviceId];
      FakeUSDKDevice *kFakeUSDKDevice = (FakeUSDKDevice *)kuSDKDevice;
      [kFakeUSDKDevice mockDeviceAttributeResult:kuSDKDeviceAttribute];
    });

    When(@"^调用获取属性列表接口,参数deviceId为\"([^\"]*)\"$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      NSString *kDeviceId = args[0];
      if ([kDeviceId isEqualToString:@"空对象"]) {
          kDeviceId = nil;
      }
      [self.mWifiDeviceToolkitImpl getDeviceAttributeList:kDeviceId
                                              finishBlock:^(UpDeviceResult *result) {
                                                self.mCallbackResult = result;
                                              }];
    });

    Then(@"^获取属性列表接口的回调结果为\"([^\"]*)\",回调结果中的属性列表值为:$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      NSArray *kExpectuSDKDeviceAttribute = convertDeviceAttributeFromStepTableData(userInfo);
      NSString *kResult = args[0];
      BOOL bResult = [kResult isEqualToString:@"Success"];
      NSArray<id<UpDeviceAttribute>> *kDeviceAttributeList = self.mCallbackResult.extraData;
      if (bResult && kDeviceAttributeList.count > 0) {
          CCIAssert(self.mCallbackResult.isSuccessful == bResult && [[StepsUtils sharedInstance] isEqualUSDKAttribute:self.mCallbackResult.extraData expectAttribute:kExpectuSDKDeviceAttribute], [NSString stringWithFormat:@"回调结果期望不一致,期望为%d,实际为%d", bResult, self.mCallbackResult.isSuccessful]);
      }
      else {
          [self assertResult:kResult];
      }
    });

    Then(@"^deviceID为\"([^\"]*)\"的uSDKDevice对象的attributeDict接口被调用\"([^\"]*)\"次$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      NSString *kInvocationTimes = args[1];
      NSString *kDeviceId = args[0];
      uSDKDevice *kuSDKDevice = [self.mUSDKDeviceManager getDeviceWithID:kDeviceId];
      CCIAssert(kuSDKDevice, [NSString stringWithFormat:@"deviceId为%@的设备不存在", kDeviceId]);
      FakeUSDKDevice *kFakeUSDKDevice = (FakeUSDKDevice *)kuSDKDevice;
      NSUInteger actualTimes = [kFakeUSDKDevice getInvocationTimesOfSelector:@selector(attributeDict)];
      CCIAssert(actualTimes == kInvocationTimes.intValue, @"执行结果与实际不一致");
    });

    Given(@"^deviceID为\"([^\"]*)\"的uSDKDevice对象的alarmList接口,返回结果为:$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      NSArray<uSDKDeviceAlarm *> *kuSDKDeviceAlarm = convertUSDKDeviceAlarmFromStepTableData(userInfo);
      NSString *kDeviceId = args[0];
      uSDKDevice *kuSDKDevice = [self.mUSDKDeviceManager getDeviceWithID:kDeviceId];
      FakeUSDKDevice *kFakeUSDKDevice = (FakeUSDKDevice *)kuSDKDevice;
      [kFakeUSDKDevice mockDeviceAlarmResult:kuSDKDeviceAlarm];
    });

    When(@"^调用获取设备告警接口,参数deviceId为\"([^\"]*)\"$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      NSString *kDeviceId = args[0];
      if ([kDeviceId isEqualToString:@"空对象"]) {
          kDeviceId = nil;
      }
      [self.mWifiDeviceToolkitImpl getDeviceCautionList:kDeviceId
                                            finishBlock:^(UpDeviceResult *result) {
                                              self.mCallbackResult = result;
                                            }];
    });

    Then(@"^获取设备告警接口的回调结果为\"([^\"]*)\",回调结果中的属性列表值为:$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      NSArray<uSDKDeviceAlarm *> *kExpectuSDKDeviceAlarm = convertUSDKDeviceAlarmFromStepTableData(userInfo);
      NSString *kResult = args[0];
      BOOL bResult = [kResult isEqualToString:@"Success"];
      NSArray<id<UpDeviceAttribute>> *kDeviceAttributeList = self.mCallbackResult.extraData;
      if (bResult && kDeviceAttributeList.count > 0) {
          CCIAssert(self.mCallbackResult.isSuccessful == bResult && [[StepsUtils sharedInstance] isEqualUSDKAttribute:kDeviceAttributeList expectAttribute:kExpectuSDKDeviceAlarm], [NSString stringWithFormat:@"回调结果期望不一致,期望为%d,实际为%d", bResult, self.mCallbackResult.isSuccessful]);
      }
      else {
          [self assertResult:kResult];
      }
    });

    Then(@"^deviceID为\"([^\"]*)\"的uSDKDevice对象的alarmList接口被调用\"([^\"]*)\"次$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      NSString *kInvocationTimes = args[1];
      NSString *kDeviceId = args[0];
      uSDKDevice *kuSDKDevice = [self.mUSDKDeviceManager getDeviceWithID:kDeviceId];
      CCIAssert(kuSDKDevice, [NSString stringWithFormat:@"deviceId为%@的设备不存在", kDeviceId]);
      FakeUSDKDevice *kFakeUSDKDevice = (FakeUSDKDevice *)kuSDKDevice;
      NSUInteger actualTimes = [kFakeUSDKDevice getInvocationTimesOfSelector:@selector(alarmList)];
      CCIAssert(actualTimes == kInvocationTimes.intValue, @"执行结果与实际不一致");
    });

    Given(@"^deviceID为\"([^\"]*)\"的uSDKDevice对象的state,值为\"([^\"]*)\"$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      NSString *kDeviceId = args[0];
      NSString *kConnectionStatus = args[1];
      uSDKDevice *kuSDKDevice = [self.mUSDKDeviceManager getDeviceWithID:kDeviceId];
      FakeUSDKDevice *kFakeUSDKDevice = (FakeUSDKDevice *)kuSDKDevice;
      [kFakeUSDKDevice mockConnectionStateResult:kConnectionStatus.intValue];
    });

    When(@"^调用获取设备连接状态接口,参数deviceId为\"([^\"]*)\"$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      NSString *kDeviceId = args[0];
      if ([kDeviceId isEqualToString:@"空对象"]) {
          kDeviceId = nil;
      }
      [self.mWifiDeviceToolkitImpl getDeviceConnection:kDeviceId
                                           finishBlock:^(UpDeviceResult *result) {
                                             self.mCallbackResult = result;
                                           }];
    });

    Then(@"^获取设备连接状态接口的回调结果为\"([^\"]*)\",回调结果中的connection值为\"([^\"]*)\"$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      NSString *kResult = args[0];
      NSString *kExpectState = args[1];
      BOOL bResult = [kResult isEqualToString:@"Success"];
      if (bResult) {
          NSNumber *kConnectionState = self.mCallbackResult.extraData;
          CCIAssert(self.mCallbackResult.isSuccessful == bResult && kExpectState.intValue == kConnectionState.intValue, [NSString stringWithFormat:@"回调结果期望不一致,期望为%d,实际为%d", bResult, self.mCallbackResult.isSuccessful]);
      }
      else {
          [self assertResult:kResult];
      }
    });

#pragma mark - 9016
    Given(@"^deviceID为\"([^\"]*)\"的uSDKDevice对象的moduleOTAWithProgress接口的结果为\"([^\"]*)\"$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      NSString *deviceId = args[0];
      NSString *result = args[1];
      uSDKDevice *kuSDKDevice = [self.mUSDKDeviceManager getDeviceWithID:deviceId];
      FakeUSDKDevice *kFakeUSDKDevice = (FakeUSDKDevice *)kuSDKDevice;
      BOOL bResult = [result isEqualToString:@"Success"];
      [kFakeUSDKDevice mockModuleOTAWithProgressResult:bResult];
    });

    When(@"^调用执行OTA升级接口,参数deviceId为\"([^\"]*)\"$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      NSString *deviceId = args[0];
      if ([deviceId isEqualToString:@"空对象"]) {
          deviceId = nil;
      }
      if ([deviceId isEqualToString:@"空字符串"]) {
          deviceId = @"";
      }

      [self.mWifiDeviceToolkitImpl startModuleUpdateSuccess:deviceId
                                                finishBlock:^(UpDeviceResult *result) {
                                                  self.mCallbackResult = result;
                                                }];
    });

    Then(@"^执行OTA升级接口的回调结果为\"([^\"]*)\"$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      NSString *kResult = args[0];
      [self assertResult:kResult];
    });

    Then(@"^deviceID为\"([^\"]*)\"的uSDKDevice对象的moduleOTAWithProgress接口被调用\"([^\"]*)\"次$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      NSString *deviceId = args[0];
      NSString *kInvocationTimes = args[1];
      uSDKDevice *kuSDKDevice = [self.mUSDKDeviceManager getDeviceWithID:deviceId];
      FakeUSDKDevice *kFakeUSDKDevice = (FakeUSDKDevice *)kuSDKDevice;
      NSUInteger actualTimes = [kFakeUSDKDevice getInvocationTimesOfSelector:@selector(moduleOTAWithProgress:success:failure:)];
      CCIAssert(actualTimes == kInvocationTimes.intValue, @"执行结果与实际不一致,实际为%d,期望为%d", actualTimes, kInvocationTimes.intValue);
    });
#pragma mark - 9017
    Given(@"^deviceID为\"([^\"]*)\"的uSDKDevice对象的getNetworkQualityV2Success接口的结果为\"([^\"]*)\",回调结果中的uSDKNetworkQualityInfoV2对象的值为:$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      NSString *deviceId = args[0];
      uSDKDevice *kuSDKDevice = [self.mUSDKDeviceManager getDeviceWithID:deviceId];
      FakeUSDKDevice *kFakeUSDKDevice = (FakeUSDKDevice *)kuSDKDevice;

      NSString *result = args[1];
      BOOL bResult = [result isEqualToString:@"Success"];
      NSArray *list = getTableDataListFromExpectUserInfo(userInfo);
      uSDKNetworkQualityInfoV2 *networkQuality = nil;
      if (list.count > 0) {
          networkQuality = [self parseUSDKNetworkQualityInfo:list[0]];
      }
      [OCMStub([kFakeUSDKDevice getNetworkQualityV2Success:[OCMArg any] failure:[OCMArg any]]) andDo:^(NSInvocation *invocation) {
        if (bResult) {
            void (^succeedResponse)(uSDKNetworkQualityInfoV2 *q);
            [invocation getArgument:&succeedResponse atIndex:2];
            succeedResponse(networkQuality);
        }
        else {
            void (^failResponse)(NSError *error);
            [invocation getArgument:&failResponse atIndex:3];
            NSError *error = [NSError errorWithDomain:@"" code:-1 userInfo:nil];
            failResponse(error);
        }
      }];
    });

    When(@"^调用获取网络质量接口,参数deviceId为\"([^\"]*)\"$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      NSString *deviceId = args[0];
      if ([deviceId isEqualToString:@"空对象"]) {
          deviceId = nil;
      }
      if ([deviceId isEqualToString:@"空字符串"]) {
          deviceId = @"";
      }

      [self.mWifiDeviceToolkitImpl getNetworkQuality:deviceId
                                         finishBlock:^(UpDeviceResult *result) {
                                           self.mCallbackResult = result;
                                         }];
    });

    Then(@"^获取网络质量接口的回调结果为\"([^\"]*)\",回调结果中的DeviceNetWorkQualityInfo对象的值为:$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      NSString *result = args[0];
      BOOL bResult = [result isEqualToString:@"Success"];
      CCIAssert(bResult == self.mCallbackResult.isSuccessful, @"执行结果与实际不一致,实际为%d,期望为%d", self.mCallbackResult.isSuccessful, bResult);
      if (bResult) {
          NSArray *list = getTableDataListFromExpectUserInfo(userInfo);
          DeviceNetWorkQualityInfo *expectInfo = [self parseDeviceNetWorkQualityInfo:list[0]];
          DeviceNetWorkQualityInfo *actualInfo = (DeviceNetWorkQualityInfo *)self.mCallbackResult.extraData;
          CCIAssert([self isEqualQualityInfo:expectInfo actualInfo:actualInfo], @"执行结果与实际不一致,实际为%@,期望为%@", actualInfo, expectInfo);
      }
    });

    Then(@"^deviceID为\"([^\"]*)\"的uSDKDevice对象的getNetworkQualityV2Success接口被调用\"([^\"]*)\"次$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      NSString *deviceId = args[0];
      uSDKDevice *kuSDKDevice = [self.mUSDKDeviceManager getDeviceWithID:deviceId];
      FakeUSDKDevice *kFakeUSDKDevice = (FakeUSDKDevice *)kuSDKDevice;
      NSString *invocationTimes = args[1];
      NSInteger expectedInvocationTimes = invocationTimes.integerValue;
      OCMVerify(times(expectedInvocationTimes), [kFakeUSDKDevice getNetworkQualityV2Success:[OCMArg any] failure:[OCMArg any]]);
    });
#pragma mark - 9018
    Given(@"^deviceID为\"([^\"]*)\"的uSDKDevice对象的fetchBoardFOTAStatusWithSuccess接口的结果为\"([^\"]*)\",回调结果中的uSDKFOTAStatusInfo对象的值为:$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      NSString *deviceId = args[0];
      uSDKDevice *kuSDKDevice = [self.mUSDKDeviceManager getDeviceWithID:deviceId];
      FakeUSDKDevice *kFakeUSDKDevice = (FakeUSDKDevice *)kuSDKDevice;

      NSString *result = args[1];
      BOOL bResult = [result isEqualToString:@"Success"];

      NSArray *list = getTableDataListFromExpectUserInfo(userInfo);
      NSArray *parameterArr = list[0];
      uSDKFOTAStatusInfo *expectedInfo = [uSDKFOTAStatusInfo new];
      NSString *upgradeStatus = parameterArr[0];
      expectedInfo.upgradeStatus = upgradeStatus.intValue;
      expectedInfo.upgradeErrorInfo = parameterArr[1];

      [kFakeUSDKDevice mockFetchBoardFOTAStatusResult:bResult];
      [kFakeUSDKDevice mockFetchBoardFOTAStatusInfo:expectedInfo];
    });

    When(@"^调用获取Fota状态接口,参数deviceId为\"([^\"]*)\"$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      NSString *deviceId = args[0];
      if ([deviceId isEqualToString:@"空对象"]) {
          deviceId = nil;
      }
      if ([deviceId isEqualToString:@"空字符串"]) {
          deviceId = @"";
      }

      [self.mWifiDeviceToolkitImpl fetchBoardFOTAStatusSuccess:deviceId
                                                   finishBlock:^(UpDeviceResult *result) {
                                                     self.mCallbackResult = result;
                                                   }];
    });

    Then(@"^获取Fota状态接口的回调结果为\"([^\"]*)\",回调结果中的DeviceFOTAStatusInfo对象的值为:$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      NSString *result = args[0];
      BOOL bResult = [result isEqualToString:@"Success"];
      CCIAssert(bResult == self.mCallbackResult.isSuccessful, @"执行结果与实际不一致,实际为%d,期望为%d", self.mCallbackResult.isSuccessful, bResult);
      if (bResult) {
          NSArray *list = getTableDataListFromExpectUserInfo(userInfo);
          NSArray *parameterArr = list[0];
          NSString *upgradeStatus = parameterArr[0];
          DeviceFOTAStatusInfo *expectedInfo = [DeviceFOTAStatusInfo DeviceFOTAStatusInfo:upgradeStatus.intValue upgradeErrInfo:parameterArr[1]];
          DeviceFOTAStatusInfo *actualInfo = (DeviceFOTAStatusInfo *)self.mCallbackResult.extraData;
          CCIAssert(expectedInfo.upgradeStatus == actualInfo.upgradeStatus, @"执行结果与实际不一致,实际为%@,期望为%@", actualInfo, expectedInfo);
      }
    });

    Then(@"^deviceID为\"([^\"]*)\"的uSDKDevice对象的fetchBoardFOTAStatusWithSuccess接口被调用\"([^\"]*)\"次$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      NSString *deviceId = args[0];
      uSDKDevice *kuSDKDevice = [self.mUSDKDeviceManager getDeviceWithID:deviceId];
      FakeUSDKDevice *kFakeUSDKDevice = (FakeUSDKDevice *)kuSDKDevice;
      NSString *invocationTimes = args[1];
      NSUInteger actualTimes = [kFakeUSDKDevice getInvocationTimesOfSelector:@selector(fetchBoardFOTAStatusWithSuccess:failure:)];
      CCIAssert(actualTimes == invocationTimes.intValue, @"执行结果与实际不一致,实际为%d,期望为%d", actualTimes, invocationTimes.intValue);
    });
#pragma mark - 9019
    Given(@"^deviceID为\"([^\"]*)\"的uSDKDevice对象的checkBoardFOTAInfoWithSuccess接口的结果为\"([^\"]*)\",回调结果中的uSDKFOTAInfo对象的值为:$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      NSString *deviceId = args[0];
      uSDKDevice *kuSDKDevice = [self.mUSDKDeviceManager getDeviceWithID:deviceId];
      FakeUSDKDevice *kFakeUSDKDevice = (FakeUSDKDevice *)kuSDKDevice;

      NSString *result = args[1];
      BOOL bResult = [result isEqualToString:@"Success"];

      NSArray *list = getTableDataListFromExpectUserInfo(userInfo);
      NSArray *parameterArr = list[0];
      uSDKFOTAInfo *info = [uSDKFOTAInfo new];
      NSString *isNeedFOTA = parameterArr[0];
      [info setValue:@(isNeedFOTA.boolValue) forKey:@"isNeedFOTA"];
      [info setValue:parameterArr[1] forKey:@"currentVersion"];
      [info setValue:parameterArr[2] forKey:@"newestVersion"];
      [info setValue:parameterArr[3] forKey:@"newestVersionDescription"];
      [info setValue:parameterArr[4] forKey:@"model"];

      [kFakeUSDKDevice mockCheckBoardFOTAResult:bResult];
      [kFakeUSDKDevice mockCheckBoardFOTAInfo:info];
    });

    When(@"^调用获取Fota信息接口,参数deviceId为\"([^\"]*)\"$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      NSString *deviceId = args[0];
      if ([deviceId isEqualToString:@"空对象"]) {
          deviceId = nil;
      }
      if ([deviceId isEqualToString:@"空字符串"]) {
          deviceId = @"";
      }

      [self.mWifiDeviceToolkitImpl checkBoardFOTAInfoSuccess:deviceId
                                                 finishBlock:^(UpDeviceResult *result) {
                                                   self.mCallbackResult = result;
                                                 }];
    });

    Then(@"^获取Fota信息接口的回调结果为\"([^\"]*)\",回调结果中的DeviceFOTAInfo对象的值为:$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      NSString *result = args[0];
      BOOL bResult = [result isEqualToString:@"Success"];
      CCIAssert(bResult == self.mCallbackResult.isSuccessful, @"执行结果与实际不一致,实际为%d,期望为%d", self.mCallbackResult.isSuccessful, bResult);

      if (bResult) {
          NSArray *list = getTableDataListFromExpectUserInfo(userInfo);
          NSArray *parameterArr = list[0];
          DeviceFOTAInfo *expectedInfo = [DeviceFOTAInfo new];
          NSString *isNeedFOTA = parameterArr[0];
          [expectedInfo setValue:@(isNeedFOTA.boolValue) forKey:@"isNeedFOTA"];
          [expectedInfo setValue:parameterArr[1] forKey:@"currentVersion"];
          [expectedInfo setValue:parameterArr[2] forKey:@"newestVersion"];
          [expectedInfo setValue:parameterArr[3] forKey:@"newestVersionDescription"];
          [expectedInfo setValue:parameterArr[4] forKey:@"model"];
          DeviceFOTAInfo *actualInfo = (DeviceFOTAInfo *)self.mCallbackResult.extraData;
          CCIAssert([self isEqualFOTAInfo:expectedInfo actualInfo:actualInfo], @"执行结果与实际不一致,实际为%@,期望为%@", actualInfo, expectedInfo);
      }
    });

    Then(@"^deviceID为\"([^\"]*)\"的uSDKDevice对象的checkBoardFOTAInfoWithSuccess接口被调用\"([^\"]*)\"次$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      NSString *deviceId = args[0];
      uSDKDevice *kuSDKDevice = [self.mUSDKDeviceManager getDeviceWithID:deviceId];
      FakeUSDKDevice *kFakeUSDKDevice = (FakeUSDKDevice *)kuSDKDevice;
      NSString *invocationTimes = args[1];
      NSUInteger actualTimes = [kFakeUSDKDevice getInvocationTimesOfSelector:@selector(checkBoardFOTAInfoWithSuccess:failure:)];
      CCIAssert(actualTimes == invocationTimes.intValue, @"执行结果与实际不一致,实际为%d,期望为%d", actualTimes, invocationTimes.intValue);
    });

#pragma mark - 9020
    Given(@"^deviceID为\"([^\"]*)\"的uSDKDevice对象信息为:$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      NSString *deviceId = args[0];
      uSDKDevice *kuSDKDevice = [self.mUSDKDeviceManager getDeviceWithID:deviceId];
      FakeUSDKDevice *kFakeUSDKDevice = (FakeUSDKDevice *)kuSDKDevice;
      NSArray *list = getTableDataListFromExpectUserInfo(userInfo);
      NSArray *parameterArr = list[0];
      NSString *type = parameterArr[1];
      [kFakeUSDKDevice mockuSDKDeviceID:parameterArr[0]];
      [kFakeUSDKDevice mockuSDKDeviceUplusID:parameterArr[2]];
      [kFakeUSDKDevice mockuSDKDeviceType:[type intValue]];

    });

    When(@"^调用获取设备信息接口,参数deviceId为\"([^\"]*)\"$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      NSString *deviceId = args[0];
      if ([deviceId isEqualToString:@"空对象"]) {
          deviceId = nil;
      }
      if ([deviceId isEqualToString:@"空字符串"]) {
          deviceId = @"";
      }

      [self.mWifiDeviceToolkitImpl getDeviceBaseInfo:deviceId
                                         finishBlock:^(UpDeviceResult *result) {
                                           self.mCallbackResult = result;
                                         }];
    });

    Then(@"^获取设备信息接口的回调结果为\"([^\"]*)\",回调结果中的DeviceBaseInfo对象为:$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      NSString *result = args[0];
      BOOL bResult = [result isEqualToString:@"Success"];
      CCIAssert(bResult == self.mCallbackResult.isSuccessful, @"执行结果与实际不一致,实际为%d,期望为%d", self.mCallbackResult.isSuccessful, bResult);
      if (bResult) {
          NSArray *list = getTableDataListFromExpectUserInfo(userInfo);
          NSArray *parameterArr = list[0];
          DeviceBaseInfo *expectedInfo = [DeviceBaseInfo new];
          [expectedInfo setDeviceId:parameterArr[0]];
          [expectedInfo setTypeName:parameterArr[1]];
          [expectedInfo setTypeId:parameterArr[2]];

          DeviceBaseInfo *actualInfo = (DeviceBaseInfo *)self.mCallbackResult.extraData;
          CCIAssert([self isEqualBaseInfo:expectedInfo actualInfo:actualInfo], @"执行结果与实际不一致,实际为%@,期望为%@", actualInfo, expectedInfo);
      }
    });

#pragma mark - 9028-9029
    Given(@"^设备代理的接入网关参数accessToken为\"([^\"]*)\"$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      NSString *accessToken = args[0];
      if ([accessToken isEqualToString:@"null"]) {
          accessToken = nil;
      }
      NSMutableDictionary *params = [NSMutableDictionary dictionary];
      params[@"usdk-access-token"] = accessToken;
      id<UpDeviceBroker> broker = [[UpDeviceInjection getInstance].deviceManager getBroker];
      [broker setGatewayParams:params];
    });
    Given(@"deviceID为\"([^\"]*)\"的uSDKDevice对象的getDeviceBindInfo接口的结果为\"([^\"]*)\",返回结果\"([^\"]*)\"$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      NSString *deviceId = args[0];
      NSString *result = args[1];
      NSString *extraData = args[2];
      if ([extraData isEqualToString:@"null"]) {
          extraData = nil;
      }
      if ([deviceId isEqualToString:@"null"]) {
          deviceId = nil;
      }
      BOOL bResult = [result isEqualToString:@"Success"];
      uSDKDevice *kuSDKDevice = [self.mUSDKDeviceManager getDeviceWithID:deviceId];
      FakeUSDKDevice *kFakeUSDKDevice = (FakeUSDKDevice *)kuSDKDevice;
      [kFakeUSDKDevice mockDeviceBindInfoResult:bResult];
      [kFakeUSDKDevice mockDeviceBindInfo:extraData];
    });
    When(@"获取设备绑定信息,参数deviceId为\"([^\"]*)\"$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      NSString *deviceId = args[0];
      if ([deviceId isEqualToString:@"null"]) {
          deviceId = nil;
      }
      [self.mWifiDeviceToolkitImpl getDeviceBindInfo:deviceId
                                         finishBlock:^(UpDeviceResult *result) {
                                           self.mCallbackResult = result;
                                         }];

    });
    Then(@"获取设备绑定信息接口的回调结果为\"([^\"]*)\",回调结果中绑定信息为\"([^\"]*)\"$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      NSString *result = args[0];
      NSString *extraData = args[1];
      if ([extraData isEqualToString:@"null"]) {
          extraData = nil;
      }
      CCIAssert([result isEqualToString:@"Success"] == self.mCallbackResult.isSuccessful, @"执行结果与实际不一致");
      if (extraData && self.mCallbackResult.extraData) {
          CCIAssert([extraData isEqualToString:self.mCallbackResult.extraData], @"执行结果与实际不一致");
      }
      else {
          CCIAssert(extraData == self.mCallbackResult.extraData, @"执行结果与实际不一致");
      }
    });
    Then(@"deviceID为\"([^\"]*)\"的uSDKDevice对象的getDeviceBindInfo接口被调用\"([^\"]*)\"次$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      NSString *deviceId = args[0];
      NSString *kInvocationTimes = args[1];
      uSDKDevice *kuSDKDevice = [self.mUSDKDeviceManager getDeviceWithID:deviceId];
      FakeUSDKDevice *kFakeUSDKDevice = (FakeUSDKDevice *)kuSDKDevice;
      NSUInteger actualTimes = [kFakeUSDKDevice getInvocationTimesOfSelector:@selector(getDeviceBindInfoWithToken:timeoutInterval:traceNodeCS:success:failure:)];
      CCIAssert(actualTimes == kInvocationTimes.intValue, @"执行结果与实际不一致,实际为%d,期望为%d", actualTimes, kInvocationTimes.intValue);
    });
#pragma mark - 9030-9031
    When(@"^获取当前设备的子设备列表,参数deviceId为\"([^\"]*)\"$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      NSString *deviceId = args[0];
      if ([deviceId isEqualToString:@"null"]) {
          deviceId = nil;
      }
      [self.mWifiDeviceToolkitImpl getSubDevBaseInfoList:deviceId
                                             finishBlock:^(UpDeviceResult *result) {
                                               self.mCallbackResult = result;
                                             }];
    });

    Then(@"^deviceID为\"([^\"]*)\"的uSDKDevice对象的getSubDeviceList接口被调用\"([^\"]*)\"次$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      NSString *deviceId = args[0];
      NSString *kInvocationTimes = args[1];
      uSDKDevice *kuSDKDevice = [self.mUSDKDeviceManager getDeviceWithID:deviceId];
      OCMVerify(times(kInvocationTimes.intValue), [kuSDKDevice subDeviceList]);
    });
#pragma mark - 9032
    Given(@"^deviceID为\"([^\"]*)\"的uSDKDevice对象的getSmartLinkSoftwareVersion接口的结果为\"([^\"]*)\"$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      NSString *deviceId = args[0];
      NSString *result = args[1];
      uSDKDevice *kuSDKDevice = [self.mUSDKDeviceManager getDeviceWithID:deviceId];
      FakeUSDKDevice *kFakeUSDKDevice = (FakeUSDKDevice *)kuSDKDevice;
      if ([result isEqualToString:@"null"]) {
          result = nil;
      }
      [kFakeUSDKDevice mockGetSmartLinkSoftwareVersionResult:result];
    });
    When(@"^获取设备模块使用的配置文件版本号,参数deviceId为\"([^\"]*)\"$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      NSString *deviceId = args[0];
      if ([deviceId isEqualToString:@"null"]) {
          deviceId = nil;
      }
      NSString *softwareVersion = [self.mWifiDeviceToolkitImpl getSmartLinkSoftwareVersion:deviceId];
      self.softwareVersion = softwareVersion;
    });
    Then(@"^获取设备模块使用的配置文件版本号的回调结果为\"([^\"]*)\",回调结果中版本信息为\"([^\"]*)\"$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      NSString *result = args[0];
      NSString *version = args[1];
      BOOL isSuccess = NO;
      if (self.softwareVersion.length > 0) {
          isSuccess = YES;
      }
      if ([version isEqualToString:@"null"]) {
          version = nil;
      }
      CCIAssert([result isEqualToString:@"Success"] == isSuccess, @"执行结果与实际不一致");
      if (isSuccess) {
          CCIAssert([version isEqualToString:self.softwareVersion], @"执行结果与实际不一致");
      }
    });
    Then(@"deviceID为\"([^\"]*)\"的uSDKDevice对象的getSmartLinkSoftwareVersion接口被调用\"([^\"]*)\"次$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      NSString *deviceId = args[0];
      NSString *kInvocationTimes = args[1];
      uSDKDevice *kuSDKDevice = [self.mUSDKDeviceManager getDeviceWithID:deviceId];
      FakeUSDKDevice *kFakeUSDKDevice = (FakeUSDKDevice *)kuSDKDevice;
      NSUInteger actualTimes = [kFakeUSDKDevice getInvocationTimesOfSelector:@selector(smartLinkSoftwareVersion)];
      CCIAssert(actualTimes == kInvocationTimes.intValue, @"执行结果与实际不一致,实际为%d,期望为%d", actualTimes, kInvocationTimes.intValue);
    });

#pragma mark - 9033
    Given(@"^deviceID为\"([^\"]*)\"的uSDKDevice对象的GetNetType接口的结果为\"([^\"]*)\"$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      NSString *deviceId = args[0];
      NSString *result = args[1];
      uSDKDevice *kuSDKDevice = [self.mUSDKDeviceManager getDeviceWithID:deviceId];
      FakeUSDKDevice *kFakeUSDKDevice = (FakeUSDKDevice *)kuSDKDevice;
      if ([result isEqualToString:@"null"]) {
          result = nil;
      }
      [kFakeUSDKDevice mockGetNetTypeResult:result];
    });
    When(@"^获取设备连接网络类型,参数deviceId为\"([^\"]*)\"$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      NSString *deviceId = args[0];
      if ([deviceId isEqualToString:@"null"]) {
          deviceId = nil;
      }
      DeviceNetType deviceNetType = [self.mWifiDeviceToolkitImpl getNetType:deviceId];
      self.deviceNetType = deviceNetType;
    });
    Then(@"^获取设备连接网络类型的回调结果为\"([^\"]*)\",回调结果中网络类型为\"([^\"]*)\"$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      NSString *deviceNetTypeStr = args[1];
      DeviceNetType netType = DeviceNetTypeUnknown;
      if ([deviceNetTypeStr isEqualToString:@"fakeNetType"]) {
          netType = DeviceNetTypeLOCAL;
      }
      CCIAssert(self.deviceNetType == netType, @"执行结果与实际不一致");
    });
    Then(@"^deviceID为\"([^\"]*)\"的uSDKDevice对象的GetNetType接口被调用\"([^\"]*)\"次$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      NSString *deviceId = args[0];
      NSString *kInvocationTimes = args[1];
      uSDKDevice *kuSDKDevice = [self.mUSDKDeviceManager getDeviceWithID:deviceId];
      FakeUSDKDevice *kFakeUSDKDevice = (FakeUSDKDevice *)kuSDKDevice;
      NSUInteger actualTimes = [kFakeUSDKDevice getInvocationTimesOfSelector:@selector(netType)];
      CCIAssert(actualTimes == kInvocationTimes.intValue, @"执行结果与实际不一致,实际为%d,期望为%d", actualTimes, kInvocationTimes.intValue);
    });

    Given(@"^uSDK Toolkit的appID值为\"([^\"]*)\",appKey值为\"([^\"]*)\",secretKey值为\"([^\"]*)\"$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      NSString *appIDStr = args[0];
      NSString *appKeyStr = args[1];
      NSString *secretKeyStr = args[2];
      if ([appIDStr isEqualToString:@"空对象"]) {
          appIDStr = nil;
      }
      if ([appKeyStr isEqualToString:@"空对象"]) {
          appKeyStr = nil;
      }
      if ([secretKeyStr isEqualToString:@"空对象"]) {
          secretKeyStr = nil;
      }
      self.mAppID = appIDStr;
      self.mAppKey = appKeyStr;
      [self.mWifiDeviceToolkitImpl setWifiDeviceToolkitAppId:self.mAppID appKey:self.mAppKey secretKey:secretKeyStr area:nil features:nil];
    });

#pragma mark - 9034-9046
    Given(@"^deviceID为\"([^\"]*)\"的uSDKDevice对象信息为:$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      NSString *deviceId = args[0];
      NSArray *kList = getTableDataListFromExpectUserInfo(userInfo);
      NSArray *parameterArr = kList[0];
      uSDKDevice *kuSDKDevice = [self.mUSDKDeviceManager getDeviceWithID:deviceId];
      FakeUSDKDevice *kFakeUSDKDevice = (FakeUSDKDevice *)kuSDKDevice;
      [kFakeUSDKDevice mockuSDKDeviceType:ALL_TYPE];
      deviceId = [NSString stringWithFormat:@"%@", parameterArr[0]];
      NSString *uplusID = [NSString stringWithFormat:@"%@", parameterArr[2]];
      [kFakeUSDKDevice mockuSDKDeviceID:deviceId];
      [kFakeUSDKDevice mockuSDKDeviceUplusID:uplusID];
    });

    When(@"^设备\"([^\"]*)\"报警状态发生变化，变化信息如下:$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      NSString *deviceId = args[0];
      uSDKDevice *kuSDKDevice = [self.mUSDKDeviceManager getDeviceWithID:deviceId];
      NSArray<uSDKDeviceAlarm *> *kExpectuSDKDeviceAlarm = convertUSDKDeviceAlarmFromStepTableData(userInfo);
      [self.mWifiDeviceToolkitImpl device:kuSDKDevice didReceiveAlarms:kExpectuSDKDeviceAlarm];
    });
    Then(@"^收到设备\"([^\"]*)\"的报警变化信息如下:$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      NSArray<uSDKDeviceAlarm *> *kExpectuSDKDeviceAlarm = convertUSDKDeviceAlarmFromStepTableData(userInfo);
      NSArray *cautionList = [self.reportListener getParametersOfSelector:@selector(onDeviceCaution:cautionList:)];
      CCIAssert([[StepsUtils sharedInstance] isEqualUSDKAttribute:cautionList[0] expectAttribute:kExpectuSDKDeviceAlarm], @"执行结果与实际不一致");
    });
    When(@"^设备\"([^\"]*)\"属性状态发生变化，变化信息如下:$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      NSString *deviceId = args[0];
      uSDKDevice *kuSDKDevice = [self.mUSDKDeviceManager getDeviceWithID:deviceId];
      NSArray *kExpectuSDKDeviceAttribute = convertUSDKDeviceAttributesFromStep(userInfo);
      [self.mWifiDeviceToolkitImpl device:kuSDKDevice didUpdateValueForAttributes:kExpectuSDKDeviceAttribute];
    });
    Then(@"^收到设备\"([^\"]*)\"的属性信息如下:$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      NSArray *kExpectuSDKDeviceAttribute = convertUSDKDeviceAttributesFromStep(userInfo);
      uSDKDeviceAttribute *uSdkAttribute = nil;
      DeviceAttribute *deviceAttribute = nil;
      NSArray *parameters = [self.reportListener getParametersOfSelector:@selector(onAttributesChange:attributeList:)];
      NSArray *attributeList = parameters[0];
      if (kExpectuSDKDeviceAttribute.count && attributeList.count) {
          uSdkAttribute = kExpectuSDKDeviceAttribute[0];
          deviceAttribute = attributeList[0];
          CCIAssert(uSdkAttribute.attrName == deviceAttribute.name, @"执行结果与实际不一致");
          CCIAssert(uSdkAttribute.attrValue == deviceAttribute.value, @"执行结果与实际不一致");
      }
    });
    When(@"^设备\"([^\"]*)\"连接状态发生变化，连接信息为\"([^\"]*)\"$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      NSString *deviceId = args[0];
      NSString *connected = args[1];
      uSDKDeviceState state = uSDKDeviceStateUnconnect;
      if ([connected isEqualToString:@"STATUS_CONNECTED"]) {
          state = uSDKDeviceStateConnecting;
      }
      uSDKDevice *kuSDKDevice = [self.mUSDKDeviceManager getDeviceWithID:deviceId];
      [self.mWifiDeviceToolkitImpl device:kuSDKDevice didUpdateState:state error:nil];
    });
    Then(@"^收到设备\"([^\"]*)\"连接状态信息为\"([^\"]*)\"$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      NSString *connected = args[1];
      UpDeviceConnection state = UpDeviceConnection_OFFLINE;
      if ([connected isEqualToString:@"STATUS_CONNECTED"]) {
          state = UpDeviceConnection_CONNECTING;
      }
      NSArray *parameters = [self.reportListener getParametersOfSelector:@selector(onConnectionChange:connection:)];
      UpDeviceConnection connection = [parameters[0] integerValue];
      CCIAssert(connection == state, @"执行结果与实际不一致");
    });
    When(@"^设备\"([^\"]*)\"基本信息发生变化$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      NSString *deviceId = args[0];
      uSDKDevice *kuSDKDevice = [self.mUSDKDeviceManager getDeviceWithID:deviceId];
      [self.mWifiDeviceToolkitImpl deviceDidUpdateBaseInfo:kuSDKDevice];
    });
    Then(@"^收到设备\"([^\"]*)\"的基本信息如下:$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      NSArray<DeviceInfo *> *kExpectList = getDeviceListFromExpectUserInfo(userInfo);
      NSArray *parameters = [self.reportListener getParametersOfSelector:@selector(onDeviceInfoChange:)];
      CCIAssert(isEqualDeviceList(kExpectList, [NSArray arrayWithObject:parameters[0]]), @"执行结果与实际不一致");
    });

    When(@"^设备\"([^\"]*)\"的子设备列表发生变化,变化信息如下:$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      NSString *deviceId = args[0];
      uSDKDevice *kuSDKDevice = [self.mUSDKDeviceManager getDeviceWithID:deviceId];
      NSArray<uSDKSubDevice *> *kSubDeviceList = convertUSDKSubDeviceListFromStepTableData(userInfo);
      [self.mWifiDeviceToolkitImpl device:kuSDKDevice didAddSubDevices:kSubDeviceList];
    });
    Then(@"^收到设备\"([^\"]*)\"的子设备信息如下:$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      NSArray<DeviceInfo *> *kExpectList = getDeviceListFromExpectUserInfo(userInfo);
      NSArray *parameters = [self.reportListener getParametersOfSelector:@selector(onSubDevListChange:subDevInfoList:)];
      CCIAssert(isEqualDeviceList(kExpectList, parameters[0]), @"执行结果与实际不一致");
    });
    When(@"^设备\"([^\"]*)\"的数据发生变化,变化信息如下:$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      NSString *deviceId = args[0];
      uSDKDevice *kuSDKDevice = [self.mUSDKDeviceManager getDeviceWithID:deviceId];
      NSArray *kList = getTableDataListFromExpectUserInfo(userInfo);
      NSArray *parameterArr = kList[0];
      NSData *data = [parameterArr[1] dataUsingEncoding:NSUTF8StringEncoding];
      [self.mWifiDeviceToolkitImpl device:kuSDKDevice didReceiveResource:parameterArr[0] data:data];
    });
    When(@"^设备\"([^\"]*)\"的解码资源数据发生变化,变化信息如下:$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      NSString *deviceId = args[0];
      uSDKDevice *kuSDKDevice = [self.mUSDKDeviceManager getDeviceWithID:deviceId];
      NSArray *kList = getTableDataListFromExpectUserInfo(userInfo);
      NSArray *parameterArr = kList[0];
      NSString *data = parameterArr[1];
      [self.mWifiDeviceToolkitImpl device:kuSDKDevice didReceiveDecodeResource:parameterArr[0] data:data];
    });
    Then(@"^收到设备\"([^\"]*)\"的数据信息如下:$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      NSArray *kList = getTableDataListFromExpectUserInfo(userInfo);
      NSArray *parameterArr = kList[0];
      NSData *data = [parameterArr[1] dataUsingEncoding:NSUTF8StringEncoding];
      NSArray *parameters = [self.reportListener getParametersOfSelector:@selector(onDeviceReceive:name:data:)];
      NSData *realityData = (NSData *)parameters[1];
      CCIAssert([parameters[0] isEqualToString:parameterArr[0]], @"执行结果与实际不一致");
      CCISAssert(realityData.length == data.length, @"执行结果与实际不一致");
    });
    Then(@"^收到设备\"([^\"]*)\"的资源解码数据信息如下:$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      NSArray *kList = getTableDataListFromExpectUserInfo(userInfo);
      NSArray *parameterArr = kList[0];
      NSString *data = parameterArr[1];
      NSArray *parameters = [self.reportListener getParametersOfSelector:@selector(onDeviceReceive:name:data:)];
      NSString *realityData = parameters[1];
      CCIAssert([parameters[0] isEqualToString:parameterArr[0]], @"执行结果与实际不一致");
      CCISAssert([realityData isEqualToString:data], @"执行结果与实际不一致");
    });
    When(@"^设备\"([^\"]*)\"的底板固件升级状态发生变化,变化信息如下:$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      NSString *deviceId = args[0];
      NSArray *parameterArr = convertuSDKFOTAStatusInfoFromExpectUserInfo(userInfo);
      uSDKFOTAStatusInfo *expectedInfo = parameterArr[0];
      uSDKDevice *kuSDKDevice = [self.mUSDKDeviceManager getDeviceWithID:deviceId];
      [self.mWifiDeviceToolkitImpl device:kuSDKDevice didUpdateBoardFOTAStatus:expectedInfo];
    });
    Then(@"^收到设备\"([^\"]*)\"的设备底板固件升级状态信息如下:$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      NSArray *parameterArr = convertuSDKFOTAStatusInfoFromExpectUserInfo(userInfo);
      uSDKFOTAStatusInfo *expectedInfo = parameterArr[0];
      NSArray *parameters = [self.reportListener getParametersOfSelector:@selector(onDeviceUpdateBoardFOTAStatus:FOTAStatusInfo:)];
      id<UpDeviceFOTAStatusInfo> statusInfo = parameters[0];
      CCISAssert((statusInfo.upgradeStatus == expectedInfo.upgradeStatus) && (statusInfo.upgradeErrInfo == expectedInfo.upgradeErrorInfo), @"执行结果与实际不一致");
    });
    When(@"^设备\"([^\"]*)\"的蓝牙实时数据发生变化,变化信息为\"([^\"]*)\"$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      NSString *deviceId = args[0];
      NSString *fakeBluetoothData = args[1];
      NSData *bluetoothData;
      if ([fakeBluetoothData isEqualToString:@"fakeBluetoothData"]) {
          bluetoothData = [fakeBluetoothData dataUsingEncoding:NSUTF8StringEncoding];
      }
      uSDKDevice *kuSDKDevice = [self.mUSDKDeviceManager getDeviceWithID:deviceId];
      [self.mWifiDeviceToolkitImpl device:kuSDKDevice didReceiveBLERealTimeData:bluetoothData];
    });
    Then(@"^收到设备\"([^\"]*)\"的蓝牙实时数据为\"([^\"]*)\"$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      NSString *fakeBluetoothData = args[1];
      NSData *bluetoothData;
      if ([fakeBluetoothData isEqualToString:@"fakeBluetoothData"]) {
          bluetoothData = [fakeBluetoothData dataUsingEncoding:NSUTF8StringEncoding];
      }
      NSArray *parameters = [self.reportListener getParametersOfSelector:@selector(onDeviceReceiveBLERealTimeData:data:)];
      NSData *BLERealTimeData = (NSData *)parameters[0];
      CCISAssert(BLERealTimeData.length == bluetoothData.length, @"执行结果与实际不一致");
    });
    When(@"^设备\"([^\"]*)\"的蓝牙历史数据发生变化,变化信息如下:$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      NSString *deviceId = args[0];
      uSDKDevice *kuSDKDevice = [self.mUSDKDeviceManager getDeviceWithID:deviceId];
      NSArray *parameterArr = convertDeviceBLEHistoryInfoFromExpectUserInfo(userInfo);
      DeviceBLEHistoryInfo *historyInfo = parameterArr[0];
      [self.mWifiDeviceToolkitImpl device:kuSDKDevice didReceiveBLEHistoryData:historyInfo.data currentCount:historyInfo.currentCount totalCount:historyInfo.totalCount];
    });
    Then(@"^收到设备\"([^\"]*)\"的蓝牙历史数据如下:$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      NSArray *parameterArr = convertDeviceBLEHistoryInfoFromExpectUserInfo(userInfo);
      DeviceBLEHistoryInfo *historyInfo = parameterArr[0];
      NSArray *parameters = [self.reportListener getParametersOfSelector:@selector(onDeviceReceiveBLEHistoryData:BLEHistoryInfo:)];
      id<UpDeviceBLEHistoryInfo> realHistoryInfo = parameters[0];
      CCIAssert(realHistoryInfo.currentCount == historyInfo.currentCount, @"执行结果与实际不一致");
      CCIAssert(realHistoryInfo.totalCount == historyInfo.totalCount, @"执行结果与实际不一致");
      CCIAssert(realHistoryInfo.data.length == historyInfo.data.length, @"执行结果与实际不一致");
    });
#pragma mark - 9043-1
    When(@"^设备绑定代理方法被触发,设备id为\"([^\"]*)\"$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      NSString *deviceId = args[0];
      if ([deviceId isEqualToString:@"null"]) {
          deviceId = nil;
      }
      [self.mWifiDeviceToolkitImpl deviceManager:self.mUSDKDeviceManager didBindDevice:deviceId];
    });
    Then(@"^设备列表变化接口被调用\"([^\"]*)\"次$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      NSString *kInvocationTimes = args[0];
      NSUInteger actualTimes = [self.detectListener getInvocationTimesOfSelector:@selector(onDeviceListChanged)];
      CCIAssert(actualTimes == kInvocationTimes.intValue, @"执行结果与实际不一致");
    });
#pragma mark - 9044-1
    When(@"^设备解除绑定代理方法被触发,设备id为\"([^\"]*)\"$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      NSString *deviceId = args[0];
      if ([deviceId isEqualToString:@"null"]) {
          deviceId = nil;
      }
      [self.mWifiDeviceToolkitImpl deviceManager:self.mUSDKDeviceManager didUnbindDevice:deviceId];
    });
#pragma mark - 9045-1
    When(@"^发现新设备代理方法被触发,设备列表如下:$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      NSArray<uSDKDevice *> *kList = convertUSDKDeviceListFromStepTableData(userInfo);
      [self.mWifiDeviceToolkitImpl deviceManager:self.mUSDKDeviceManager didAddDevices:kList];
    });
    Then(@"^收到新发现设备列表如下:$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      NSArray<DeviceInfo *> *kExpectList = getDeviceListFromExpectUserInfo(userInfo);
      NSArray *findInfoList = [self.detectListener getParametersOfSelector:@selector(onFind:)];
      CCIAssert(isEqualDeviceList(kExpectList, findInfoList[0]), @"执行结果与实际不一致");
    });
#pragma mark - 9046-1
    When(@"^删除设备代理方法被触发,设备列表如下:$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      NSArray<uSDKDevice *> *kList = convertUSDKDeviceListFromStepTableData(userInfo);
      [self.mWifiDeviceToolkitImpl deviceManager:self.mUSDKDeviceManager didRemoveDevices:kList];
    });
    Then(@"^收到新删除设备列表如下:$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      NSArray<DeviceInfo *> *kExpectList = getDeviceListFromExpectUserInfo(userInfo);
      NSArray *loseInfoList = [self.detectListener getParametersOfSelector:@selector(onLose:)];
      CCIAssert(isEqualDeviceList(kExpectList, loseInfoList[0]), @"执行结果与实际不一致");
    });

#pragma mark -9047
    Given(@"^deviceID为\"([^\"]*)\"的uSDKDevice对象的subscribeResourceWithDecode接口的回调结果为\"([^\"]*)\"$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      NSString *kDeviceId = args[0];
      uSDKDevice *kuSDKDevice = [self.mUSDKDeviceManager getDeviceWithID:kDeviceId];
      FakeUSDKDevice *kFakeUSDKDevice = (FakeUSDKDevice *)kuSDKDevice;
      NSString *kResult = args[1];
      BOOL bResult = [kResult isEqualToString:@"Success"];
      [kFakeUSDKDevice mockSubscribeResourceWithDecodeResult:bResult];
    });

    When(@"^调用订阅资源解码接口,参数deviceId为\"([^\"]*)\", 资源名称为\"([^\"]*)\"$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      NSString *deviceId = args[0];
      if ([deviceId isEqualToString:@"空对象"]) {
          deviceId = nil;
      }
      if ([deviceId isEqualToString:@"空字符串"]) {
          deviceId = @"";
      }
      NSString *resourceName = args[1];
      if ([resourceName isEqualToString:@"空对象"]) {
          resourceName = nil;
      }
      if ([resourceName isEqualToString:@"空字符串"]) {
          resourceName = @"";
      }
      [self.mWifiDeviceToolkitImpl attachResourceWithDecode:deviceId
                                               resourceName:resourceName
                                                finishBlock:^(UpDeviceResult *result) {
                                                  self.mCallbackResult = result;
                                                }];
    });
    Then(@"^订阅资源解码接口回调结果为\"([^\"]*)\"$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      NSString *kResult = args[0];
      [self assertResult:kResult];
    });

    Then(@"^deviceID为\"([^\"]*)\"的uSDKDevice对象的subscribeResourceWithDecode接口被调用\"([^\"]*)\"次,参数为\"([^\"]*)\"$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      [self assertSelectorCountAndParamsWithArgs:args
                                        selector:@selector((subscribeResourceWithDecode
                                                            : success
                                                            : failure:))];
    });

#pragma mark -9048
    Given(@"^deviceID为\"([^\"]*)\"的uSDKDevice对象的createGroupWithTimeoutInterval接口的调用结果为\"([^\"]*)\"$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      NSString *kDeviceId = args[0];
      uSDKDevice *kuSDKDevice = [self.mUSDKDeviceManager getDeviceWithID:kDeviceId];
      FakeUSDKDevice *kFakeUSDKDevice = (FakeUSDKDevice *)kuSDKDevice;
      NSString *kResult = args[1];
      BOOL bResult = [kResult isEqualToString:@"Success"];
      [kFakeUSDKDevice mockCreateGroupResult:bResult];
    });

    When(@"^调用创建设备组接口,参数deviceId为\"([^\"]*)\"$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      NSString *deviceId = args[0];
      if ([deviceId isEqualToString:@"空对象"]) {
          deviceId = nil;
      }
      if ([deviceId isEqualToString:@"空字符串"]) {
          deviceId = @"";
      }

      [self.mWifiDeviceToolkitImpl createDeviceGroup:deviceId
                                         finishBlock:^(UpDeviceResult *result) {
                                           self.mCallbackResult = result;
                                         }];
    });

    Then(@"^创建设备组接口回调结果为\"([^\"]*)\"$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      NSString *kResult = args[0];
      [self assertResult:kResult];
    });

    Then(@"^deviceID为\"([^\"]*)\"的uSDKDevice对象的createGroupWithTimeoutInterval接口被调用\"([^\"]*)\"次$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      [self assertSelectorCountWithArgs:args selector:@selector(createGroupWithTimeoutInterval:completionHandler:)];
    });
#pragma mark -9051
    Given(@"^deviceID为\"([^\"]*)\"的uSDKDevice对象的deleteGroupCompletionHandler接口的调用结果为\"([^\"]*)\"$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      NSString *kDeviceId = args[0];
      uSDKDevice *kuSDKDevice = [self.mUSDKDeviceManager getDeviceWithID:kDeviceId];
      FakeUSDKDevice *kFakeUSDKDevice = (FakeUSDKDevice *)kuSDKDevice;
      NSString *kResult = args[1];
      BOOL bResult = [kResult isEqualToString:@"Success"];
      [kFakeUSDKDevice mockDeleteGroupResult:bResult];
    });

    When(@"^调用删除组设备接口,参数deviceId为\"([^\"]*)\"$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      NSString *deviceId = args[0];
      if ([deviceId isEqualToString:@"空对象"]) {
          deviceId = nil;
      }
      if ([deviceId isEqualToString:@"空字符串"]) {
          deviceId = @"";
      }
      [self.mWifiDeviceToolkitImpl deleteDeviceGroup:deviceId
                                         finishBlock:^(UpDeviceResult *result) {
                                           self.mCallbackResult = result;
                                         }];

    });
    Then(@"^删除组设备接口回调结果为\"([^\"]*)\"$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      NSString *kResult = args[0];
      [self assertResult:kResult];
    });

    Then(@"^deviceID为\"([^\"]*)\"的uSDKDevice对象的deleteGroupCompletionHandler接口被调用\"([^\"]*)\"次$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      [self assertSelectorCountWithArgs:args selector:@selector(deleteGroupCompletionHandler:)];
    });
#pragma mark - 9098,9099
    Given(@"^deviceID为\"([^\"]*)\"的uSDKDevice对象的startFoTa接口的调用结果为\"([^\"]*)\"$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      NSString *kResult = args[1];
      NSString *kDeviceId = args[0];
      BOOL bResult = [kResult isEqualToString:@"Success"];
      uSDKDevice *kuSDKDevice = [self.mUSDKDeviceManager getDeviceWithID:kDeviceId];
      FakeUSDKDevice *kFakeUSDKDevice = (FakeUSDKDevice *)kuSDKDevice;
      [kFakeUSDKDevice mockStartFOTAWithDeviceFOTAInfoResult:bResult];
    });

    When(@"^调用洗衣机Fota升级接口,参数traceId为 \"([^\"]*)\", 参数firmwareId为 \"([^\"]*)\", 参数deviceId为\"([^\"]*)\"$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      NSString *deviceId = args[2];
      if ([deviceId isEqualToString:@"空对象"]) {
          deviceId = nil;
      }
      if ([deviceId isEqualToString:@"空字符串"]) {
          deviceId = @"";
      }

      NSString *traceId = args[0];
      NSString *firmwareId = args[1];
      [self.mWifiDeviceToolkitImpl startFOTAWithDeviceFOTA:deviceId
                                                   traceId:traceId
                                                firmwareId:firmwareId
                                               finishBlock:^(UpDeviceResult *result) {
                                                 self.mCallbackResult = result;
                                               }];
    });

    Then(@"^洗衣机Fota升级方法回调结果为\"([^\"]*)\"$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      NSString *kResult = args[0];
      [self assertResult:kResult];
    });

    Then(@"^deviceID为\"([^\"]*)\"的uSDKDevice对象的startFoTa接口被调用\"([^\"]*)\"次,参数为\"([^\"]*)\"$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      NSString *kDeviceId = args[0];
      NSString *kInvocationTimes = args[1];
      NSString *kParameter = args[2];
      if ([kParameter isEqualToString:@""]) {
          kParameter = nil;
      }
      uSDKDevice *kuSDKDevice = [self.mUSDKDeviceManager getDeviceWithID:kDeviceId];
      FakeUSDKDevice *kFakeUSDKDevice = (FakeUSDKDevice *)kuSDKDevice;
      CCIAssert(kuSDKDevice, [NSString stringWithFormat:@"deviceId为%@的设备不存在", kDeviceId]);
      uSDKDeviceFOTAInfo *info = [kFakeUSDKDevice getParametersOfSelector:@selector(startFOTAWithDeviceFOTAInfo:completionHandler:)].firstObject;
      NSString *kActualParameter = [NSString stringWithFormat:@"%@,%@", info.traceID ?: @"", info.firmwareID ?: @""];
      if (kParameter && kActualParameter) {
          CCIAssert([kActualParameter isEqualToString:kParameter], @"执行结果与实际不一致");
      }
      NSUInteger actualTimes = [kFakeUSDKDevice getInvocationTimesOfSelector:@selector(startFOTAWithDeviceFOTAInfo:completionHandler:)];
      CCIAssert(actualTimes == kInvocationTimes.intValue, @"执行结果与实际不一致");

    });
#pragma mark -9053
    Given(@"^deviceID为\"([^\"]*)\"的uSDKDevice对象的fetchGroupableDeviceList接口的调用结果为\"([^\"]*)\",回调结果为:$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      NSString *kDeviceId = args[0];
      uSDKDevice *kuSDKDevice = [self.mUSDKDeviceManager getDeviceWithID:kDeviceId];
      FakeUSDKDevice *kFakeUSDKDevice = (FakeUSDKDevice *)kuSDKDevice;
      NSString *kResult = args[1];
      BOOL bResult = [kResult isEqualToString:@"Success"];
      [kFakeUSDKDevice mockFetchGroupableDeviceListResult:bResult];
    });

    When(@"^调用获取组设备列表接口,参数deviceId为\"([^\"]*)\"$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      NSString *deviceId = args[0];
      if ([deviceId isEqualToString:@"空对象"]) {
          deviceId = nil;
      }
      if ([deviceId isEqualToString:@"空字符串"]) {
          deviceId = @"";
      }
      [self.mWifiDeviceToolkitImpl fetchGroupableDeviceList:deviceId
                                                finishBlock:^(UpDeviceResult *result) {
                                                  self.mCallbackResult = result;
                                                }];

    });

    Then(@"^获取组设备列表接口回调结果为\"([^\"]*)\",回调结果的extraData值为:$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      NSString *kResult = args[0];
      [self assertResult:kResult];
    });

    Then(@"^deviceID为\"([^\"]*)\"的uSDKDevice对象的fetchGroupableDeviceList接口被调用\"([^\"]*)\"次$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      [self assertSelectorCountWithArgs:args selector:@selector(fetchGroupableDeviceListCompletionHandler:)];
    });
#pragma mark -9054
    Given(@"^deviceID为\"([^\"]*)\"的uSDKDevice对象的isGroup值为\"([^\"]*)\"$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      NSString *kDeviceId = args[0];
      NSString *kIsGroup = args[1];
      uSDKDevice *kuSDKDevice = [self.mUSDKDeviceManager getDeviceWithID:kDeviceId];
      FakeUSDKDevice *kFakeUSDKDevice = (FakeUSDKDevice *)kuSDKDevice;
      [kFakeUSDKDevice mockIsGroupResult:[kIsGroup isEqualToString:@"true"]];
    });

    When(@"^调用是否组设备接口,参数deviceId为\"([^\"]*)\"$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      NSString *deviceId = args[0];
      if ([deviceId isEqualToString:@"空对象"]) {
          deviceId = nil;
      }
      if ([deviceId isEqualToString:@"空字符串"]) {
          deviceId = @"";
      }
      self.isGroup = [self.mWifiDeviceToolkitImpl isGroup:deviceId];

    });

    Then(@"^获取是否组设备接口的结果为\"([^\"]*)\",回调结果的extraData值为\"([^\"]*)\"$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      NSString *kResult = args[0];
      NSString *kValue = args[1];
      CCIAssert(self.isGroup == [kResult isEqualToString:@"true"], @"执行结果与实际不一致");
      CCIAssert(self.isGroup == [kValue isEqualToString:@"true"], @"执行结果与实际不一致");
    });
#pragma mark -9055
    Given(@"^deviceID为\"([^\"]*)\"的uSDKDevice对象的groupMembers接口的结果为:$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      NSString *kDeviceId = args[0];
      uSDKDevice *kuSDKDevice = [self.mUSDKDeviceManager getDeviceWithID:kDeviceId];
      FakeUSDKDevice *kFakeUSDKDevice = (FakeUSDKDevice *)kuSDKDevice;
      NSArray<uSDKDevice *> *kDeviceList = convertUSDKDeviceListFromStepTableData(userInfo);
      OCMStub([kFakeUSDKDevice groupMembers]).andReturn(kDeviceList);
    });

    When(@"^调用获取组设备成员列表接口,参数deviceId为\"([^\"]*)\"$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      NSString *deviceId = args[0];
      if ([deviceId isEqualToString:@"空对象"]) {
          deviceId = nil;
      }
      if ([deviceId isEqualToString:@"空字符串"]) {
          deviceId = @"";
      }
      [self.mWifiDeviceToolkitImpl getGroupMemberList:deviceId
                                          finishBlock:^(UpDeviceResult *result) {
                                            self.mCallbackResult = result;
                                          }];

    });

    Then(@"^获取组设备成员列表接口回调结果为\"([^\"]*)\",回调结果的extraData值为:$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      NSArray<DeviceInfo *> *kExpectDeviceList = getDeviceListFromExpectUserInfo(userInfo);
      NSString *kResult = args[0];
      BOOL bResult = [kResult isEqualToString:@"Success"];
      NSArray<DeviceInfo *> *kDeviceList = self.mCallbackResult.extraData;
      CCIAssert(self.mCallbackResult.isSuccessful == bResult && isEqualDeviceList(kExpectDeviceList, kDeviceList), [NSString stringWithFormat:@"回调结果期望不一致,期望为%d,实际为%d", bResult, self.mCallbackResult.isSuccessful]);

    });

    Then(@"deviceID为\"([^\"]*)\"的uSDKDevice对象的groupMembers接口被调用\"([^\"]*)\"次", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      NSString *kDeviceId = args[0];
      NSString *kInvocationTimes = args[1];
      uSDKSubDevice *kuSDKDevice = (uSDKSubDevice *)[self.mUSDKDeviceManager getDeviceWithID:kDeviceId];
      OCMVerify(times(kInvocationTimes.intValue), [kuSDKDevice groupMembers]);
    });
#pragma mark -9058
    Given(@"deviceID为\"([^\"]*)\"的uSDKDevice对象的addDevices接口的completionHandler调用结果为\"([^\"]*)\",progressNotify回调结果为\"([^\"]*)\"$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      NSString *kDeviceId = args[0];
      NSString *kuSDKDResult = args[1];
      NSString *kProgressNotifyResult = args[2];
      uSDKSubDevice *kuSDKDevice = (uSDKSubDevice *)[self.mUSDKDeviceManager getDeviceWithID:kDeviceId];
      FakeUSDKDevice *kFakeUSDKDevice = (FakeUSDKDevice *)kuSDKDevice;
      [kFakeUSDKDevice mockAddDeviceResult:[kuSDKDResult isEqualToString:@"Success"] notifyResult:[kProgressNotifyResult isEqualToString:@"Success"]];
    });

    When(@"调用向组设备添加设备接口,参数deviceId为\"([^\"]*)\",设备列表为\"([^\"]*)\"$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      NSString *deviceId = args[0];
      if ([deviceId isEqualToString:@"空对象"]) {
          deviceId = nil;
      }
      if ([deviceId isEqualToString:@"空字符串"]) {
          deviceId = @"";
      }
      NSString *deviceIds = args[1];
      NSArray *deviceIdsArray = [NSArray array];
      if ([deviceIds isEqualToString:@"空对象"]) {
          deviceIdsArray = nil;
      }
      if (deviceIds) {
          deviceIdsArray = [deviceIds componentsSeparatedByString:@","];
      }
      [self.mWifiDeviceToolkitImpl addDevicesToGroup:deviceId
                                           deviceIds:deviceIdsArray
                                         finishBlock:^(UpDeviceResult *result) {
                                           self.mCallbackResult = result;
                                         }];
    });

    Then(@"^向组设备添加设备接口回调结果为\"([^\"]*)\"$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      NSString *kResult = args[0];
      [self assertResult:kResult];
    });

    Then(@"^deviceID为\"([^\"]*)\"的uSDKDevice对象的addDevice接口被调用\"([^\"]*)\"次,参数为\"([^\"]*)\"$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      [self assertSelectorHandleDeviceWithArgs:args selector:@selector(addDevices:toGroupWithTimeoutInterval:progressNotify:completionHandler:)];
    });
#pragma mark -9061
    Given(@"deviceID为\"([^\"]*)\"的uSDKDevice对象的removeDevices接口的completionHandler调用结果为\"([^\"]*)\",progressNotify回调结果为\"([^\"]*)\"$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      NSString *kDeviceId = args[0];
      NSString *kuSDKDResult = args[1];
      NSString *kProgressNotifyResult = args[2];
      uSDKSubDevice *kuSDKDevice = (uSDKSubDevice *)[self.mUSDKDeviceManager getDeviceWithID:kDeviceId];
      FakeUSDKDevice *kFakeUSDKDevice = (FakeUSDKDevice *)kuSDKDevice;
      [kFakeUSDKDevice mockRemoveDeviceResult:[kuSDKDResult isEqualToString:@"Success"] notifyResult:[kProgressNotifyResult isEqualToString:@"Success"]];
    });

    When(@"调用从组设备中移除设备接口,参数deviceId为\"([^\"]*)\",设备列表为\"([^\"]*)\"$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      NSString *deviceId = args[0];
      if ([deviceId isEqualToString:@"空对象"]) {
          deviceId = nil;
      }
      if ([deviceId isEqualToString:@"空字符串"]) {
          deviceId = @"";
      }
      NSString *deviceIds = args[1];
      NSArray *deviceIdsArray = [NSArray array];
      if ([deviceIds isEqualToString:@"空对象"]) {
          deviceIdsArray = nil;
      }
      if (deviceIds) {
          deviceIdsArray = [deviceIds componentsSeparatedByString:@","];
      }
      [self.mWifiDeviceToolkitImpl removeDevicesFromGroup:deviceId
                                                deviceIds:deviceIdsArray
                                              finishBlock:^(UpDeviceResult *result) {
                                                self.mCallbackResult = result;
                                              }];
    });

    Then(@"^从组设备中移除设备接口回调结果为\"([^\"]*)\"$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      NSString *kResult = args[0];
      [self assertResult:kResult];
    });

    Then(@"^deviceID为\"([^\"]*)\"的uSDKDevice对象的removeDevices接口被调用\"([^\"]*)\"次,参数为\"([^\"]*)\"$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      [self assertSelectorHandleDeviceWithArgs:args selector:@selector(removeDevices:fromGroupWithTimeoutInterval:progressNotify:completionHandler:)];
    });
#pragma mark - 9066
    Given(@"^deviceID为\"([^\"]*)\"的uSDKDevice对象的onlineState,值为\"([^\"]*)\"$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      NSString *kDeviceId = args[0];
      NSString *kConnectionStatus = args[1];
      uSDKDevice *kuSDKDevice = [self.mUSDKDeviceManager getDeviceWithID:kDeviceId];
      FakeUSDKDevice *kFakeUSDKDevice = (FakeUSDKDevice *)kuSDKDevice;
      [kFakeUSDKDevice mockOnlineState:kConnectionStatus.intValue];
    });
    When(@"^调用获取设备真实连接状态接口,参数deviceId为\"([^\"]*)\"$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      NSString *kDeviceId = args[0];
      if ([kDeviceId isEqualToString:@"空对象"]) {
          kDeviceId = nil;
      }
      [self.mWifiDeviceToolkitImpl getDeviceOnlineStatus:kDeviceId
                                             finishBlock:^(UpDeviceResult *result) {
                                               self.mCallbackResult = result;
                                             }];
    });
    Then(@"^获取设备真实连接状态接口的回调结果为\"([^\"]*)\",回调结果中的status值为\"([^\"]*)\"$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      NSString *kResult = args[0];
      NSString *kExpectState = args[1];
      BOOL bResult = [kResult isEqualToString:@"Success"];
      if (bResult) {
          NSNumber *kConnectionState = self.mCallbackResult.extraData;
          CCIAssert(self.mCallbackResult.isSuccessful == bResult && kExpectState.intValue == kConnectionState.intValue, [NSString stringWithFormat:@"回调结果期望不一致,期望为%d,实际为%d", bResult, self.mCallbackResult.isSuccessful]);
      }
      else {
          [self assertResult:kResult];
      }
    });
#pragma mark - 9067
    When(@"^设备\"([^\"]*)\"真实在线状态发生变化，状态为\"([^\"]*)\"$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      NSString *deviceId = args[0];
      NSString *connected = args[1];
      uSDKDeviceOnlineState state = uSDKDeviceOnlineStateOffline;
      if ([connected isEqualToString:@"ONLINE"]) {
          state = uSDKDeviceOnlineStateOnline;
      }
      uSDKDevice *kuSDKDevice = [self.mUSDKDeviceManager getDeviceWithID:deviceId];
      [self.mWifiDeviceToolkitImpl device:kuSDKDevice didUpdateOnlineState:state];
    });
    Then(@"^收到设备\"([^\"]*)\"真实在线状态为\"([^\"]*)\"$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      NSString *connected = args[1];
      UpDeviceRealOnline state = UpDeviceRealOnline_OFFLINE;
      if ([connected isEqualToString:@"ONLINE"]) {
          state = UpDeviceRealOnline_ONLINE;
      }
      NSArray *parameters = [self.reportListener getParametersOfSelector:@selector(onRealOnlineChange:status:)];
      UpDeviceRealOnline connection = [parameters[0] integerValue];
      CCIAssert(connection == state, @"执行结果与实际不一致");
    });

#pragma mark ——— blestate
    Given(@"^deviceID为\"([^\"]*)\"的uSDKDevice对象的bleState的值为\"([^\"]*)\"$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      NSString *kDeviceId = args[0];
      uSDKDevice *kuSDKDevice = [self.mUSDKDeviceManager getDeviceWithID:kDeviceId];
      FakeUSDKDevice *kFakeUSDKDevice = (FakeUSDKDevice *)kuSDKDevice;
      NSString *blestate = args[1];
      if ([blestate isEqualToString:@"OFFLINE"]) {
          [kFakeUSDKDevice mockBleState:uSDKDeviceStateOffline];
      }
      else if ([blestate isEqualToString:@"READY"]) {
          [kFakeUSDKDevice mockBleState:uSDKDeviceStateReady];
      }
      else if ([blestate isEqualToString:@"DISCONNECTED"]) {
          [kFakeUSDKDevice mockBleState:uSDKDeviceStateUnconnect];
      }
      else if ([blestate isEqualToString:@"CONNECTING"]) {
          [kFakeUSDKDevice mockBleState:uSDKDeviceStateConnecting];
      }
      else if ([blestate isEqualToString:@"CONNECTED"]) {
          [kFakeUSDKDevice mockBleState:uSDKDeviceStateConnected];
      }

    });

    When(@"^调用获取设备蓝牙连接状态接口,参数deviceId为\"([^\"]*)\"$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      NSString *deviceId = args[0];
      if ([deviceId isEqualToString:@"空字符串"]) {
          deviceId = @"";
      }
      if ([deviceId isEqualToString:@"空对象"]) {
          deviceId = nil;
      }
      [self.mWifiDeviceToolkitImpl getDeviceBleState:deviceId
                                         finishBlock:^(UpDeviceResult *result) {
                                           self.mCallbackResult = result;
                                         }];
    });

    Then(@"^获取设备蓝牙连接状态接口的回调结果为\"([^\"]*)\",回调结果中的extraData值为\"([^\"]*)\"$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      BOOL isSuccess = [args[0] isEqualToString:@"success"];
      CCIAssert(self.mCallbackResult.isSuccessful == isSuccess, [NSString stringWithFormat:@"回调结果期望不一致,期望为%d,实际为%d", (int)isSuccess, self.mCallbackResult.isSuccessful]); //首先断言结果是否和预期一致
      NSNumber *bleState = self.mCallbackResult.extraData;
      NSString *kBlestate = args[1];
      BOOL isExpectBleStateNull = [kBlestate isEqualToString:@""]; //期望值是否为空 true:空
      BOOL isRealBleStateNull = bleState == nil; //真实值是否为空 true:空
      if (!isExpectBleStateNull && !isRealBleStateNull) { //期望和实际都不为空
          UpDeviceConnection kExpectBleState = UpDeviceConnection_DISCONNECTED;
          if ([kBlestate isEqualToString:@"OFFLINE"]) {
              kExpectBleState = UpDeviceConnection_OFFLINE;
          }
          else if ([kBlestate isEqualToString:@"READY"]) {
              kExpectBleState = UpDeviceConnection_READY;
          }
          else if ([kBlestate isEqualToString:@"DISCONNECTED"]) {
              kExpectBleState = UpDeviceConnection_DISCONNECTED;
          }
          else if ([kBlestate isEqualToString:@"CONNECTING"]) {
              kExpectBleState = UpDeviceConnection_CONNECTING;
          }
          else if ([kBlestate isEqualToString:@"CONNECTED"]) {
              kExpectBleState = UpDeviceConnection_CONNECTED;
          }
          CCIAssert(kExpectBleState == bleState.intValue, [NSString stringWithFormat:@"蓝牙状态期望不一致,期望为%d,实际为%d", (int)kExpectBleState, bleState.intValue]);
      }
      else { //至少有一个为空
          CCIAssert(isExpectBleStateNull == isRealBleStateNull, [NSString stringWithFormat:@"蓝牙状态期望不一致,期望为%@,实际为%@", kBlestate, bleState]); //有一个不为空,断言失败
      }

    });

    When(@"^设备\"([^\"]*)\"baseinfo发生变化，其中蓝牙连接状态为\"([^\"]*)\"$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      NSString *deviceId = args[0];
      NSString *bleState = args[1];
      uSDKDevice *kuSDKDevice = [self.mUSDKDeviceManager getDeviceWithID:deviceId];
      FakeUSDKDevice *kFakeUSDKDevice = (FakeUSDKDevice *)kuSDKDevice;
      if ([bleState isEqualToString:@"OFFLINE"]) {
          [kFakeUSDKDevice mockBleState:uSDKDeviceStateOffline];
      }
      else if ([bleState isEqualToString:@"READY"]) {
          [kFakeUSDKDevice mockBleState:uSDKDeviceStateReady];
      }
      else if ([bleState isEqualToString:@"DISCONNECTED"]) {
          [kFakeUSDKDevice mockBleState:uSDKDeviceStateUnconnect];
      }
      else if ([bleState isEqualToString:@"CONNECTING"]) {
          [kFakeUSDKDevice mockBleState:uSDKDeviceStateConnecting];
      }
      else if ([bleState isEqualToString:@"CONNECTED"]) {
          [kFakeUSDKDevice mockBleState:uSDKDeviceStateConnected];
      }

      [self.mWifiDeviceToolkitImpl device:kFakeUSDKDevice didUpdateBleState:kFakeUSDKDevice.bleState error:nil];
    });

    Then(@"^收到设备\"([^\"]*)\"蓝牙连接状态为\"([^\"]*)\"$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      NSString *kDeviceId = args[0];
      NSString *kBlestate = args[1];
      UpDeviceConnection kExpectBleState = UpDeviceConnection_DISCONNECTED;
      if ([kBlestate isEqualToString:@"OFFLINE"]) {
          kExpectBleState = UpDeviceConnection_OFFLINE;
      }
      else if ([kBlestate isEqualToString:@"READY"]) {
          kExpectBleState = UpDeviceConnection_READY;
      }
      else if ([kBlestate isEqualToString:@"DISCONNECTED"]) {
          kExpectBleState = UpDeviceConnection_DISCONNECTED;
      }
      else if ([kBlestate isEqualToString:@"CONNECTING"]) {
          kExpectBleState = UpDeviceConnection_CONNECTING;
      }
      else if ([kBlestate isEqualToString:@"CONNECTED"]) {
          kExpectBleState = UpDeviceConnection_CONNECTED;
      }
      NSArray *parameters = [self.reportListener getParametersOfSelector:@selector(onBleStateChange:state:)];
      CCIAssert([kDeviceId isEqualToString:parameters[0]], [NSString stringWithFormat:@"回调结果期望不一致,期望为%@,实际为%@", kDeviceId, parameters[0]]);
      UpDeviceConnection bleState = [parameters[1] intValue];
      CCIAssert(bleState == kExpectBleState, [NSString stringWithFormat:@"回调结果期望不一致,期望为%d,实际为%d", (int)kExpectBleState, (int)bleState]);
    });
}

#pragma mark - private
- (BOOL)isEqualQualityInfo:(DeviceNetWorkQualityInfo *)expectInfo actualInfo:(DeviceNetWorkQualityInfo *)actualInfo
{
    BOOL isEqual = YES;
    if (expectInfo.connectStatus != actualInfo.connectStatus) {
        return NO;
    }
    if (![expectInfo.machineId isEqualToString:actualInfo.machineId]) {
        return NO;
    }
    if (expectInfo.isOnLine != actualInfo.isOnLine) {
        return NO;
    }
    return isEqual;
}

- (BOOL)isEqualFOTAInfo:(DeviceFOTAInfo *)expectedInfo actualInfo:(DeviceFOTAInfo *)actualInfo
{
    if (expectedInfo.isNeedFOTA != actualInfo.isNeedFOTA) {
        return NO;
    }
    if (![expectedInfo.currentVersion isEqualToString:actualInfo.currentVersion]) {
        return NO;
    }
    if (![expectedInfo.newestVersion isEqualToString:actualInfo.newestVersion]) {
        return NO;
    }
    if (![expectedInfo.newestVersionDescription isEqualToString:actualInfo.newestVersionDescription]) {
        return NO;
    }
    if (![expectedInfo.model isEqualToString:actualInfo.model]) {
        return NO;
    }
    return YES;
}

- (BOOL)isEqualBaseInfo:(DeviceBaseInfo *)expectedInfo actualInfo:(DeviceBaseInfo *)actualInfo
{
    if (![expectedInfo.deviceId isEqualToString:actualInfo.deviceId]) {
        return NO;
    }
    if (![expectedInfo.typeId isEqualToString:actualInfo.typeId]) {
        return NO;
    }
    if (![expectedInfo.typeName isEqualToString:actualInfo.typeName]) {
        return NO;
    }
    return YES;
}

- (uSDKNetworkQualityInfoV2 *)parseUSDKNetworkQualityInfo:(NSArray *)parameters
{
    uSDKNetworkQualityInfoV2 *networkQuality = [uSDKNetworkQualityInfoV2 new];
    NSString *connectStatus = parameters[0];
    networkQuality.connectStatus = connectStatus.intValue;
    networkQuality.machineId = parameters[1];
    NSString *isOnLineStr = parameters[2];
    BOOL isOnLine = [isOnLineStr isEqualToString:@"true"];
    networkQuality.isOnLine = isOnLine;
    NSString *statusLastChangeTime = parameters[3];
    networkQuality.statusLastChangeTime = statusLastChangeTime.intValue;
    networkQuality.netType = parameters[4];
    networkQuality.ssid = parameters[5];
    NSString *rssi = parameters[6];
    networkQuality.rssi = rssi.intValue;
    NSString *prssi = parameters[7];
    networkQuality.prssi = prssi.intValue;
    NSString *signalLevel = parameters[8];
    networkQuality.signalLevel = signalLevel.intValue;
    NSString *ilostRatio = parameters[9];
    networkQuality.ilostRatio = ilostRatio.intValue;
    NSString *its = parameters[10];
    networkQuality.its = its.intValue;
    networkQuality.lanIP = parameters[11];
    networkQuality.moduleVersion = parameters[12];
    return networkQuality;
}

- (DeviceNetWorkQualityInfo *)parseDeviceNetWorkQualityInfo:(NSArray *)parameters
{
    DeviceNetWorkQualityInfo *expectInfo = [DeviceNetWorkQualityInfo new];
    NSString *connectStatus = parameters[0];
    expectInfo.connectStatus = connectStatus.intValue;
    expectInfo.machineId = parameters[1];
    NSString *isOnLineStr = parameters[2];
    BOOL isOnLine = [isOnLineStr isEqualToString:@"true"];
    expectInfo.isOnLine = isOnLine;
    NSString *statusLastChangeTime = parameters[3];
    expectInfo.statusLastChangeTime = statusLastChangeTime.intValue;
    expectInfo.netType = parameters[4];
    expectInfo.ssid = parameters[5];
    NSString *rssi = parameters[6];
    expectInfo.rssi = rssi.intValue;
    NSString *prssi = parameters[7];
    expectInfo.prssi = prssi.intValue;
    NSString *signalLevel = parameters[8];
    expectInfo.signalLevel = signalLevel.intValue;
    NSString *ilostRatio = parameters[9];
    expectInfo.ilostRatio = ilostRatio.intValue;
    NSString *its = parameters[10];
    expectInfo.its = its.intValue;
    expectInfo.lanIP = parameters[11];
    expectInfo.moduleVersion = parameters[12];
    return expectInfo;
}

- (void)assertResult:(NSString *)result
{
    BOOL kResult = NO;
    if ([result isEqualToString:@"Success"] || [result isEqualToString:@"true"]) {
        kResult = YES;
    }
    CCIAssert(self.mCallbackResult.isSuccessful == kResult, [NSString stringWithFormat:@"回调结果期望不一致,期望为%d,实际为%d", kResult, self.mCallbackResult.isSuccessful]);
}

- (void)assertSelectorCountWithArgs:(NSArray *)args selector:(SEL)selector
{
    NSString *deviceId = args[0];
    NSString *invocationTimes = args[1];
    uSDKDevice *kuSDKDevice = [self.mUSDKDeviceManager getDeviceWithID:deviceId];
    FakeUSDKDevice *kFakeUSDKDevice = (FakeUSDKDevice *)kuSDKDevice;
    NSUInteger expectedInvocationTimes = invocationTimes.integerValue;
    NSUInteger actualTimes = [kFakeUSDKDevice getInvocationTimesOfSelector:selector];
    CCIAssert(actualTimes == expectedInvocationTimes, @"执行结果与实际不一致,调用次数应该为:%ld,实际为:%ld", expectedInvocationTimes, actualTimes);
}
- (void)assertSelectorCountAndParamsWithArgs:(NSArray *)args selector:(SEL)selector
{
    NSString *deviceId = args[0];
    NSString *invocationTimes = args[1];
    NSString *kParameter = args[2];
    if ([kParameter isEqualToString:@""]) {
        kParameter = nil;
    }
    uSDKDevice *kuSDKDevice = [self.mUSDKDeviceManager getDeviceWithID:deviceId];
    FakeUSDKDevice *kFakeUSDKDevice = (FakeUSDKDevice *)kuSDKDevice;
    NSUInteger expectedInvocationTimes = invocationTimes.integerValue;
    NSUInteger actualTimes = [kFakeUSDKDevice getInvocationTimesOfSelector:selector];
    CCIAssert(actualTimes == expectedInvocationTimes, @"执行结果与实际不一致,调用次数应该为:%ld,实际为:%ld", expectedInvocationTimes, actualTimes);
    NSString *kActualParameter = [kFakeUSDKDevice getParametersOfSelector:selector].firstObject;
    if (kParameter && kActualParameter) {
        CCIAssert([kActualParameter isEqualToString:kParameter], @"执行结果与实际不一致");
    }
}
- (void)assertSelectorHandleDeviceWithArgs:(NSArray *)args selector:(SEL)selector
{
    NSString *deviceId = args[0];
    NSString *invocationTimes = args[1];
    NSString *kParameter = args[2];
    if ([kParameter isEqualToString:@""]) {
        kParameter = nil;
    }
    uSDKDevice *kuSDKDevice = [self.mUSDKDeviceManager getDeviceWithID:deviceId];
    FakeUSDKDevice *kFakeUSDKDevice = (FakeUSDKDevice *)kuSDKDevice;
    NSUInteger expectedInvocationTimes = invocationTimes.integerValue;
    NSUInteger actualTimes = [kFakeUSDKDevice getInvocationTimesOfSelector:selector];
    CCIAssert(actualTimes == expectedInvocationTimes, @"执行结果与实际不一致,调用次数应该为:%ld,实际为:%ld", expectedInvocationTimes, actualTimes);
    NSArray *kActualParameterArr = [kFakeUSDKDevice getParametersOfSelector:selector].firstObject;
    NSMutableArray *deviceIds = [NSMutableArray array];
    for (uSDKDevice *device in kActualParameterArr) {
        [deviceIds addObject:device.deviceID];
    }
    NSString *kActualParameter = [deviceIds componentsJoinedByString:@","];
    if (kParameter && kActualParameter.length > 0) {
        CCIAssert([kActualParameter isEqualToString:kParameter], @"执行结果与实际不一致");
    }
}
@end
