//
//  DeviceBaseSteps.m
//  UPDeviceTests
//
//  Created by <PERSON><PERSON><PERSON><PERSON> on 2020/12/24.
//  Copyright © 2020 海尔优家智能科技（北京）有限公司. All rights reserved.
//

#import "DeviceBaseSteps.h"
#import <Cucumberish/Cucumberish.h>
#import <OCMock/OCMock.h>
#import "InitializationSteps.h"
#import "UpDeviceInfo.h"
#import <LogicEngine/LEEngine.h>
#import "DeviceBaseInfo.h"
#import "DeviceInfo.h"
#import "StepsUtils.h"
#import "UpDeviceBase.h"
#import "UpDeviceReporter.h"
#import "UpDeviceCarrier.h"
#import "DeviceFOTAStatusInfo.h"
#import "DeviceBLEHistoryInfo.h"
#import <LogicEngine/LECommonFuncs.h>
#import "FakeUpDeviceListener.h"
#import "FakeUpDeviceReceiver.h"
#import "FakeUpDeviceBase.h"

typedef void (^ConnectRemoteDeviceResult)(UpDeviceResult *result);

@interface DeviceBaseSteps ()

@property (nonatomic, strong) UpDeviceResult *mUpDeviceResult;

@property (nonatomic, strong) id<UpDeviceInfo> mUpSubDeviceInfo;

@property (nonatomic, strong) NSMutableDictionary *mNotifyEventListenerList;

@end

@implementation DeviceBaseSteps
- (void)defineStepsAndHocks
{
    before(^(CCIScenarioDefinition *scenario) {
      self.mUpDeviceResult = NULL;
      self.mUpSubDeviceInfo = NULL;
      self.mNotifyEventListenerList = NULL;
      self.mNotifyEventListenerList = [NSMutableDictionary dictionaryWithCapacity:0];
    });

    When(@"^订阅者\"([^\"]*)\"订阅设备\"([^\"]*)\"的设备状态$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      NSString *kParameter = args[0];
      NSString *kDeviceId = args[1];
      FakeUpDeviceListener *kFakeUpDeviceListener = [[FakeUpDeviceListener alloc] init];
      [self.mNotifyEventListenerList setValue:kFakeUpDeviceListener forKey:kParameter];
      [[[StepsUtils sharedInstance].mUpDeviceList objectForKey:kDeviceId] attach:kFakeUpDeviceListener];
    });

    When(@"^设备\"([^\"]*)\"状态发生变化，具体属性变化列表如下$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      NSString *kDeviceId = args[0];
      NSArray<id<UpDeviceAttribute>> *kLEDeviceAttributeList = convertLEDeviceAttributeFromStepTableData(userInfo);
      [[[StepsUtils sharedInstance].mDefaultDeviceBroker getNotification] notifyAttributesChange:[StepsUtils sharedInstance].mUpDeviceInfo list:kLEDeviceAttributeList listeners:[NSArray arrayWithObject:[[StepsUtils sharedInstance].mUpDeviceList objectForKey:kDeviceId]]];
    });

    Then(@"^订阅者\"([^\"]*)\"收到设备\"([^\"]*)\"的事件如下$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      NSString *kParameter = args[0];
      FakeUpDeviceListener *kFakeUpDeviceListener = [self.mNotifyEventListenerList objectForKey:kParameter];
      NSArray<NSArray<NSString *> *> *kNotifyEventNameList = convertNotifyEventNameFromStepTableData(userInfo);
      NSMutableDictionary *kExpectSubscriberNotifyEventDictionary = [[StepsUtils sharedInstance] convertNotifyEventListToSubscriberDictionary:kNotifyEventNameList];
      NSMutableDictionary *kActualSubscriberNotifyEventDictionary = kFakeUpDeviceListener.mNotifyEventDictionary;
      //Android no EVENT_ATTACHED event
      [kActualSubscriberNotifyEventDictionary removeObjectForKey:@"EVENT_ATTACHED"];
      BOOL kCheckResult = [[StepsUtils sharedInstance] isEqualNotifyEvent:kActualSubscriberNotifyEventDictionary expect:kExpectSubscriberNotifyEventDictionary];
      CCIAssert(kCheckResult == YES, @"执行结果与实际不一致");
    });

    Then(@"^使用者查询设备\"([^\"]*)\"属性列表如下$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      NSString *kDeviceId = args[0];
      NSArray<id<UpDeviceAttribute>> *kLEDeviceAttributeList = convertLEDeviceAttributeFromStepTableData(userInfo);
      NSArray<id<UpDeviceAttribute>> *kActualUpDeviceAttributeList = [[[StepsUtils sharedInstance].mUpDeviceList objectForKey:kDeviceId] getAttributeList];
      CCIAssert([[StepsUtils sharedInstance] isEqualUpDeviceAttributeList:kActualUpDeviceAttributeList expectList:kLEDeviceAttributeList], @"执行结果与实际不一致");
    });

    When(@"^设备\"([^\"]*)\"状态发生变化,具体报警变化列表如下$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      NSString *kDeviceId = args[0];
      NSArray<id<UpDeviceCaution>> *kUpDeviceCautionList = convertUpDeviceCautionFromStepTableData(userInfo);
      [[[StepsUtils sharedInstance].mDefaultDeviceBroker getNotification] notifyDeviceCaution:[StepsUtils sharedInstance].mUpDeviceInfo list:kUpDeviceCautionList listeners:[NSArray arrayWithObject:[[StepsUtils sharedInstance].mUpDeviceList objectForKey:kDeviceId]]];
    });

    Then(@"^使用者查询设备\"([^\"]*)\"报警列表如下$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      NSString *kDeviceId = args[0];
      NSArray<id<UpDeviceCaution>> *kExpectUpDeviceCautionList = convertUpDeviceCautionFromStepTableData(userInfo);
      NSArray<id<UpDeviceCaution>> *kActualUpDeviceCautionList = [[[StepsUtils sharedInstance].mUpDeviceList objectForKey:kDeviceId] getCautionList];
      CCIAssert([[StepsUtils sharedInstance] isEqualUpDeviceCautionList:kActualUpDeviceCautionList expectList:kExpectUpDeviceCautionList], @"执行结果与实际不一致");
    });

    When(@"^设备\"([^\"]*)\"状态发生变化,具体连接状态变为\"([^\"]*)\"$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      NSString *kDeviceId = args[0];
      NSString *kConnectionStatus = args[1];
      int kConnectionStatusEnum = UpDeviceConnection_OFFLINE;

      if ([kConnectionStatus isEqualToString:@"CONNECTING"]) {
          kConnectionStatusEnum = UpDeviceConnection_CONNECTING;
      }
      else if ([kConnectionStatus isEqualToString:@"READY"]) {
          kConnectionStatusEnum = UpDeviceConnection_READY;
      }
      else if ([kConnectionStatus isEqualToString:@"OFFLINE"]) {
          kConnectionStatusEnum = UpDeviceConnection_OFFLINE;
      }
      [[[StepsUtils sharedInstance].mDefaultDeviceBroker getNotification] notifyConnectionChange:[StepsUtils sharedInstance].mUpDeviceInfo connection:kConnectionStatusEnum listeners:[NSArray arrayWithObject:[[StepsUtils sharedInstance].mUpDeviceList objectForKey:kDeviceId]]];
    });

    Then(@"^使用者查询设备\"([^\"]*)\"连接状态为\"([^\"]*)\"$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      NSString *kDeviceId = args[0];
      NSString *kParameter = args[1];
      UpDeviceConnection kExpectUpDeviceConnection = UpDeviceConnection_OFFLINE;
      if ([kParameter isEqualToString:@"CONNECTING"]) {
          kExpectUpDeviceConnection = UpDeviceConnection_CONNECTING;
      }
      else if ([kParameter isEqualToString:@"READY"]) {
          kExpectUpDeviceConnection = UpDeviceConnection_READY;
      }
      UpDeviceConnection kUpDeviceConnection = [[[StepsUtils sharedInstance].mUpDeviceList objectForKey:kDeviceId] getConnection];
      CCIAssert(kExpectUpDeviceConnection == kUpDeviceConnection, @"执行结果与实际不一致");
    });

    When(@"^设备\"([^\"]*)\"状态发生变化,具体子设备变化列表如下$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      NSString *kDeviceId = args[0];
      NSArray<id<UpDeviceInfo>> *kUpDeviceInfoList = convertUpDeviceInfoFromStepTableData(userInfo);
      [[[StepsUtils sharedInstance].mDefaultDeviceBroker getNotification] notifySubDevListChange:[StepsUtils sharedInstance].mUpDeviceInfo list:kUpDeviceInfoList listeners:[NSArray arrayWithObject:[[StepsUtils sharedInstance].mUpDeviceList objectForKey:kDeviceId]]];
    });

    Then(@"^使用者查询设备\"([^\"]*)\"子设备列表如下$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      NSString *kDeviceId = args[0];
      NSArray<id<UpDeviceInfo>> *kExpectUpDeviceInfoList = convertUpDeviceInfoFromStepTableData(userInfo);
      //self.connectionRef != UpDeviceConnection_READY,there is a test case just for ios
      NSArray *kSubDeviceList = [[[StepsUtils sharedInstance].mUpDeviceList objectForKey:kDeviceId] getSubDevList];
      if (kSubDeviceList.count > 0) {
          NSArray<id<UpDeviceInfo>> *kActualUpDeviceInfoList = convertUpDeviceInfoListFromUpDeviceList(kSubDeviceList);
          CCIAssert(isEqualDeviceList(kActualUpDeviceInfoList, kExpectUpDeviceInfoList), @"执行结果与实际不一致");
      }
    });

    When(@"^设备\"([^\"]*)\"状态发生变化,具体设备基本信息变化为$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      NSString *kDeviceId = args[0];
      NSArray<id<UpDeviceInfo>> *kExpectUpDeviceInfoList = convertUpDeviceInfoFromStepTableData(userInfo);
      id<UpDeviceInfo> kUpDeviceInfo = kExpectUpDeviceInfoList[0];
      [StepsUtils sharedInstance].mUpDeviceInfo = kUpDeviceInfo;
      [[[StepsUtils sharedInstance].mDefaultDeviceBroker getNotification] notifyDeviceInfoChange:kUpDeviceInfo listeners:[NSArray arrayWithObject:[[StepsUtils sharedInstance].mUpDeviceList objectForKey:kDeviceId]]];
    });

    Then(@"^使用者查询设备\"([^\"]*)\"基本信息如下$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      NSString *kDeviceId = args[0];
      NSArray<id<UpDeviceInfo>> *kExpectUpDeviceInfoList = convertUpDeviceInfoFromStepTableData(userInfo);
      id<UpDeviceInfo> kExpectUpDeviceInfo = kExpectUpDeviceInfoList[0];
      id<UpDeviceInfo> kUpDeviceInfo = [[[StepsUtils sharedInstance].mUpDeviceList objectForKey:kDeviceId] getInfo];
      CCIAssert([[kUpDeviceInfo deviceId] isEqualToString:kExpectUpDeviceInfo.deviceId] && [[kUpDeviceInfo subDevNo] isEqualToString:kExpectUpDeviceInfo.subDevNo] && [[kUpDeviceInfo parentId] isEqualToString:kExpectUpDeviceInfo.parentId], @"执行结果与实际不一致");
    });

    When(@"^设备\"([^\"]*)\"的Fota状态发生变化$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      NSString *kDeviceId = args[0];
      DeviceFOTAStatusInfo *kDeviceFOTAStatusInfo = [DeviceFOTAStatusInfo DeviceFOTAStatusInfo:20 upgradeErrInfo:nil];
      [[[StepsUtils sharedInstance].mDefaultDeviceBroker getNotification] notifyUpdateBoardFOTAStatus:[StepsUtils sharedInstance].mUpDeviceInfo FOTAStatusInfo:kDeviceFOTAStatusInfo listeners:[NSMutableArray arrayWithObject:[[StepsUtils sharedInstance].mUpDeviceList objectForKey:kDeviceId]]];
    });

    When(@"^设备\"([^\"]*)\"状态发生变化,具体蓝牙数据变化为\"([^\"]*)\"$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      NSString *kDeviceId = args[0];
      NSString *kBLEParameter = args[1];
      NSData *kBLEData = [kBLEParameter dataUsingEncoding:NSUTF8StringEncoding];
      [[[StepsUtils sharedInstance].mDefaultDeviceBroker getNotification] notifyBLERealTimeData:[StepsUtils sharedInstance].mUpDeviceInfo BLERealTimeData:kBLEData listeners:[NSMutableArray arrayWithObject:[[StepsUtils sharedInstance].mUpDeviceList objectForKey:kDeviceId]]];
    });

    Then(@"^使用者查询设备\"([^\"]*)\"蓝牙数据为\"([^\"]*)\"$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      NSString *kDeviceId = args[0];
      NSString *kExpectBLERealData = args[1];
      NSData *kBLEData = [[[StepsUtils sharedInstance].mUpDeviceList objectForKey:kDeviceId] getDeviceBLERealData];
      NSString *kActualBLERealData = [[NSString alloc] initWithData:kBLEData encoding:NSUTF8StringEncoding];
      CCIAssert([kExpectBLERealData isEqualToString:kActualBLERealData], @"执行结果与实际不一致");
    });

    When(@"^设备\"([^\"]*)\"状态发生变化,具体蓝牙历史变化如下$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      NSString *kDeviceId = args[0];
      DeviceBLEHistoryInfo *kDeviceBLEHistoryInfo = convertDeviceBLEHistoryInfoFromStepTableData(userInfo);
      [[[StepsUtils sharedInstance].mDefaultDeviceBroker getNotification] notifyBLEHistoryData:[StepsUtils sharedInstance].mUpDeviceInfo BLEHistoryInfo:kDeviceBLEHistoryInfo listeners:[NSMutableArray arrayWithObject:[[StepsUtils sharedInstance].mUpDeviceList objectForKey:kDeviceId]]];
    });

    Then(@"^使用者查询设备\"([^\"]*)\"蓝牙历史数据为$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      NSString *kDeviceId = args[0];
      DeviceBLEHistoryInfo *kExpectDeviceBLEHistoryInfo = convertDeviceBLEHistoryInfoFromStepTableData(userInfo);
      DeviceBLEHistoryInfo *kActualDeviceBLEHistoryInfo = [[[StepsUtils sharedInstance].mUpDeviceList objectForKey:kDeviceId] getDeviceBLEHistoryInfo];
      CCIAssert([kActualDeviceBLEHistoryInfo.data isEqual:kExpectDeviceBLEHistoryInfo.data] && kActualDeviceBLEHistoryInfo.totalCount == kExpectDeviceBLEHistoryInfo.totalCount && kActualDeviceBLEHistoryInfo.currentCount == kExpectDeviceBLEHistoryInfo.currentCount, @"执行结果与实际不一致");
    });

    When(@"^订阅者\"([^\"]*)\"订阅设备\"([^\"]*)\"的数据$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      NSString *kParameter = args[0];
      NSString *kDeviceId = args[1];
      FakeUpDeviceReceiver *kFakeUpDeviceReceiver = [[FakeUpDeviceReceiver alloc] init];
      [self.mNotifyEventListenerList setValue:kFakeUpDeviceReceiver forKey:kParameter];
      [[[StepsUtils sharedInstance].mUpDeviceList objectForKey:kDeviceId] attachReceiver:kFakeUpDeviceReceiver];
    });

    When(@"^设备\"([^\"]*)\"状态发生变化,具体资源数据如下$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      NSString *kDeviceId = args[0];
      NSArray<NSArray<NSString *> *> *infoList = getTableDataListFromExpectUserInfo(userInfo);
      NSArray<NSString *> *deviceInfoItemList = infoList.firstObject;
      [[[StepsUtils sharedInstance].mDefaultDeviceBroker getNotification] notifyDeviceReceive:[StepsUtils sharedInstance].mUpDeviceInfo name:deviceInfoItemList[0] data:deviceInfoItemList[1] listeners:[NSMutableArray arrayWithObject:[[StepsUtils sharedInstance].mUpDeviceList objectForKey:kDeviceId]]];
    });

    Then(@"^订阅者\"([^\"]*)\"收到设备\"([^\"]*)\"资源如下$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      NSString *kParameter = args[0];
      NSArray<NSArray<NSString *> *> *infoList = getTableDataListFromExpectUserInfo(userInfo);
      NSArray<NSString *> *deviceInfoItemList = infoList.firstObject;
      FakeUpDeviceReceiver *kFakeUpDeviceReceiver = [self.mNotifyEventListenerList objectForKey:kParameter];
      CCIAssert([deviceInfoItemList[0] isEqualToString:kFakeUpDeviceReceiver.mReceiverDataName] && [deviceInfoItemList[1] isEqualToString:(NSString *)kFakeUpDeviceReceiver.mReceiverData], @"执行结果与实际不一致");
    });

    Given(@"^Toolkit的连接设备返回\"([^\"]*)\"结果$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      //add deviceid parameter to judge device
      NSString *kResult = args[0];
      BOOL isSuccessful = NO;
      int errCode = ErrorCode_FAILURE;
      NSString *errDomain = @"failure";
      if ([kResult isEqualToString:@"成功"]) {
          isSuccessful = YES;
          errCode = 0;
          errDomain = @"success";
      }
      [(FakeUpDeviceBase *)[[StepsUtils sharedInstance].mUpDeviceList objectForKey:[StepsUtils sharedInstance].mUpDeviceInfo.deviceId] mockPrepareExtApiResult:isSuccessful];
      UpDeviceResult *kUpDeviceResult = [UpDeviceResult UpDeviceResult:errCode extraData:nil];
      OCMStub([[[StepsUtils sharedInstance].mDefaultDeviceBroker getToolkit] attachDevice:[StepsUtils sharedInstance].mUpDeviceInfo.deviceId reportListener:[OCMArg any] finishBlock:[OCMArg any]]).andDo(^(NSInvocation *invocation) { /* block that handles the method invocation */
                                                                                                                                                                                                                                        [invocation retainArguments];
                                                                                                                                                                                                                                        void *successBlockPointer;
                                                                                                                                                                                                                                        [invocation getArgument:&successBlockPointer atIndex:4];
                                                                                                                                                                                                                                        ConnectRemoteDeviceResult successBlock = (__bridge ConnectRemoteDeviceResult)successBlockPointer;
                                                                                                                                                                                                                                        successBlock(kUpDeviceResult);
      });
    });

    Given(@"^Toolkit的获取设备连接状态返回\"([^\"]*)\"$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      NSString *kResult = args[0];
      NSString *kDeviceId = [StepsUtils sharedInstance].mUpDeviceInfo.deviceId;
      UpDeviceConnection kUpDeviceConnection = UpDeviceConnection_OFFLINE;
      int errCode = ErrorCode_SUCCESS;
      if ([kResult isEqualToString:@"OFFLINE"]) {
          kUpDeviceConnection = UpDeviceConnection_OFFLINE;
          errCode = ErrorCode_FAILURE;
      }
      else if ([kResult isEqualToString:@"READY"]) {
          kUpDeviceConnection = UpDeviceConnection_READY;
      }
      UpDeviceResult *kUpDeviceResult = [UpDeviceResult UpDeviceResult:errCode extraData:[NSNumber numberWithInt:kUpDeviceConnection]];
      OCMStub([[[StepsUtils sharedInstance].mDefaultDeviceBroker getToolkit] getDeviceConnection:kDeviceId finishBlock:[OCMArg any]]).andDo(^(NSInvocation *invocation) { /* block that handles the method invocation */
                                                                                                                                                                          [invocation retainArguments];
                                                                                                                                                                          void *successBlockPointer;
                                                                                                                                                                          [invocation getArgument:&successBlockPointer atIndex:3];
                                                                                                                                                                          ConnectRemoteDeviceResult successBlock = (__bridge ConnectRemoteDeviceResult)successBlockPointer;
                                                                                                                                                                          successBlock(kUpDeviceResult);
      });
    });

    Given(@"^Toolkit的获取设备基础信息返回数据如下$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      NSArray<id<UpDeviceInfo>> *kExpectUpDeviceInfoList = convertUpDeviceInfoFromStepTableData(userInfo);
      id<UpDeviceInfo> kUpDeviceInfo;
      if (kExpectUpDeviceInfoList.count == 0) {
          kUpDeviceInfo = nil;
      }
      else {
          kUpDeviceInfo = kExpectUpDeviceInfoList[0];
      }
      int errCode = ErrorCode_SUCCESS;
      UpDeviceResult *kUpDeviceResult = [UpDeviceResult UpDeviceResult:errCode extraData:kUpDeviceInfo];
      OCMStub([[[StepsUtils sharedInstance].mDefaultDeviceBroker getToolkit] getDeviceBaseInfo:[OCMArg any] finishBlock:[OCMArg any]]).andDo(^(NSInvocation *invocation) { /* block that handles the method invocation */
                                                                                                                                                                           [invocation retainArguments];
                                                                                                                                                                           void *successBlockPointer;
                                                                                                                                                                           [invocation getArgument:&successBlockPointer atIndex:3];
                                                                                                                                                                           ConnectRemoteDeviceResult successBlock = (__bridge ConnectRemoteDeviceResult)successBlockPointer;
                                                                                                                                                                           successBlock(kUpDeviceResult);
      });
    });

    Given(@"^Toolkit的获取设备属性列表返回数据如下$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      NSArray<id<UpDeviceAttribute>> *kLEDeviceAttributeList = convertLEDeviceAttributeFromStepTableData(userInfo);
      int errCode = ErrorCode_SUCCESS;
      UpDeviceResult *kUpDeviceResult = [UpDeviceResult UpDeviceResult:errCode extraData:kLEDeviceAttributeList];
      OCMStub([[[StepsUtils sharedInstance].mDefaultDeviceBroker getToolkit] getDeviceAttributeList:[StepsUtils sharedInstance].mUpDeviceInfo.deviceId finishBlock:[OCMArg any]]).andDo(^(NSInvocation *invocation) { /* block that handles the method invocation */
                                                                                                                                                                                                                      [invocation retainArguments];
                                                                                                                                                                                                                      void *successBlockPointer;
                                                                                                                                                                                                                      [invocation getArgument:&successBlockPointer atIndex:3];
                                                                                                                                                                                                                      ConnectRemoteDeviceResult successBlock = (__bridge ConnectRemoteDeviceResult)successBlockPointer;
                                                                                                                                                                                                                      successBlock(kUpDeviceResult);
      });
    });

    Given(@"^Toolkit的获取设备报警列表返回数据如下$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      NSArray<id<UpDeviceCaution>> *kUpDeviceCautionList = convertUpDeviceCautionFromStepTableData(userInfo);
      int errCode = ErrorCode_SUCCESS;
      UpDeviceResult *kUpDeviceResult = [UpDeviceResult UpDeviceResult:errCode extraData:kUpDeviceCautionList];
      OCMStub([[[StepsUtils sharedInstance].mDefaultDeviceBroker getToolkit] getDeviceCautionList:[StepsUtils sharedInstance].mUpDeviceInfo.deviceId finishBlock:[OCMArg any]]).andDo(^(NSInvocation *invocation) { /* block that handles the method invocation */
                                                                                                                                                                                                                    [invocation retainArguments];
                                                                                                                                                                                                                    void *successBlockPointer;
                                                                                                                                                                                                                    [invocation getArgument:&successBlockPointer atIndex:3];
                                                                                                                                                                                                                    ConnectRemoteDeviceResult successBlock = (__bridge ConnectRemoteDeviceResult)successBlockPointer;
                                                                                                                                                                                                                    successBlock(kUpDeviceResult);
      });
    });

    Given(@"^Toolkit的获取子设备列表返回数据如下$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      NSArray<id<UpDeviceInfo>> *kExpectUpDeviceInfoList = convertUpDeviceInfoFromStepTableData(userInfo);
      self.mUpSubDeviceInfo = kExpectUpDeviceInfoList[0];
      int errCode = ErrorCode_SUCCESS;
      UpDeviceResult *kUpDeviceResult = [UpDeviceResult UpDeviceResult:errCode extraData:[NSMutableArray arrayWithObject:self.mUpSubDeviceInfo]];
      OCMStub([[[StepsUtils sharedInstance].mDefaultDeviceBroker getToolkit] getSubDevBaseInfoList:[StepsUtils sharedInstance].mUpDeviceInfo.subDevNo finishBlock:[OCMArg any]]).andDo(^(NSInvocation *invocation) { /* block that handles the method invocation */
                                                                                                                                                                                                                     [invocation retainArguments];
                                                                                                                                                                                                                     void *successBlockPointer;
                                                                                                                                                                                                                     [invocation getArgument:&successBlockPointer atIndex:3];
                                                                                                                                                                                                                     ConnectRemoteDeviceResult successBlock = (__bridge ConnectRemoteDeviceResult)successBlockPointer;
                                                                                                                                                                                                                     successBlock(kUpDeviceResult);
      });
    });

    When(@"^使用者调用设备\"([^\"]*)\"的准备接口$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      //getSubDevBaseInfoList is synchronized with dispatch group,should excute finishblock,otherwise thread is block
      NSString *kDeviceId = args[0];
      UpDeviceResult *kUpDeviceResult = [UpDeviceResult UpDeviceResult:0 extraData:[NSMutableArray arrayWithObject:self.mUpSubDeviceInfo]];
      OCMStub([[[StepsUtils sharedInstance].mDefaultDeviceBroker getToolkit] getSubDevBaseInfoList:[OCMArg any] finishBlock:[OCMArg any]]).andDo(^(NSInvocation *invocation) { /* block that handles the method invocation */
                                                                                                                                                                               [invocation retainArguments];
                                                                                                                                                                               void *successBlockPointer;
                                                                                                                                                                               [invocation getArgument:&successBlockPointer atIndex:3];
                                                                                                                                                                               ConnectRemoteDeviceResult successBlock = (__bridge ConnectRemoteDeviceResult)successBlockPointer;
                                                                                                                                                                               successBlock(kUpDeviceResult);
      });
      [[[StepsUtils sharedInstance].mUpDeviceList objectForKey:kDeviceId] prepare:^(UpDeviceResult *result) {
        NSLog(@"prepare | errorCode %ld", (long)result.errorCode);
      }];
    });

    Then(@"^使用者收到设备\"([^\"]*)\"的准备接口的结果为\"([^\"]*)\"$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      NSString *kDeviceId = args[0];
      NSString *kResult = args[1];
      BOOL kIsSuccessful = NO;
      if ([kResult isEqualToString:@"成功"]) {
          kIsSuccessful = YES;
      }
      CCIAssert(kIsSuccessful == [[[StepsUtils sharedInstance].mUpDeviceList objectForKey:kDeviceId] isReady], @"执行结果与实际不一致");
    });

    Then(@"^Toolkit的获取设备基础信息接口调用\"([^\"]*)\"次,设备id为\"([^\"]*)\"$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      NSString *kInvocationTimes = args[0];
      NSString *kDeviceId = args[1];
      int kTimes = kInvocationTimes.intValue;
      OCMVerify(times(kTimes), [[[StepsUtils sharedInstance].mDefaultDeviceBroker getToolkit] getDeviceBaseInfo:kDeviceId finishBlock:[OCMArg any]]);
    });

    Then(@"^Toolkit的获取设备连接状态接口调用\"([^\"]*)\"次,设备id为\"([^\"]*)\"$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      NSString *kInvocationTimes = args[0];
      NSString *kDeviceId = args[1];
      int kTimes = kInvocationTimes.intValue;
      OCMVerify(times(kTimes), [[[StepsUtils sharedInstance].mDefaultDeviceBroker getToolkit] getDeviceConnection:kDeviceId finishBlock:[OCMArg any]]);
    });

    Then(@"^Toolkit的获取子设备列表接口调用\"([^\"]*)\"次,设备id为\"([^\"]*)\"$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      NSString *kInvocationTimes = args[0];
      NSString *kDeviceId = args[1];
      int kTimes = kInvocationTimes.intValue;
      OCMVerify(times(kTimes), [[[StepsUtils sharedInstance].mDefaultDeviceBroker getToolkit] getSubDevBaseInfoList:kDeviceId finishBlock:[OCMArg any]]);
    });

    Then(@"^Toolkit的获取设备属性列表接口调用\"([^\"]*)\"次,设备id为\"([^\"]*)\"$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      NSString *kInvocationTimes = args[0];
      NSString *kDeviceId = args[1];
      int kTimes = kInvocationTimes.intValue;
      OCMVerify(times(kTimes), [[[StepsUtils sharedInstance].mDefaultDeviceBroker getToolkit] getDeviceAttributeList:kDeviceId finishBlock:[OCMArg any]]);
    });

    Then(@"^Toolkit的获取设备报警列表接口调用\"([^\"]*)\"次,设备id为\"([^\"]*)\"$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      NSString *kInvocationTimes = args[0];
      NSString *kDeviceId = args[1];
      int kTimes = kInvocationTimes.intValue;
      OCMVerify(times(kTimes), [[[StepsUtils sharedInstance].mDefaultDeviceBroker getToolkit] getDeviceCautionList:kDeviceId finishBlock:[OCMArg any]]);
    });
}
@end
