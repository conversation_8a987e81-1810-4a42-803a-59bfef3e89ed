//
//  UpDeviceResourceSteps.m
//  UPDeviceTests
//
//  Created by 王杰 on 2022/12/15.
//  Copyright © 2022 海尔优家智能科技（北京）有限公司. All rights reserved.
//

#import "UpDeviceResourceSteps.h"
#import "UpDeviceResourceManager.h"
#import <UPResource/UPResourceInjection.h>
#import "UpDeviceInjection.h"
#import <OCMock/OCMock.h>
#import <Cucumberish/Cucumberish.h>
#import "DeviceBaseInfo.h"
#import "UpDeviceBase.h"
#import "StepsUtils.h"
@interface UpDeviceResourceSteps ()
/**
 Description
 */
@property (nonatomic, strong) UpDeviceResourceManager *deviceResourceManager;
@property (nonatomic, strong) UPResourceManager *mockResourceManager;
@property (nonatomic, strong) UPResourceInfo *serverResource;
@property (nonatomic, strong) UpDeviceResult *appFuncModelResult;
@property (nonatomic, strong) UpDeviceManager *deviceManager;
@property (nonatomic, strong) NSMutableArray *pathArray;
@end
@implementation UpDeviceResourceSteps
- (void)defineStepsAndHocks
{
    before(^(CCIScenarioDefinition *scenario) {
      self.mockResourceManager = nil;
      self.appFuncModelResult = nil;
      self.deviceResourceManager = nil;
      self.appFuncModelResult = nil;
      if (self.pathArray) {
          for (NSString *path in self.pathArray) {
              [[NSFileManager defaultManager] removeItemAtPath:path error:nil];
          }
      }
    });
#pragma mark ——— Given
    Given(@"^\"([^\"]*)\"使用\"([^\"]*)\"$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      if ([args[0] isEqualToString:@"UpResource"]) {
          if ([args[1] isEqualToString:@"模拟的"]) {
              self.deviceResourceManager = [UpDeviceResourceManager getInstance];
              self.mockResourceManager = OCMClassMock([UPResourceManager class]);
              [self.deviceResourceManager setValue:self.mockResourceManager forKey:@"resourceManager"];
          }
          else if ([args[1] isEqualToString:@"空对象"]) {
              self.mockResourceManager = nil;
              self.deviceResourceManager = [UpDeviceResourceManager getInstance];
              [self.deviceResourceManager setValue:self.mockResourceManager forKey:@"resourceManager"];
          }
      }
      else if ([args[0] isEqualToString:@"UpDeviceManager"]) {
          if ([args[1] isEqualToString:@"模拟的"]) {
              self.deviceManager = OCMClassMock([UpDeviceManager class]);
              UpDeviceInjection *injection = [UpDeviceInjection getInstance];
              [injection setValue:self.deviceManager forKey:@"deviceManager"];
          }
          else if ([args[1] isEqualToString:@"空对象"]) {
              self.deviceManager = nil;
              UpDeviceInjection *injection = [UpDeviceInjection getInstance];
              [injection setValue:self.deviceManager forKey:@"deviceManager"];
          }
      }
    });

    Given(@"^本地路径\"([^\"]*)\"文件内容为\"([^\"]*)\"$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      NSString *conetent = args[1];
      if ([conetent isEqualToString:@"空字符串"]) {
          conetent = @"";
      }
      else if ([conetent isEqualToString:@"空对象"]) {
          conetent = nil;
      }
      NSString *path = [NSString stringWithFormat:@"%@/%@.json", NSSearchPathForDirectoriesInDomains(NSDocumentDirectory, NSUserDomainMask, YES)[0], args[0]];
      NSData *data = [conetent dataUsingEncoding:NSUTF8StringEncoding];
      [data writeToFile:path atomically:YES];
      if (!self.pathArray) {
          self.pathArray = [NSMutableArray new];
      }
      [self.pathArray addObject:path];
    });

    Given(@"^upDeviceManager中设备Id\"([^\"]*)\"的设备信息为:$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      NSString *deviceId = args[0];
      NSArray *list = getTableDataListFromExpectUserInfo(userInfo)[0];
      NSString *prodNo = list[1];
      if ([prodNo isEqualToString:@"空字符串"]) {
          prodNo = @"";
      }
      else if ([prodNo isEqualToString:@"空对象"]) {
          prodNo = nil;
      }
      DeviceBaseInfo *info = [DeviceBaseInfo DeviceBaseInfo:@"" DeviceId:deviceId TypeId:@"" TypeName:@"" typeCode:@"" Model:@"" ProdNo:prodNo ParentId:@"" SubDevNo:@""];
      DeviceInfo *deviceInfo = [DeviceInfo DeviceInfo:info];
      UpDeviceBase *device = [UpDeviceBase UpDeviceBase:deviceInfo broker:nil factory:nil];
      if (self.deviceManager) {
          OCMStub([self.deviceManager getDevice:deviceId]).andReturn(device);
      }


    });

    Given(@"^UpResource库根据prodNo:\"([^\"]*)\"查询本地设备资源,接口返回:$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      if (!self.mockResourceManager) {
          return;
      }
      NSString *json = getTableDataListFromExpectUserInfo(userInfo)[0][0];
      if (!json || json.length == 0) {
          OCMStub([self.mockResourceManager searchDeviceResList:[OCMArg checkWithBlock:^BOOL(UPResourceDeviceCondition *obj) {
                                              return [obj.prodNo isEqualToString:args[0]];
                                            }]])
              .andReturn(nil);
      }
      else if ([json isEqualToString:@"空对象"]) {
          OCMStub([self.mockResourceManager searchDeviceResList:[OCMArg checkWithBlock:^BOOL(UPResourceDeviceCondition *obj) {
                                              return [obj.prodNo isEqualToString:args[0]];
                                            }]])
              .andReturn(@[]);
      }
      else {
          NSDictionary *dict = jsonObjectFromEscapedString(json);
          UPResourceInfo *resource = OCMClassMock([UPResourceInfo class]);
          if ([dict[@"path"] isEqualToString:@""]) {
              OCMStub([resource path]).andReturn(dict[@"path"]);
          }
          else {
              NSString *path = [NSString stringWithFormat:@"%@/%@.json", NSSearchPathForDirectoriesInDomains(NSDocumentDirectory, NSUserDomainMask, YES)[0], dict[@"path"]];
              OCMStub([resource path]).andReturn(path);
          }
          OCMStub([resource active]).andReturn([dict[@"active"] boolValue]);
          OCMStub([self.mockResourceManager searchDeviceResList:[OCMArg checkWithBlock:^BOOL(UPResourceDeviceCondition *obj) {
                                              return [obj.prodNo isEqualToString:args[0]];
                                            }]])
              .andReturn(@[ resource ]);
      }

    });

    Given(@"^UpResource库根据prodNo:\"([^\"]*)\"获取资源接口,服务器查询结果回调返回:$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      if (!self.mockResourceManager) {
          return;
      }
      NSString *json = getTableDataListFromExpectUserInfo(userInfo)[0][1];
      NSString *successResult = getTableDataListFromExpectUserInfo(userInfo)[0][0];
      NSDictionary *dict = jsonObjectFromEscapedString(json);
      UPResourceInfo *resource = OCMClassMock([UPResourceInfo class]);
      if (!json || json.length == 0 || [json isEqualToString:@"空对象"]) {
          resource = nil;
      }
      else {
          if ([dict[@"path"] isEqualToString:@""]) {
              OCMStub([resource path]).andReturn(dict[@"path"]);
          }
          else {
              NSString *path = [NSString stringWithFormat:@"%@/%@.json", NSSearchPathForDirectoriesInDomains(NSDocumentDirectory, NSUserDomainMask, YES)[0], dict[@"path"]];
              OCMStub([resource path]).andReturn(path);
          }
          OCMStub([resource active]).andReturn([dict[@"active"] boolValue]);
      }

      self.serverResource = resource;
      OCMStub([self.mockResourceManager getDeviceResource:[OCMArg checkWithBlock:^BOOL(UPResourceDeviceCondition *obj) {
                                          return [obj.prodNo isEqualToString:args[0]];
                                        }]
                                                 selector:[OCMArg any]
                                                 callback:[OCMArg any]
                                                 listener:[OCMArg any]])
          .andDo(^(NSInvocation *invocation) {
            [invocation retainArguments];
            id<UPResourceCallback> callBack;
            [invocation retainArguments];
            [invocation getArgument:&callBack atIndex:4];
            [callBack onResult:[successResult isEqualToString:@"成功"] message:@"" resourceInfo:resource];

          });
    });


#pragma mark ——— when
    When(@"^用户调用获取设备侧应用模型接口,参数为\"([^\"]*)\"$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      NSString *deviceId = args[0];
      if ([deviceId isEqualToString:@"空字符串"]) {
          deviceId = @"";
      }
      else if ([deviceId isEqualToString:@"空对象"]) {
          deviceId = nil;
      }
      [self.deviceResourceManager getAppFuncModelWithDeviceId:deviceId
                                                  finishBlock:^(UpDeviceResult *result) {
                                                    self.appFuncModelResult = result;
                                                  }];

    });

#pragma mark ——— Then
    Then(@"^获取设备侧应用模型接口结果为\"([^\"]*)\",回调结果的extraData值为\"([^\"]*)\"$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      NSString *result = args[0];
      NSString *content = args[1];
      CCIAssert([result isEqualToString:@"成功"] == self.appFuncModelResult.isSuccessful, @"执行结果与实际不一致");
      if ([content isEqualToString:@""]) {
          content = nil;
      }
      if (content) {
          CCIAssert([self.appFuncModelResult.extraData isEqualToString:content], @"执行结果与实际不一致");
      }
      else {
          CCIAssert(!self.appFuncModelResult.extraData, @"执行结果与实际不一致");
      }
    });

    Then(@"^异步调用UpResource查询服务器更新资源文件被调用\"([^\"]*)\"次", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      OCMVerify(times(args[0].intValue), [self.mockResourceManager getDeviceResource:[OCMArg any] selector:[OCMArg any] callback:[OCMArg any] listener:[OCMArg any]]);
    });
}
@end
