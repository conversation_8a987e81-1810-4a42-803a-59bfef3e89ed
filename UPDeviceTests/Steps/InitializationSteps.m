//
//  InitializationSteps.m
//  UPDeviceTests
//
//  Created by <PERSON><PERSON><PERSON><PERSON> on 2020/12/24.
//  Copyright © 2020 海尔优家智能科技（北京）有限公司. All rights reserved.
//

#import "InitializationSteps.h"
#import "UpDeviceInfo.h"
#import "DeviceBaseInfo.h"
#import "DeviceInfo.h"
#import "StepsUtils.h"
#import <OCMock/OCMock.h>
#import <Cucumberish/Cucumberish.h>
#import "UpEngineDevice.h"
#import "FakeUpDeviceBase.h"
#import "UpDeviceBroker.h"
#import "UpDeviceToolkit.h"
#import "UpDeviceManager.h"
#import "UpDeviceDataSourceWrapper.h"

@interface InitializationSteps ()
@property (nonatomic, strong) UpDeviceManager *mUpDeviceManager;
@end

@implementation InitializationSteps
- (void)defineStepsAndHocks
{
    before(^(CCIScenarioDefinition *scenario) {
      [[StepsUtils sharedInstance] setValuesToDefault];
    });

    Given(@"Toolkit使用\"([^\"]*)\"$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      NSString *kParameter = args[0];
      if ([kParameter isEqualToString:@"模拟的"]) {
          WifiDeviceToolkitImpl *kToolkitHandler = OCMClassMock([WifiDeviceToolkitImpl class]);
          [StepsUtils sharedInstance].mDefaultDeviceBroker = [[DefaultDeviceBroker alloc] initWithKit:kToolkitHandler];
      }
      else if ([kParameter isEqualToString:@"参数是模拟的"]) {
          uSDKManager *kUSDKManager = OCMClassMock([uSDKManager class]);
          uSDKDeviceManager *kUSDKDeviceManager = OCMClassMock([uSDKDeviceManager class]);
          WifiDeviceToolkitImpl *kToolkitHandler = [WifiDeviceToolkitImpl WifiDeviceToolkitImpl:kUSDKManager DeviceManager:kUSDKDeviceManager];
          [StepsUtils sharedInstance].mDefaultDeviceBroker = [[DefaultDeviceBroker alloc] initWithKit:kToolkitHandler];
      }
      else {
          WifiDeviceToolkitImpl *kToolkitHandler = [WifiDeviceToolkitImpl WifiDeviceToolkitImpl:[uSDKManager defaultManager] DeviceManager:[uSDKDeviceManager defaultDeviceManager]];
          [StepsUtils sharedInstance].mDefaultDeviceBroker = [[DefaultDeviceBroker alloc] initWithKit:kToolkitHandler];
      }
    });

    Given(@"^资源管理器使用\"([^\"]*)\"$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      NSString *kParameter = args[0];
      if ([kParameter isEqualToString:@"模拟的"]) {
          [StepsUtils sharedInstance].mResourceManager = OCMPartialMock([FakeResourceManager new]);
      }
    });

    Given(@"^使用者创建\"([^\"]*)\"设备工厂$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      NSString *kParameter = args[0];
      if ([kParameter isEqualToString:@"逻辑引擎"]) {
          [StepsUtils sharedInstance].mEngineDeviceFactory = [UpEngineDeviceFactory EngineDeviceFactory:[StepsUtils sharedInstance].mResourceManager];
      }
      else {
      }
    });

    Given(@"^创建\"([^\"]*)\"设备,唯一标识ID为\"([^\"]*)\",设备信息如下:$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      NSString *kParameter = args[0];
      NSString *kUniqueId = convertUniqueIdFromStepTableData(userInfo);
      NSArray<id<UpDeviceInfo>> *kExpectUpDeviceInfoList = convertUpDeviceInfoFromStepTableData(userInfo);
      id<UpDeviceInfo> kUpDeviceInfo = kExpectUpDeviceInfoList[0];
      [StepsUtils sharedInstance].mUpDeviceInfo = kUpDeviceInfo;
      if (NULL == [StepsUtils sharedInstance].mUpDeviceList) {
          [StepsUtils sharedInstance].mUpDeviceList = [NSMutableDictionary dictionaryWithCapacity:0];
      }
      if ([kParameter isEqualToString:@"逻辑引擎"]) {
          UpEngineDevice *kUpEngineDevice = [[UpEngineDevice alloc] initEngineDeviceWithUniqueID:kUniqueId deviceInfo:[StepsUtils sharedInstance].mUpDeviceInfo broker:[StepsUtils sharedInstance].mDefaultDeviceBroker factory:[StepsUtils sharedInstance].mEngineDeviceFactory];
          [kUpEngineDevice setResManager:[StepsUtils sharedInstance].mResourceManager];
          [[StepsUtils sharedInstance].mUpDeviceList setValue:kUpEngineDevice forKey:kUpDeviceInfo.deviceId];
      }
      else if ([kParameter isEqualToString:@"测试"]) {
          FakeUpDeviceBase *kFakeUpDeviceBase = [[FakeUpDeviceBase alloc] initFakeDeviceWithUniqueID:kUniqueId deviceInfo:[StepsUtils sharedInstance].mUpDeviceInfo broker:[StepsUtils sharedInstance].mDefaultDeviceBroker factory:[StepsUtils sharedInstance].mEngineDeviceFactory];
          [[StepsUtils sharedInstance].mUpDeviceList setValue:kFakeUpDeviceBase forKey:kUpDeviceInfo.deviceId];
      }
      else {
          UpDeviceBase *kUpDeviceBase = [UpDeviceBase UpDeviceBase:[StepsUtils sharedInstance].mUpDeviceInfo broker:[StepsUtils sharedInstance].mDefaultDeviceBroker factory:[StepsUtils sharedInstance].mEngineDeviceFactory];
          [[StepsUtils sharedInstance].mUpDeviceList setValue:kUpDeviceBase forKey:kUpDeviceInfo.deviceId];
      }
    });

    When(@"^初始化设备管理器,参数设备工具为\"([^\"]*)\",设备数据源为\"([^\"]*)\"$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      NSString *kWifiToolkitParameter = args[0];
      NSString *kDataSourceParameter = args[1];
      UpDeviceDataSourceWrapper *kUpDeviceDataSourceWrapper = NULL;
      WifiDeviceToolkitImpl *kWifiDeviceToolkitImpl = NULL;
      if (![kWifiToolkitParameter isEqualToString:@"null"]) {
          kWifiDeviceToolkitImpl = OCMPartialMock([[WifiDeviceToolkitImpl alloc] init]);
      }
      if (![kDataSourceParameter isEqualToString:@"null"]) {
          id<UpDeviceDataSource> deviceSource = OCMProtocolMock(@protocol(UpDeviceDataSource));
          kUpDeviceDataSourceWrapper = [[UpDeviceDataSourceWrapper alloc] init];
          [kUpDeviceDataSourceWrapper setDataSource:deviceSource];
      }
      self.mUpDeviceManager = [[UpDeviceManager alloc] initDeviceManagerWithToolkit:kWifiDeviceToolkitImpl dataSource:kUpDeviceDataSourceWrapper];
    });

    Then(@"^初始化设备管理器结果为\"([^\"]*)\"$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      NSString *kResult = args[0];
      CCIAssert([kResult isEqualToString:@"success"] == (self.mUpDeviceManager != nil ? YES : NO), @"执行结果与实际不一致");
    });
}
@end
