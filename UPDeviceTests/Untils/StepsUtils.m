//
//  StepsUtils.m
//  UPDeviceTests
//
//  Created by <PERSON><PERSON><PERSON><PERSON> on 2020/12/24.
//  Copyright © 2020 海尔优家智能科技（北京）有限公司. All rights reserved.
//

#import "StepsUtils.h"
#import "UpDeviceBaseInfo.h"
#import "DeviceInfo.h"
#import <MJExtension/MJExtension.h>
#import "WifiDeviceToolkitImpl.h"
#import <LogicEngine/LECommonFuncs.h>
#import "UpDevice.h"
#import "UpDeviceBase.h"
#import "WifiDeviceToolkitImpl.h"
#import <LogicEngine/LECommonFuncs.h>
#import "UpDevice.h"
#import "UpDeviceBase.h"
#import "WifiDeviceToolkitImpl.h"
#import <LogicEngine/LECommonFuncs.h>
#import <LogicEngine/LEValueRange.h>

@implementation StepsUtils

+ (StepsUtils *)sharedInstance
{
    static StepsUtils *instance = nil;
    static dispatch_once_t onceToken;
    dispatch_once(&onceToken, ^{
      instance = [[StepsUtils alloc] init];
    });
    return instance;
}

- (void)setValuesToDefault
{
    self.mDefaultDeviceBroker = NULL;
    self.mResourceManager = NULL;
    self.mEngineDeviceFactory = NULL;
    self.mUniqueId = NULL;
    self.mUpDeviceInfo = NULL;
    self.mUpDeviceList = NULL;
}

NSArray<NSArray<NSString *> *> *getTableDataListFromExpectUserInfo(NSDictionary *userInfo)
{
    NSArray<NSArray<NSString *> *> *expect = userInfo[@"DataTable"];
    NSMutableArray *result = expect.mutableCopy;
    [result removeObjectAtIndex:0];
    return result;
}

NSArray<DeviceInfo *> *getDeviceListFromExpectUserInfo(NSDictionary *userInfo)
{
    NSArray *list = getTableDataListFromExpectUserInfo(userInfo);
    NSMutableArray *deviceList = [NSMutableArray array];
    for (NSArray *data in list) {
        if (3 == data.count) {
            DeviceBaseInfo *baseInfo = [DeviceBaseInfo DeviceBaseInfo:@"haier-usdk" DeviceId:data[0] TypeId:data[2] TypeName:data[1] typeCode:nil Model:nil ProdNo:nil ParentId:nil SubDevNo:nil];
            DeviceInfo *deviceInfo = [DeviceInfo DeviceInfo:baseInfo];
            [deviceList addObject:deviceInfo];
        }
        else {
            DeviceBaseInfo *baseInfo = [DeviceBaseInfo DeviceBaseInfo:data[0] DeviceId:data[1] TypeId:data[2] TypeName:data[3] typeCode:data[4] Model:data[5] ProdNo:data[6] ParentId:data[7] SubDevNo:data[8]];
            DeviceInfo *deviceInfo = [DeviceInfo DeviceInfo:baseInfo];
            NSData *jsonData = [data[9] dataUsingEncoding:NSUTF8StringEncoding];
            NSError *error;
            NSDictionary *extraDic = [NSJSONSerialization JSONObjectWithData:jsonData options:0 error:&error];
            if (!error) {
                [deviceInfo putExtras:extraDic];
            }
            [deviceList addObject:deviceInfo];
        }
    }
    return deviceList;
}
NSArray<DeviceBLEHistoryInfo *> *convertDeviceBLEHistoryInfoFromExpectUserInfo(NSDictionary *userInfo)
{
    NSArray *list = getTableDataListFromExpectUserInfo(userInfo);
    NSMutableArray *historyInfoList = [NSMutableArray array];
    for (NSArray *data in list) {
        NSUInteger currentCount = [data[0] intValue];
        NSUInteger totalCount = [data[1] intValue];
        NSData *historyData = [data[2] dataUsingEncoding:NSUTF8StringEncoding];
        DeviceBLEHistoryInfo *historyInfo = [DeviceBLEHistoryInfo DeviceBLEHistoryInfo:historyData currentCount:currentCount totalCount:totalCount];
        [historyInfoList addObject:historyInfo];
    }
    return historyInfoList;
}
NSArray<uSDKFOTAStatusInfo *> *convertuSDKFOTAStatusInfoFromExpectUserInfo(NSDictionary *userInfo)
{
    NSArray *list = getTableDataListFromExpectUserInfo(userInfo);
    NSMutableArray *FOTAStatusInfList = [NSMutableArray array];
    for (NSArray *data in list) {
        uSDKFOTAStatusInfo *expectedInfo = [uSDKFOTAStatusInfo new];
        NSString *upgradeStatus = data[0];
        expectedInfo.upgradeStatus = upgradeStatus.intValue;
        expectedInfo.upgradeErrorInfo = data[1];
        [FOTAStatusInfList addObject:expectedInfo];
    }
    return FOTAStatusInfList;
}
NSArray<DeviceInfo *> *getDeviceListFromExpectUserInfoWithTotalNumber(NSDictionary *userInfo, int total)
{
    NSArray *list = getTableDataListFromExpectUserInfo(userInfo);
    NSMutableArray *deviceList = [NSMutableArray array];
    if (deviceList.count < 0) {
        return nil;
    }
    NSArray *data = list[0];
    for (int i = 0; i < total; i++) {
        DeviceBaseInfo *baseInfo = [DeviceBaseInfo DeviceBaseInfo:data[0] DeviceId:[NSString stringWithFormat:@"%@%d", data[1], i] TypeId:[NSString stringWithFormat:@"%@%d", data[2], i] TypeName:[NSString stringWithFormat:@"%@%d", data[3], i] typeCode:[NSString stringWithFormat:@"%@%d", data[4], i] Model:[NSString stringWithFormat:@"%@%d", data[5], i] ProdNo:[NSString stringWithFormat:@"%@%d", data[6], i] ParentId:[NSString stringWithFormat:@"%@%d", data[7], i] SubDevNo:[NSString stringWithFormat:@"%@%d", data[8], i]];
        DeviceInfo *deviceInfo = [DeviceInfo DeviceInfo:baseInfo];
        NSData *jsonData = [data[9] dataUsingEncoding:NSUTF8StringEncoding];
        NSError *error;
        NSDictionary *extraDic = [NSJSONSerialization JSONObjectWithData:jsonData options:0 error:&error];
        if (!error) {
            [deviceInfo putExtras:extraDic];
        }
        [deviceList addObject:deviceInfo];
    }
    return deviceList;
}

id<UpDeviceInfo> convertUpDeviceFromStepTableData(NSDictionary *userInfo)
{
    NSArray<NSArray<NSString *> *> *infoList = getTableDataListFromExpectUserInfo(userInfo);
    NSArray<NSString *> *deviceItemList = infoList.firstObject;
    if (!deviceItemList) {
        return nil;
    }
    DeviceBaseInfo *kDeviceBaseInfo = [DeviceBaseInfo DeviceBaseInfo:deviceItemList[0] DeviceId:deviceItemList[1] TypeId:deviceItemList[2] TypeName:deviceItemList[3] typeCode:deviceItemList[4] Model:deviceItemList[5] ProdNo:deviceItemList[6] ParentId:deviceItemList[7] SubDevNo:deviceItemList[8]];

    DeviceInfo *kDeviceInfo = [DeviceInfo DeviceInfo:kDeviceBaseInfo];
    NSData *jsonData = [deviceItemList[9] dataUsingEncoding:NSUTF8StringEncoding];
    NSError *error;
    NSDictionary *extraDic = [NSJSONSerialization JSONObjectWithData:jsonData options:0 error:&error];
    if (!error) {
        [kDeviceInfo putExtras:extraDic];
    }
    return kDeviceInfo;
}
BOOL isEqualDevice(DeviceInfo *device1, DeviceInfo *device2)
{
    if (!device1 && !device2) {
        return YES;
    }
    if (!device1 || !device2) {
        return NO;
    }
    BOOL isEqual = NO;
    if ([device1.protocol isEqualToString:device2.protocol] && [device1.deviceId isEqualToString:device2.deviceId] && [device1.typeName isEqualToString:device2.typeName] && [device1.typeId isEqualToString:device2.typeId] && [device1.typeCode isEqualToString:device2.typeCode] && [device1.model isEqualToString:device2.model] && [device1.prodNo isEqualToString:device2.prodNo] && [device1.parentId isEqualToString:device2.parentId] && [device1.subDevNo isEqualToString:device2.subDevNo]) {
        isEqual = YES;
    }

    if (!isEqual) {
        if ([device1.protocol isEqualToString:device2.protocol] && [device1.deviceId isEqualToString:device2.deviceId] && [device1.typeName isEqualToString:device2.typeName] && [device1.typeId isEqualToString:device2.typeId]) {
            isEqual = YES;
        }
    }

    if (!isEqualExtras(device1, device2)) {
        isEqual = NO;
    }

    return isEqual;
}

BOOL isEqualDeviceList(NSArray<DeviceInfo *> *deviceList1, NSArray<DeviceInfo *> *deviceList2)
{
    if (0 == deviceList1.count && 0 == deviceList2.count) {
        return YES;
    }
    if (deviceList1.count != deviceList2.count) {
        return NO;
    }
    BOOL isEqual = YES;
    for (int i = 0; i < deviceList1.count; i++) {
        DeviceInfo *device1 = [deviceList1 objectAtIndex:i];
        DeviceInfo *device2 = [deviceList2 objectAtIndex:i];
        if (!isEqualDevice(device1, device2)) {
            isEqual = NO;
            break;
        }
    }
    return isEqual;
}

LECalcLogicResult *mockEngineCaculateResult(NSArray<LEDeviceAttribute *> *attributes, BOOL clean, BOOL isSuccessful, NSString *deviceId)
{
    LECalcLogicResult *kLECalcLogicResult = [LECalcLogicResult new];
    NSString *errInfo = [NSString stringWithFormat:@"设备（%@）的逻辑引擎当前状态不可执行操作！请稍后再试！", deviceId];
    kLECalcLogicResult.success = isSuccessful;
    if (!isSuccessful) {
        kLECalcLogicResult.errorDesc = errInfo;
    }

    return kLECalcLogicResult;
}

NSArray<id<UpDeviceInfo>> *convertUpDeviceInfoFromStepTableData(NSDictionary *userInfo)
{
    NSArray<NSArray<NSString *> *> *infoList = getTableDataListFromExpectUserInfo(userInfo);
    NSUInteger kNumCount = infoList.count;
    NSMutableArray *kUpDeviceInfoList = [NSMutableArray arrayWithCapacity:0];

    for (int index = 0; index < kNumCount; index++) {
        NSArray<NSString *> *deviceInfoItemList = infoList[index];
        DeviceBaseInfo *kDeviceBaseInfo = [DeviceBaseInfo DeviceBaseInfo:deviceInfoItemList[0] DeviceId:deviceInfoItemList[1] TypeId:deviceInfoItemList[2] TypeName:deviceInfoItemList[3] typeCode:deviceInfoItemList[4] Model:deviceInfoItemList[5] ProdNo:deviceInfoItemList[6] ParentId:deviceInfoItemList[7] SubDevNo:deviceInfoItemList[8]];

        DeviceInfo *kDeviceInfo = [DeviceInfo DeviceInfo:kDeviceBaseInfo];
        if (deviceInfoItemList.count >= 10) {
            NSData *jsonData = [deviceInfoItemList[9] dataUsingEncoding:NSUTF8StringEncoding];
            NSError *error;
            NSDictionary *extraDic = [NSJSONSerialization JSONObjectWithData:jsonData options:0 error:&error];
            if (!error) {
                [kDeviceInfo putExtras:extraDic];
            }
        }

        [kUpDeviceInfoList addObject:kDeviceInfo];
    }

    return kUpDeviceInfoList;
}

DeviceBLEHistoryInfo *convertDeviceBLEHistoryInfoFromStepTableData(NSDictionary *userInfo)
{
    NSArray<NSArray<NSString *> *> *infoList = getTableDataListFromExpectUserInfo(userInfo);
    NSArray<NSString *> *deviceInfoItemList = infoList.firstObject;

    DeviceBLEHistoryInfo *kDeviceBLEHistoryInfo = [DeviceBLEHistoryInfo DeviceBLEHistoryInfo:[deviceInfoItemList[2] dataUsingEncoding:NSUTF8StringEncoding] currentCount:deviceInfoItemList[0].integerValue totalCount:deviceInfoItemList[1].integerValue];

    return kDeviceBLEHistoryInfo;
}

NSArray<id<UpDeviceInfo>> *convertUpDeviceInfoListFromUpDeviceList(NSArray<id<UpDevice>> *deviceList)
{
    NSMutableArray<id<UpDeviceInfo>> *kActualUpDeviceInfoList = [NSMutableArray arrayWithCapacity:0];

    for (int index = 0; index < deviceList.count; index++) {
        id<UpDevice> kSubDevice = deviceList[index];
        id<UpDeviceInfo> kSubDeviceInfo = [kSubDevice getInfo];
        [kActualUpDeviceInfoList addObject:kSubDeviceInfo];
    }

    return kActualUpDeviceInfoList;
}

NSArray<LEAttribute *> *convertLEAttributeFromStepTableData(NSDictionary *userInfo)
{
    NSArray<NSArray<NSString *> *> *infoList = getTableDataListFromExpectUserInfo(userInfo);
    NSUInteger kNumCount = infoList.count;
    NSMutableArray<LEAttribute *> *kActualLEAttributeList = [NSMutableArray arrayWithCapacity:0];

    for (int index = 0; index < kNumCount; index++) {
        NSArray<NSString *> *deviceInfoItemList = infoList[index];
        LEAttribute *kLEAttribute = [[LEAttribute alloc] init];
        kLEAttribute.name = deviceInfoItemList[0];
        kLEAttribute.value = deviceInfoItemList[1];
        LEValueRange *kLEValueRange = [[LEValueRange alloc] init];
        kLEValueRange.type = deviceInfoItemList[2];
        kLEAttribute.valueRange = kLEValueRange;
        kLEAttribute.writable = [deviceInfoItemList[3] isEqualToString:@"true"] ? YES : NO;
        kLEAttribute.defaultValue = deviceInfoItemList[4];

        [kActualLEAttributeList addObject:kLEAttribute];
    }

    return kActualLEAttributeList;
}

NSArray<LEAlarm *> *convertLEAlarmFromStepTableData(NSDictionary *userInfo)
{
    //format: attrA,1;attrB,1
    NSArray<NSArray<NSString *> *> *infoList = getTableDataListFromExpectUserInfo(userInfo);
    NSUInteger kNumCount = infoList.count;
    NSMutableArray<LEAlarm *> *kActualLEAlarmList = [NSMutableArray arrayWithCapacity:0];

    for (int index = 0; index < kNumCount; index++) {
        NSArray<NSString *> *deviceInfoItemList = infoList[index];
        LEAlarm *kLEAlarm = [[LEAlarm alloc] init];
        kLEAlarm.name = deviceInfoItemList[0];
        kLEAlarm.code = deviceInfoItemList[1];
        kLEAlarm.desc = deviceInfoItemList[2];
        kLEAlarm.time = deviceInfoItemList[3];
        kLEAlarm.clear = [deviceInfoItemList[4] isEqualToString:@"true"] ? YES : NO;

        [kActualLEAlarmList addObject:kLEAlarm];
    }

    return kActualLEAlarmList;
}

NSMutableDictionary *convertCMDMapFromArgs(NSString *CMDParameter)
{
    if ([CMDParameter isEqualToString:@"空对象"]) {
        CMDParameter = nil;
    }
    NSMutableDictionary *kCMDMap = [NSMutableDictionary dictionaryWithCapacity:0];
    NSArray *kCMDList = [CMDParameter componentsSeparatedByString:@";"];
    NSUInteger kNumCount = kCMDList.count;

    for (int index = 0; index < kNumCount; index++) {
        NSString *kCommandParameter = [kCMDList objectAtIndex:index];
        NSString *kAttributeName = @"";
        NSString *kAttributeValue = @"";
        NSRange kNSRange = [kCommandParameter rangeOfString:@","];
        if (NSNotFound != kNSRange.location) {
            kAttributeName = [kCommandParameter substringToIndex:kNSRange.location];
            kAttributeValue = [kCommandParameter substringFromIndex:kNSRange.location + 1];
            [kCMDMap setValue:kAttributeValue forKey:kAttributeName];
        }
    }

    return kCMDMap;
}

DeviceCommand *convertCommandFromStepTableData(NSDictionary *userInfo)
{
    NSArray<NSArray<NSString *> *> *infoList = getTableDataListFromExpectUserInfo(userInfo);
    DeviceCommand *kDeviceCommand = nil;

    NSArray<NSString *> *deviceInfoItemList = infoList[0];
    NSString *kGroupCMDName = deviceInfoItemList[0];
    NSDictionary *kAttributes = convertCMDMapFromArgs(deviceInfoItemList[1]);
    kDeviceCommand = [[DeviceCommand alloc] initWithGroupName:kGroupCMDName Attributes:kAttributes];

    return kDeviceCommand;
}

NSArray<uSDKDevice *> *convertUSDKDeviceListFromStepTableData(NSDictionary *userInfo)
{
    NSArray *list = getTableDataListFromExpectUserInfo(userInfo);
    NSMutableArray *deviceList = [NSMutableArray array];
    for (NSArray *data in list) {
        if ([data[0] isEqualToString:@"null"] && [data[1] isEqualToString:@"null"] && [data[2] isEqualToString:@"null"]) {
            continue;
        }
        uSDKDevice *kuSDKDevice = [[uSDKDevice alloc] initWithDeviceID:data[0] uplusID:data[2]];
        NSString *kType = (NSString *)data[1];
        [kuSDKDevice setValue:[NSNumber numberWithInt:kType.intValue] forKeyPath:@"type"];
        [deviceList addObject:kuSDKDevice];
    }
    return deviceList;
}
NSArray<uSDKSubDevice *> *convertUSDKSubDeviceListFromStepTableData(NSDictionary *userInfo)
{
    NSArray *list = getTableDataListFromExpectUserInfo(userInfo);
    NSMutableArray *deviceList = [NSMutableArray array];
    for (NSArray *data in list) {
        uSDKSubDevice *kuSDKDevice = [[uSDKSubDevice alloc] initWithDeviceID:data[0] uplusID:data[2]];
        NSString *kType = (NSString *)data[1];
        [kuSDKDevice setValue:[NSNumber numberWithInt:kType.intValue] forKeyPath:@"type"];
        [deviceList addObject:kuSDKDevice];
    }
    return deviceList;
}

NSDictionary<NSString *, uSDKDeviceAttribute *> *convertUSDKDeviceAttributeFromStepTableData(NSDictionary *userInfo)
{
    NSArray *list = getTableDataListFromExpectUserInfo(userInfo);
    NSMutableDictionary *kAttributeList = [NSMutableDictionary dictionaryWithCapacity:0];
    for (NSArray *data in list) {
        uSDKDeviceAttribute *kuSDKDeviceAttribute = [[uSDKDeviceAttribute alloc] init];
        kuSDKDeviceAttribute.attrName = data[1];
        kuSDKDeviceAttribute.attrValue = data[2];
        [kAttributeList setValue:kuSDKDeviceAttribute forKey:data[0]];
    }
    return kAttributeList;
}
NSArray<uSDKDeviceAttribute *> *convertUSDKDeviceAttributesFromStep(NSDictionary *userInfo)
{
    NSArray *list = getTableDataListFromExpectUserInfo(userInfo);
    NSMutableArray *kAttributeList = [NSMutableArray array];
    for (NSArray *data in list) {
        uSDKDeviceAttribute *kuSDKDeviceAttribute = [[uSDKDeviceAttribute alloc] init];
        kuSDKDeviceAttribute.attrName = data[0];
        kuSDKDeviceAttribute.attrValue = data[1];
        [kAttributeList addObject:kuSDKDeviceAttribute];
    }
    return kAttributeList;
}

NSArray<uSDKDeviceAlarm *> *convertUSDKDeviceAlarmFromStepTableData(NSDictionary *userInfo)
{
    NSArray *list = getTableDataListFromExpectUserInfo(userInfo);
    NSMutableArray *kAlarmList = [NSMutableArray arrayWithCapacity:0];
    for (NSArray *data in list) {
        uSDKDeviceAlarm *kuSDKDeviceAlarm = [[uSDKDeviceAlarm alloc] initWithName:data[0] vlaue:data[1]];
        [kuSDKDeviceAlarm setValue:data[2] forKey:@"alarmTimestamp"];
        [kAlarmList addObject:kuSDKDeviceAlarm];
    }
    return kAlarmList;
}

NSArray *convertDeviceAttributeFromStepTableData(NSDictionary *userInfo)
{
    NSArray *infoList = getTableDataListFromExpectUserInfo(userInfo);
    NSMutableArray<LEAttribute *> *kActualLEAttributeList = [NSMutableArray arrayWithCapacity:0];
    NSUInteger kNumCount = infoList.count;
    for (int index = 0; index < kNumCount; index++) {
        NSArray<NSString *> *deviceInfoItemList = infoList[index];
        LEAttribute *kLEAttribute = [[LEAttribute alloc] init];
        kLEAttribute.name = deviceInfoItemList[0];
        kLEAttribute.value = deviceInfoItemList[1];
        [kActualLEAttributeList addObject:kLEAttribute];
    }
    return kActualLEAttributeList;
}

NSString *convertUniqueIdFromStepTableData(NSDictionary *userInfo)
{
    NSArray<NSArray<NSString *> *> *infoList = getTableDataListFromExpectUserInfo(userInfo);
    NSArray<NSString *> *deviceInfoItemList = infoList.firstObject;

    return deviceInfoItemList[0];
}

LEDeviceAttribute *convertLEDeviceAttributeFromArgs(NSArray<NSString *> *args, int index)
{
    NSString *kCMDParameter = args[index];

    LEDeviceAttribute *kLEDeviceAttribute = NULL;

    if (LECommon_isValidString(kCMDParameter)) {
        NSRange kRange = [kCMDParameter rangeOfString:@"="];

        if (NSNotFound != kRange.location) {
            kLEDeviceAttribute = [[LEDeviceAttribute alloc] initDeviceAttributeWithName:[kCMDParameter substringToIndex:kRange.location] value:[kCMDParameter substringFromIndex:kRange.location + 1]];
        }
    }

    return kLEDeviceAttribute;
}

NSArray<id<UpDeviceAttribute>> *convertLEDeviceAttributeFromStepTableData(NSDictionary *userInfo)
{
    NSArray<NSArray<NSString *> *> *infoList = getTableDataListFromExpectUserInfo(userInfo);
    NSUInteger kNumCount = infoList.count;

    NSMutableArray *kLEDeviceAttributeList = [NSMutableArray arrayWithCapacity:0];

    for (int index = 0; index < kNumCount; index++) {
        NSArray<NSString *> *deviceInfoItemList = infoList[index];
        if (LECommon_isValidString(deviceInfoItemList[0]) || LECommon_isValidString(deviceInfoItemList[1])) {
            LEDeviceAttribute *kLEDeviceAttribute = [[LEDeviceAttribute alloc] initDeviceAttributeWithName:deviceInfoItemList[0] value:deviceInfoItemList[1]];
            [kLEDeviceAttributeList addObject:kLEDeviceAttribute];
        }
    }

    return kLEDeviceAttributeList;
}

NSArray<id<UpDeviceCaution>> *convertUpDeviceCautionFromStepTableData(NSDictionary *userInfo)
{
    NSArray<NSArray<NSString *> *> *infoList = getTableDataListFromExpectUserInfo(userInfo);
    NSUInteger kNumCount = infoList.count;
    NSMutableArray *kUpDeviceCautionList = [NSMutableArray arrayWithCapacity:0];

    for (int index = 0; index < kNumCount; index++) {
        NSArray<NSString *> *deviceInfoItemList = infoList[index];
        DeviceCaution *kDeviceCaution = [DeviceCaution DeviceCaution:deviceInfoItemList[0] Value:deviceInfoItemList[1] Time:deviceInfoItemList[2]];
        [kUpDeviceCautionList addObject:kDeviceCaution];
    }

    return kUpDeviceCautionList;
}

UPResourceInfo *convertUPResourceInfoFromStepTableData(NSDictionary *userInfo)
{
    NSArray<NSArray<NSString *> *> *infoList = getTableDataListFromExpectUserInfo(userInfo);
    NSArray<NSString *> *deviceInfoItemList = infoList.firstObject;

    // | Type | Name | Version | Link            | MD5   | Model       | TypeId        | ProdNo       | TypeCode        | Path                                  |

    UPResourceInfo *kUPResourceInfo = [[UPResourceInfo alloc] init];
    if (deviceInfoItemList.count > 0) {
        kUPResourceInfo.type = deviceInfoItemList[0].integerValue;
        kUPResourceInfo.name = deviceInfoItemList[1];
        kUPResourceInfo.version = deviceInfoItemList[2];
        kUPResourceInfo.link = deviceInfoItemList[3];
        kUPResourceInfo.hashStr = deviceInfoItemList[4];
        kUPResourceInfo.model = deviceInfoItemList[5];
        kUPResourceInfo.typeId = deviceInfoItemList[6];
        kUPResourceInfo.prodNo = deviceInfoItemList[7];
        kUPResourceInfo.typeId = deviceInfoItemList[8];
        kUPResourceInfo.path = deviceInfoItemList[9];
    }

    return kUPResourceInfo;
}

NSArray<NSArray<NSString *> *> *convertNotifyEventNameFromStepTableData(NSDictionary *userInfo)
{
    NSArray<NSArray<NSString *> *> *infoList = getTableDataListFromExpectUserInfo(userInfo);
    //    NSArray<NSString *> *deviceInfoItemList = infoList.firstObject;

    return infoList;
}

- (BOOL)isEqualNotifyEvent:(NSMutableDictionary *)actualEvent expect:(NSMutableDictionary *)expectEvent
{
    BOOL isEqualList = NO;
    NSUInteger actualCount = actualEvent.count;
    NSUInteger expectCount = expectEvent.count;

    if (actualCount == expectCount) {
        if ([actualEvent isEqualToDictionary:expectEvent]) {
            isEqualList = YES;
        }
        if (!isEqualList) {
            isEqualList = [self isSubEventList:actualEvent parentList:expectEvent];
        }
    }
    else {
        isEqualList = [self isSubEventList:actualEvent parentList:expectEvent];
    }
    return isEqualList;
}

- (BOOL)containNotifyEvent:(NSString *)eventName sourceList:(NSMutableDictionary *)eventList
{
    BOOL kIsExsit = NO;
    if ([eventList.allKeys containsObject:eventName]) {
        kIsExsit = YES;
    }
    return kIsExsit;
}

- (NSMutableDictionary *)convertNotifyEventListToSubscriberDictionary:(NSArray *)eventList
{
    NSMutableDictionary *kSubscriberDictionary = [NSMutableDictionary dictionaryWithCapacity:0];
    NSUInteger kNumCount = eventList.count;

    for (int index = 0; index < kNumCount; index++) {
        NSArray *kSearchEventSubList = [eventList objectAtIndex:index];
        NSUInteger kSubNumCount = kSearchEventSubList.count;

        for (int subIndex = 0; subIndex < kSubNumCount; subIndex++) {
            NSString *kSearchEventName = [kSearchEventSubList objectAtIndex:subIndex];
            BOOL kIsExsit = [self containNotifyEvent:kSearchEventName sourceList:kSubscriberDictionary];
            if (!kIsExsit) {
                [kSubscriberDictionary setValue:[NSNumber numberWithInt:1] forKey:kSearchEventName];
            }
            else {
                int kNewCount = [(NSNumber *)[kSubscriberDictionary objectForKey:kSearchEventName] intValue];
                kNewCount++;
                [kSubscriberDictionary setValue:[NSNumber numberWithInt:kNewCount] forKey:kSearchEventName];
            }
        }
    }

    return kSubscriberDictionary;
}

- (BOOL)isSubEventList:(NSMutableDictionary *)subEventList parentList:(NSMutableDictionary *)parentEventList
{
    BOOL kIsSubList = NO;
    NSArray *kParentEventKeyList = parentEventList.allKeys;
    NSArray *kSubEventKeyList = subEventList.allKeys;
    NSUInteger kSubNumCount = kSubEventKeyList.count;
    for (int index = 0; index < kSubNumCount; index++) {
        if ([kParentEventKeyList containsObject:[kSubEventKeyList objectAtIndex:index]]) {
            kIsSubList = YES;
            break;
        }
    }
    return kIsSubList;
}

- (BOOL)isEqualUpDeviceAttributeList:(NSArray<id<UpDeviceAttribute>> *)actualList expectList:(NSArray<id<UpDeviceAttribute>> *)expectList
{
    BOOL isEqualList = YES;
    NSUInteger kActualListNumCount = actualList.count;
    NSUInteger kExpectListNumCount = expectList.count;
    if (kExpectListNumCount != kActualListNumCount) {
        return NO;
    }
    for (int index = 0; index < kActualListNumCount; index++) {
        id<UpDeviceAttribute> kExpectLEDeviceAttribute = [expectList objectAtIndex:index];
        id<UpDeviceAttribute> kActualLEDeviceAttribute = [actualList objectAtIndex:index];

        if (![kExpectLEDeviceAttribute.name isEqualToString:kActualLEDeviceAttribute.name] || ![kExpectLEDeviceAttribute.value isEqualToString:kActualLEDeviceAttribute.value]) {
            isEqualList = NO;
            break;
        }
    }
    return isEqualList;
}

- (BOOL)isEqualUpDeviceCautionList:(NSArray<id<UpDeviceCaution>> *)actualList expectList:(NSArray<id<UpDeviceCaution>> *)expectList
{
    BOOL isEqualList = YES;
    NSUInteger kActualListNumCount = actualList.count;
    NSUInteger kExpectListNumCount = expectList.count;
    if (kExpectListNumCount != kActualListNumCount) {
        return NO;
    }
    for (int index = 0; index < kActualListNumCount; index++) {
        id<UpDeviceCaution> kExpectUpDeviceCaution = [expectList objectAtIndex:index];
        id<UpDeviceCaution> kActualUpDeviceCaution = [actualList objectAtIndex:index];

        if (![kActualUpDeviceCaution.name isEqualToString:kExpectUpDeviceCaution.name] || ![kActualUpDeviceCaution.value isEqualToString:kExpectUpDeviceCaution.value] || ![kActualUpDeviceCaution.time isEqualToString:kExpectUpDeviceCaution.time]) {
            isEqualList = NO;
            break;
        }
    }
    return isEqualList;
}

- (BOOL)isEqualUpDeviceInfoList:(NSArray<id<UpDeviceInfo>> *)actualList expectList:(NSArray<id<UpDeviceInfo>> *)expectList
{
    BOOL isEqualList = YES;
    NSUInteger kActualListNumCount = actualList.count;
    NSUInteger kExpectListNumCount = expectList.count;
    if (kExpectListNumCount != kActualListNumCount) {
        return NO;
    }
    for (int index = 0; index < kActualListNumCount; index++) {
        id<UpDeviceInfo> kExpectUpDeviceInfo = [expectList objectAtIndex:index];
        id<UpDeviceInfo> kActualUpDeviceInfo = [actualList objectAtIndex:index];

        if (![[kExpectUpDeviceInfo deviceId] isEqualToString:kActualUpDeviceInfo.deviceId] || ![[kExpectUpDeviceInfo subDevNo] isEqualToString:kActualUpDeviceInfo.subDevNo]) {
            isEqualList = NO;
            break;
        }
    }
    return isEqualList;
}

- (BOOL)isEqualLEAttributeList:(NSArray<LEAttribute *> *)actualList expectList:(NSArray<LEAttribute *> *)expectList
{
    BOOL isEqualList = YES;
    NSUInteger kActualListNumCount = actualList.count;
    NSUInteger kExpectListNumCount = expectList.count;
    if (kExpectListNumCount != kActualListNumCount) {
        return NO;
    }
    for (int index = 0; index < kActualListNumCount; index++) {
        LEAttribute *kExpectLEAttribute = [expectList objectAtIndex:index];
        LEAttribute *kActualLEAttribute = [actualList objectAtIndex:index];

        if (![kExpectLEAttribute.name isEqualToString:kActualLEAttribute.name] || ![kExpectLEAttribute.value isEqualToString:kActualLEAttribute.value] ||
            ![kExpectLEAttribute.defaultValue isEqualToString:kActualLEAttribute.defaultValue] || ![kExpectLEAttribute.valueRange.type isEqualToString:kActualLEAttribute.valueRange.type] || kExpectLEAttribute.writable != kActualLEAttribute.writable) {
            isEqualList = NO;
            break;
        }
    }
    return isEqualList;
}

- (BOOL)isEqualLEAlarmList:(NSArray<LEAlarm *> *)actualList expectList:(NSArray<LEAlarm *> *)expectList
{
    BOOL isEqualList = YES;
    NSUInteger kActualListNumCount = actualList.count;
    NSUInteger kExpectListNumCount = expectList.count;
    if (kExpectListNumCount != kActualListNumCount) {
        return NO;
    }
    for (int index = 0; index < kActualListNumCount; index++) {
        LEAlarm *kExpectLEAlarm = [expectList objectAtIndex:index];
        LEAlarm *kActualLEAlarm = [actualList objectAtIndex:index];

        if (![kExpectLEAlarm.name isEqualToString:kActualLEAlarm.name] || ![kExpectLEAlarm.code isEqualToString:kActualLEAlarm.code] ||
            ![kExpectLEAlarm.desc isEqualToString:kActualLEAlarm.desc] || ![kExpectLEAlarm.time isEqualToString:kActualLEAlarm.time] || kExpectLEAlarm.clear != kActualLEAlarm.clear) {
            isEqualList = NO;
            break;
        }
    }
    return isEqualList;
}

- (BOOL)isEqualUSDKAttribute:(NSArray *)actualAttribute expectAttribute:(NSArray *)expectAttribute
{
    __block BOOL isEqualList = YES;

    if (0 == actualAttribute.count && 0 == expectAttribute.count) {
        return YES;
    }

    if (actualAttribute.count != expectAttribute.count) {
        return NO;
    }
    NSUInteger kNumCount = actualAttribute.count;

    for (int index = 0; index < kNumCount; index++) {
        id<UpDeviceAttribute> kUpDeviceAttribute = [actualAttribute objectAtIndex:index];
        LEAttribute *kLEAttribute = [expectAttribute objectAtIndex:index];
        if (![kLEAttribute.name isEqualToString:kUpDeviceAttribute.name] || ![kLEAttribute.value isEqualToString:kUpDeviceAttribute.value]) {
            isEqualList = NO;
            break;
        }
    }

    return isEqualList;
}

BOOL isEqualExtras(DeviceInfo *device1, DeviceInfo *device2)
{
    if (![device1 isKindOfClass:DeviceInfo.class] || ![device2 isKindOfClass:DeviceInfo.class]) {
        return YES;
    }
    NSDictionary *dic1 = [device1 getExtras];
    NSDictionary *dic2 = [device2 getExtras];
    if (dic1 == nil && dic2 == nil) {
        return YES;
    }
    if (dic1 == nil || dic2 == nil) {
        return NO;
    }
    if ([[device1 getExtras] isEqual:[device2 getExtras]]) {
        return YES;
    }
    return NO;
}

id jsonObjectFromEscapedString(NSString *escapedString)
{
    if ([escapedString hasPrefix:@"*S*"]) {
        return [[escapedString stringByReplacingOccurrencesOfString:@"*S*" withString:@""] stringByReplacingOccurrencesOfString:@"\\" withString:@""];
    }

    id nonJsonObj = objectFromStepStringArg(escapedString);
    if (!nonJsonObj) {
        return nil;
    }
    if (nonJsonObj && (![nonJsonObj isKindOfClass:NSString.class] || [nonJsonObj length] == 0)) {
        return nonJsonObj;
    }

    NSString *str = [escapedString stringByReplacingOccurrencesOfString:@"\\" withString:@""];
    id result = str;
    if (([str hasPrefix:@"{"] && [str hasSuffix:@"}"]) ||
        ([str hasPrefix:@"["] && [str hasSuffix:@"]"])) {
        result = [NSJSONSerialization JSONObjectWithData:[[escapedString stringByReplacingOccurrencesOfString:@"\\" withString:@""] dataUsingEncoding:NSUTF8StringEncoding] options:NSJSONReadingFragmentsAllowed error:NULL];
    }
    return result;
}

id _Nullable objectFromStepStringArg(NSString *stringArg)
{
    NSString *str = [stringArg stringByReplacingOccurrencesOfString:@"\\" withString:@""];
    id result = str;

    if ([str hasPrefix:@"*B*"]) {
        str = [str stringByReplacingOccurrencesOfString:@"*B*" withString:@""];
    }

    if ([str isEqualToString:@"null"]) {
        result = nil;
    }
    else if ([str isEqualToString:@"\"\""]) {
        result = @"";
    }
    else if (([@[ @"true", @"yes" ] containsObject:str.lowercaseString])) {
        result = @(YES);
    }
    else if ([@[ @"false", @"no" ] containsObject:str.lowercaseString]) {
        result = @(NO);
    }
    else if ([str hasPrefix:@"*N*"]) {
        str = [str stringByReplacingOccurrencesOfString:@"*N*" withString:@""];
        result = nil;
    }
    else if ([str hasPrefix:@"*D*"]) {
        str = [str stringByReplacingOccurrencesOfString:@"*D*" withString:@""];
        if ([str containsString:@"."]) {
            double doubleVaule = [str doubleValue];
            NSScanner *scanner = [NSScanner scannerWithString:str];
            [scanner scanDouble:&doubleVaule];
            result = @(doubleVaule);
        }
        else {
            result = @(str.integerValue);
        }
    }
    return result;
}
@end
