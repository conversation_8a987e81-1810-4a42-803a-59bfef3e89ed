//
//  StepsUtils.h
//  UPDeviceTests
//
//  Created by <PERSON><PERSON><PERSON><PERSON> on 2020/12/24.
//  Copyright © 2020 海尔优家智能科技（北京）有限公司. All rights reserved.
//

#import <Foundation/Foundation.h>
#import <LogicEngine/LECalcLogicResult.h>
#import <LogicEngine/LEAlarm.h>
#import "DeviceBaseInfo.h"
#import "DeviceInfo.h"
#import "LEDeviceAttribute.h"
#import "UPResourceInfo.h"
#import "DeviceCaution.h"
#import "DefaultDeviceBroker.h"
#import "WifiDeviceToolkitImpl.h"
#import "UPResourceManager.h"
#import "UpEngineDeviceFactory.h"
#import "DeviceBLEHistoryInfo.h"
#import "UpDeviceBase.h"
#import "DeviceCommand.h"
#import "FakeResourceManager.h"
NS_ASSUME_NONNULL_BEGIN
@class DeviceInfo;
@interface StepsUtils : NSObject

@property (nonatomic, strong) DefaultDeviceBroker *mDefaultDeviceBroker;

@property (nonatomic, strong) WifiDeviceToolkitImpl *mToolkitHandler;

@property (nonatomic, strong) FakeResourceManager *mResourceManager;

@property (nonatomic, strong) UpEngineDeviceFactory *mEngineDeviceFactory;

@property (nonatomic, copy) NSString *mUniqueId;

@property (nonatomic, strong) id<UpDeviceInfo> mUpDeviceInfo;

@property (nonatomic, strong) NSMutableDictionary<NSString *, UpDeviceBase *> *mUpDeviceList;

+ (StepsUtils *)sharedInstance;
- (void)setValuesToDefault;
NSArray<NSArray<NSString *> *> *getTableDataListFromExpectUserInfo(NSDictionary *userInfo);
NSArray<DeviceInfo *> *getDeviceListFromExpectUserInfo(NSDictionary *userInfo);
NSArray<DeviceInfo *> *getDeviceListFromExpectUserInfoWithTotalNumber(NSDictionary *userInfo, int total);
id<UpDeviceInfo> convertUpDeviceFromStepTableData(NSDictionary *userInfo);
NSArray<DeviceInfo *> *getDeviceListFromExpectUserInfo(NSDictionary *userInfo);
id<UpDeviceInfo> convertUpDeviceFromStepTableData(NSDictionary *userInfo);
BOOL isEqualDevice(DeviceInfo *device1, DeviceInfo *device2);
BOOL isEqualDeviceList(NSArray<DeviceInfo *> *deviceList1, NSArray<DeviceInfo *> *deviceList2);
LECalcLogicResult *mockEngineCaculateResult(NSArray<LEDeviceAttribute *> *attributes, BOOL clean, BOOL isSuccessful, NSString *deviceId);
NSArray<id<UpDeviceInfo>> *convertUpDeviceInfoFromStepTableData(NSDictionary *userInfo);
NSArray<DeviceBLEHistoryInfo *> *convertDeviceBLEHistoryInfoFromExpectUserInfo(NSDictionary *userInfo);
NSArray<uSDKFOTAStatusInfo *> *convertuSDKFOTAStatusInfoFromExpectUserInfo(NSDictionary *userInfo);
NSString *convertUniqueIdFromStepTableData(NSDictionary *userInfo);
LEDeviceAttribute *convertLEDeviceAttributeFromArgs(NSArray<NSString *> *args, int index);
UPResourceInfo *convertUPResourceInfoFromStepTableData(NSDictionary *userInfo);
NSArray<NSArray<NSString *> *> *convertNotifyEventNameFromStepTableData(NSDictionary *userInfo);
NSArray<id<UpDeviceAttribute>> *convertLEDeviceAttributeFromStepTableData(NSDictionary *userInfo);
NSArray<id<UpDeviceCaution>> *convertUpDeviceCautionFromStepTableData(NSDictionary *userInfo);
DeviceBLEHistoryInfo *convertDeviceBLEHistoryInfoFromStepTableData(NSDictionary *userInfo);
NSArray<id<UpDeviceInfo>> *convertUpDeviceInfoListFromUpDeviceList(NSArray<id<UpDevice>> *deviceList);
NSArray<LEAttribute *> *convertLEAttributeFromStepTableData(NSDictionary *userInfo);
NSArray<LEAlarm *> *convertLEAlarmFromStepTableData(NSDictionary *userInfo);
NSMutableDictionary *convertCMDMapFromArgs(NSString *CMDParameter);
DeviceCommand *convertCommandFromStepTableData(NSDictionary *userInfo);
NSArray<uSDKDevice *> *convertUSDKDeviceListFromStepTableData(NSDictionary *userInfo);
NSArray *convertDeviceAttributeFromStepTableData(NSDictionary *userInfo);
NSDictionary<NSString *, uSDKDeviceAttribute *> *convertUSDKDeviceAttributeFromStepTableData(NSDictionary *userInfo);
NSArray<uSDKDeviceAttribute *> *convertUSDKDeviceAttributesFromStep(NSDictionary *userInfo);
NSArray<uSDKSubDevice *> *convertUSDKSubDeviceListFromStepTableData(NSDictionary *userInfo);
NSArray<uSDKDeviceAlarm *> *convertUSDKDeviceAlarmFromStepTableData(NSDictionary *userInfo);
- (BOOL)isEqualNotifyEvent:(NSMutableDictionary *)actualList expect:(NSMutableDictionary *)expectList;
- (NSMutableDictionary *)convertNotifyEventListToSubscriberDictionary:(NSArray *)eventList;
- (BOOL)isEqualUpDeviceAttributeList:(NSArray<id<UpDeviceAttribute>> *)actualList expectList:(NSArray<id<UpDeviceAttribute>> *)expectList;
- (BOOL)isEqualUpDeviceCautionList:(NSArray<id<UpDeviceCaution>> *)actualList expectList:(NSArray<id<UpDeviceCaution>> *)expectList;
- (BOOL)isEqualUpDeviceInfoList:(NSArray<id<UpDeviceInfo>> *)actualList expectList:(NSArray<id<UpDeviceInfo>> *)expectList;
- (BOOL)isEqualLEAttributeList:(NSArray<LEAttribute *> *)actualList expectList:(NSArray<LEAttribute *> *)expectList;
- (BOOL)isEqualLEAlarmList:(NSArray<LEAlarm *> *)actualList expectList:(NSArray<LEAlarm *> *)expectList;
- (BOOL)isEqualUSDKAttribute:(NSArray *)actualAttribute expectAttribute:(NSArray *)expectAttribute;
id jsonObjectFromEscapedString(NSString *escapedString);
@end

NS_ASSUME_NONNULL_END
