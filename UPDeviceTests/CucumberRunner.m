//
//  CucumberRunner.m
//  UPDeviceTests
//
//  Created by <PERSON><PERSON><PERSON><PERSON> on 2020/9/10.
//  Copyright © 2020 海尔优家智能科技（北京）有限公司. All rights reserved.
//

#import "Cucumberish.h"
#import <XCTest/XCTest.h>
#import "EngineDeviceSteps.h"
#import "DeviceManagerSteps.h"
#import "DeviceBaseSteps.h"
#import "DeviceManagerSteps.h"
#import "InitializationSteps.h"
#import "WifiDeviceToolkitSteps.h"
#import "UpDeviceResourceSteps.h"
#import "WashDeviceManagerSteps.h"
@interface CucumberRunner : NSObject

@end

@implementation CucumberRunner

__attribute__((constructor)) void CucumberInit()
{
    [[Cucumberish instance] setPrettyNamesAllowed:NO];
    [Cucumberish instance].fixMissingLastScenario = YES;

    [[[InitializationSteps alloc] init] defineStepsAndHocks];
    [[[EngineDeviceSteps alloc] init] defineStepsAndHocks];
    [[[DeviceManagerSteps alloc] init] defineStepsAndHocks];
    [[[DeviceBaseSteps alloc] init] defineStepsAndHocks];
    [[[WifiDeviceToolkitSteps alloc] init] defineStepsAndHocks];
    [[[UpDeviceResourceSteps alloc] init] defineStepsAndHocks];
    [[[WashDeviceManagerSteps alloc] init] defineStepsAndHocks];
    NSBundle *bundle = [NSBundle bundleForClass:[CucumberRunner class]];
    Cucumberish *cucumber = [[Cucumberish instance] parserFeaturesInDirectory:@"features" fromBundle:bundle includeTags:nil excludeTags:@[ @"ios_ignore", @"ignore" ]];
    [cucumber beginExecution];
}

@end
