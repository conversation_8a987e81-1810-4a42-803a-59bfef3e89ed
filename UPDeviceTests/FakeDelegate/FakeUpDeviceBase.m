//
//  FakeUpDeviceBase.m
//  UPDeviceTests
//
//  Created by 韩波标 on 2021/1/21.
//  Copyright © 2021 海尔优家智能科技（北京）有限公司. All rights reserved.
//

#import "FakeUpDeviceBase.h"

@interface FakeUpDeviceBase ()


@property (nonatomic, assign) BOOL mPrepareExtApiSuccessful;
@property (nonatomic, assign) BOOL mReleaseExtApiSuccessful;
@end

@implementation FakeUpDeviceBase

- (instancetype)initFakeDeviceWithUniqueID:(NSString *)uniqueId deviceInfo:(id<UpDeviceInfo>)deviceInfo broker:(id<UpDeviceBroker>)broker factory:(id<UpDeviceFactory>)factory
{
    if (self = [super initWithDeviceInfo:uniqueId deviceInfo:deviceInfo broker:broker factory:factory]) {
    }
    return self;
}

- (void)onPrepareExtApi:(void (^)(UpDeviceResult *))finishBlock
{
    int errCode = ErrorCode_FAILURE;
    if (self.mPrepareExtApiSuccessful) {
        errCode = ErrorCode_SUCCESS;
    }
    UpDeviceResult *kUpDeviceResult = [UpDeviceResult UpDeviceResult:errCode extraData:nil];
    finishBlock(kUpDeviceResult);
}
- (void)onReleaseExtApi:(void (^)(UpDeviceResult *))finishBlock
{
    int errCode = ErrorCode_FAILURE;
    if (self.mReleaseExtApiSuccessful) {
        errCode = ErrorCode_SUCCESS;
    }
    UpDeviceResult *kUpDeviceResult = [UpDeviceResult UpDeviceResult:errCode extraData:nil];
    finishBlock(kUpDeviceResult);
}

- (void)mockPrepareExtApiResult:(BOOL)result
{
    self.mPrepareExtApiSuccessful = result;
}
- (void)mockReleaseExtApiResult:(BOOL)result
{
    self.mReleaseExtApiSuccessful = result;
}

@end
