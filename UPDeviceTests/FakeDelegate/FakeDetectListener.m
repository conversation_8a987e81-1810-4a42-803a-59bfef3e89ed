//
//  FakeDetectListener.m
//  UPDeviceTests
//
//  Created by 冉东军 on 2021/4/21.
//  Copyright © 2021 海尔优家智能科技（北京）有限公司. All rights reserved.
//

#import "FakeDetectListener.h"

@implementation FakeDetectListener

- (void)onDeviceListChanged
{
    [self recordInvokedActionOfSelector:_cmd];
}

- (void)onFind:(NSArray<id<UpDeviceBaseInfo>> *)baseInfoList
{
    [self recordInvokeActionOfSelector:_cmd parameters:@[ baseInfoList ]];
}

- (void)onLose:(NSArray<id<UpDeviceBaseInfo>> *)baseInfoList
{
    [self recordInvokeActionOfSelector:_cmd parameters:@[ baseInfoList ]];
}


@end
