//
//  CustomFilterProvider.m
//  UPDeviceTests
//
//  Created by 冉东军 on 2021/1/25.
//  Copyright © 2021 海尔优家智能科技（北京）有限公司. All rights reserved.
//

#import "CustomFilterProvider.h"

@implementation CustomFilterProvider
- (id)getDataFromDevice:(id<UpDevice>)device
{
    if ([self.filterType isEqualToString:@"Model"]) {
        return device.getInfo.model;
    }
    else if ([self.filterType isEqualToString:@"DeviceId"]) {
        return device.getInfo.deviceId;
    }
    return nil;
}

@end
