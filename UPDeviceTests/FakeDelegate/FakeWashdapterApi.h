//
//  FakeWashdapterApi.h
//  UPDeviceTests
//
//  Created by 王杰 on 2023/2/1.
//  Copyright © 2023 海尔优家智能科技（北京）有限公司. All rights reserved.
//

#import <Foundation/Foundation.h>
#import <upnetwork/UPNetwork.h>
#import "UpWashModel.h"
NS_ASSUME_NONNULL_BEGIN

@interface FakeWashdapterApi : NSObject
@property (nonatomic, strong) id<UPResponseParser> responseParser;
@property (nonatomic, copy) NSString *typeId;
@property (nonatomic, assign) BOOL isSuccess;
@property (nonatomic, strong) NSDictionary *resultDict;
- (instancetype)initWithWashAdapterParam:(NSDictionary *)param;
- (void)startRequestWithSuccess:(UPRequestSuccess)success failure:(UPRequestFailure)failure;
@end

NS_ASSUME_NONNULL_END
