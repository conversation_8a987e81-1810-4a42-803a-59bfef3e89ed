//
//  FakeResourceManager.m
//  UPDeviceTests
//
//  Created by 王杰 on 2022/5/12.
//  Copyright © 2022 海尔优家智能科技（北京）有限公司. All rights reserved.
//

#import "FakeResourceManager.h"
@interface FakeResourceManager ()
/**
 Description
 */
@property (nonatomic, strong) UPResourceInfo *syncResource;
@property (nonatomic, strong) UPResourceInfo *asyncResouce;
@property (nonatomic, strong) NSError *error;

@end

@implementation FakeResourceManager

- (void)mockSyncResource:(UPResourceInfo *)syncResource asyncResouce:(UPResourceInfo *)asyncResouce error:(nonnull NSError *)error
{
    self.syncResource = syncResource;
    self.asyncResouce = asyncResouce;
    self.error = error;
}

- (NSArray<UPResourceInfo *> *)updateDeviceResList:(UPResourceDeviceCondition *)condition immediate:(BOOL)immediate completion:(void (^)(NSArray<UPResourceInfo *> *, NSError *))completion
{
    dispatch_async(dispatch_get_global_queue(DISPATCH_QUEUE_PRIORITY_DEFAULT, 0), ^{
      [NSThread sleepForTimeInterval:0.8];
      if (completion) {
          dispatch_async(dispatch_get_main_queue(), ^{
            if (self.asyncResouce) {
                completion([NSArray arrayWithObject:self.asyncResouce], nil);
            }
            else {
                completion(nil, self.error);
            }

          });
      }
    });
    return self.syncResource ? [NSArray arrayWithObject:self.syncResource] : nil;
}


@end
