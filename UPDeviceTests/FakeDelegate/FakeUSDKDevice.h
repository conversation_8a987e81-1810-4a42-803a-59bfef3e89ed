//
//  FakeUSDKDevice.h
//  UPDeviceTests
//
//  Created by gump on 28/1/2021.
//  Copyright © 2021 海尔优家智能科技（北京）有限公司. All rights reserved.
//

#import <Foundation/Foundation.h>
#import <uSDK/uSDK.h>
#import <XCTest/XCTest.h>
#import "UPDeviceNetType.h"
NS_ASSUME_NONNULL_BEGIN

@interface FakeUSDKDevice : uSDKSubDevice

- (void)recordInvokedActionOfSelector:(SEL)selector;
- (NSUInteger)getInvocationTimesOfSelector:(SEL)selector;
- (BOOL)isDelegateSelectorInvoked:(SEL)selector;
- (void)deleteRecordInvokedActionOfSelector:(SEL)selector;
- (void)clearFakeDataInfo;
- (void)recordInvokeActionOfSelector:(SEL)selector parameters:(NSArray *)parameters;
- (NSArray *)getParametersOfSelector:(SEL)selector;
- (void)recordMultipleInvocationParameters:(NSMutableArray *)parameters ofSelector:(SEL)selector;
- (NSMutableArray *)getMultipleInvocationParametersRecrodsOfSelector:(SEL)selector;
- (void)recordKeyInvokedOfSelector:(NSString *)key;
- (NSInteger)getKeyInvokedOfSelector:(NSString *)key;

- (void)mockSubscribeResourceResult:(BOOL)result;
- (void)mockModuleOTAWithProgressResult:(BOOL)result;
- (void)mockFetchBoardFOTAStatusResult:(BOOL)result;
- (void)mockFetchBoardFOTAStatusInfo:(uSDKFOTAStatusInfo *)info;
- (void)mockCheckBoardFOTAResult:(BOOL)result;
- (void)mockCheckBoardFOTAInfo:(uSDKFOTAInfo *)info;
- (void)mockUnSubscribeResourceResult:(BOOL)result;
- (void)mockWriteAttributeWithNameResult:(BOOL)result;
- (void)mockExecuteOperationResult:(BOOL)result;
- (void)mockDisconnectDeviceResult:(BOOL)result;
- (void)mockStartBoardFOTAResult:(BOOL)result;
- (void)mockFetchBLEHistoryDataResult:(BOOL)result;
- (void)mockCancelFetchBLEHistoryDataResult:(BOOL)result;
- (void)mockConnectNeedPropertiesWithSuccessResult:(BOOL)result;
- (void)mockOutFocusSuccessfulResult:(BOOL)result;
- (void)mockInFocusSuccessfulResult:(BOOL)result;
- (void)mockDeviceAttributeResult:(NSDictionary<NSString *, uSDKDeviceAttribute *> *)result;
- (void)mockDeviceAlarmResult:(NSArray<uSDKDeviceAlarm *> *)result;
- (void)mockConnectionStateResult:(uSDKDeviceState)result;
- (void)mockOnlineState:(uSDKDeviceOnlineState)state;
- (void)mockIsBoundsSuccessfulResult:(BOOL)result;
- (void)mockIsModuleNeedOTASuccessfulResult:(BOOL)result;
- (void)mockIsSubscribedSuccessfulResult:(BOOL)result;
- (void)mockDeviceBindInfoResult:(BOOL)result;
- (void)mockDeviceBindInfo:(NSString *)info;
- (void)mockReadAttributeResult:(BOOL)result;
- (void)mockReadAttributeResultValue:(NSString *)resultValue;
- (void)mockGetNetTypeResult:(NSString *)netType;
- (void)mockGetSmartLinkSoftwareVersionResult:(NSString *)resultValue;
- (void)mockuSDKDeviceUplusID:(NSString *)uplusID;
- (void)mockuSDKDeviceType:(uSDKDeviceTypeConst)type;
- (void)mockuSDKDeviceID:(NSString *)deviceId;
- (void)mockSubscribeResourceWithDecodeResult:(BOOL)result;
- (void)mockCreateGroupResult:(BOOL)result;
- (void)mockDeleteGroupResult:(BOOL)result;
- (void)mockFetchGroupableDeviceListResult:(BOOL)result;
- (void)mockIsGroupResult:(BOOL)result;
- (void)mockAddDeviceResult:(BOOL)result notifyResult:(BOOL)notifyResult;
- (void)mockRemoveDeviceResult:(BOOL)result notifyResult:(BOOL)notifyResult;
- (void)mockStartFOTAWithDeviceFOTAInfoResult:(BOOL)result;
- (void)mockBleState:(uSDKDeviceState)state;
@end

NS_ASSUME_NONNULL_END
