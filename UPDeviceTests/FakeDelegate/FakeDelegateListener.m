//
//  FakeDelegateLister.m
//  UPDeviceTests
//
//  Created by 冉东军 on 2021/1/22.
//  Copyright © 2021 海尔优家智能科技（北京）有限公司. All rights reserved.
//

#import "FakeDelegateListener.h"

@interface FakeDelegateListener ()
@property (nonatomic, strong) NSArray<id<UpDevice>> *listenerDeviceList;
@end

@implementation FakeDelegateListener

- (void)onDeviceListChange:(NSArray<id<UpDevice>> *)deviceList
{
    NSMutableArray *infoArray = [NSMutableArray array];
    for (id<UpDevice> info in deviceList) {
        [infoArray addObject:info.getInfo];
    }
    [self recordInvokeActionOfSelector:_cmd parameters:@[ infoArray ]];
}

@end
