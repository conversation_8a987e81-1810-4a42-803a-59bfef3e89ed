//
//  FakeWashdapterApi.m
//  UPDeviceTests
//
//  Created by 王杰 on 2023/2/1.
//  Copyright © 2023 海尔优家智能科技（北京）有限公司. All rights reserved.
//

#import "FakeWashdapterApi.h"
@interface FakeWashdapterApi ()
@property (nonatomic, strong) NSDictionary *param;
@end

@implementation FakeWashdapterApi

- (instancetype)initWithWashAdapterParam:(NSDictionary *)param
{
    NSString *typeId = param[@"typeIds"][0];
    if (![typeId isEqualToString:self.typeId]) {
        self.isSuccess = NO;
    }
    self.param = param;
    return self;
}

- (void)startRequestWithSuccess:(UPRequestSuccess)success failure:(UPRequestFailure)failure
{
    if (self.isSuccess) {
        if (self.resultDict) {
            success([self.responseParser parseResponseObject:self.resultDict]);
        }
        else {
            success(nil);
        }
    }
    else {
        failure([NSError errorWithDomain:@"失败" code:-1 userInfo:nil], nil);
    }
}


@end
