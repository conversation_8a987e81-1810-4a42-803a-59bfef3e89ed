//
//  FakeDelegateBase.h
//  UPDeviceTests
//
//  Created by <PERSON><PERSON><PERSON><PERSON> on 2020/12/24.
//  Copyright © 2020 海尔优家智能科技（北京）有限公司. All rights reserved.
//

#import <Foundation/Foundation.h>
#import <XCTest/XCTest.h>
NS_ASSUME_NONNULL_BEGIN

@interface FakeDelegateBase : NSObject
@property (nonatomic, strong) NSMutableDictionary<NSString *, NSNumber *> *selectorInvokedInfo;
@property (nonatomic, strong) NSMutableDictionary<NSString *, NSArray *> *selectorParametersInfo;
@property (nonatomic, strong) NSMutableDictionary<NSString *, NSMutableArray *> *selectorMultipleInvocationParametersInfo;
@property (nonatomic, strong) NSMutableDictionary<NSString *, NSNumber *> *selectorOfKeyInvokedInfo;

- (void)recordInvokedActionOfSelector:(SEL)selector;
- (NSUInteger)getInvocationTimesOfSelector:(SEL)selector;
- (BOOL)isDelegateSelectorInvoked:(SEL)selector;
- (void)deleteRecordInvokedActionOfSelector:(SEL)selector;
- (void)clearFakeDataInfo;
- (void)recordInvokeActionOfSelector:(SEL)selector parameters:(NSArray *)parameters;
- (NSArray *)getParametersOfSelector:(SEL)selector;
- (void)recordMultipleInvocationParameters:(NSMutableArray *)parameters ofSelector:(SEL)selector;
- (NSMutableArray *)getMultipleInvocationParametersRecrodsOfSelector:(SEL)selector;
- (void)recordKeyInvokedOfSelector:(NSString *)key;
- (NSInteger)getKeyInvokedOfSelector:(NSString *)key;
@property (nonatomic, strong) XCTestExpectation *expectation;
@end

NS_ASSUME_NONNULL_END
