//
//  FakeResourceManager.h
//  UPDeviceTests
//
//  Created by 王杰 on 2022/5/12.
//  Copyright © 2022 海尔优家智能科技（北京）有限公司. All rights reserved.
//

#import "UPResourceManager.h"

NS_ASSUME_NONNULL_BEGIN

@interface FakeResourceManager : UPResourceManager

- (void)mockSyncResource:(UPResourceInfo *)syncResource asyncResouce:(UPResourceInfo *)asyncResouce error:(NSError *)error;
- (NSArray<UPResourceInfo *> *)updateDeviceResList:(UPResourceDeviceCondition *)condition immediate:(BOOL)immediate completion:(void (^)(NSArray<UPResourceInfo *> *, NSError *))completion;
@end

NS_ASSUME_NONNULL_END
