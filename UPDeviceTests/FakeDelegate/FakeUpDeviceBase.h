//
//  FakeUpDeviceBase.h
//  UPDeviceTests
//
//  Created by 韩波标 on 2021/1/21.
//  Copyright © 2021 海尔优家智能科技（北京）有限公司. All rights reserved.
//

#import "UpDeviceBase.h"

NS_ASSUME_NONNULL_BEGIN

@interface FakeUpDeviceBase : UpDeviceBase

- (instancetype)initFakeDeviceWithUniqueID:(NSString *)uniqueId deviceInfo:(id<UpDeviceInfo>)deviceInfo broker:(id<UpDeviceBroker>)broker factory:(id<UpDeviceFactory>)factory;

- (void)mockPrepareExtApiResult:(BOOL)result;
- (void)mockReleaseExtApiResult:(BOOL)result;
@end

NS_ASSUME_NONNULL_END
