//
//  FakeUpDeviceListener.m
//  UPDeviceTests
//
//  Created by 韩波标 on 2021/1/21.
//  Copyright © 2021 海尔优家智能科技（北京）有限公司. All rights reserved.
//

#import "FakeUpDeviceListener.h"
#import <LogicEngine/LECommonFuncs.h>

@implementation FakeUpDeviceListener

- (instancetype)init
{
    if (self = [super init]) {
        self.mNotifyEventDictionary = [NSMutableDictionary dictionaryWithCapacity:0];
    }
    return self;
}

- (void)onDeviceReport:(NSInteger)event device:(id)device
{
    [self updateNotifyEventInfo:event eventList:self.mNotifyEventDictionary];
}

#pragma mark - Private Methods

- (NSString *)getEventNameAccordingToEnumValue:(NSInteger)eventEnum
{
    NSString *kNotifyEnumName = @"";
    if (eventEnum == EVENT_ATTRIBUTES_CHANGE) {
        kNotifyEnumName = @"EVENT_ATTRIBUTES_CHANGE";
    }
    else if (eventEnum == EVENT_DEVICE_CAUTION) {
        kNotifyEnumName = @"EVENT_DEVICE_CAUTION";
    }
    else if (eventEnum == EVENT_SUB_DEV_LIST_CHANGE) {
        kNotifyEnumName = @"EVENT_SUB_DEV_LIST_CHANGE";
    }
    else if (eventEnum == EVENT_DEVICE_STATE_CHANGE) {
        kNotifyEnumName = @"EVENT_CONNECTION_CHANGE";
    }
    else if (eventEnum == EVENT_BASE_INFO_CHANGE) {
        kNotifyEnumName = @"EVENT_BASE_INFO_CHANGE";
    }
    else if (eventEnum == EVENT_DEVICE_FOTASTATUS_CHANGE) {
        kNotifyEnumName = @"EVENT_DEVICE_FOTA_STATUS_CHANGE";
    }
    else if (eventEnum == EVENT_DEVICE_BLE_REAL) {
        kNotifyEnumName = @"EVENT_DEVICE_BLE_REALTIME_DATA";
    }
    else if (eventEnum == EVENT_DEVICE_BLE_HISTORY) {
        kNotifyEnumName = @"EVENT_DEVICE_BLE_HISTORY_DATA";
    }
    else if (eventEnum == EVENT_ATTACHED) {
        kNotifyEnumName = @"EVENT_ATTACHED";
    }
    return kNotifyEnumName;
}

- (BOOL)containNotifyEvent:(NSString *)eventName sourceList:(NSMutableDictionary *)eventList
{
    BOOL kIsExsit = NO;
    if ([eventList.allKeys containsObject:eventName]) {
        kIsExsit = YES;
    }
    return kIsExsit;
}

- (void)updateNotifyEventInfo:(NSInteger)eventEnum eventList:(NSMutableDictionary *)notifyEventList
{
    NSMutableDictionary *kNotifyEventDictionary = notifyEventList;
    NSString *kSearchEventName = [self getEventNameAccordingToEnumValue:eventEnum];
    if (LECommon_isValidString(kSearchEventName)) {
        BOOL kIsExsit = [self containNotifyEvent:kSearchEventName sourceList:kNotifyEventDictionary];
        if (!kIsExsit) {
            [kNotifyEventDictionary setValue:[NSNumber numberWithInt:1] forKey:kSearchEventName];
        }
        else {
            int kNewCount = [(NSNumber *)[kNotifyEventDictionary objectForKey:kSearchEventName] intValue];
            kNewCount++;
            [kNotifyEventDictionary setValue:[NSNumber numberWithInt:kNewCount] forKey:kSearchEventName];
        }
    }
}

@end
