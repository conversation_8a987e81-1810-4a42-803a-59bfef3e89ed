//
//  FakeDelegateFactory.m
//  UPDeviceTests
//
//  Created by 冉东军 on 2021/1/27.
//  Copyright © 2021 海尔优家智能科技（北京）有限公司. All rights reserved.
//

#import "FakeDelegateFactory.h"
#import "FakeUpDeviceBase.h"

@implementation FakeDelegateFactory

- (nullable id<UpDevice>)create:(NSString *)uniqueId deviceInfo:(id<UpDeviceInfo>)deviceInfo broker:(id<UpDeviceBroker>)broker factory:(id<UpDeviceFactory>)factory
{
    [self recordInvokedActionOfSelector:_cmd];

    FakeUpDeviceBase *fakeDeviceBase = [[FakeUpDeviceBase alloc] initFakeDeviceWithUniqueID:uniqueId deviceInfo:deviceInfo broker:broker factory:factory];
    [fakeDeviceBase mockPrepareExtApiResult:YES];
    [fakeDeviceBase mockReleaseExtApiResult:YES];
    return fakeDeviceBase;
}


@end
