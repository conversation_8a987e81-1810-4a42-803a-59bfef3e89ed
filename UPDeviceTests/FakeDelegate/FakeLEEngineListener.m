//
//  FakeLEEngineListener.m
//  UPDeviceTests
//
//  Created by 冉东军 on 2021/4/22.
//  Copyright © 2021 海尔优家智能科技（北京）有限公司. All rights reserved.
//

#import "FakeLEEngineListener.h"

@implementation FakeLEEngineListener

- (void)engine:(LEEngine *)engine deviceAlarmsDidChange:(NSArray<LEAlarm *> *)alarms
{
    [self recordInvokedActionOfSelector:_cmd];
}

- (void)engine:(LEEngine *)engine deviceAttributesDidChange:(NSArray<LEAttribute *> *)attributes
{
    [self recordInvokedActionOfSelector:_cmd];
}

- (void)engine:(LEEngine *)engine deviceNetStatusDidChange:(LEDeviceNetStatus)status
{
    [self recordInvokedActionOfSelector:_cmd];
}

@end
