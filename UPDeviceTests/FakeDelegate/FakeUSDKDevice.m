//
//  FakeUSDKDevice.m
//  UPDeviceTests
//
//  Created by gump on 28/1/2021.
//  Copyright © 2021 海尔优家智能科技（北京）有限公司. All rights reserved.
//

#import "FakeUSDKDevice.h"

@interface FakeUSDKDevice ()
@property (nonatomic, assign) BOOL mSubscribeResourceSuccessful;
@property (nonatomic, assign) BOOL mModuleOTAWithProgressSuccessful;
@property (nonatomic, assign) BOOL mFetchBoardFOTAStatusSuccessful;
@property (nonatomic, strong) uSDKFOTAStatusInfo *mFetchBoardFOTAStatusInfo;
@property (nonatomic, assign) BOOL mCheckBoardFOTASuccessful;
@property (nonatomic, strong) uSDKFOTAInfo *mCheckBoardFOTAInfo;
@property (nonatomic, assign) BOOL mUnsubscribeResourceSuccessful;
@property (nonatomic, assign) BOOL mWriteAttributeWithNameSuccessful;
@property (nonatomic, assign) BOOL mExecuteOperationSuccessful;
@property (nonatomic, assign) BOOL mDisconnectDeviceSuccessful;
@property (nonatomic, assign) BOOL mStartBoardFOTASuccessful;
@property (nonatomic, assign) BOOL mFetchBLEHistoryDataSuccessful;
@property (nonatomic, assign) BOOL mCancelFetchBLEHistoryDataSuccessful;
@property (nonatomic, assign) BOOL mConnectNeedPropertiesWithSuccessSuccessful;
@property (nonatomic, assign) BOOL mOutFocusSuccessful;
@property (nonatomic, assign) BOOL mInFocusSuccessful;
@property (nonatomic, strong) NSDictionary<NSString *, uSDKDeviceAttribute *> *mAttributeList;
@property (nonatomic, strong) NSArray<uSDKDeviceAlarm *> *mAlarmList;
@property (nonatomic, assign) uSDKDeviceState mDeviceStateSuccessful;
@property (nonatomic, assign) uSDKDeviceOnlineState mDeviceOnlineState;
@property (nonatomic, assign) uSDKDeviceState mDeviceBleState;
@property (nonatomic, strong) NSMutableDictionary<NSString *, NSNumber *> *selectorInvokedInfo;
@property (nonatomic, strong) NSMutableDictionary<NSString *, NSArray *> *selectorParametersInfo;
@property (nonatomic, strong) NSMutableDictionary<NSString *, NSMutableArray *> *selectorMultipleInvocationParametersInfo;
@property (nonatomic, strong) NSMutableDictionary<NSString *, NSNumber *> *selectorOfKeyInvokedInfo;
@property (nonatomic, assign) BOOL mIsBoundSuccessful;
@property (nonatomic, assign) BOOL mIsModuleNeedOTASuccessful;
@property (nonatomic, assign) BOOL mIsSubscribedSuccessful;
@property (nonatomic, assign) BOOL mIsDeviceBindInfoResult;
@property (nonatomic, strong) NSString *deviceBindInfo;
@property (nonatomic, assign) BOOL mIsReadAttributeResult;
@property (nonatomic, strong) NSString *readAttributeValue;
@property (nonatomic, strong) NSString *netTypeResult;
@property (nonatomic, strong) NSString *softwareVersion;
@property (nonatomic, strong) NSString *deviceUplusID;
@property (nonatomic, assign) uSDKDeviceTypeConst deviceType;
@property (nonatomic, strong) NSString *fakeDeviceID;
@property (nonatomic, assign) BOOL mSubscribeResourceWithCodeSuccessful;
@property (nonatomic, assign) BOOL mCreateGroupSuccessful;
@property (nonatomic, assign) BOOL mDeleteGroupSuccessful;
@property (nonatomic, assign) BOOL mIsGroupSuccessful;
@property (nonatomic, assign) BOOL mFetchGroupableDeviceListSuccessful;
@property (nonatomic, assign) BOOL mAddDeviceSuccessful;
@property (nonatomic, assign) BOOL mRemoveDeviceSuccessful;
@property (nonatomic, assign) BOOL mNotifySuccessful;
@property (nonatomic, assign) BOOL mStartFOTAWithDeviceFOTAInfoResult;
@end

@implementation FakeUSDKDevice

#pragma mark - public
- (instancetype)init
{
    if (self = [super init]) {
        _selectorInvokedInfo = [NSMutableDictionary dictionary];
        _selectorParametersInfo = [NSMutableDictionary dictionary];
        _selectorMultipleInvocationParametersInfo = [NSMutableDictionary dictionary];
        _selectorOfKeyInvokedInfo = [NSMutableDictionary dictionary];
    }
    return self;
}

- (void)mockSubscribeResourceResult:(BOOL)result
{
    self.mSubscribeResourceSuccessful = result;
}

- (void)mockModuleOTAWithProgressResult:(BOOL)result
{
    self.mModuleOTAWithProgressSuccessful = result;
}

- (void)mockFetchBoardFOTAStatusResult:(BOOL)result
{
    self.mFetchBoardFOTAStatusSuccessful = result;
}

- (void)mockFetchBoardFOTAStatusInfo:(uSDKFOTAStatusInfo *)info
{
    self.mFetchBoardFOTAStatusInfo = info;
}

- (void)mockCheckBoardFOTAResult:(BOOL)result
{
    self.mCheckBoardFOTASuccessful = result;
}

- (void)mockCheckBoardFOTAInfo:(uSDKFOTAInfo *)info
{
    self.mCheckBoardFOTAInfo = info;
}

- (void)mockUnSubscribeResourceResult:(BOOL)result
{
    self.mUnsubscribeResourceSuccessful = result;
}

- (void)mockWriteAttributeWithNameResult:(BOOL)result
{
    self.mWriteAttributeWithNameSuccessful = result;
}

- (void)mockExecuteOperationResult:(BOOL)result
{
    self.mExecuteOperationSuccessful = result;
}

- (void)mockDisconnectDeviceResult:(BOOL)result
{
    self.mDisconnectDeviceSuccessful = result;
}
- (void)mockStartBoardFOTAResult:(BOOL)result
{
    self.mStartBoardFOTASuccessful = result;
}
- (void)mockFetchBLEHistoryDataResult:(BOOL)result
{
    self.mFetchBLEHistoryDataSuccessful = result;
}
- (void)mockCancelFetchBLEHistoryDataResult:(BOOL)result
{
    self.mCancelFetchBLEHistoryDataSuccessful = result;
}
- (void)mockConnectNeedPropertiesWithSuccessResult:(BOOL)result
{
    self.mConnectNeedPropertiesWithSuccessSuccessful = result;
}

- (void)mockOutFocusSuccessfulResult:(BOOL)result
{
    self.mOutFocusSuccessful = result;
}

- (void)mockInFocusSuccessfulResult:(BOOL)result
{
    self.mInFocusSuccessful = result;
}

- (void)mockDeviceAttributeResult:(NSDictionary<NSString *, uSDKDeviceAttribute *> *)result
{
    self.mAttributeList = result;
}

- (void)mockDeviceAlarmResult:(NSArray<uSDKDeviceAlarm *> *)result
{
    self.mAlarmList = result;
}

- (void)mockConnectionStateResult:(uSDKDeviceState)result
{
    self.mDeviceStateSuccessful = result;
}

- (void)mockOnlineState:(uSDKDeviceOnlineState)state
{
    self.mDeviceOnlineState = state;
}

- (void)mockIsBoundsSuccessfulResult:(BOOL)result
{
    self.mIsBoundSuccessful = result;
}

- (void)mockIsModuleNeedOTASuccessfulResult:(BOOL)result
{
    self.mIsModuleNeedOTASuccessful = result;
}

- (void)mockIsSubscribedSuccessfulResult:(BOOL)result
{
    self.mIsSubscribedSuccessful = result;
}
- (void)mockDeviceBindInfoResult:(BOOL)result
{
    self.mIsDeviceBindInfoResult = result;
}
- (void)mockDeviceBindInfo:(NSString *)info
{
    self.deviceBindInfo = info;
}
- (void)mockReadAttributeResult:(BOOL)result
{
    self.mIsReadAttributeResult = result;
}
- (void)mockReadAttributeResultValue:(NSString *)resultValue
{
    self.readAttributeValue = resultValue;
}
- (void)mockGetNetTypeResult:(NSString *)netType
{
    self.netTypeResult = netType;
}
- (void)mockGetSmartLinkSoftwareVersionResult:(NSString *)resultValue
{
    self.softwareVersion = resultValue;
}
- (void)mockuSDKDeviceUplusID:(NSString *)uplusID
{
    self.deviceUplusID = uplusID;
}
- (void)mockuSDKDeviceType:(uSDKDeviceTypeConst)type
{
    self.deviceType = type;
}
- (void)mockuSDKDeviceID:(NSString *)deviceId
{
    self.fakeDeviceID = deviceId;
}
- (void)mockSubscribeResourceWithDecodeResult:(BOOL)result
{
    self.mSubscribeResourceWithCodeSuccessful = result;
}
- (void)mockCreateGroupResult:(BOOL)result
{
    self.mCreateGroupSuccessful = result;
}
- (void)mockDeleteGroupResult:(BOOL)result
{
    self.mDeleteGroupSuccessful = result;
}
- (void)mockFetchGroupableDeviceListResult:(BOOL)result
{
    self.mFetchGroupableDeviceListSuccessful = result;
}

- (void)mockIsGroupResult:(BOOL)result
{
    self.mIsGroupSuccessful = result;
}
- (void)mockAddDeviceResult:(BOOL)result notifyResult:(BOOL)notifyResult
{
    self.mAddDeviceSuccessful = result;
    self.mNotifySuccessful = notifyResult;
}
- (void)mockRemoveDeviceResult:(BOOL)result notifyResult:(BOOL)notifyResult
{
    self.mRemoveDeviceSuccessful = result;
    self.mNotifySuccessful = notifyResult;
}

- (void)mockStartFOTAWithDeviceFOTAInfoResult:(BOOL)result
{
    self.mStartFOTAWithDeviceFOTAInfoResult = result;
}

- (void)mockBleState:(uSDKDeviceState)state
{
    self.mDeviceBleState = state;
}

#pragma mark - override
- (void)removeDevices:(NSArray<uSDKDevice *> *)devices fromGroupWithTimeoutInterval:(NSTimeInterval)timeoutInterval progressNotify:(void (^)(uSDKDevice *, NSError *))progressNotify completionHandler:(void (^)(NSError *))completionHandler
{
    [self recordInvokeActionOfSelector:_cmd parameters:@[ devices ]];

    if (self.mNotifySuccessful) {
        [devices enumerateObjectsUsingBlock:^(uSDKDevice *_Nonnull obj, NSUInteger idx, BOOL *_Nonnull stop) {
          if (progressNotify) {
              progressNotify(obj, nil);
          }
        }];
    }
    else {
        NSError *error = [NSError errorWithDomain:@"" code:-1 userInfo:nil];
        [devices enumerateObjectsUsingBlock:^(uSDKDevice *_Nonnull obj, NSUInteger idx, BOOL *_Nonnull stop) {
          if (progressNotify) {
              progressNotify(obj, error);
          }
        }];
    }

    if (self.mRemoveDeviceSuccessful) {
        if (completionHandler) {
            completionHandler(nil);
        }
    }
    else {
        NSError *error = [NSError errorWithDomain:@"" code:-1 userInfo:nil];
        if (completionHandler) {
            completionHandler(error);
        }
    }
}
- (void)addDevices:(NSArray<uSDKDevice *> *)devices toGroupWithTimeoutInterval:(NSTimeInterval)timeoutInterval progressNotify:(void (^)(uSDKDevice *, NSError *))progressNotify completionHandler:(void (^)(NSError *))completionHandler
{
    [self recordInvokeActionOfSelector:_cmd parameters:@[ devices ]];

    if (self.mNotifySuccessful) {
        [devices enumerateObjectsUsingBlock:^(uSDKDevice *_Nonnull obj, NSUInteger idx, BOOL *_Nonnull stop) {
          if (progressNotify) {
              progressNotify(obj, nil);
          }
        }];
    }
    else {
        NSError *error = [NSError errorWithDomain:@"" code:-1 userInfo:nil];
        [devices enumerateObjectsUsingBlock:^(uSDKDevice *_Nonnull obj, NSUInteger idx, BOOL *_Nonnull stop) {
          if (progressNotify) {
              progressNotify(obj, error);
          }
        }];
    }

    if (self.mAddDeviceSuccessful) {
        if (completionHandler) {
            completionHandler(nil);
        }
    }
    else {
        NSError *error = [NSError errorWithDomain:@"" code:-1 userInfo:nil];
        if (completionHandler) {
            completionHandler(error);
        }
    }
}
- (void)fetchGroupableDeviceListCompletionHandler:(void (^)(NSArray<uSDKDevice *> *, NSError *))completionHandler
{
    [self recordInvokedActionOfSelector:_cmd];

    if (self.mFetchGroupableDeviceListSuccessful) {
        if (completionHandler) {
            completionHandler(nil, nil);
        }
    }
    else {
        NSError *error = [NSError errorWithDomain:@"" code:-1 userInfo:nil];
        if (completionHandler) {
            completionHandler(nil, error);
        }
    }
}
- (void)deleteGroupCompletionHandler:(void (^)(NSError *))completionHandler
{
    [self recordInvokedActionOfSelector:_cmd];

    if (self.mDeleteGroupSuccessful) {
        if (completionHandler) {
            completionHandler(nil);
        }
    }
    else {
        NSError *error = [NSError errorWithDomain:@"" code:-1 userInfo:nil];
        if (completionHandler) {
            completionHandler(error);
        }
    }
}
- (void)createGroupWithTimeoutInterval:(NSTimeInterval)timeoutInterval completionHandler:(void (^)(uSDKDevice *, NSError *))completionHandler
{
    [self recordInvokedActionOfSelector:_cmd];

    if (self.mCreateGroupSuccessful) {
        if (completionHandler) {
            completionHandler(nil, nil);
        }
    }
    else {
        NSError *error = [NSError errorWithDomain:@"" code:-1 userInfo:nil];
        if (completionHandler) {
            completionHandler(nil, error);
        }
    }
}
- (void)subscribeResourceWithDecode:(NSString *)resourceName success:(void (^)(void))success failure:(void (^)(NSError *))failure
{
    [self recordInvokeActionOfSelector:_cmd parameters:@[ resourceName ]];

    if (self.mSubscribeResourceWithCodeSuccessful) {
        if (success) {
            success();
        }
    }
    else {
        if (failure) {
            NSError *error = [NSError errorWithDomain:@"" code:-1 userInfo:nil];
            failure(error);
        }
    }
}
- (void)subscribeResource:(NSString *)resourceName success:(void (^)(void))success failure:(void (^)(NSError *))failure
{
    [self recordInvokeActionOfSelector:_cmd parameters:@[ resourceName ]];

    if (self.mSubscribeResourceSuccessful) {
        if (success) {
            success();
        }
    }
    else {
        if (failure) {
            NSError *error = [NSError errorWithDomain:@"" code:-1 userInfo:nil];
            failure(error);
        }
    }
}

- (void)moduleOTAWithProgress:(void (^)(uSDKOTAStatusInfo *))upgradeStatus success:(void (^)(void))success failure:(void (^)(NSError *))failure
{
    [self recordInvokeActionOfSelector:_cmd parameters:@[ upgradeStatus ]];

    if (upgradeStatus) {
        uSDKOTAStatusInfo *statusInfo = [uSDKOTAStatusInfo new];
        upgradeStatus(statusInfo);
    }

    if (self.mModuleOTAWithProgressSuccessful) {
        if (success) {
            success();
        }
    }
    else {
        if (failure) {
            NSError *error = [NSError errorWithDomain:@"" code:-1 userInfo:nil];
            failure(error);
        }
    }
}

- (void)fetchBoardFOTAStatusWithSuccess:(void (^)(uSDKFOTAStatusInfo *))success failure:(void (^)(NSError *))failure
{
    [self recordInvokeActionOfSelector:_cmd parameters:@[]];

    if (self.mFetchBoardFOTAStatusSuccessful) {
        if (success) {
            success(self.mFetchBoardFOTAStatusInfo);
        }
    }
    else {
        if (failure) {
            NSError *error = [NSError errorWithDomain:@"" code:-1 userInfo:nil];
            failure(error);
        }
    }
}

- (void)checkBoardFOTAInfoWithSuccess:(void (^)(uSDKFOTAInfo *))success failure:(void (^)(NSError *))failure
{
    [self recordInvokeActionOfSelector:_cmd parameters:@[]];

    if (self.mCheckBoardFOTASuccessful) {
        if (success) {
            success(self.mCheckBoardFOTAInfo);
        }
    }
    else {
        if (failure) {
            NSError *error = [NSError errorWithDomain:@"" code:-1 userInfo:nil];
            failure(error);
        }
    }
}

- (void)unSubscribeResource:(NSString *)resourceName success:(void (^)(void))success failure:(void (^)(NSError *))failure
{
    [self recordInvokeActionOfSelector:_cmd parameters:@[ resourceName ]];

    if (self.mUnsubscribeResourceSuccessful) {
        if (success) {
            success();
        }
    }
    else {
        if (failure) {
            NSError *error = [NSError errorWithDomain:@"" code:-1 userInfo:nil];
            failure(error);
        }
    }
}

- (void)writeAttributeWithName:(NSString *)name
                         value:(NSString *)value
                       success:(void (^)(void))success
                       failure:(void (^)(NSError *error))failure
{
    [self recordInvokeActionOfSelector:_cmd parameters:@[ name ? name : @"", value ? value : @"" ]];

    if (self.mWriteAttributeWithNameSuccessful) {
        if (success) {
            success();
        }
    }
    else {
        if (failure) {
            NSError *error = [NSError errorWithDomain:@"" code:-1 userInfo:nil];
            failure(error);
        }
    }
}

- (void)executeOperation:(NSString *)operationName
                    args:(NSArray<uSDKArgument *> *)args
                 success:(void (^)(void))success
                 failure:(void (^)(NSError *error))failure
{
    [self recordInvokeActionOfSelector:_cmd parameters:@[ operationName ? operationName : @"", args ? args : @[] ]];

    if (self.mExecuteOperationSuccessful) {
        if (success) {
            success();
        }
    }
    else {
        if (failure) {
            NSError *error = [NSError errorWithDomain:@"" code:-1 userInfo:nil];
            failure(error);
        }
    }
}

- (void)disconnectWithSuccess:(void (^)(void))success
                      failure:(void (^)(NSError *error))failure
{
    [self recordInvokedActionOfSelector:_cmd];

    if (self.mDisconnectDeviceSuccessful) {
        if (success) {
            success();
        }
    }
    else {
        if (failure) {
            NSError *error = [NSError errorWithDomain:@"" code:-1 userInfo:nil];
            failure(error);
        }
    }
}

- (void)startBoardFOTAWithSuccess:(void (^)(void))success
                          failure:(void (^)(NSError *error))failure
{
    [self recordInvokedActionOfSelector:_cmd];

    if (self.mStartBoardFOTASuccessful) {
        if (success) {
            success();
        }
    }
    else {
        if (failure) {
            NSError *error = [NSError errorWithDomain:@"" code:-1 userInfo:nil];
            failure(error);
        }
    }
}

- (void)fetchBLEHistoryDataSuccess:(void (^)(void))success
                           failure:(void (^)(NSError *error))failure
{
    [self recordInvokedActionOfSelector:_cmd];

    if (self.mFetchBLEHistoryDataSuccessful) {
        if (success) {
            success();
        }
    }
    else {
        if (failure) {
            NSError *error = [NSError errorWithDomain:@"" code:-1 userInfo:nil];
            failure(error);
        }
    }
}

- (void)cancelFetchBLEHistoryDataSuccess:(void (^)(void))success
                                 failure:(void (^)(NSError *error))failure
{
    [self recordInvokedActionOfSelector:_cmd];

    if (self.mCancelFetchBLEHistoryDataSuccessful) {
        if (success) {
            success();
        }
    }
    else {
        if (failure) {
            NSError *error = [NSError errorWithDomain:@"" code:-1 userInfo:nil];
            failure(error);
        }
    }
}

- (void)connectNeedPropertiesWithSuccess:(void (^)(void))success
                                 failure:(void (^)(NSError *error))failure
{
    [self recordInvokedActionOfSelector:_cmd];

    if (self.mConnectNeedPropertiesWithSuccessSuccessful) {
        if (success) {
            success();
        }
    }
    else {
        if (failure) {
            NSError *error = [NSError errorWithDomain:@"" code:-1 userInfo:nil];
            failure(error);
        }
    }
}

- (BOOL)outFocus
{
    [self recordInvokedActionOfSelector:_cmd];

    return self.mOutFocusSuccessful;
}

- (BOOL)inFocus
{
    [self recordInvokedActionOfSelector:_cmd];
    return self.mInFocusSuccessful;
}

- (NSDictionary<NSString *, uSDKDeviceAttribute *> *)attributeDict
{
    [self recordInvokedActionOfSelector:_cmd];

    return self.mAttributeList;
}

- (NSArray<uSDKDeviceAlarm *> *)alarmList
{
    [self recordInvokedActionOfSelector:_cmd];

    return self.mAlarmList;
}

- (uSDKDeviceState)state
{
    return self.mDeviceStateSuccessful;
}

- (uSDKDeviceOnlineState)onlineState
{
    return self.mDeviceOnlineState;
}

- (BOOL)isBound
{
    return self.mIsBoundSuccessful;
}

- (BOOL)isModuleNeedOTA
{
    return self.mIsModuleNeedOTASuccessful;
}

- (BOOL)isSubscribed
{
    return self.mIsSubscribedSuccessful;
}

- (uSDKDeviceState)bleState
{
    return self.mDeviceBleState;
}

- (void)getDeviceBindInfoWithToken:(NSString *)token timeoutInterval:(NSTimeInterval)timeoutInterval traceNodeCS:(uTraceNode *)traceNodeCS success:(void (^)(NSString *))success failure:(void (^)(NSError *))failure
{
    [self recordInvokedActionOfSelector:_cmd];

    if (self.mIsDeviceBindInfoResult) {
        if (success) {
            success(self.deviceBindInfo);
        }
    }
    else {
        if (failure) {
            NSError *error = [NSError errorWithDomain:@"" code:-1 userInfo:nil];
            failure(error);
        }
    }
}

- (void)readAttributeWithName:(NSString *)name timeoutInterval:(NSTimeInterval)timeoutInterval success:(void (^)(NSString *))success failure:(void (^)(NSError *))failure
{
    [self recordInvokedActionOfSelector:_cmd];

    if (self.mIsReadAttributeResult) {
        if (success) {
            success(self.readAttributeValue);
        }
    }
    else {
        if (failure) {
            NSError *error = [NSError errorWithDomain:@"" code:-1 userInfo:nil];
            failure(error);
        }
    }
}

- (uSDKDeviceNetTypeConst)netType
{
    [self recordInvokedActionOfSelector:_cmd];
    if (self.netTypeResult) {
        return NET_TYPE_LOCAL;
    }
    else {
        return NET_TYPE_REMOTE;
    }
}

- (NSString *)smartLinkSoftwareVersion
{
    [self recordInvokedActionOfSelector:_cmd];
    return self.softwareVersion;
}

- (uSDKDeviceTypeConst)type
{
    [self recordInvokedActionOfSelector:_cmd];
    return self.deviceType;
}
- (NSString *)uplusID
{
    [self recordInvokedActionOfSelector:_cmd];
    return self.deviceUplusID;
}
- (NSString *)deviceID
{
    [self recordInvokedActionOfSelector:_cmd];
    return self.fakeDeviceID;
}

- (BOOL)isGroup
{
    [self recordInvokedActionOfSelector:_cmd];
    return self.mIsGroupSuccessful;
}

- (void)startFOTAWithDeviceFOTAInfo:(uSDKDeviceFOTAInfo *)deviceFOTAInfo completionHandler:(void (^)(NSError *))completionHandler
{
    [self recordInvokeActionOfSelector:_cmd parameters:@[ deviceFOTAInfo ]];

    if (self.mStartFOTAWithDeviceFOTAInfoResult) {
        completionHandler(nil);
    }
    else {
        completionHandler([[NSError alloc] initWithDomain:@"failure" code:1 userInfo:nil]);
    }
}
#pragma mark - Record Function Info
- (void)recordInvokedActionOfSelector:(SEL)selector
{
    NSUInteger iCount = [self getInvocationTimesOfSelector:selector];
    NSString *selectorName = NSStringFromSelector(selector);
    if ([selectorName containsString:@"ocmock_replaced_"]) {
        selectorName = [selectorName substringFromIndex:@"ocmock_replaced_".length];
    }
    iCount++;
    [self.selectorInvokedInfo setObject:@(iCount) forKey:selectorName];
}

- (NSUInteger)getInvocationTimesOfSelector:(SEL)selector
{
    NSString *selectorName = NSStringFromSelector(selector);
    if ([selectorName containsString:@"ocmock_replaced_"]) {
        selectorName = [selectorName substringFromIndex:@"ocmock_replaced_".length];
    }
    NSNumber *num = self.selectorInvokedInfo[selectorName];
    return [num isKindOfClass:[NSNumber class]] ? num.integerValue : 0;
}

- (void)deleteRecordInvokedActionOfSelector:(SEL)selector
{
    if (selector == nil) {
        return;
    }
    NSString *selectorName = NSStringFromSelector(selector);
    [self.selectorInvokedInfo removeObjectForKey:selectorName];
}

- (BOOL)isDelegateSelectorInvoked:(SEL)selector
{
    if (selector == nil) {
        return NO;
    }
    NSString *selectorName = NSStringFromSelector(selector);
    NSNumber *num = self.selectorInvokedInfo[selectorName];
    return [num isKindOfClass:[NSNumber class]] && num.boolValue;
}

- (void)clearFakeDataInfo
{
    [self.selectorInvokedInfo removeAllObjects];
    [self.selectorParametersInfo removeAllObjects];
    [self.selectorMultipleInvocationParametersInfo removeAllObjects];
    [self.selectorOfKeyInvokedInfo removeAllObjects];
    self.mFetchBoardFOTAStatusInfo = nil;
}

- (void)recordInvokeActionOfSelector:(SEL)selector parameters:(NSArray *)parameters
{
    if (!selector) {
        return;
    }
    [self recordInvokedActionOfSelector:selector];
    NSString *selectorName = NSStringFromSelector(selector);
    if ([selectorName containsString:@"ocmock_replaced_"]) {
        selectorName = [selectorName substringFromIndex:@"ocmock_replaced_".length];
    }
    [self.selectorParametersInfo setValue:parameters forKey:selectorName];
}

- (NSArray *)getParametersOfSelector:(SEL)selector
{
    if (!selector) {
        return nil;
    }
    NSString *selectorName = NSStringFromSelector(selector);
    return self.selectorParametersInfo[selectorName];
}

- (void)recordMultipleInvocationParameters:(NSMutableArray *)parameters ofSelector:(SEL)selector
{
    if (!selector) {
        return;
    }
    NSString *selectorName = NSStringFromSelector(selector);
    [self.selectorMultipleInvocationParametersInfo setValue:parameters forKey:selectorName];
}

- (NSMutableArray *)getMultipleInvocationParametersRecrodsOfSelector:(SEL)selector
{
    if (!selector) {
        return [NSMutableArray array];
    }
    NSString *selectorName = NSStringFromSelector(selector);
    NSMutableArray *arr = self.selectorMultipleInvocationParametersInfo[selectorName];
    if (arr == nil) {
        arr = [NSMutableArray array];
    }
    return arr;
}

- (void)recordKeyInvokedOfSelector:(NSString *)key
{
    if (!key) {
        return;
    }
    NSUInteger iCount = [self getKeyInvokedOfSelector:key];
    iCount++;
    [self.selectorOfKeyInvokedInfo setObject:@(iCount) forKey:key];
}

- (NSInteger)getKeyInvokedOfSelector:(NSString *)key
{
    if (!key) {
        return 0;
    }
    NSNumber *num = self.selectorOfKeyInvokedInfo[key];
    return [num isKindOfClass:[NSNumber class]] ? num.integerValue : 0;
}


@end
