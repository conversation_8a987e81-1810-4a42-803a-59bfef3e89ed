//
//  FakeReportListener.m
//  UPDeviceTests
//
//  Created by 冉东军 on 2021/4/21.
//  Copyright © 2021 海尔优家智能科技（北京）有限公司. All rights reserved.
//

#import "FakeReportListener.h"

@implementation FakeReportListener

- (void)onAttributesChange:(id<UpDeviceBaseInfo>)deviceBaseInfo attributeList:(NSArray<id<UpDeviceAttribute>> *)attributeList
{
    [self recordInvokeActionOfSelector:_cmd parameters:@[ attributeList ]];
}

- (void)onConnectionChange:(id<UpDeviceBaseInfo>)deviceBaseInfo connection:(UpDeviceConnection)connection
{
    [self recordInvokeActionOfSelector:_cmd parameters:@[ @(connection) ]];
}

- (void)onRealOnlineChange:(id<UpDeviceBaseInfo>)deviceBaseInfo status:(UpDeviceRealOnline)status
{
    [self recordInvokeActionOfSelector:_cmd parameters:@[ @(status) ]];
}

- (void)onDeviceCaution:(id<UpDeviceBaseInfo>)deviceBaseInfo cautionList:(NSArray<id<UpDeviceCaution>> *)cautionList
{
    [self recordInvokeActionOfSelector:_cmd parameters:@[ cautionList ]];
}

- (void)onDeviceInfoChange:(id<UpDeviceBaseInfo>)deviceBaseInfo
{
    [self recordInvokeActionOfSelector:_cmd parameters:@[ deviceBaseInfo ]];
}

- (void)onDeviceReceive:(id<UpDeviceBaseInfo>)deviceBaseInfo name:(NSString *)name data:(id)data
{
    [self recordInvokeActionOfSelector:_cmd parameters:@[ name, data ]];
}

- (void)onDeviceReceiveBLEHistoryData:(id<UpDeviceBaseInfo>)deviceBaseInfo BLEHistoryInfo:(id<UpDeviceBLEHistoryInfo>)historyInfo
{
    [self recordInvokeActionOfSelector:_cmd parameters:@[ historyInfo ]];
}

- (void)onDeviceReceiveBLERealTimeData:(id<UpDeviceBaseInfo>)deviceBaseInfo data:(NSData *)data
{
    [self recordInvokeActionOfSelector:_cmd parameters:@[ data ]];
}

- (void)onDeviceUpdateBoardFOTAStatus:(id<UpDeviceBaseInfo>)deviceBaseInfo FOTAStatusInfo:(id<UpDeviceFOTAStatusInfo>)statusInfo
{
    [self recordInvokeActionOfSelector:_cmd parameters:@[ statusInfo ]];
}

- (void)onSubDevListChange:(id<UpDeviceBaseInfo>)deviceBaseInfo subDevInfoList:(NSArray<id<UpDeviceBaseInfo>> *)subDevInfoList
{
    [self recordInvokeActionOfSelector:_cmd parameters:@[ subDevInfoList ]];
}

- (void)onControlStateChange:(id<UpDeviceBaseInfo>)deviceBaseInfo controlState:(UpDeviceControlState)state
{
    [self recordInvokeActionOfSelector:_cmd parameters:@[ @(state) ]];
}

- (void)onBleStateChange:(id<UpDeviceBaseInfo>)deviceBaseInfo state:(UpDeviceConnection)state
{
    [self recordInvokeActionOfSelector:_cmd parameters:@[ deviceBaseInfo.deviceId, @(state) ]];
}

@end
