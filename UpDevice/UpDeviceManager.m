//
//  UpDeviceManager.m
//  UPDevice
//
//  Created by gump on 8/1/2019.
//  Copyright © 2019 海尔优家智能科技（北京）有限公司. All rights reserved.
//

#import "UpDeviceManager.h"
#import "UpDeviceCreator.h"
#import "UpDevice.h"
#import "UpDeviceHelper.h"
#import "UpDeviceException.h"
#import "UPDeviceLog.h"
#import "UpDeviceDaemon.h"
#import "DummyDeviceBroker.h"
#import "DeviceManagerToolkitListener.h"
#import "DeviceManagerDetectListener.h"
#import "UpDeviceBase.h"
#import "UpDeviceDataSourceWrapper.h"
#import "DefaultDeviceBroker.h"
#import "WifiDeviceToolkitImpl.h"
#import <uSDK/uSDK.h>
#import "UpDeviceHelper.h"
#import "DeviceBaseInfo.h"
#import "WifiDeviceHelper.h"
#import "DeviceInfo.h"
#import "UPStorage.h"
#import "UpDeviceObjectCommonHelper.h"
#import "UpDeviceDaemonGIO.h"
#import "Reachability.h"
#import "UpDeviceReportMonitor.h"
#import <upuserdomain/UpUserDomainHolder.h>

static const NSString *VERSION = @"7.8.1";
static NSString *const UpDevice_QuickyStorageKey = @"productInfo";
static NSString *const ExtraAppTypeNameKey = @"DI-Basic.apptypeName";
static NSString *const ExtraAppTypeImageUrlKey = @"DI-Product.imageAddr1";
static NSString *const ExtraAppTypeBrandKey = @"DI-Product.brand";
static NSString *const ExtraAppTypeShareDeviceFlag = @"DI-Product.sharedDeviceFlag";
@interface UpDeviceManager () <uSDKDeviceScannerDelegate, UpDeviceReportMonitorRunnable>
//监听设备列表变化监听者数组
@property (nonatomic, strong) NSMutableArray<id<Listener>> *listenerSet;
//有序设备列表
@property (nonatomic, strong) NSMutableArray<NSString *> *orderList;
//strong or weak ?   设备监听经纪人  监听设备列表变化 设备状态变化
@property (nonatomic, strong) id<UpDeviceBroker> broker;
//strong or weak ? 得到设备列表 得到设备信息
@property (nonatomic, strong) UpDeviceDataSourceWrapper *source;
//设备工厂类（管理，可以无限添加）
@property (nonatomic, strong) UpDeviceCreator *creator;
//设备创建进程守护
@property (nonatomic, strong) UpDeviceDaemon *deviceDaemon;
//虚假设备监听经纪人
@property (nonatomic, strong) DummyDeviceBroker *dummyBroker;
//设备比较算法
@property (nonatomic, strong) NSComparator deviceComparator;
@property (nonatomic, strong) DeviceManagerToolkitListener *managerToolkitListener;
@property (nonatomic, strong) DeviceManagerDetectListener *managerDetectListener;
@property (nonatomic, strong) NSLock *deviceLock;

// 按照家庭订阅
// 存储订阅关系
// device : [listener1, listener2]
@property (nonatomic, copy) NSMutableDictionary<NSString *, NSMutableArray<id<UpDeviceChangeObserver>> *> *familySubscriptionListeners;

// 按照设备ID订阅
// 存储订阅关系
// device : [listener1, listener2]
@property (nonatomic, copy) NSMutableDictionary<NSString *, NSMutableArray<id<UpDeviceChangeObserver>> *> *devicesSubscriptionListeners;

// 设备上报流量控制
@property (nonatomic, strong) UpDeviceManagerReportMonitor *currentReportMonitor;

@end

@implementation UpDeviceManager

#pragma mark - init method
- (instancetype)initDeviceManagerWithToolkit:(id<WifiDeviceToolkit>)toolkit dataSource:(id<UpDeviceDataSource>)source
{
    if (toolkit == nil || source == nil) {
        UPDeviceLogError(@"[%s][%d]Neither broker nor source could be NULL. Abort.", __PRETTY_FUNCTION__, __LINE__);
        return nil;
    }
    if (self = [self init]) {
        self.broker = [[DefaultDeviceBroker alloc] initWithKit:toolkit deviceManager:self];
        self.source = source;
        UPDeviceLogInfo(@"%s[%d]UpDeviceManager is initialized. Version:%@", __PRETTY_FUNCTION__, __LINE__, VERSION);
    }
    return self;
}

- (instancetype)init
{
    self = [super init];

    if (self) {
        _familySubscriptionListeners = [NSMutableDictionary dictionary];
        _devicesSubscriptionListeners = [NSMutableDictionary dictionary];
        _currentReportMonitor = [[UpDeviceManagerReportMonitor alloc] init];
        self.listenerSet = [NSMutableArray new];
        self.orderList = [NSMutableArray new];
        self.creator = [[UpDeviceCreator alloc] init];
        self.deviceDaemon = [[UpDeviceDaemon alloc] init];
        self.dummyBroker = [[DummyDeviceBroker alloc] init];
        _deviceLock = [[NSLock alloc] init];
        self.managerToolkitListener = [[DeviceManagerToolkitListener alloc] init];
        self.managerDetectListener = [[DeviceManagerDetectListener alloc] init];
        self.isDeviceListCreateComplete = NO;
    }

    return self;
}

- (void)addConfigOperation:(NSBlockOperation *)operation
{
    [self.deviceDaemon addConfigOperation:operation];
}

#pragma mark - growIO
- (void)setTracker:(id<UpDeviceTracker>)tracker
{
    id<UpDeviceBroker> broker = [self getBroker];
    if (broker == nil) {
        UPDeviceLogError(@"[%s][%d]UpDeviceBroker is missing.", __PRETTY_FUNCTION__, __LINE__);
        return;
    }
    id<UpDeviceToolkit> toolkit = [broker getToolkit];
    if (toolkit == nil) {
        UPDeviceLogError(@"[%s][%d]UpDeviceException{extraCode='%@', extraInfo='不支持的协议: '%@'}", __PRETTY_FUNCTION__, __LINE__, CODE_NOT_SUPPORT, INFO_NOT_SUPPORT);
        return;
    }
    [toolkit setTracker:tracker];
}

#pragma mark - deviceFactory
- (void)appendDeviceFactory:(id<UpDeviceFactory>)deviceFactory
{
    [self.creator append:deviceFactory];
}

- (void)removeDeviceFactory:(id<UpDeviceFactory>)deviceFactory
{
    [self.creator remove:deviceFactory];
}

- (void)setupDefaultFactory:(id<UpDeviceFactory>)deviceFactory
{
    [self.creator setFallbackFactory:deviceFactory];
}

#pragma mark - private

- (void)notifyDeviceListChange:(NSArray<id<UpDevice>> *)list listeners:(NSArray<id<Listener>> *)listeners
{
    UPDeviceLogDebug(@"%s[%d]notifyDeviceListChange！", __PRETTY_FUNCTION__, __LINE__);
    if (!listeners || listeners.count == 0) {
        return;
    }
    NSArray *listenerArray = [NSArray arrayWithArray:listeners];
    for (id<Listener> listener in listenerArray) {
        if (listener == nil) {
            continue;
        }
        [listener onDeviceListChange:list];
    }
}
- (void)brokerAttchToolkit:(id<UpDeviceBroker>)broker finishBlock:(void (^)(UpDeviceResult *result))finishBlock
{
    [broker attachToolkit:self.managerToolkitListener
           detectListener:self.managerDetectListener
                immediate:YES
              finishBlock:^(UpDeviceResult *result) {
                if ([result isSuccessful]) {
                    UPDeviceLogDebug(@"%s[%d]broker attachToolkit成功！", __PRETTY_FUNCTION__, __LINE__);
                    [self.deviceDaemon start];
                }
                else {
                    UPDeviceLogError(@"%s[%d]broker attachToolkit失败！error:%@", __PRETTY_FUNCTION__, __LINE__, result.extraInfo);
                }
                if (finishBlock) {
                    finishBlock(result);
                }
              }];
}
- (void)brokerDetachToolkit:(id<UpDeviceBroker>)broker finishBlock:(void (^)(UpDeviceResult *result))finishBlock
{
    [broker detachToolkit:self.managerToolkitListener
           detectListener:self.managerDetectListener
              finishBlock:^(UpDeviceResult *result) {
                if ([result isSuccessful]) {
                    UPDeviceLogDebug(@"%s[%d]broker detachToolkit成功！", __PRETTY_FUNCTION__, __LINE__);
                    [self.deviceDaemon stop];
                }
                else {
                    UPDeviceLogError(@"%s[%d]broker detachToolkit失败！error:%@", __PRETTY_FUNCTION__, __LINE__, result.extraInfo);
                }
                if (finishBlock) {
                    finishBlock(result);
                }
              }];
}

- (void)compareWithOldList:(NSMutableArray<id<UpDevice>> *)deviceList
{
    if (!deviceList) {
        return;
    }

    for (NSString *uniqueId in self.orderList) {
        if (![self isInNewList:deviceList uniqueId:uniqueId]) {
            [self.deviceDaemon remove:uniqueId];
        }
    }
}

- (BOOL)isInNewList:(NSMutableArray<id<UpDevice>> *)deviceList uniqueId:(NSString *)uniqueId
{
    if (!uniqueId) {
        return NO;
    }

    for (id<UpDevice> device in deviceList) {
        NSString *newuniqueId = [device uniqueId];
        if ([uniqueId isEqualToString:newuniqueId]) {
            return YES;
        }
    }

    return NO;
}

- (void)refreshUsdkDeviceList:(void (^)(UpDeviceResult *result))finishBlock
{
    id<UpDeviceBroker> broker = [self getBroker];
    if (broker == nil) {
        UPDeviceLogError(@"[%s][%d]UpDeviceBroker is missing.", __PRETTY_FUNCTION__, __LINE__);
        return;
    }
    id<UpDeviceToolkit> toolkit = [broker getToolkit];
    if (toolkit == nil) {
        UPDeviceLogError(@"[%s][%d]UpDeviceException{extraCode='%@', extraInfo='不支持的协议: '%@'}", __PRETTY_FUNCTION__, __LINE__, CODE_NOT_SUPPORT, INFO_NOT_SUPPORT);
        return;
    }
    [toolkit refreshUsdkDeviceList:^(UpDeviceResult *result) {
      if (finishBlock) {
          finishBlock(result);
      }
    }];
}

- (id<UpDeviceBaseInfo>)createUpDeviceByConfigDevice:(id<UpDeviceBaseInfo>)baseInfo
{
    DeviceBaseInfo *targetBaseInfo;
    if (baseInfo != nil) {
        targetBaseInfo = baseInfo;
        NSString *typeId = baseInfo.typeId;
        NSString *typeCode = baseInfo.typeCode;
        NSString *model = baseInfo.model;
        if (typeId.length == 0 || typeId == nil) {
            targetBaseInfo.typeId = @"empty";
            UPDeviceLogDebug(@"%s[%d]device(%@) typeId is null", __PRETTY_FUNCTION__, __LINE__, baseInfo.deviceId);
        }
        if (typeCode.length == 0 || typeCode == nil) {
            targetBaseInfo.typeCode = @"empty";
        }
        if (model.length == 0 || model == nil) {
            targetBaseInfo.model = @"empty";
        }
    }
    return targetBaseInfo;
}

#pragma mark - public
- (void)setDataSource:(id<UpDeviceDataSource>)deviceDataSource
{
    [self.source setDataSource:deviceDataSource];
}
- (NSString *)getSupportProtocol
{
    NSString *protocol = nil;
    id<UpDeviceToolkit> toolkit = [self.broker getToolkit];
    if (toolkit != nil) {
        protocol = [toolkit getSupportProtocol];
    }
    if ([protocol isKindOfClass:[NSString class]] && protocol.length == 0) {
        protocol = PROTOCOL_UNKNOWN;
    }
    return protocol;
}

- (void)setDeviceComparator:(NSComparator)deviceComparator
{
    self.deviceComparator = deviceComparator;
}

- (void)connectRemoteDevices
{
    id<UpDeviceBroker> broker = [self getBroker];
    if (broker == nil) {
        UPDeviceLogError(@"[%s][%d]UpDeviceBroker is missing.", __PRETTY_FUNCTION__, __LINE__);
        return;
    }
    NSMutableDictionary<NSString *, id> *gatewayParams = [broker gatewayParams];
    UPDeviceLogInfo(@"%s[%d]connectRemoteDevices gatewayParams = %@！", __PRETTY_FUNCTION__, __LINE__, gatewayParams);
    id<UpDeviceToolkit> toolkit = broker.getToolkit;
    if (toolkit == nil) {
        UPDeviceLogError(@"[%s][%d]toolkit为空", __PRETTY_FUNCTION__, __LINE__);
        return;
    }
    [toolkit connectRemoteDevices:nil
                           params:gatewayParams
                      finishBlock:^(UpDeviceResult *result){
                      }];
}

- (id<UpDevice>)destroyDevice:(NSString *)deviceId
{
    NSString *protocol = [self getSupportProtocol];
    return [self destroyDevice:protocol deviceId:deviceId];
}

- (id<UpDevice>)getDevice:(NSString *)deviceId
{
    NSString *protocol = [self getSupportProtocol];
    id<UpDevice> device = [self getDevice:protocol deviceId:deviceId];
    [self perfectDeviceInfoWithDevice:device];
    return device;
}

- (id<UpDevice>)getScannerDevice:(NSString *)deviceId
{
    NSString *protocol = [self getSupportProtocol];
    id<UpDevice> device = [self getScannerDevice:protocol deviceId:deviceId];
    [self perfectDeviceInfoWithDevice:device];
    return device;
}

- (void)disconnectRemoteDevices
{
    self.isDeviceListCreateComplete = NO;

    id<UpDeviceBroker> broker = [self getBroker];
    if (broker == nil) {
        UPDeviceLogError(@"[%s][%d]UpDeviceBroker is missing.", __PRETTY_FUNCTION__, __LINE__);
        return;
    }
    id<UpDeviceToolkit> toolkit = [broker getToolkit];
    if (toolkit == nil) {
        UPDeviceLogError(@"[%s][%d]toolkit为空", __PRETTY_FUNCTION__, __LINE__);
        return;
    }
    [toolkit disconnectRemoteDevices:^(UpDeviceResult *result) {
      if ([result isSuccessful]) {
          UPDeviceLogInfo(@"%s[%d]disconnectRemoteDevices success！", __PRETTY_FUNCTION__, __LINE__);
      }
      else {
          UPDeviceLogInfo(@"%s[%d]disconnectRemoteDevices fail！error:%@", __PRETTY_FUNCTION__, __LINE__, result.extraInfo);
      }
    }];
}

- (void)setCurFamilyPriorityPrepareQueue:(NSArray<NSString *> *)deviceIds
{
    if (![deviceIds isKindOfClass:NSArray.class]) {
        return;
    }
    NSMutableArray<NSString *> *uniqueIds = [NSMutableArray new];
    for (NSString *devcieId in deviceIds) {
        NSString *uniqueId = [UpDeviceHelper genUniqueId:[self getSupportProtocol] DeviceId:devcieId];
        [uniqueIds addObject:uniqueId];
    }
    [self.deviceDaemon setCurFamilyPriorityPrepareQueue:uniqueIds];
}

- (NSArray<NSString *> *)curFamilyPriorityPrepareQueue
{
    return [self.deviceDaemon curFamilyPriorityPrepareQueue];
}

- (void)GIOCountActiveDevice:(NSString *)deviceId
{
    [self.deviceDaemon GIOCountActiveDevice:deviceId];
}

- (void)GIOCountAttachTime
{
    [self.deviceDaemon GIOCountAttachTime];
}

- (void)GIOCountFindConfigFileTime
{
    [self.deviceDaemon GIOCountFindConfigFileTime];
}

- (void)GIOCountParseConfigFileTime
{
    [self.deviceDaemon GIOCountParseConfigFileTime];
}

- (void)updateCurrentFamilyDeviceInfo
{
    NSString *currentFamilyId = UpUserDomainHolder.instance.userDomain.user.currentFamily.familyId;
    if (![currentFamilyId isKindOfClass:[NSString class]]) {
        return;
    }
    [self.source getDeviceList:NO
                   finishBlock:^(UpDeviceResult *result) {
                     if (![result isSuccessful]) {
                         return;
                     }
                     for (id<UpDeviceInfo> info in result.extraData) {
                         NSString *deviceId = info.deviceId;
                         if (![deviceId isKindOfClass:[NSString class]]) {
                             continue;
                         }
                         NSString *deviceFamilyId = info.getExtras[@"DI-Relation.familyId"];
                         NSNumber *sharedDeviceFlag = info.getExtras[@"DI-Product.sharedDeviceFlag"];
                         if (sharedDeviceFlag.boolValue ||
                             [deviceFamilyId isEqualToString:currentFamilyId]) {
                             NSString *uniqueId = [UpDeviceHelper genUniqueId:[info protocol] DeviceId:[info deviceId]];
                             id<UpDevice> device = [self.deviceDaemon getControlDevice:uniqueId];
                             [[device getInfo] updateInfo:info];
                         }
                     }
                   }];
}

#pragma mark - UpDeviceCenter
- (id<UpDeviceBroker>)getBroker
{
    return self.broker;
}
//判断所有协议是否准备就绪
- (BOOL)isReady
{
    id<UpDeviceBroker> broker = [self getBroker];
    return broker != nil && [broker isToolkitReady];
}
//准备协议对应的设备工具
- (void)prepare:(void (^)(UpDeviceResult *result))finishBlock
{
    id<UpDeviceBroker> broker = [self getBroker];
    if (broker == nil) {
        UPDeviceLogError(@"[%s][%d]UpDeviceBroker is missing.", __PRETTY_FUNCTION__, __LINE__);
        if (finishBlock) {
            UpDeviceResult *result = [[UpDeviceResult alloc] initWithErrorCode:ErrorCode_FAILURE extraData:nil extraCode:@"UpDeviceBroker is missing." extraInfo:nil];

            finishBlock(result);
        }
        return;
    }
    [broker prepareToolkit:^(UpDeviceResult *result) {
      if ([result isSuccessful]) {
          UPDeviceLogDebug(@"%s[%d]broker prepareToolkit成功！", __PRETTY_FUNCTION__, __LINE__);
          [[uSDKDeviceScanner sharedScanner] addDelegate:self];
          [self brokerAttchToolkit:broker finishBlock:finishBlock];
      }
      else {
          UPDeviceLogError(@"%s[%d]broker prepareToolkit失败！error:%@", __PRETTY_FUNCTION__, __LINE__, result.extraInfo);
          if (finishBlock) {
              finishBlock(result);
          }
      }
    }];
}
//释放协议对应的设备工具
- (void)release:(void (^)(UpDeviceResult *result))finishBlock
{
    id<UpDeviceBroker> broker = [self getBroker];
    if (broker == nil) {
        UPDeviceLogError(@"[%s][%d]UpDeviceBroker is missing.", __PRETTY_FUNCTION__, __LINE__);
        if (finishBlock) {

            UpDeviceResult *result = [[UpDeviceResult alloc] initWithErrorCode:ErrorCode_FAILURE extraData:nil extraCode:@"UpDeviceBroker is missing." extraInfo:nil];
            finishBlock(result);
        }
        return;
    }
    [broker releaseToolkit:^(UpDeviceResult *result) {
      if ([result isSuccessful]) {
          UPDeviceLogDebug(@"%s[%d]broker releaseToolkit成功！", __PRETTY_FUNCTION__, __LINE__);
          [self brokerDetachToolkit:broker finishBlock:finishBlock];
      }
      else {
          UPDeviceLogError(@"%s[%d]broker releaseToolkit失败！error:%@", __PRETTY_FUNCTION__, __LINE__, result.extraInfo);
          if (finishBlock) {
              finishBlock(result);
          }
      }
    }];
}

- (void)notifyDeviceListChanged
{
    [self notifyDeviceListChange:[self getDeviceList] listeners:self.listenerSet];
}

//更新设备列表
- (void)updateDeviceList:(void (^)(UpDeviceResult *result))finishBlock
{
    [self.source getDeviceList:YES
                   finishBlock:^(UpDeviceResult *result) {
                     if (![result isSuccessful]) {
                         UPDeviceLogError(@"[%s][%d]%@", __PRETTY_FUNCTION__, __LINE__, result.extraInfo);
                         if (finishBlock) {
                             finishBlock(result);
                         }
                         return;
                     }
                     else {
                         UPDeviceLogDebug(@"%s[%d]更新设备列表成功！", __PRETTY_FUNCTION__, __LINE__);
                     }

                     NSMutableArray<id<UpDevice>> *deviceList = [NSMutableArray new];
                     for (id<UpDeviceInfo> info in result.extraData) {
                         id<UpDevice> device = [self createDevice:info]; //设备创建
                         if (device) {
                             [deviceList addObject:device];
                         }
                     }
                     self.isDeviceListCreateComplete = YES;
                     @synchronized(self.orderList)
                     {
                         [self compareWithOldList:deviceList];
                         [self.orderList removeAllObjects];
                         for (id<UpDevice> device in deviceList) {
                             NSString *uniqueId = [device uniqueId];
                             if (uniqueId) {
                                 [self.orderList addObject:uniqueId];
                             }
                         }
                     }
                     result.errorCode = ErrorCode_SUCCESS;
                     result.extraData = deviceList;
                     UPDeviceLogDebug(@"%s[%d]getDeviceList result=%@！", __PRETTY_FUNCTION__, __LINE__, deviceList);
                     if (finishBlock) {
                         finishBlock(result);
                     }

                     if ([result isSuccessful]) {
                         // 修改为异步登陆
                         UPDeviceLogDebug(@"%s[%d]设备列表拉取成功，开始在deviceRemoteLogin线程中异步执行远程登录！", __PRETTY_FUNCTION__, __LINE__);
                         dispatch_queue_t queue =
                             dispatch_queue_create("deviceRemoteLogin", NULL);
                         dispatch_sync(queue, ^{
                           [self connectRemoteDevices];
                           [self refreshUsdkDeviceList:nil];
                         });
                     }
                   }];
}

- (void)updateDeviceList:(BOOL)immediate finishBlock:(void (^)(UpDeviceResult *result))finishBlock
{
    [self.source getDeviceList:immediate
                   finishBlock:^(UpDeviceResult *result) {
                     if (![result isSuccessful]) {
                         UPDeviceLogError(@"[%s][%d]%@", __PRETTY_FUNCTION__, __LINE__, result.extraData);
                         if (finishBlock) {
                             finishBlock(result);
                         }
                         return;
                     }
                     NSMutableArray<id<UpDevice>> *deviceList = [NSMutableArray new];
                     NSArray *extraArray = (NSArray *)result.extraData;
                     [self.deviceDaemon beginGIO:extraArray];
                     for (id<UpDeviceInfo> info in extraArray) {
                         id<UpDevice> device = [self createDevice:info]; //设备创建
                         if (device) {
                             [deviceList addObject:device];
                         }
                         else {
                             UPDeviceLogDebug(@"%s[%d]deviceArray: 设备创建失败 deviceId = %@", __PRETTY_FUNCTION__, __LINE__, info.deviceId);
                         }
                     }
                     self.isDeviceListCreateComplete = YES;
                     @synchronized(self.orderList)
                     {
                         [self compareWithOldList:deviceList];
                         [self.orderList removeAllObjects];
                         for (id<UpDevice> device in deviceList) {
                             NSString *uniqueId = [device uniqueId];
                             if (uniqueId) {
                                 [self.orderList addObject:uniqueId];
                             }
                         }
                     }
                     UPDeviceLogDebug(@"%s[%d]deviceArray: 设备列表更新成功 deviceList = %ld,extraArray = %ld,self.orderList =%ld", __PRETTY_FUNCTION__, __LINE__, deviceList.count, extraArray.count, self.orderList.count);

                     result.errorCode = ErrorCode_SUCCESS;
                     result.extraData = deviceList;
                     if (finishBlock) {
                         finishBlock(result);
                     }
                     if ([result isSuccessful]) {
                         // 修改为异步登陆
                         UPDeviceLogDebug(@"%s[%d]设备列表拉取成功，开始在deviceRemoteLogin线程中异步执行远程登录！", __PRETTY_FUNCTION__, __LINE__);
                         dispatch_queue_t queue =
                             dispatch_queue_create("deviceRemoteLogin", NULL);
                         dispatch_sync(queue, ^{
                           [self connectRemoteDevices];
                           [self refreshUsdkDeviceList:nil];
                         });
                     }
                   }];
}

- (void)getGroupMemberListWithFamilyId:(NSString *)familyId finishBlock:(void (^)(UpDeviceResult *))finishBlock
{
    if (![familyId isKindOfClass:[NSString class]] || familyId.length == 0) {
        UpDeviceResult *result = [[UpDeviceResult alloc] initWithErrorCode:ErrorCode_FAILURE extraData:@[] extraCode:@"familyId is null" extraInfo:nil];
        if (finishBlock) {
            finishBlock(result);
        }
    }
    else {
        [self.source getGroupMemberListWithFamilyId:familyId
                                        finishBlock:^(UpDeviceResult *result) {
                                          if (!result.isSuccessful) {
                                              if (finishBlock) {
                                                  finishBlock(result);
                                              }
                                              return;
                                          }
                                          NSMutableArray<id<UpDevice>> *devices = [NSMutableArray new];
                                          NSArray<id<UpDeviceInfo>> *deviceInfos = result.extraData;
                                          if (![deviceInfos isKindOfClass:[NSArray class]] || deviceInfos.count == 0) {
                                              UpDeviceResult *newResult = [UpDeviceResult UpDeviceResult:ErrorCode_SUCCESS extraData:@[]];
                                              if (finishBlock) {
                                                  finishBlock(newResult);
                                              }
                                              return;
                                          }
                                          for (id<UpDeviceInfo> deviceInfo in deviceInfos) {
                                              id<UpDevice> updevice = [[UpDeviceBase alloc] initWithDeviceInfo:nil deviceInfo:deviceInfo broker:nil factory:nil];
                                              [devices addObject:updevice];
                                          }
                                          UpDeviceResult *newResult = [UpDeviceResult UpDeviceResult:ErrorCode_SUCCESS extraData:devices];
                                          if (finishBlock) {
                                              finishBlock(newResult);
                                          }
                                        }];
    }
}


//添加设备列表变化监听
- (void)attach:(id<Listener>)listener immediate:(BOOL)immediate
{
    if (listener == nil) {
        UPDeviceLogWarning(@"%s[%d]listener参数为空！", __PRETTY_FUNCTION__, __LINE__);
        return;
    }
    @synchronized(self.listenerSet)
    {
        if (![self.listenerSet containsObject:listener]) {
            [self.listenerSet addObject:listener];
        }
    }

    if (!immediate || !self.isDeviceListCreateComplete) {
        return;
    }
    [self notifyListener:listener];
}

- (void)notifyListener:(id<Listener>)listener
{
    NSArray<id<UpDevice>> *deviceList = [self getDeviceList];
    [self notifyDeviceListChange:deviceList listeners:@[ listener ]];
}

//取消设备列表变化监听
- (void)detach:(id<Listener>)listener
{
    if (listener == nil) {
        UPDeviceLogWarning(@"%s[%d]listener参数为空！", __PRETTY_FUNCTION__, __LINE__);
        return;
    }
    @synchronized(self.listenerSet)
    {
        [self.listenerSet removeObject:listener];
    }
}
//创建设备
- (id<UpDevice>)createDevice:(id<UpDeviceInfo>)deviceInfo
{
    if (deviceInfo == nil) {
        return nil;
    }
    id<UpDevice> device = nil;
    [_deviceLock lock];
    NSString *uniqueId = [UpDeviceHelper genUniqueId:[deviceInfo protocol] DeviceId:[deviceInfo deviceId]];
    device = [self.deviceDaemon getControlDevice:uniqueId];
    if (device == nil) {
        id<UpDeviceBroker> broker = [self getBroker];
        device = [self.creator create:uniqueId deviceInfo:deviceInfo broker:broker factory:self.creator];
        [self.deviceDaemon put:uniqueId device:device];
        UPDeviceLogDebug(@"%s[%d]开始创建设备对象，并将设备加入准备(prepare)队列。 uniqueId=%@", __PRETTY_FUNCTION__, __LINE__, uniqueId);
    }
    else {
        if (![[device getInfo].model isEqualToString:deviceInfo.model] && deviceInfo.model.length > 0) {
            UPDeviceLogDebug(@"%s[%d]设备型号（model）更新成功，并将设备加入准备(prepare)队列。 uniqueId=%@,newModel = %@,oldModel = %@", __PRETTY_FUNCTION__, __LINE__, uniqueId, deviceInfo.model, [device getInfo].model);
            [[device getInfo] updateInfo:deviceInfo];
            [self resetLogicEngineDeviceState:device];
            [self.deviceDaemon put:uniqueId device:device];
        }
        else {
            [[device getInfo] updateInfo:deviceInfo];
        }
        UPDeviceLogDebug(@"%s[%d]设备子类对象已经存在，更新设备信息uniqueId=%@", __PRETTY_FUNCTION__, __LINE__, uniqueId);
    }
    [_deviceLock unlock];
    return device;
}
//型号补全时调用重置准备状态方法
- (void)resetLogicEngineDeviceState:(id<UpDevice>)device
{
    if ([device isKindOfClass:[UpDeviceBase class]]) {
        [(UpDeviceBase *)device resetLogicEngineConfigState];
    }
}
//注销后销毁所有设备
- (void)clearDeviceList
{
    for (id<UpDevice> device in [self getDeviceList]) {
        [self destroyDevice:device.getInfo.deviceId];
    }
    [self notifyDeviceListChange:@[] listeners:self.listenerSet];
    [self.deviceDaemon clearPrepareQueue];
}
//销毁设备
- (id<UpDevice>)destroyDevice:(NSString *)protocol deviceId:(NSString *)deviceId
{
    NSString *uniqueId = [UpDeviceHelper genUniqueId:protocol DeviceId:deviceId];
    return [self.deviceDaemon remove:uniqueId];
}
//得到设备
- (id<UpDevice>)getDevice:(NSString *)protocol deviceId:(NSString *)deviceId
{
    NSString *uniqueId = [UpDeviceHelper genUniqueId:protocol DeviceId:deviceId];
    return [self.deviceDaemon get:uniqueId];
}

//得到Scanner设备
- (id<UpDevice>)getScannerDevice:(NSString *)protocol deviceId:(NSString *)deviceId
{
    NSString *uniqueId = [UpDeviceHelper genUniqueId:protocol DeviceId:deviceId];
    return [self.deviceDaemon getScannerDevice:uniqueId];
}

//得到设备列表
- (NSArray<id<UpDevice>> *)getDeviceList
{
    return [self getDeviceList:nil];
}
//得到设备列表
- (NSArray<id<UpDevice>> *)getDeviceList:(nullable id<UpDeviceFilter>)filter
{
    NSArray<id<UpDevice>> *deviceList = [self.deviceDaemon list:filter];
    NSMutableArray *newDevicelist = [NSMutableArray array];
    if (deviceList != nil && deviceList.count > 0) {
        for (id<UpDevice> device in deviceList) {
            NSString *uniqueID = device.uniqueId;
            @synchronized(self.orderList)
            {
                if ([self.orderList containsObject:uniqueID]) {
                    [newDevicelist addObject:device];
                }
            }
        }
    }

    return newDevicelist;
}

//设备是否被绑定
- (BOOL)isBound:(NSString *)deviceId
{
    if (deviceId.length == 0) {
        UPDeviceLogError(@"[%s][%d]deviceId为空", __PRETTY_FUNCTION__, __LINE__);
        return NO;
    }
    id<UpDeviceBroker> broker = [self getBroker];
    if (broker == nil) {
        UPDeviceLogError(@"[%s][%d]UpDeviceBroker is missing.", __PRETTY_FUNCTION__, __LINE__);
        return NO;
    }
    id<UpDeviceToolkit> kit = [broker getToolkit];
    if (kit == nil) {
        UPDeviceLogError(@"[%s][%d]toolkit为空", __PRETTY_FUNCTION__, __LINE__);
        return NO;
    }
    return [kit isBound:deviceId];
}

//获取设备绑定信息
- (void)getDeviceBindInfo:(NSString *)deviceId finishBlock:(void (^)(UpDeviceResult *result))finishBlock
{
    if (deviceId.length == 0) {
        UPDeviceLogError(@"[%s][%d]deviceId为空", __PRETTY_FUNCTION__, __LINE__);
        if (finishBlock) {
            UpDeviceResult *result = [[UpDeviceResult alloc] initWithErrorCode:ErrorCode_FAILURE extraData:nil extraCode:@"deviceId is missing." extraInfo:nil];
            finishBlock(result);
        }
        return;
    }
    id<UpDeviceBroker> broker = [self getBroker];
    if (broker == nil) {
        UPDeviceLogError(@"[%s][%d]UpDeviceBroker is missing.", __PRETTY_FUNCTION__, __LINE__);
        if (finishBlock) {
            UpDeviceResult *result = [[UpDeviceResult alloc] initWithErrorCode:ErrorCode_FAILURE extraData:nil extraCode:@"UpDeviceBroker is missing." extraInfo:nil];

            finishBlock(result);
        }
        return;
    }
    id<UpDeviceToolkit> kit = [broker getToolkit];
    if (kit == nil) {
        UPDeviceLogError(@"[%s][%d]toolkit为空", __PRETTY_FUNCTION__, __LINE__);
        if (finishBlock) {
            UpDeviceResult *result = [[UpDeviceResult alloc] initWithErrorCode:ErrorCode_FAILURE extraData:nil extraCode:@"toolkit is missing." extraInfo:nil];
            finishBlock(result);
        }
        return;
    }
    [kit getDeviceBindInfo:deviceId
               finishBlock:^(UpDeviceResult *result) {
                 if (finishBlock) {
                     finishBlock(result);
                 }
               }];
}

- (void)moveDeviceToQueueHead:(NSString *)deviceId
{
    if (!deviceId) {
        UPLogError(kUPDevicePrefix, @"%s[%d]deviceId is empty.", __PRETTY_FUNCTION__, __LINE__);
        return;
    }
    NSString *uniqueId = [UpDeviceHelper genUniqueId:[self getSupportProtocol] DeviceId:deviceId];
    [self.deviceDaemon moveDeviceToQueueHead:uniqueId];
}

- (void)moveDeviceToQueueHeadPure:(NSString *)deviceId
{
    if (!deviceId) {
        UPLogError(kUPDevicePrefix, @"%s[%d]deviceId is empty.", __PRETTY_FUNCTION__, __LINE__);
        return;
    }
    NSString *uniqueId = [UpDeviceHelper genUniqueId:[self getSupportProtocol] DeviceId:deviceId];
    [self.deviceDaemon moveDeviceToQueueHeadPure:uniqueId];
}

- (void)moveDevicesToQueueHead:(NSArray<NSString *> *)deviceIds
{
    if (!deviceIds) {
        UPLogError(kUPDevicePrefix, @"%s[%d]deviceIds is empty.", __PRETTY_FUNCTION__, __LINE__);
        return;
    }
    NSMutableArray *uniqueIds = [NSMutableArray new];
    [deviceIds enumerateObjectsUsingBlock:^(NSString *_Nonnull devId, NSUInteger idx, BOOL *_Nonnull stop) {
      NSString *uniqueId = [UpDeviceHelper genUniqueId:[self getSupportProtocol] DeviceId:devId];
      if (UPDevice_isValidString(uniqueId)) {
          [uniqueIds addObject:uniqueId];
      }
    }];
    if (uniqueIds.count > 0) {
        [self.deviceDaemon moveDevicesToQueueHead:uniqueIds];
    }
}

- (NSArray<NSString *> *)getPriorityPrepareQueue
{
    return [self.deviceDaemon getPriorityPrepareQueue];
}

- (NSArray<NSString *> *)getPrepareQueue
{
    return [self.deviceDaemon getPrepareQueue];
}

#pragma mark -uSDKDeviceScannerDelegate

- (void)deviceScanner:(uSDKDeviceScanner *)scanner didFindNewDevice:(uSDKDeviceInfo *)device
{
    UPLogDebug(kUPDevicePrefix, @"%s[%d]快连设备(%@)发现新设备!,controlState:%lu", __PRETTY_FUNCTION__, __LINE__, device.deviceID, device.controlState);
    if (device.controlState != uSDKDeviceControlStateNone) {
        [WifiDeviceHelper saveUSDKDeviceInfo:device];
        id<UpDevice> addDevice = [self createScannerDevice:device];
        [addDevice setControlState:device.controlState];
        [self.deviceDaemon putScannerDevice:addDevice.uniqueId device:addDevice];
    }
}

- (void)deviceScanner:(uSDKDeviceScanner *)scanner didRemoveDevice:(uSDKDeviceInfo *)device
{
    UPLogDebug(kUPDevicePrefix, @"%s[%d]快连设备(%@)移除设备!,controlState:%lu", __PRETTY_FUNCTION__, __LINE__, device.deviceID, device.controlState);
}

- (void)deviceScanner:(uSDKDeviceScanner *)scanner didUpdateDevice:(uSDKDeviceInfo *)device
{
    UPLogDebug(kUPDevicePrefix, @"%s[%d]快连设备(%@)更新设备!,controlState:%lu,typeId:%@", __PRETTY_FUNCTION__, __LINE__, device.deviceID, device.controlState, device.uplusID);
    if (device.controlState != uSDKDeviceControlStateNone) {
        [WifiDeviceHelper saveUSDKDeviceInfo:device];
        NSString *uniqueId = [UpDeviceHelper genUniqueId:[self getSupportProtocol] DeviceId:device.deviceID];
        id<UpDevice> updevice = [self.deviceDaemon getScannerDevice:uniqueId];
        if (updevice) {
            id<UpDeviceBaseInfo> baseInfo = [WifiDeviceHelper createDeviceBaseInfoBy:device];
            id<UpDeviceBaseInfo> quickBindBaseInfo = [self createUpDeviceByConfigDevice:baseInfo];
            id<UpDeviceInfo> deviceInfo = [DeviceInfo DeviceInfo:quickBindBaseInfo];
            [updevice.getInfo updateInfo:deviceInfo];
            [updevice setControlState:device.controlState];
            [self.deviceDaemon putScannerDevice:updevice.uniqueId device:updevice];
        }
        else {
            id<UpDevice> addDevice = [self createScannerDevice:device];
            [addDevice setControlState:device.controlState];
            [self.deviceDaemon putScannerDevice:addDevice.uniqueId device:addDevice];
        }
    }
}

- (void)deviceScanner:(uSDKDeviceScanner *)scanner didPermissionInvalid:(uSDKInvalidPermission)invalidPermission
{
    UPLogDebug(kUPDevicePrefix, @"%s[%d]quickbind PermissionInvalid !,%lu", __PRETTY_FUNCTION__, __LINE__, invalidPermission);
}

- (id<UpDevice>)createScannerDevice:(uSDKDeviceInfo *)usdkDevice
{
    id<UpDeviceBaseInfo> baseInfo = [WifiDeviceHelper createDeviceBaseInfoBy:usdkDevice];
    id<UpDeviceBaseInfo> quickBindBaseInfo = [self createUpDeviceByConfigDevice:baseInfo];
    id<UpDeviceBroker> broker = [self getBroker];
    id<UpDeviceInfo> deviceInfo = [DeviceInfo DeviceInfo:baseInfo];

    NSString *uniqueId = [UpDeviceHelper genUniqueId:[quickBindBaseInfo protocol] DeviceId:[quickBindBaseInfo deviceId]];
    id<UpDevice> addDevice = [self.creator create:uniqueId deviceInfo:deviceInfo broker:broker factory:self.creator];
    return addDevice;
}

#pragma mark - perfectWtihStorge

//快连设备补充详细参数
- (void)perfectDeviceInfoWithDevice:(id<UpDevice>)device
{
    if (!device) {
        UPLogDebug(kUPDevicePrefix, @"%s[%d]perfectDeviceInfoWithDevice! device is null", __PRETTY_FUNCTION__, __LINE__);
        return;
    }
    if (device.getControlState == UpDeviceControlState_None) { //非为快连设备
        UPLogDebug(kUPDevicePrefix, @"%s[%d]perfectDeviceInfoWithDevice! device is not quickyDevice,controlState:%ld ", __PRETTY_FUNCTION__, __LINE__, device.getControlState);
        return;
    }

    id<UpDeviceInfo> deviceInfo = [device getInfo];
    if (!UPDevice_isValidOutSpaceString(deviceInfo.prodNo)) {
        UPLogDebug(kUPDevicePrefix, @"%s[%d]perfectDeviceInfoWithDevice! prodNo is null", __PRETTY_FUNCTION__, __LINE__);
        return;
    }
    NSString *storageName = [NSString stringWithFormat:@"%@-%@", UpDevice_QuickyStorageKey, deviceInfo.prodNo];
    NSString *json = [UPStorage getMemoryString:storageName defaultValue:nil];
    if (!UPDevice_isValidOutSpaceString(json)) {
        UPLogDebug(kUPDevicePrefix, @"%s[%d]perfectDeviceInfoWithDevice! json is null", __PRETTY_FUNCTION__, __LINE__);
        return;
    }
    UPLogDebug(kUPDevicePrefix, @"%s[%d]perfectDeviceInfoWithDevice! json is %@", __PRETTY_FUNCTION__, __LINE__, json);
    NSData *jsonData = [json dataUsingEncoding:NSUTF8StringEncoding];
    if (!jsonData) {
        UPLogDebug(kUPDevicePrefix, @"%s[%d]perfectBaseInfo jsonData is null", __PRETTY_FUNCTION__, __LINE__);
        return;
    }
    NSError *error;
    NSDictionary *productInfoDict = [NSJSONSerialization JSONObjectWithData:jsonData options:0 error:&error];
    if (error) {
        UPLogDebug(kUPDevicePrefix, @"%s[%d]perfectBaseInfo json to productInfoDict fail, error:%@", __PRETTY_FUNCTION__, __LINE__, error.description);
        return;
    }
    if (!UPDevice_isValidDictionary(productInfoDict)) {
        UPLogDebug(kUPDevicePrefix, @"%s[%d]perfectBaseInfo productInfoDict is null", __PRETTY_FUNCTION__, __LINE__);
        return;
    }
    [[device getInfo] updateInfo:[self perfectDeviceInfo:deviceInfo WithStorageDict:productInfoDict]];
}

//根据从Storage取出的数据更新deviceInfo
- (id<UpDeviceInfo>)perfectDeviceInfo:(id<UpDeviceInfo>)deviceInfo WithStorageDict:(NSDictionary *)productInfoDict
{
    NSString *model = productInfoDict[@"model"]; //设备型号
    NSString *appTypeName = productInfoDict[@"appTypeName"]; //应用分类名称
    NSString *imageUrl = productInfoDict[@"imageUrl"];
    NSString *brand = productInfoDict[@"brand"];
    if (!UPDevice_isValidOutSpaceString(model)) {
        UPLogDebug(kUPDevicePrefix, @"%s[%d]perfectBaseInfoSetModel is null", __PRETTY_FUNCTION__, __LINE__);
    }

    DeviceBaseInfo *perfectBaseInfo = [DeviceBaseInfo DeviceBaseInfo:deviceInfo.protocol DeviceId:deviceInfo.deviceId TypeId:deviceInfo.typeId TypeName:deviceInfo.typeName typeCode:deviceInfo.typeCode Model:model ProdNo:deviceInfo.prodNo ParentId:deviceInfo.parentId SubDevNo:deviceInfo.subDevNo];
    DeviceInfo *perfectDeviceInfo = [DeviceInfo DeviceInfo:perfectBaseInfo];
    //补充 apptypeName 和 apptypeIcon

    if (!UPDevice_isValidOutSpaceString(appTypeName)) {
        UPLogDebug(kUPDevicePrefix, @"%s[%d]setAppTypeNameFail! appTypeName is null", __PRETTY_FUNCTION__, __LINE__);
    }
    else {
        [perfectDeviceInfo putQCExtra:ExtraAppTypeNameKey Value:appTypeName];
    }

    if (!UPDevice_isValidOutSpaceString(imageUrl)) {
        UPLogDebug(kUPDevicePrefix, @"%s[%d]setImageUrlFail! imageUrl is null", __PRETTY_FUNCTION__, __LINE__);
    }
    else {
        [perfectDeviceInfo putQCExtra:ExtraAppTypeImageUrlKey Value:imageUrl];
    }

    if (!UPDevice_isValidOutSpaceString(brand)) {
        UPLogDebug(kUPDevicePrefix, @"%s[%d]setBrandFail! brand is null", __PRETTY_FUNCTION__, __LINE__);
    }
    else {
        [perfectDeviceInfo putQCExtra:ExtraAppTypeBrandKey Value:brand];
    }

    UPLogDebug(kUPDevicePrefix, @"%s[%d]perfectDeviceInfoExtra extra:%@", __PRETTY_FUNCTION__, __LINE__, perfectDeviceInfo.getExtras);
    return perfectDeviceInfo;
}

#pragma mark 插件逻辑下沉 UpDeviceListener Procotol

- (void)onDeviceReport:(NSInteger)event device:(id)device
{
    [self.currentReportMonitor execute:device event:event runnable:self];
}

- (void)onDeviceReport:(NSInteger)event device:(id)device extra:(id)extra
{
    // 处理设备上报逻辑
}

#pragma mark 插件逻辑下沉 UPDevicePluginCenter Procotol
- (NSArray<id<UpDevice>> *)getDeviceListWithFamilyId:(NSString *)familyId
{
    if (!familyId || ![familyId isKindOfClass:[NSString class]]) {
        UPLogDebug(kUPDevicePrefix, @"%s[%d] familyId error ", __PRETTY_FUNCTION__, __LINE__);
        return nil;
    }

    // 1. 判断是否刷新过设备列表
    if (!self.isDeviceListCreateComplete) {
        UPLogDebug(kUPDevicePrefix, @"%s[%d] device list created complete is false ", __PRETTY_FUNCTION__, __LINE__);
        return nil;
    }

    return (NSArray<id<UpDevice>> *)[UpDeviceCardManager filterDeviceCardList:[[self getDeviceList] copy] with:familyId];
}

- (void)executeOperation:(nonnull NSString *)deviceId subDevice:(nullable NSString *)subDeviceId command:(id<UpDeviceCommand> _Nonnull)command complete:(void (^_Nonnull)(UpDeviceResult *_Nonnull))completeResult
{
    if (!deviceId || ![deviceId isKindOfClass:[NSString class]]) {
        completeResult ?: completeResult([[UpDeviceResult alloc] initWithErrorCode:ErrorCode_INVALID extraData:nil extraCode:@"ErrorCode_INVALID" extraInfo:nil]);
        return;
    }

    id<UpDevice> device = [self getDevice:deviceId];

    if (!device) {
        completeResult ?: completeResult([[UpDeviceResult alloc] initWithErrorCode:ErrorCode_INVALID extraData:nil extraCode:@"ErrorCode_INVALID" extraInfo:nil]);
        return;
    }

    if (subDeviceId && [subDeviceId isKindOfClass:[NSString class]]) {
        NSArray<id<UpDevice>> *subdeviceList = [device getSubDevList];
        __block id<UpDevice> subDevice;
        [subdeviceList enumerateObjectsUsingBlock:^(id<UpDevice> _Nonnull _subDevice, NSUInteger idx, BOOL *_Nonnull stop) {
          if (subDeviceId && [_subDevice.getInfo.deviceId isEqualToString:subDeviceId]) {
              subDevice = _subDevice;
              *stop = YES;
          }
        }];
        if (subDevice) {
            device = subDevice;
        }
        else {
            UPLogDebug(kUPDevicePrefix, @"%s[%d] subDevice is error", __PRETTY_FUNCTION__, __LINE__);
        }
    }

    [device executeCommand:command timeout:15.0 finishBlock:completeResult];
}

- (void)subscribeDeviceChangeWithFamilyId:(NSString *_Nullable)familyId observer:(id<UpDeviceChangeObserver> _Nonnull)observer
{
    if (observer == nil || ![observer conformsToProtocol:@protocol(UpDeviceChangeObserver)]) {
        UPLogDebug(kUPDevicePrefix, @"%s[%d] observer is not comforms to protocol UpDeviceChangeObserver ", __PRETTY_FUNCTION__, __LINE__);
        return;
    }

    if (!familyId || ![familyId isKindOfClass:[NSString class]]) {
        UPLogDebug(kUPDevicePrefix, @"%s[%d] familyId error ", __PRETTY_FUNCTION__, __LINE__);
        return;
    }

    @synchronized(self.familySubscriptionListeners)
    {
        NSArray<id<UpDevice>> *devicelist = [self getDeviceListWithFamilyId:familyId];
        if (devicelist == nil) {
            return;
        }
        for (id<UpDevice> device in devicelist) {
            NSString *deviceId = device.getInfo.deviceId;

            // 处理UpDevice订阅逻辑
            [device attach:self];

            // 存储上层业务订阅关系
            NSMutableArray *listeners = [self.familySubscriptionListeners objectForKey:deviceId];
            if (listeners == nil) {
                listeners = [NSMutableArray array];
            }
            // 查询订阅者是否存在
            if (![listeners containsObject:observer]) {
                [listeners addObject:observer];
                [self.familySubscriptionListeners setObject:listeners forKey:deviceId];
            }
        }
        [observer onFamilyDevicesPropertyChanged:devicelist];
    }
}

- (void)subscribeDeviceChangeWithDeviceIds:(NSArray<NSString *> *_Nullable)deviceIds observer:(id<UpDeviceChangeObserver> _Nonnull)observer
{
    if (observer == nil || ![observer conformsToProtocol:@protocol(UpDeviceChangeObserver)]) {
        UPLogDebug(kUPDevicePrefix, @"%s[%d] observer is not comforms to protocol UpDeviceChangeObserver ", __PRETTY_FUNCTION__, __LINE__);
        return;
    }

    if (!deviceIds || ![deviceIds isKindOfClass:[NSArray class]] || deviceIds.count == 0) {
        UPLogDebug(kUPDevicePrefix, @"%s[%d] deviceIds error ", __PRETTY_FUNCTION__, __LINE__);
        return;
    }

    @synchronized(self.devicesSubscriptionListeners)
    {
        NSArray<id<UpDevice>> *devicelist = [self filterDeviceListWithDeviceIds:deviceIds];
        for (id<UpDevice> device in devicelist) {
            NSString *deviceId = device.getInfo.deviceId;

            // 处理UpDevice订阅逻辑
            [device attach:self];

            // 存储上层业务订阅关系
            NSMutableArray *listeners = [self.devicesSubscriptionListeners objectForKey:deviceId];
            if (listeners == nil) {
                listeners = [NSMutableArray array];
            }
            // 查询订阅者是否存在
            if (![listeners containsObject:observer]) {
                [listeners addObject:observer];
                [self.devicesSubscriptionListeners setObject:listeners forKey:deviceId];
            }
        }
        [observer onDevicesPropertyChanged:devicelist];
    }
}

- (void)unsubscribeDeviceChangeWithFamilyId:(NSString *_Nullable)familyId observer:(id<UpDeviceChangeObserver> _Nonnull)observer
{
    if (observer == nil || ![observer conformsToProtocol:@protocol(UpDeviceChangeObserver)]) {
        UPLogDebug(kUPDevicePrefix, @"%s[%d] observer is not comforms to protocol UpDeviceChangeObserver ", __PRETTY_FUNCTION__, __LINE__);
        return;
    }

    if (!familyId || ![familyId isKindOfClass:[NSString class]]) {
        UPLogDebug(kUPDevicePrefix, @"%s[%d] familyId error ", __PRETTY_FUNCTION__, __LINE__);
        return;
    }

    @synchronized(self.familySubscriptionListeners)
    {
        NSArray<id<UpDevice>> *devicelist = [self getDeviceListWithFamilyId:familyId];
        if (devicelist == nil) {
            return;
        }
        for (id<UpDevice> device in devicelist) {
            NSString *deviceId = device.getInfo.deviceId;
            // 移除上层业务订阅关系
            NSMutableArray *listeners = [self.familySubscriptionListeners objectForKey:deviceId];

            if (listeners == nil) {
                continue;
            }

            [listeners removeObject:observer];

            if ([listeners count] != 0) {
                [self.familySubscriptionListeners setObject:listeners forKey:deviceId];
            }
            else {
                [self checkAndRemoveDeviceSubscription:deviceId];
            }
        }
    }
}

- (void)unsubscribeDeviceChangeWithDeviceIds:(NSArray<NSString *> *_Nullable)deviceIds observer:(id<UpDeviceChangeObserver> _Nonnull)observer
{
    if (observer == nil || ![observer conformsToProtocol:@protocol(UpDeviceChangeObserver)]) {
        UPLogDebug(kUPDevicePrefix, @"%s[%d] observer is not comforms to protocol UpDeviceChangeObserver ", __PRETTY_FUNCTION__, __LINE__);
        return;
    }

    if (!deviceIds || ![deviceIds isKindOfClass:[NSArray class]] || deviceIds.count == 0) {
        UPLogDebug(kUPDevicePrefix, @"%s[%d] deviceIds error ", __PRETTY_FUNCTION__, __LINE__);
        return;
    }
    @synchronized(self.familySubscriptionListeners)
    {
        NSArray<id<UpDevice>> *devicelist = [self filterDeviceListWithDeviceIds:deviceIds];

        for (id<UpDevice> device in devicelist) {
            NSString *deviceId = device.getInfo.deviceId;
            // 移除上层业务订阅关系
            NSMutableArray *listeners = [self.devicesSubscriptionListeners objectForKey:deviceId];

            if (listeners == nil) {
                continue;
            }

            [listeners removeObject:observer];

            if ([listeners count] != 0) {
                [self.devicesSubscriptionListeners setObject:listeners forKey:deviceId];
            }
            else {
                [self checkAndRemoveDeviceSubscription:deviceId];
            }
        }
    }
}

- (void)checkAndRemoveDeviceSubscription:(NSString *)deviceId
{
    if (!deviceId) {
        return;
    }

    NSMutableArray *familyListeners = [self.familySubscriptionListeners objectForKey:deviceId];
    NSMutableArray *deviceListeners = [self.devicesSubscriptionListeners objectForKey:deviceId];

    id<UpDevice> device = [self getDevice:deviceId];

    if (!device) {
        return;
    }

    if ((!deviceListeners || deviceListeners.count == 0) && (!familyListeners || familyListeners.count == 0)) {
        [device detach:self];
    }
}

// 按照设备ID过滤设备
- (NSArray<id<UpDevice>> *)filterDeviceListWithDeviceIds:(NSArray<NSString *> *_Nullable)deviceIds
{
    NSArray<id<UpDevice>> *deviceList = [[self getDeviceList] copy];
    NSPredicate *validPredicate = [NSPredicate predicateWithFormat:@"SELF IN %@", deviceIds];

    return [deviceList filteredArrayUsingPredicate:validPredicate];
}
/**
 * 根据指定条件过滤设备列表
 *
 * @param deviceList 原始设备列表
 * @param filterBlock 过滤条件的 block，返回 YES 表示保留该设备，返回 NO 表示过滤掉
 * @return 过滤后的设备列表
 */
- (NSArray<id<UpDevice>> *)filterDeviceList:(NSArray<id<UpDevice>> *)deviceList
                                filterBlock:(BOOL (^)(id<UpDevice> device))filterBlock
{
    if (!deviceList || deviceList.count == 0 || !filterBlock) {
        return deviceList;
    }

    return [deviceList filteredArrayUsingPredicate:[NSPredicate predicateWithBlock:^BOOL(id<UpDevice> device, NSDictionary *bindings) {
                         return filterBlock(device);
                       }]];
}

#pragma mark 设备属性数据流量控制
- (void)monitorRunnable:(NSArray<id<UpDevice>> *)deviceData
{
    [self processDeviceReportData:deviceData];
}

- (void)processDeviceReportData:(NSArray<id<UpDevice>> *)devices
{
    // 创建临时字典存储每个observer对应的设备列表
    NSMutableSet *familyObservers = [NSMutableSet set];
    NSMutableSet *deviceObservers = [NSMutableSet set];

    // 遍历每个待上报的设备
    for (id<UpDevice> device in devices) {
        NSString *deviceId = device.getInfo.deviceId;
        // 处理按家庭ID订阅的观察者
        [familyObservers addObjectsFromArray:self.familySubscriptionListeners[deviceId] ?: @[]];
        // 处理按设备ID订阅的观察者
        [deviceObservers addObjectsFromArray:self.devicesSubscriptionListeners[deviceId] ?: @[]];
    }

    [familyObservers enumerateObjectsUsingBlock:^(id _Nonnull obj, BOOL *_Nonnull stop) {
      [obj onFamilyDevicesPropertyChanged:devices];
    }];

    [deviceObservers enumerateObjectsUsingBlock:^(id _Nonnull obj, BOOL *_Nonnull stop) {
      [obj onDevicesPropertyChanged:devices];
    }];
}

@end
