//
//  DeviceManagerDetectListener.m
//  UPDevice
//
//  Created by gump on 15/1/2019.
//  Copyright © 2019 海尔优家智能科技（北京）有限公司. All rights reserved.
//

#import "DeviceManagerDetectListener.h"
#import "UPDeviceLog.h"
#import "UpDeviceInjection.h"

@implementation DeviceManagerDetectListener

- (void)onFind:(NSArray<id<UpDeviceBaseInfo>> *)baseInfoList
{
    UPDeviceLogInfo(@"%s[%d]onFind: %@", __PRETTY_FUNCTION__, __LINE__, baseInfoList);
}

- (void)onLose:(NSArray<id<UpDeviceBaseInfo>> *)baseInfoList
{
    UPDeviceLogInfo(@"%s[%d]onLose: %@", __PRETTY_FUNCTION__, __LINE__, baseInfoList);
}

- (void)onDeviceListChanged
{
    [self performSelector:@selector(delayUpdateDeviceList) withObject:nil afterDelay:5.0];
}

- (void)delayUpdateDeviceList
{
    [[UpDeviceInjection getInstance]
            .deviceManager updateDeviceList:YES
                                finishBlock:^(UpDeviceResult *result) {
                                  [[UpDeviceInjection getInstance].deviceManager notifyDeviceListChanged];
                                  if (result.isSuccessful) {
                                      UPDeviceLogInfo(@"%s[%d]onDeviceListChanged successful", __PRETTY_FUNCTION__, __LINE__);
                                  }
                                  else {
                                      UPDeviceLogInfo(@"%s[%d]onDeviceListChanged fail", __PRETTY_FUNCTION__, __LINE__);
                                  }
                                }];
}

@synthesize detectListener;

@end
