//
//  UpDeviceInjection.m
//  UPDevice
//
//  Created by gump on 8/1/2019.
//  Copyright © 2019 海尔优家智能科技（北京）有限公司. All rights reserved.
//

#import "UpDeviceInjection.h"
#import "UpDeviceDataSourceWrapper.h"
#import "DefaultDeviceBroker.h"
#import "WifiDeviceToolkitImpl.h"
#import <UPStorage/UPStorage.h>

@implementation UpDeviceInjection
+ (UpDeviceInjection *)getInstance
{
    static dispatch_once_t once_t;
    static UpDeviceInjection *instance = nil;
    dispatch_block_t block = ^{
      instance = [[self alloc] init];
    };
    dispatch_once(&once_t, block);
    return instance;
}

- (void)initDeviceManagerWithWifi:(id<WifiDeviceToolkit>)toolkit dataSource:(id<UpDeviceDataSource>)dataSource
{
    UpDeviceDataSourceWrapper *source = [[UpDeviceDataSourceWrapper alloc] init];
    [source setDataSource:dataSource];
    _deviceManager = [[UpDeviceManager alloc] initDeviceManagerWithToolkit:toolkit dataSource:source];
    [UPStorage putMemoryString:UP_DEVICE_MANGER_INIT_KEY value:UP_DEVICE_MANGER_INIT_VALUE];
}

@end
