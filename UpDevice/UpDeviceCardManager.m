//
//  UpDeviceCardManager.m
//  UPDevice
//
//  Created by <PERSON><PERSON><PERSON>   on 2025/3/31.
//  Copyright © 2025 海尔优家智能科技（北京）有限公司. All rights reserved.
//

#import "UpDeviceCardManager.h"

typedef NS_ENUM(NSInteger, UpDeviceClassType) {
    ClassType_First = 0, //设备大类
    ClassType_Second, //设备中类
};

@implementation UpDeviceCardManager
// 按照家庭ID过滤设备
+ (NSArray<id<UpDeviceCard>> *)filterDeviceCardList:(NSArray<id<UpDeviceCard>> *)deviceCardList with:(NSString *)familyId
{
    NSPredicate *validPredicate = [NSPredicate predicateWithBlock:^BOOL(id<UpDeviceCard> device, NSDictionary *bindings) {
      return [[device getDeviceCardFamilyId] isEqualToString:familyId] || [device getDeviceCardIsSharedDevice];
    }];
    return [deviceCardList filteredArrayUsingPredicate:validPredicate];
}

// 按照家庭ID排序设备列表
+ (NSArray<id<UpDeviceCard>> *)sortDeviceCardList:(NSArray<id<UpDeviceCard>> *)deviceCardList with:(NSString *)familyId
{
    NSArray<id<UpDeviceCard>> *sortedDevices = [deviceCardList sortedArrayUsingComparator:^NSComparisonResult(id<UpDeviceCard> obj1, id<UpDeviceCard> obj2) {
      UpDeviceCardInfo *card1 = [obj1 getDeviceCardInfo:familyId];
      UpDeviceCardInfo *card2 = [obj2 getDeviceCardInfo:familyId];
      NSNumber *sort1 = @(card1.cardSort);
      NSNumber *sort2 = @(card2.cardSort);
      return [sort1 compare:sort2];
    }];

    return sortedDevices;
}

/// 过滤当前家庭Top {limitCount} 的设备ID
+ (NSArray<NSString *> *)filterAndSortDeviceCardList:(NSArray<id<UpDeviceCard>> *)deviceCardList with:(NSString *)familyId limitCount:(NSInteger)limitCount
{
    // 过滤非当前家庭设备
    NSArray<id<UpDeviceCard>> *familyDevices = [UpDeviceCardManager filterDeviceCardList:deviceCardList with:familyId];
    // 过滤聚合设备
    NSPredicate *predicate = [NSPredicate predicateWithBlock:^BOOL(id<UpDeviceCard> device, NSDictionary<NSString *, id> *_Nullable bindings) {
      return ![device getDeviceCardIsAggregateDevice];
    }];
    NSArray<id<UpDeviceCard>> *noAggregateFamilyDevices = [familyDevices filteredArrayUsingPredicate:predicate];

    NSArray<id<UpDeviceCard>> *sortedDevices = [UpDeviceCardManager sortDeviceCardList:noAggregateFamilyDevices with:familyId];
    NSArray<id<UpDeviceCard>> *top20 = [sortedDevices subarrayWithRange:NSMakeRange(0, MIN(limitCount, sortedDevices.count))];
    NSMutableArray<NSString *> *deviceIds = [NSMutableArray array];
    for (id<UpDeviceCard> device in top20) {
        [deviceIds addObject:[device getDeviceCardDeviceId]];
    }
    return [deviceIds copy];
}


+ (BOOL)isWashingMachineWithTypeId:(NSString *)typeId
{
    NSString *hexResult = [UpDeviceCardManager deviceClassNumber:typeId deviceClassType:ClassType_First];

    //根据机型编码对应表 4:波轮洗衣机 5:滚筒洗衣机 31:干衣机(此数值为16进制数）
    if ([hexResult isEqualToString:@"4"] || [hexResult isEqualToString:@"5"] || [hexResult isEqualToString:@"31"]) {
        return YES;
    }
    return NO;
}

+ (NSString *)deviceClassNumber:(NSString *)typeId deviceClassType:(UpDeviceClassType)deviceClassType
{
    if (typeId == nil || typeId.length < 20) {
        return @"0";
    }
    int iRange = 14;
    if (deviceClassType == ClassType_Second) {
        iRange = 16;
    }
    int result = -1;
    NSString *begin = [typeId substringToIndex:1];
    if ([begin isEqualToString:@"0"]) {
        NSRange range = NSMakeRange(iRange, 2);
        NSString *firststr = [typeId substringWithRange:range];
        long firsti = strtoul([firststr UTF8String], 0, 16);
        firsti = firsti << 2;
        range = NSMakeRange(iRange + 2, 2);
        NSString *secondstr = [typeId substringWithRange:range];
        long secondi = strtoul([secondstr UTF8String], 0, 16);
        secondi = secondi >> 6;
        result = (firsti + secondi) & 0xff;
    }
    else {
        NSRange range = NSMakeRange(iRange + 2, 1);
        NSString *firststr = [typeId substringWithRange:range];
        long firsti = strtoul([firststr UTF8String], 0, 16);
        firsti = firsti << 4;
        range = NSMakeRange(iRange + 3, 1);
        NSString *secondstr = [typeId substringWithRange:range];
        long secondi = strtoul([secondstr UTF8String], 0, 16);
        result = (firsti + secondi) & 0xff;
    }
    //10进制转换成16进制 字符串
    NSString *numHex = [self toHex:result];
    return numHex;
}

#pragma mark - private
+ (NSString *)toHex:(long long int)tmpid
{
    NSString *nLetterValue;
    NSString *str = @"";
    long long int ttmpig;
    for (int i = 0; i < 9; i++) {
        ttmpig = tmpid % 16;
        tmpid = tmpid / 16;
        nLetterValue = [self nLetterValue:ttmpig];
        str = [nLetterValue stringByAppendingString:str];
        if (tmpid == 0) {
            break;
        }
    }
    return str;
}

+ (NSString *)nLetterValue:(long long int)ttmpig
{
    NSString *nLetterValue = nil;
    if (ttmpig == 10) {
        nLetterValue = @"a";
    }
    else if (ttmpig == 11) {
        nLetterValue = @"b";
    }
    else if (ttmpig == 12) {
        nLetterValue = @"c";
    }
    else if (ttmpig == 13) {
        nLetterValue = @"d";
    }
    else if (ttmpig == 14) {
        nLetterValue = @"e";
    }
    else if (ttmpig == 15) {
        nLetterValue = @"f";
    }
    else {
        nLetterValue = [[NSString alloc] initWithFormat:@"%lli", ttmpig];
    }
    return nLetterValue;
}

@end
