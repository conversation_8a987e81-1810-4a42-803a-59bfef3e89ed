//
//  UpDeviceResourceManager.h
//  UPDevice
//
//  Created by 王杰 on 2022/12/15.
//  Copyright © 2022 海尔优家智能科技（北京）有限公司. All rights reserved.
//

#import <Foundation/Foundation.h>
#import <UPResource/UPResourceInjection.h>
#import "UpDeviceResult.h"
NS_ASSUME_NONNULL_BEGIN

@interface UpDeviceResourceManager : NSObject
+ (UpDeviceResourceManager *)getInstance;

/// 获取设备侧应用模型
/// @param deviceId 设备Id
/// @param finishBlock  结果 result.extraData为设备侧应用模型string
- (void)getAppFuncModelWithDeviceId:(NSString *)deviceId finishBlock:(void (^)(UpDeviceResult *))finishBlock;
@end

NS_ASSUME_NONNULL_END
