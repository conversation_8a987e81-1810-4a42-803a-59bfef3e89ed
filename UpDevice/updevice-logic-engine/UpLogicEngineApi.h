//
//  UpLogicEngineApi.h
//  UPDevice
//
//  Created by <PERSON> on 2018/12/28.
//  Copyright © 2018 海尔优家智能科技（北京）有限公司. All rights reserved.
//

#import <Foundation/Foundation.h>
#import <LogicEngine/LEEngine.h>

NS_ASSUME_NONNULL_BEGIN

typedef NS_ENUM(NSInteger, UpConfigState) {
    UpConfigStateUnknown,
    UpConfigStateNotSupport,
    UpConfigStateSupport
};

@class UpDeviceResult;
@class UPResourceManager;
@protocol UpLogicEngineApi <NSObject>
@property (nonatomic, assign, readonly) UpConfigState configState;
@property (nonatomic, strong, readonly) LEEngine *engine;

- (void)setResourceManager:(UPResourceManager *)resourceManager;
- (BOOL)isEngineWarning;
- (BOOL)isEngineSupported;
- (void)attachEngine:(id<LEEngineObserver>)listener;
- (void)detachEngine:(id<LEEngineObserver>)listener;
- (nullable NSArray<LEAttribute *> *)getEngineInitAttributeList;

- (nullable LEAttribute *)getEngineAttributeByName:(NSString *)name;
- (void)calculateEngineCommand:(LEDeviceAttribute *)command clean:(BOOL)isClean completion:(void (^)(UpDeviceResult *result))completion;
- (void)calculateEngineCommands:(NSArray<LEDeviceAttribute *> *)commands clean:(BOOL)isClean completion:(void (^)(UpDeviceResult *result))completion;
- (void)operateEngineCommand:(void (^)(UpDeviceResult *result))completion;

//first excute calculateEngineCommand ,if success then excute operateEngineCommand
- (void)operateEngineCommand:(LEDeviceAttribute *)command clean:(BOOL)isClean completion:(void (^)(UpDeviceResult *result))completion;

/// 资源配置文件的下载
/// 可选择业务应用场景，按需加载，加载后，产生以下变化：
/// 1.设备属性中的功能属性描述会按照配置文件的功能属性描述进行修改；
/// 2.设备属性中的功能属性值的描述会按照配置文件的功能属性值描述进行修改；
/// 3.具备资源配置文件Icon的获取能力，通过 [engine getAttributeIconMap]方法调用，返回图标数据；
- (void)loadingResourceConfiguration;
@end
NS_ASSUME_NONNULL_END
