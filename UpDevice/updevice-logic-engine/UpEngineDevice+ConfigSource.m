//
//  UpEngineDevice+ConfigSource.m
//  UPDevice
//
//  Created by <PERSON> on 2019/1/9.
//  Copyright © 2019 海尔优家智能科技（北京）有限公司. All rights reserved.
//

#import "UpEngineDevice+ConfigSource.h"
#import <objc/runtime.h>
#import "UPDeviceLog.h"
#import <LogicEngine/LECommonFuncs.h>
#import "UPResourceErrCodes.h"
#import "UPResourceTask.h"
#import "UpEngineDevice+EngineDelegate.h"
#import "UpDeviceInjection.h"

const char *kUpEngineDeviceConfigFileState = "kUpEngineDeviceConfigFileState";
const char *kUpEngineDeviceCompletionBlockInfo = "kUpEngineDeviceCompletionBlockInfo";

@implementation UpEngineDevice (ConfigSource)
#pragma mark - UPResourceCallback
- (void)onPrompt:(UPResourceTask *)task action:(NSString *)action processor:(id<UPResourceProcessor>)processor
{
    [task resume];
}

- (void)onResult:(BOOL)success message:(NSString *)message resourceInfo:(UPResourceInfo *)info
{
    NSString *model = self.getInfo.model;
    NSString *typeID = self.getInfo.typeId;
    NSString *deviceId = self.getInfo.deviceId;
    void (^installCompletion)(UPResourceInfo *resource, NSError *error) = self.completionInfo[info.version];
    if (installCompletion == nil) {
        UPDeviceLogWarning(@"[%s][%d]逻辑引擎设备(%@)的配置文件(%@)安装回调为空！model:%@,typeID:%@", __PRETTY_FUNCTION__, __LINE__, deviceId, info.version, model, typeID);
        return;
    }
    if (success) {
        self.engineConfigFileState = UpConfigStateSupport;
        UPDeviceLogDebug(@"[%s][%d]逻辑引擎设备(%@)的配置文件安装成功！model:%@,typeID:%@", __PRETTY_FUNCTION__, __LINE__, deviceId, model, typeID);
        installCompletion(info, nil);
    }
    else {
        UPDeviceLogError(@"[%s][%d]逻辑引擎设备(%@)的配置文件安装失败！model:%@,typeID:%@,errMsg:%@", __PRETTY_FUNCTION__, __LINE__, deviceId, model, typeID, message);
        installCompletion(info, [NSError errorWithDomain:message code:-1 userInfo:nil]);
    }
    [self.completionInfo removeObjectForKey:info.version];
}

#pragma mark - UPResourceListener
- (void)onProgressChanged:(NSString *)processor progress:(NSUInteger)progress
{
}

#pragma mark - Non-Public Methods
- (void)reloadEngineWithResource:(UPResourceInfo *)resourceInfo
{
    NSString *model = self.getInfo.model;
    NSString *typeID = self.getInfo.typeId;
    NSString *deviceId = self.getInfo.deviceId;
    void (^reloadCompletion)(UpDeviceResult *result) = ^(UpDeviceResult *_Nonnull result) {
      if (result.isSuccessful) {
          UPDeviceLogDebug(@"[%s][%d]逻辑引擎设备(%@)重载完成！model:%@,typeID:%@", __PRETTY_FUNCTION__, __LINE__, deviceId, model, typeID);
      }
      else {
          UPDeviceLogError(@"[%s][%d]逻辑引擎设备(%@)重载失败！model:%@,typeID:%@", __PRETTY_FUNCTION__, __LINE__, deviceId, model, typeID);
      }
    };
    UPDeviceLogDebug(@"[%s][%d]逻辑引擎设备(%@)开始重载！model:%@,typeID:%@", __PRETTY_FUNCTION__, __LINE__, deviceId, model, typeID);
    [self handleReload:reloadCompletion];
}

#pragma mark - Property Methods
- (UpConfigState)engineConfigFileState
{
    NSNumber *number = objc_getAssociatedObject(self, kUpEngineDeviceConfigFileState);
    if (![number isKindOfClass:[NSNumber class]]) {
        return UpConfigStateUnknown;
    }
    UpConfigState state = [number integerValue];
    return state;
}
- (void)resetLogicEngineConfigState
{
    self.engineConfigFileState = UpConfigStateUnknown;
    self.dataSource.configFilePath = nil;
    [self onReleased];
    [super resetLogicEngineConfigState];
}
- (void)setEngineConfigFileState:(UpConfigState)engineConfigFileState
{
    UpConfigState storedState = UpConfigStateUnknown;
    switch (engineConfigFileState) {
        case UpConfigStateNotSupport:
            storedState = UpConfigStateNotSupport;
            break;
        case UpConfigStateSupport:
            storedState = UpConfigStateSupport;
            break;
        default:
            storedState = UpConfigStateUnknown;
            break;
    }
    objc_setAssociatedObject(self, kUpEngineDeviceConfigFileState, @(storedState), OBJC_ASSOCIATION_RETAIN);
}

- (NSMutableDictionary<NSString *, void (^)(UPResourceInfo *_Nullable, NSError *_Nullable)> *)completionInfo
{
    NSMutableDictionary *info = objc_getAssociatedObject(self, kUpEngineDeviceCompletionBlockInfo);
    return info;
}

- (void)setCompletionInfo:(NSMutableDictionary<NSString *, void (^)(UPResourceInfo *_Nullable, NSError *_Nullable)> *)completionInfo
{
    objc_setAssociatedObject(self, kUpEngineDeviceCompletionBlockInfo, completionInfo, OBJC_ASSOCIATION_RETAIN);
}

#pragma mark - Public Methods
- (void)prepareEngineDeviceConfigSource:(void (^)(UPResourceInfo *resource, NSError *error))completion
{
    NSString *model = self.getInfo.model;
    NSString *typeID = self.getInfo.typeId;
    NSString *prodNo = self.getInfo.prodNo;
    NSString *deviceId = self.getInfo.deviceId;

    void (^installCompletion)(UPResourceInfo *resource, NSError *error) = ^(UPResourceInfo *resource, NSError *error) {
      if (error) {
          UPDeviceLogError(@"[%s][%d]逻辑引擎设备(%@)的配置文件：%@ model:%@,typeID:%@", __PRETTY_FUNCTION__, __LINE__, deviceId, error.domain, model, typeID);
          return;
      }
      UPDeviceLogDebug(@"[%s][%d]设备(%@)开始重载逻辑引擎方法！model:%@,typeID:%@", __PRETTY_FUNCTION__, __LINE__, deviceId, model, typeID);
      [self reloadEngineWithResource:resource];
    };

    void (^configFileInfoUpdateCompletion)(NSArray<UPResourceInfo *> *resource, NSError *error) = ^(NSArray<UPResourceInfo *> *resource, NSError *error) {
      if (self.engineConfigFileState == UpConfigStateSupport) {
          UPResourceInfo *newResource = resource.firstObject;
          if (!newResource.active) {
              UPDeviceLogDebug(@"[%s][%d]该设备(%@)在本地已有配置文件,但配置文件有更新，需要下载！model:%@,typeID:%@", __PRETTY_FUNCTION__, __LINE__, deviceId, model, typeID);
          }
          else {
              UPDeviceLogDebug(@"[%s][%d]该设备(%@)在本地已有配置文件,不需要下载。model:%@,typeID:%@", __PRETTY_FUNCTION__, __LINE__, deviceId, model, typeID);
          }
          [self installEngineDeviceResource:resource.firstObject withCompletion:installCompletion];
          return;
      }
      if (!error) {
          UPResourceInfo *newResource = resource.firstObject;
          if (newResource.active) {
              self.engineConfigFileState = UpConfigStateSupport;
              [[UpDeviceInjection getInstance].deviceManager GIOCountActiveDevice:deviceId];
              [[UpDeviceInjection getInstance].deviceManager GIOCountFindConfigFileTime];
              UPDeviceLogDebug(@"[%s][%d]该设备(%@)在本地已有配置文件,不需要下载！model:%@,typeID:%@", __PRETTY_FUNCTION__, __LINE__, deviceId, model, typeID);
          }
          else {
              UPDeviceLogDebug(@"[%s][%d]该设备(%@)的配置文件信息更新成功！model:%@,typeID:%@", __PRETTY_FUNCTION__, __LINE__, deviceId, model, typeID);
          }
          if (completion) {
              completion(newResource, nil);
          }
          return;
      }
      UPDeviceLogError(@"[%s][%d]逻辑引擎设备(%@)的配置文件信息更新失败！model:%@,typeID:%@,error:%@", __PRETTY_FUNCTION__, __LINE__, deviceId, model, typeID, error);
      if (error.code == UPResErrorCodeServerResponseEmptyList) {
          self.engineConfigFileState = UpConfigStateNotSupport;
          [[UpDeviceInjection getInstance].deviceManager GIOCountFindConfigFileTime];
      }
      if (completion) {
          completion(nil, error);
      }
    };

    UPDeviceLogDebug(@"[%s][%d]开始更新逻辑引擎设备(%@)的配置文件信息！model:%@,typeID:%@", __PRETTY_FUNCTION__, __LINE__, deviceId, model, typeID);

    if (model.length == 0) {
        if (completion) {
            UPDeviceLogError(@"[%s][%d]逻辑引擎设备(%@)的配置文件信息型号缺失！model:%@,typeID:%@", __PRETTY_FUNCTION__, __LINE__, deviceId, model, typeID);
            self.engineConfigFileState = UpConfigStateNotSupport;
            completion(nil, [NSError errorWithDomain:@"设备型号缺失" code:-1 userInfo:nil]);
            return;
        }
    }

    if (NULL == self.resManager) {
        if (completion) {
            UPDeviceLogError(@"[%s][%d]逻辑引擎设备(%@)的资源管理器缺失！", __PRETTY_FUNCTION__, __LINE__, deviceId);
            self.engineConfigFileState = UpConfigStateUnknown;

            completion(nil, [NSError errorWithDomain:@"资源管理器缺失" code:-1 userInfo:nil]);
            return;
        }
    }

    UPResourceDeviceCondition *condition = [[UPResourceDeviceCondition alloc] initResourceType:UPResourceTypeDeviceConfig];
    condition.model = model;
    condition.typeId = typeID;
    condition.prodNo = prodNo;
    [self.resManager requestDeviceResList:condition immediate:NO completion:configFileInfoUpdateCompletion];
}

- (void)installEngineDeviceResource:(UPResourceInfo *)resource withCompletion:(void (^)(UPResourceInfo *resource, NSError *error))completion
{
    NSString *model = self.getInfo.model;
    NSString *typeID = self.getInfo.typeId;
    NSString *deviceId = self.getInfo.deviceId;
    if (![resource isKindOfClass:[UPResourceInfo class]]) {
        UPDeviceLogError(@"[%s][%d]待安装逻辑引擎设备(%@)的配置文件信息有误！model:%@,typeID:%@", __PRETTY_FUNCTION__, __LINE__, deviceId, model, typeID);
        if (completion) {
            completion(resource, [NSError errorWithDomain:@"资源信息有误！" code:-1 userInfo:nil]);
        }
        return;
    }
    if (resource.active) {
        UPDeviceLogDebug(@"[%s][%d]逻辑引擎设备(%@)的配置文件本地已存在，无需下载！model:%@,typeID:%@", __PRETTY_FUNCTION__, __LINE__, deviceId, model, typeID);
        if (completion) {
            completion(resource, [NSError errorWithDomain:@"配置文件本地已存在，无需下载！" code:-1 userInfo:nil]);
        }
        return;
    }
    if (![self.completionInfo isKindOfClass:[NSMutableDictionary class]]) {
        self.completionInfo = [NSMutableDictionary dictionary];
    }
    if (completion) {
        [self.completionInfo setObject:completion forKey:resource.version];
    }
    UPDeviceLogDebug(@"[%s][%d]开始下载逻辑引擎设备(%@)的配置文件！model:%@,typeID:%@", __PRETTY_FUNCTION__, __LINE__, deviceId, model, typeID);
    [self.resManager install:resource callback:self listener:self];
}

- (void)prepareEngineWithResource:(UPResourceInfo *)resource withCompletion:(void (^)(LEEngine *engine, NSError *error))completion
{
    NSString *model = self.getInfo.model;
    NSString *typeID = self.getInfo.typeId;
    NSString *deviceId = self.getInfo.deviceId;
    UPDeviceLogDebug(@"[%s][%d]开始创建逻辑引擎设备(%@)的逻辑引擎对象！model:%@,typeID:%@", __PRETTY_FUNCTION__, __LINE__, deviceId, model, typeID);
    if (![resource isKindOfClass:[UPResourceInfo class]] || !resource.active || !LECommon_isValidString(resource.path)) {
        UPDeviceLogError(@"[%s][%d]逻辑引擎设备(%@)的配置文件信息有误！model:%@,typeID:%@", __PRETTY_FUNCTION__, __LINE__, deviceId, model, typeID);
        if (completion) {
            completion(nil, [NSError errorWithDomain:@"资源信息有误！" code:-1 userInfo:nil]);
        }
        return;
    }
    self.dataSource.configFilePath = resource.path;
    LEEngine *engine = [LEEngine engineWithDeviceID:deviceId deviceDelegate:self configDataSource:self.dataSource];
    if (![engine isKindOfClass:[LEEngine class]]) {
        UPDeviceLogError(@"[%s][%d]逻辑引擎设备(%@)的逻辑引擎对象创建失败！model:%@,typeID:%@", __PRETTY_FUNCTION__, __LINE__, deviceId, model, typeID);
        if (completion) {
            completion(nil, [NSError errorWithDomain:@"逻辑引擎对象创建失败!" code:-1 userInfo:nil]);
        }
        return;
    }
    void (^startEngineCompletion)(NSError *error) = ^(NSError *error) {
      [[UpDeviceInjection getInstance].deviceManager GIOCountParseConfigFileTime];
      LEEngine *eng = nil;
      if (error == nil) {
          UPDeviceLogDebug(@"[%s][%d]逻辑引擎设备(%@)的逻辑引擎对象创建成功！model:%@,typeID:%@", __PRETTY_FUNCTION__, __LINE__, deviceId, model, typeID);
      }
      else {
          UPDeviceLogError(@"[%s][%d]逻辑引擎设备(%@)的逻辑引擎对象创建失败！model:%@,typeID:%@,error:%@", __PRETTY_FUNCTION__, __LINE__, deviceId, model, typeID, error);
      }
      eng = engine;

      if (completion) {
          completion(eng, error);
      }
    };
    [engine startLogicEngineWithCompletion:startEngineCompletion];
}

@end
