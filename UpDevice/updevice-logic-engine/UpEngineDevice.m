//
//  UpEngineDevice.m
//  UPDevice
//
//  Created by <PERSON> on 2018/12/28.
//  Copyright © 2018 海尔优家智能科技（北京）有限公司. All rights reserved.
//

#import "UpEngineDevice.h"
#import "UpEngineDevice+ConfigSource.h"
#import "UpEngineReporter.h"
#import <LogicEngine/LEEngine.h>
#import <LogicEngine/LEDeviceAttribute.h>
#import <LogicEngine/LECalcLogicResult.h>
#import "UpEngineDevice+EngineDelegate.h"
#import "UPDeviceLog.h"
#import "GetDeviceAttributes.h"
#import "GetDeviceCautions.h"
#import "GetDeviceConnection.h"
#import "GetDeviceOnlineStateV2.h"
#import "UpEngineDevice+ResourceConfigSource.h"

@interface UpEngineDevice ()
@property (atomic, strong) LEEngine *logicEngine;
@property (nonatomic, strong) UpEngineReporter *engineReporter;
@property (nonatomic, strong) UpDeviceResult *dummyEngineResult;
@property (nonatomic, strong) UpDeviceResult *notSupportEngineResult;
@end

@implementation UpEngineDevice
#pragma mark - UpLogicEngineApi
- (LEEngine *)engine
{
    return self.logicEngine;
}

- (UpConfigState)configState
{
    return self.engineConfigFileState;
}

- (void)setResourceManager:(UPResourceManager *)resourceManager
{
    self.resManager = resourceManager;
}

- (BOOL)isEngineWarning
{
    return self.engine.alarms.count;
}

- (BOOL)isEngineSupported
{
    return self.engineConfigFileState == UpConfigStateSupport;
}

- (void)attachEngine:(id<LEEngineObserver>)listener
{
    if (![listener conformsToProtocol:@protocol(LEEngineObserver)]) {
        NSString *deviceId = self.deviceId;
        UPDeviceLogError(@"[%s][%d]逻辑引擎设备(%@)订阅失败！订阅对象(%@)不符合LEEngineObserver协议！", __PRETTY_FUNCTION__, __LINE__, deviceId, listener);
        return;
    }
    [self.engineReporter add:listener];
}

- (void)detachEngine:(id<LEEngineObserver>)listener
{
    if (![listener conformsToProtocol:@protocol(LEEngineObserver)]) {
        NSString *deviceId = self.deviceId;
        UPDeviceLogError(@"[%s][%d]逻辑引擎设备(%@)订阅取消失败！订阅对象(%@)不符合LEEngineObserver协议！", __PRETTY_FUNCTION__, __LINE__, deviceId, listener);
        return;
    }
    [self.engineReporter remove:listener];
}


- (nullable NSArray<LEAttribute *> *)getEngineInitAttributeList
{
    NSString *deviceId = self.deviceId;
    LEEngine *engine = self.engine;
    if (![engine isKindOfClass:[LEEngine class]]) {
        UPDeviceLogError(@"[%s][%d]逻辑引擎设备(%@)初始属性列表获取失败！引擎对象格式有误！engine:%@", __PRETTY_FUNCTION__, __LINE__, deviceId, engine);
        return nil;
    }
    return engine.initialFunctionAttributes;
}

- (NSArray<LEAttribute *> *)getEngineAttributeList
{
    NSString *deviceId = self.deviceId;
    LEEngine *engine = self.engine;
    if (![engine isKindOfClass:[LEEngine class]]) {
        UPDeviceLogError(@"[%s][%d]逻辑引擎设备(%@)属性列表获取失败！引擎对象格式有误！engine:%@", __PRETTY_FUNCTION__, __LINE__, deviceId, engine);
        return nil;
    }
    return engine.attributes;
}

- (nullable LEAttribute *)getEngineAttributeByName:(NSString *)name
{
    NSString *deviceId = self.deviceId;
    LEEngine *engine = self.engine;
    if (![engine isKindOfClass:[LEEngine class]]) {
        UPDeviceLogError(@"[%s][%d]逻辑引擎设备(%@)属性(%@)获取失败！引擎对象格式有误！engine:%@", __PRETTY_FUNCTION__, __LINE__, deviceId, name, engine);
        return nil;
    }
    return [engine getAttributeByName:name];
}

- (NSArray<LEAlarm *> *)getEngineCautionList
{
    NSString *deviceId = self.deviceId;
    LEEngine *engine = self.engine;
    if (![engine isKindOfClass:[LEEngine class]]) {
        UPDeviceLogError(@"[%s][%d]逻辑引擎设备(%@)报警列表获取失败！引擎对象格式有误！engine:%@", __PRETTY_FUNCTION__, __LINE__, deviceId, engine);
        return nil;
    }
    return engine.alarms;
}

- (void)calculateEngineCommand:(LEDeviceAttribute *)command clean:(BOOL)isClean completion:(void (^)(UpDeviceResult *result))completion
{
    NSString *deviceId = self.deviceId;
    LEEngine *engine = self.engine;
    if (![engine isKindOfClass:[LEEngine class]] || !engine.isEngineReady) {
        UPDeviceLogError(@"[%s][%d]逻辑引擎设备(%@)命令计算失败！引擎对象格式有误或状态未就绪！", __PRETTY_FUNCTION__, __LINE__, deviceId);
        if (completion) {
            UpDeviceResult *result = [UpDeviceResult UpDeviceResult:ErrorCode_FAILURE extraData:nil];
            completion(result);
        }
        return;
    }
    if (![command isKindOfClass:[LEDeviceAttribute class]]) {
        UPDeviceLogError(@"[%s][%d]逻辑引擎设备(%@)命令计算失败！命令参数格式有误！command:%@", __PRETTY_FUNCTION__, __LINE__, deviceId, command);
        if (completion) {
            UpDeviceResult *result = [UpDeviceResult UpDeviceResult:ErrorCode_INVALID extraData:nil];
            completion(result);
        }
        return;
    }
    NSString *cmdName = command.name;
    NSString *cmdValue = command.value;
    NSString *isCleanStr = isClean ? @"YES" : @"NO";
    UPDeviceLogDebug(@"[%s][%d]逻辑引擎设备(%@)开始计算命令。cmdName:%@,cmdValue:%@.isClean:%@", __PRETTY_FUNCTION__, __LINE__, deviceId, cmdName, cmdValue, isCleanStr);
    LECalcLogicResult *result = [engine calculate:@[ command ] clean:isClean];
    if (result.success) {
        UPDeviceLogDebug(@"[%s][%d]逻辑引擎设备(%@)命令计算成功！cmdName:%@,cmdValue:%@.isClean:%@", __PRETTY_FUNCTION__, __LINE__, deviceId, cmdName, cmdValue, isCleanStr);
    }
    else {
        UPDeviceLogError(@"[%s][%d]逻辑引擎设备(%@)命令计算失败！cmdName:%@,cmdValue:%@.isClean:%@,error:%@", __PRETTY_FUNCTION__, __LINE__, deviceId, cmdName, cmdValue, isCleanStr, result.errorDesc);
    }
    UpDeviceResultErrorCode errCode = result.success ? ErrorCode_SUCCESS : ErrorCode_FAILURE;
    if (completion) {
        UpDeviceResult *result = [UpDeviceResult UpDeviceResult:errCode extraData:nil];
        completion(result);
    }
}

- (void)calculateEngineCommands:(NSArray<LEDeviceAttribute *> *)commands clean:(BOOL)isClean completion:(void (^)(UpDeviceResult *result))completion
{
    NSString *deviceId = self.deviceId;
    LEEngine *engine = self.engine;
    if (![engine isKindOfClass:[LEEngine class]] || !engine.isEngineReady) {
        UPDeviceLogError(@"[%s][%d]逻辑引擎设备(%@)命令计算失败！引擎对象格式有误或状态未就绪！", __PRETTY_FUNCTION__, __LINE__, deviceId);
        if (completion) {
            UpDeviceResult *result = [UpDeviceResult UpDeviceResult:ErrorCode_FAILURE extraData:nil];
            completion(result);
        }
        return;
    }
    if (![commands isKindOfClass:[NSArray class]]) {
        UPDeviceLogError(@"[%s][%d]逻辑引擎设备(%@)命令计算失败！命令参数格式有误！commands:%@", __PRETTY_FUNCTION__, __LINE__, deviceId, commands);
        if (completion) {
            UpDeviceResult *result = [UpDeviceResult UpDeviceResult:ErrorCode_INVALID extraData:nil];
            completion(result);
        }
        return;
    }
    NSString *isCleanStr = isClean ? @"YES" : @"NO";
    UPDeviceLogDebug(@"[%s][%d]逻辑引擎设备(%@)开始计算命令。cmds:%@,isClean:%@", __PRETTY_FUNCTION__, __LINE__, deviceId, commands, isCleanStr);
    LECalcLogicResult *result = [engine calculate:commands clean:isClean];
    if (result.success) {
        UPDeviceLogDebug(@"[%s][%d]逻辑引擎设备(%@)命令计算成功！cmds:%@,isClean:%@", __PRETTY_FUNCTION__, __LINE__, deviceId, commands, isCleanStr);
    }
    else {
        UPDeviceLogError(@"[%s][%d]逻辑引擎设备(%@)命令计算失败！cmds:%@,isClean:%@,error:%@", __PRETTY_FUNCTION__, __LINE__, deviceId, commands, isCleanStr, result.errorDesc);
    }
    UpDeviceResultErrorCode errCode = result.success ? ErrorCode_SUCCESS : ErrorCode_FAILURE;
    if (completion) {
        UpDeviceResult *result = [UpDeviceResult UpDeviceResult:errCode extraData:nil];
        completion(result);
    }
}

- (void)operateEngineCommand:(void (^)(UpDeviceResult *result))completion
{
    NSString *deviceId = self.deviceId;
    LEEngine *engine = self.engine;
    if (![engine isKindOfClass:[LEEngine class]] || !engine.isEngineReady) {
        UPDeviceLogError(@"[%s][%d]逻辑引擎设备(%@)命令下发失败！逻辑引擎对象格式有误或状态未就绪！", __PRETTY_FUNCTION__, __LINE__, deviceId);
        if (completion) {
            UpDeviceResult *result = [UpDeviceResult UpDeviceResult:ErrorCode_FAILURE extraData:nil];
            completion(result);
        }
        return;
    }
    LECompletion completionBlock = ^(NSError *error) {
      NSString *errorDescription = nil;
      NSInteger errCode = ErrorCode_SUCCESS;
      if (error) {
          errorDescription = error.localizedDescription;
          errCode = error.code;
          UPDeviceLogError(@"[%s][%d]逻辑引擎设备(%@)命令下发失败！error:%@", __PRETTY_FUNCTION__, __LINE__, deviceId, errorDescription);
      }
      else {
          UPDeviceLogDebug(@"[%s][%d]逻辑引擎设备(%@)命令下发成功！", __PRETTY_FUNCTION__, __LINE__, deviceId);
      }
      if (completion) {
          UpDeviceResult *result = [[UpDeviceResult alloc] initWithErrorCode:errCode extraData:nil extraCode:errorDescription extraInfo:nil];
          completion(result);
      }
    };
    UPDeviceLogDebug(@"[%s][%d]逻辑引擎设备(%@)命令执行下发操作。", __PRETTY_FUNCTION__, __LINE__, deviceId);
    [engine operateCMDWithCompletion:completionBlock];
}

- (void)operateEngineCommand:(LEDeviceAttribute *)command clean:(BOOL)isClean completion:(void (^)(UpDeviceResult *result))completion
{
    __weak typeof(self) weakSelf = self;
    [self calculateEngineCommand:command
                           clean:isClean
                      completion:^(UpDeviceResult *_Nonnull result) {

                        __strong typeof(weakSelf) strongSelf = weakSelf;

                        if (!strongSelf) {
                            return;
                        }

                        UPDeviceLogDebug(@"[%s][%d]逻辑引擎设备(%@)计算命令(%@)操作结果(%ld)", __PRETTY_FUNCTION__, __LINE__, strongSelf.deviceId, command, result.errorCode);

                        if (result.isSuccessful) {
                            [strongSelf operateEngineCommand:^(UpDeviceResult *_Nonnull result) {
                              completion(result);
                            }];
                        }
                        else {
                            completion(result);
                        }
                      }];
}

#pragma mark - UpEngineReloadHandler
- (void)handleReload:(void (^)(UpDeviceResult *))completion
{
    [self reload:completion];
}

#pragma mark - UpExtendDevice
- (id<UpDevice>)getExtApi
{
    return self;
}

- (BOOL)isExtApiPrepared
{
    LEEngine *engine = self.engine;
    return [engine isKindOfClass:[LEEngine class]] && engine.isEngineReady;
}

- (void)onPrepareExtApi:(void (^)(UpDeviceResult *result))finishBlock
{
    //added according to 7005 of engineDevice.feature
    if (self.engineConfigFileState == UpConfigStateNotSupport) {
        UPDeviceLogWarning(@"[%s][%d]逻辑引擎设备(%@)没有支持的配置文件资源。", __PRETTY_FUNCTION__, __LINE__, self.deviceId);
        if (finishBlock) {
            finishBlock(self.notSupportEngineResult);
        }
        return;
    }

    //added according to 7006 of engineDevice.feature
    if (self.engine && [self.engine isKindOfClass:[LEEngine class]]) {

        [self.engine startLogicEngineWithCompletion:^(NSError *error) {
          if (finishBlock) {
              UpDeviceResult *result = [UpDeviceResult UpDeviceResult:ErrorCode_SUCCESS extraData:nil];
              if (error && error.code != ErrorCode_SUCCESS) {
                  result.errorCode = error.code;
              }

              finishBlock(result);
          }
        }];
        return;
    }

    NSString *deviceId = self.deviceId;
    void (^enginePrepareCompletion)(LEEngine *engine, NSError *error) = ^(LEEngine *engine, NSError *error) {
      //just prepate failure,the engin object is created successful
      self.logicEngine = engine;

      if (error) {
          UPDeviceLogError(@"[%s][%d]逻辑引擎设备(%@)准备失败！error:%@", __PRETTY_FUNCTION__, __LINE__, deviceId, error);
          if (finishBlock) {
              UpDeviceResult *result = [[UpDeviceResult alloc] initWithErrorCode:ErrorCode_FAILURE extraData:nil extraCode:error.localizedDescription extraInfo:nil];
              finishBlock(result);
          }
          return;
      }


      UPDeviceLogDebug(@"[%s][%d]逻辑引擎设备(%@)准备完毕!", __PRETTY_FUNCTION__, __LINE__, deviceId);
      if (finishBlock) {
          UpDeviceResult *result = [UpDeviceResult UpDeviceResult:ErrorCode_SUCCESS extraData:nil];
          finishBlock(result);
      }
    };

    void (^installCompletion)(UPResourceInfo *resource, NSError *error) = ^(UPResourceInfo *resource, NSError *error) {
      if (error) {
          UPDeviceLogError(@"[%s][%d]逻辑引擎设备(%@)准备失败！error:%@", __PRETTY_FUNCTION__, __LINE__, deviceId, error);
          if (finishBlock) {
              UpDeviceResult *result = [[UpDeviceResult alloc] initWithErrorCode:ErrorCode_FAILURE extraData:nil extraCode:error.localizedDescription extraInfo:nil];
              finishBlock(result);
          }
          return;
      }
      [self prepareEngineWithResource:resource withCompletion:enginePrepareCompletion];
    };

    void (^completion)(UPResourceInfo *resource, NSError *error) = ^(UPResourceInfo *resource, NSError *error) {

      if (self.engineConfigFileState == UpConfigStateNotSupport) {
          UPDeviceLogWarning(@"[%s][%d]逻辑引擎设备(%@)没有支持的配置文件资源。", __PRETTY_FUNCTION__, __LINE__, deviceId);
          if (finishBlock) {
              finishBlock(self.notSupportEngineResult);
          }
          return;
      }

      if (error) {
          UPDeviceLogError(@"[%s][%d]逻辑引擎设备(%@)准备失败！error:%@", __PRETTY_FUNCTION__, __LINE__, deviceId, error);
          if (finishBlock) {
              UpDeviceResult *result = [[UpDeviceResult alloc] initWithErrorCode:ErrorCode_FAILURE extraData:nil extraCode:error.localizedDescription extraInfo:nil];
              finishBlock(result);
          }
          return;
      }
      if (!resource.active) {
          [self installEngineDeviceResource:resource withCompletion:installCompletion];
          return;
      }

      [self prepareEngineWithResource:resource withCompletion:enginePrepareCompletion];
    };
    UPDeviceLogDebug(@"[%s][%d]逻辑引擎设备(%@)开始准备。", __PRETTY_FUNCTION__, __LINE__, deviceId);
    [self prepareEngineDeviceConfigSource:completion];
}

- (void)onReleaseExtApi:(void (^)(UpDeviceResult *result))finishBlock
{
    NSString *deviceId = self.deviceId;
    if (self.engineConfigFileState == UpConfigStateNotSupport || self.engineConfigFileState == UpConfigStateUnknown) {
        UPDeviceLogWarning(@"[%s][%d]逻辑引擎设备(%@)没有支持的配置文件资源。state =%ld", __PRETTY_FUNCTION__, __LINE__, deviceId, self.engineConfigFileState);
        if (finishBlock) {
            finishBlock(self.notSupportEngineResult);
        }
        return;
    }
    LEEngine *engine = self.engine;
    if (![engine isKindOfClass:[LEEngine class]]) {
        UPDeviceLogError(@"[%s][%d]逻辑引擎设备(%@)释放失败！设备的逻辑引擎对象属性格式有误！engine:%@", __PRETTY_FUNCTION__, __LINE__, deviceId, engine);
        if (finishBlock) {
            UpDeviceResult *result = [UpDeviceResult UpDeviceResult:ErrorCode_FAILURE extraData:nil];
            finishBlock(result);
        }
        return;
    }
    void (^engineStopCompletion)(NSError *error) = ^(NSError *error) {
      if (error) {
          UPDeviceLogError(@"[%s][%d]逻辑引擎设备(%@)释放失败！error:%@", __PRETTY_FUNCTION__, __LINE__, deviceId, error);
          if (finishBlock) {
              UpDeviceResult *result = [[UpDeviceResult alloc] initWithErrorCode:ErrorCode_FAILURE extraData:nil extraCode:error.localizedDescription extraInfo:nil];
              finishBlock(result);
          }
          return;
      }
      UPDeviceLogDebug(@"[%s][%d]逻辑引擎设备(%@)释放完毕!", __PRETTY_FUNCTION__, __LINE__, deviceId);
      if (finishBlock) {
          UpDeviceResult *result = [UpDeviceResult UpDeviceResult:ErrorCode_SUCCESS extraData:nil];
          finishBlock(result);
      }
    };
    UPDeviceLogDebug(@"[%s][%d]逻辑引擎设备(%@)开始释放。", __PRETTY_FUNCTION__, __LINE__, deviceId);
    [engine stopLogicEngineWithCompletion:engineStopCompletion];
}

- (void)onReloadExtApi:(void (^)(UpDeviceResult *result))finishBlock
{
    NSString *deviceId = self.deviceId;
    if (self.engineConfigFileState == UpConfigStateNotSupport) {
        UPDeviceLogWarning(@"[%s][%d]逻辑引擎设备(%@)没有支持的配置文件资源。state =%ld", __PRETTY_FUNCTION__, __LINE__, deviceId, self.engineConfigFileState);
        if (finishBlock) {
            finishBlock(self.notSupportEngineResult);
        }
        return;
    }
    LEEngine *engine = self.logicEngine;
    if (![engine isKindOfClass:[LEEngine class]]) {
        UPDeviceLogError(@"[%s][%d]逻辑引擎设备(%@)重载失败！设备的逻辑引擎对象属性格式有误！engine:%@", __PRETTY_FUNCTION__, __LINE__, deviceId, engine);
        if (finishBlock) {
            UpDeviceResult *result = [UpDeviceResult UpDeviceResult:ErrorCode_FAILURE extraData:nil];
            finishBlock(result);
        }
        return;
    }

    if (![engine isEngineReady]) {
        if (finishBlock) {
            UpDeviceResult *result = [UpDeviceResult UpDeviceResult:ErrorCode_FAILURE extraData:nil];
            finishBlock(result);
        }
        return;
    }

    [engine updateConfigFile];
    UPDeviceLogDebug(@"[%s][%d]逻辑引擎设备(%@)重载完毕。", __PRETTY_FUNCTION__, __LINE__, deviceId);
    if (finishBlock) {
        UpDeviceResult *result = [UpDeviceResult UpDeviceResult:ErrorCode_SUCCESS extraData:nil];
        finishBlock(result);
    }
}

#pragma mark - Property Methods
- (UpDeviceResult *)dummyEngineResult
{
    if (_dummyEngineResult == nil) {
        _dummyEngineResult = [[UpDeviceResult alloc] initWithErrorCode:ErrorCode_SUCCESS extraData:nil extraCode:@"There is no need to implement this function." extraInfo:nil];
    }
    return _dummyEngineResult;
}

- (UpDeviceResult *)notSupportEngineResult
{
    if (_notSupportEngineResult == nil) {
        _notSupportEngineResult = [[UpDeviceResult alloc] initWithErrorCode:ErrorCode_SUCCESS extraData:nil extraCode:@"LogicEngine doesn't support this device." extraInfo:nil];
    }
    return _notSupportEngineResult;
}

#pragma mark - Non-Public Methods
- (void)onPrepared
{
    NSArray *observers = [self.engineReporter getAll];
    for (id<LEEngineObserver> engineObserver in observers) {
        [self.engine addEngineObserver:engineObserver];
    }
    [self processConnection:[self getConnection]];
    [self processAttributes:[self getAttributeList]];
    [self processCautions:[self getCautionList]];
    [self refreshDeviceStatus];
}

- (void)refreshDeviceStatus
{
    id<UpDeviceBroker> broke = [self getBroker];
    id<UpDeviceToolkit> toolKit = broke.getToolkit;
    [toolKit execute:GetDeviceConnection_NAME
              params:@{ WifiDeviceAction_KEY_DEVICE_ID : self.deviceId }
         finishBlock:^(UpDeviceResult *result) {
           if (result.isSuccessful) {
               NSNumber *number = result.extraData;
               if ([number isKindOfClass:[NSNumber class]]) {
                   [self processConnection:number.integerValue];
               }
           }
         }];
    [toolKit execute:GetDeviceAttributes_NAME
              params:@{ WifiDeviceAction_KEY_DEVICE_ID : self.deviceId }
         finishBlock:^(UpDeviceResult *result) {
           if (result.isSuccessful) {
               [self processAttributes:result.extraData];
           }
         }];
    [toolKit execute:GetDeviceCautions_NAME
              params:@{ WifiDeviceAction_KEY_DEVICE_ID : self.deviceId }
         finishBlock:^(UpDeviceResult *result) {
           if (result.isSuccessful) {
               [self processCautions:result.extraData];
           }
         }];
    [toolKit execute:GetDeviceOnlineStateV2_NAME
              params:@{ WifiDeviceAction_KEY_DEVICE_ID : self.deviceId }
         finishBlock:^(UpDeviceResult *result) {
           if (result.isSuccessful) {
               NSNumber *number = result.extraData;
               if ([number isKindOfClass:[NSNumber class]]) {
                   [self processOnlineStateV2:number.integerValue];
               }
           }
         }];
}

- (void)onReleased
{
    self.logicEngine = nil;
}

#pragma mark - Public Methods
- (instancetype)initEngineDeviceWithUniqueID:(NSString *)uniqueId deviceInfo:(id<UpDeviceInfo>)deviceInfo broker:(id<UpDeviceBroker>)broker factory:(id<UpDeviceFactory>)factory
{
    if (self = [super initWithDeviceInfo:uniqueId deviceInfo:deviceInfo broker:broker factory:factory]) {
        _engineReporter = [UpEngineReporter EngineReporter];
        self.engineConfigFileState = UpConfigStateUnknown;
        _dataSource = [[UpEngineDataSource alloc] initWithDeviceInfo:deviceInfo];
    }
    return self;
}

- (void)processDeviceInfo:(id<UpDeviceBaseInfo>)baseInfo
{
}

- (void)processConnection:(UpDeviceConnection)connection
{
    [self.engine refreshDeviceNetStatus:UPEngineDevice_convertConnectionStatusToLEDeviceNetStatus(connection)];
    [self.engineReporter notifyLogicEngine:self.engine event:UpEngineEvent_EVENT_CONNECTION_CHANGE];
}

- (void)processOnlineStateV2:(UpDeviceRealOnlineV2)state
{
    [self.engine refreshOnlineStateV2:UPEngineDevice_convertOnlineStateV2(state)];
}

- (void)processSubDevList:(NSArray<id<UpDeviceBaseInfo>> *)subDevList
{
    UPDeviceLogWarning(@"%s[%d]该方法尚未实现，不应该被调用！", __PRETTY_FUNCTION__, __LINE__);
}

- (void)processAttributes:(NSArray<id<UpDeviceAttribute>> *)attributeList
{
    [self.engine refreshDeviceAttributes:UPEngineDevice_convertUpDeviceAttributesIntoLEDeviceAttributes(attributeList)];
    [self.engineReporter notifyLogicEngine:self.engine event:UpEngineEvent_EVENT_ATTRIBUTES_CHANGE];
}

- (void)processCautions:(NSArray<id<UpDeviceCaution>> *)cautionList
{
    [self.engine refreshDeviceAlarms:UPEngineDevice_convertUpDeviceAlarmsIntoLEDeviceAlarms(cautionList)];
    [self.engineReporter notifyLogicEngine:self.engine event:UpEngineEvent_EVENT_DEVICE_CAUTION];
}

- (void)processBleState:(UpDeviceConnection)state
{
    [self.engine refreshDeviceBleState:UPEngineDevice_convertConnectionStatusToLEDeviceNetStatus(state)];
}

- (void)processOfflineCause:(UpDeviceOfflineCause)offlineCause
{
    [self.engine refreshDeviceOfflineCause:UPEngineDevice_convertOfflineCause(offlineCause)];
}

- (void)processReceived:(NSString *)name data:(id)data
{
    UPDeviceLogWarning(@"%s[%d]该方法尚未实现，不应该被调用！", __PRETTY_FUNCTION__, __LINE__);
}

- (void)loadingResourceConfiguration
{
    if (self.getInfo == nil) {
        return;
    }
    NSString *deviceId = self.getInfo.deviceId;
    if (![deviceId isKindOfClass:[NSString class]]) {
        return;
    }
    __weak typeof(self) weakSelf = self;
    [self executeLoadingResourceConfigurationTaskWithDeviceId:deviceId
                                                     complete:^(NSString *_Nullable content, NSError *_Nullable error) {
                                                       if ([error isKindOfClass:[NSError class]]) {
                                                           UPDeviceLogDebug(@"[%s][%d]逻辑引擎设备ErrorMessage:%@, errorCode:%ld", __PRETTY_FUNCTION__, __LINE__, error.localizedDescription, error.code);
                                                           return;
                                                       }
                                                       if (weakSelf.engine != nil) {
                                                           [weakSelf.engine loadResourceConfig:content];
                                                       }
                                                     }];
}


@end
