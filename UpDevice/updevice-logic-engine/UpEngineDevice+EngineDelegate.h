//
//  UpEngineDevice+EngineDelegate.h
//  UPDevice
//
//  Created by <PERSON> on 2019/1/9.
//  Copyright © 2019 海尔优家智能科技（北京）有限公司. All rights reserved.
//

#import "UpEngineDevice.h"
#import <LogicEngine/LEDeviceDelegate.h>

NS_ASSUME_NONNULL_BEGIN

LEDeviceNetStatus UPEngineDevice_convertConnectionStatusToLEDeviceNetStatus(UpDeviceConnection connnection);
LEDeviceOnlineStateV2 UPEngineDevice_convertOnlineStateV2(UpDeviceRealOnlineV2 state);
LEDeviceOfflineCause UPEngineDevice_convertOfflineCause(UpDeviceOfflineCause offlineCause);

NSArray<LEDeviceAttribute *> *UPEngineDevice_convertUpDeviceAttributesIntoLEDeviceAttributes(NSArray<id<UpDeviceAttribute>> *attributes);
NSArray<LEDeviceAlarm *> *UPEngineDevice_convertUpDeviceAlarmsIntoLEDeviceAlarms(NSArray<id<UpDeviceCaution>> *alarms);

@interface UpEngineDevice (EngineDelegate) <LEDeviceDelegate>

@end

NS_ASSUME_NONNULL_END
