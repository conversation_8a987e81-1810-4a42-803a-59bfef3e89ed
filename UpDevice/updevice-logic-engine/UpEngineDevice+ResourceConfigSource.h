//
//  UpEngineDevice+ResourceConfigSource.h
//  UPDevice
//
//  Created by xianwen<PERSON> on 2023/5/8.
//

#import "UpEngineDevice.h"
#import "UpDeviceResourceManager.h"

NS_ASSUME_NONNULL_BEGIN

@interface UpEngineDevice (ResourceConfigSource)
/// 获取资源配置文件下载管理器
/// @return 下载管理器
- (UpDeviceResourceManager *)getDeviceResourceManager;

/// 执行加载资源配置文件
/// @param completeBlock 返回资源配置文件Json，error
/// Ext 错误原因[1001 参数错误,1002 内部错误]
- (void)executeLoadingResourceConfigurationTaskWithDeviceId:(NSString *_Nullable)deviceId complete:(void (^)(NSString *_Nullable, NSError *_Nullable))completeBlock;
@end

NS_ASSUME_NONNULL_END
