//
//  UpEngineDevice+EngineDelegate.m
//  UPDevice
//
//  Created by <PERSON> on 2019/1/9.
//  Copyright © 2019 海尔优家智能科技（北京）有限公司. All rights reserved.
//

#import "UpEngineDevice+EngineDelegate.h"
#import <LogicEngine/LECommonFuncs.h>
#import <LogicEngine/LEDeviceBaseInfo.h>
#import <LogicEngine/LEDeviceAttribute.h>
#import <LogicEngine/LEDeviceAlarm.h>
#import <LogicEngine/LEDeviceCMD.h>
#import "UpDeviceConnection.h"
#import "UpDeviceAttribute.h"
#import "UpDeviceCaution.h"
#import "UpDeviceCommand.h"
#import "DeviceCommand.h"
#import "UPDeviceLog.h"

LEDeviceNetStatus UPEngineDevice_convertConnectionStatusToLEDeviceNetStatus(UpDeviceConnection connnection)
{
    NSDictionary *statusInfo = @{ @(UpDeviceConnection_DISCONNECTED) : @(LEDeviceNetStatusDisconnected),
                                  @(UpDeviceConnection_OFFLINE) : @(LEDeviceNetStatusOffline),
                                  @(UpDeviceConnection_CONNECTING) : @(LEDeviceNetStatusConnecting),
                                  @(UpDeviceConnection_CONNECTED) : @(LEDeviceNetStatusConnected),
                                  @(UpDeviceConnection_READY) : @(LEDeviceNetStatusReady)
    };
    NSNumber *number = statusInfo[@(connnection)];
    return [number isKindOfClass:[NSNumber class]] ? number.integerValue : LEDeviceNetStatusDisconnected;
}

LEDeviceOnlineStateV2 UPEngineDevice_convertOnlineStateV2(UpDeviceRealOnlineV2 state)
{
    NSDictionary *statusInfo = @{ @(UpDeviceRealOnlineV2_ONLINE_READY) : @(LEDeviceOnlineStateV2OnlineReady),
                                  @(UpDeviceRealOnlineV2_ONLINE_NOT_READY) : @(LEDeviceOnlineStateV2OnlineNotReady),
                                  @(UpDeviceRealOnlineV2_OFFLINE) : @(LEDeviceOnlineStateV2Offline)
    };
    NSNumber *number = statusInfo[@(state)];
    return [number isKindOfClass:[NSNumber class]] ? number.integerValue : LEDeviceOnlineStateV2OnlineReady;
}

LEDeviceOfflineCause UPEngineDevice_convertOfflineCause(UpDeviceOfflineCause offlineCause)
{
    NSDictionary *info = @{ @(UpDeviceOfflineCause_None) : @(LEDeviceOfflineCauseNone),
                            @(UpDeviceOfflineCause_Normal) : @(LEDeviceOfflineCauseNormal),
                            @(UpDeviceOfflineCause_LowPower) : @(LEDeviceOfflineCauseLowPower),
                            @(UpDeviceOfflineCause_WIFI_Closed) : @(LEDeviceOfflineCauseWifiClosed)
    };
    NSNumber *number = info[@(offlineCause)];
    return [number isKindOfClass:[NSNumber class]] ? number.integerValue : LEDeviceOfflineCauseNone;
}

NSArray<LEDeviceAttribute *> *UPEngineDevice_convertUpDeviceAttributesIntoLEDeviceAttributes(NSArray<id<UpDeviceAttribute>> *attributes)
{
    NSMutableArray *attrs = [NSMutableArray array];
    for (id<UpDeviceAttribute> attribute in attributes) {
        LEDeviceAttribute *attr = [[LEDeviceAttribute alloc] initDeviceAttributeWithName:attribute.name value:attribute.value];
        if ([attr isKindOfClass:[LEDeviceAttribute class]]) {
            [attrs addObject:attr];
        }
    }
    return attrs;
}

NSArray<LEDeviceAlarm *> *UPEngineDevice_convertUpDeviceAlarmsIntoLEDeviceAlarms(NSArray<id<UpDeviceCaution>> *alarms)
{
    NSMutableArray *alarmsArray = [NSMutableArray array];
    for (id<UpDeviceCaution> alarm in alarms) {
        LEDeviceAlarm *deviceAlarm = [[LEDeviceAlarm alloc] initDeviceAlarmWithName:alarm.name value:alarm.value time:alarm.time];
        if ([deviceAlarm isKindOfClass:[LEDeviceAlarm class]]) {
            [alarmsArray addObject:deviceAlarm];
        }
    }
    return alarmsArray;
}

id<UpDeviceCommand> UPEngineDevice_convertLEDeviceCMDToUpDeviceCommand(LEDeviceCMD *command)
{
    DeviceCommand *deviceCMD = [DeviceCommand DeviceCommand:command.groupName Attributes:command.attributes.mutableCopy];
    return deviceCMD;
}

@implementation UpEngineDevice (EngineDelegate)
#pragma mark - LEDeviceDelegate
- (LEDeviceBaseInfo *)LE_getDeviceBaseInfo:(NSString *)deviceID
{
    NSString *model = self.getInfo.model;
    NSString *typeName = self.getInfo.typeName;
    NSString *typeId = self.getInfo.typeId;
    LEDeviceBaseInfo *baseInfo = [[LEDeviceBaseInfo alloc] initDeviceBaseInfoWithDeviceID:deviceID typeID:typeId typeName:typeName model:model status:UPEngineDevice_convertConnectionStatusToLEDeviceNetStatus(self.getConnection)];
    baseInfo.onlineStateV2 = UPEngineDevice_convertOnlineStateV2(self.getDeviceOnlineStateV2);
    baseInfo.bleState = UPEngineDevice_convertConnectionStatusToLEDeviceNetStatus(self.getBleState);
    baseInfo.offlineCause = UPEngineDevice_convertOfflineCause(self.getDeviceOfflineCause);
    return baseInfo;
}

- (LEDeviceNetStatus)LE_getDeviceNetStatus:(NSString *)deviceID
{
    return UPEngineDevice_convertConnectionStatusToLEDeviceNetStatus(self.getConnection);
}

- (LEDeviceOnlineStateV2)LE_getOnlineStateV2
{
    return UPEngineDevice_convertOnlineStateV2(self.getDeviceOnlineStateV2);
}

- (NSArray<LEDeviceAttribute *> *)LE_getDeviceAttributes:(NSString *)deviceID
{
    return UPEngineDevice_convertUpDeviceAttributesIntoLEDeviceAttributes(self.getAttributeList);
}

- (NSArray<LEDeviceAlarm *> *)LE_getDeviceAlarms:(NSString *)deviceID
{
    return UPEngineDevice_convertUpDeviceAlarmsIntoLEDeviceAlarms(self.getCautionList);
}

- (LEDeviceNetStatus)LE_getBleState
{
    return UPEngineDevice_convertConnectionStatusToLEDeviceNetStatus(self.getBleState);
}

- (LEDeviceOfflineCause)LE_getOfflineCause
{
    return UPEngineDevice_convertOfflineCause(self.getDeviceOfflineCause);
}

- (void)LE_connectDeviceWithID:(NSString *)deviceID deviceChangeObserver:(id<LEDeviceObserver>)observer completion:(LECompletion)completion
{
    if (completion) {
        completion(nil);
    }
}

- (void)LE_disconnectDeviceWithID:(NSString *)deviceID completion:(LECompletion)completion
{
    if (completion) {
        completion(nil);
    }
}

- (void)LE_executeCommandWithDeviceID:(NSString *)deviceID command:(LEDeviceCMD *)command completion:(LECompletion)completion
{
    NSString *deviceId = self.getInfo.deviceId;
    if (![command isKindOfClass:[LEDeviceCMD class]]) {
        UPDeviceLogError(@"[%s][%d]逻辑引擎设备(%@)的命令执行失败！命令参数对象有误！cmd:%@", __PRETTY_FUNCTION__, __LINE__, deviceId, command);
        if (completion) {
            UpDeviceResult *result = [UpDeviceResult UpDeviceResult:ErrorCode_INVALID extraData:nil];
            completion([NSError errorWithDomain:[NSString stringWithFormat:@"%@", result.extraInfo] code:result.errorCode userInfo:nil]);
        }
        return;
    }
    NSString *groupName = command.groupName;
    NSDictionary<NSString *, NSString *> *attributes = command.attributes;
    NSUInteger timeout = command.timeout;
    void (^completionBlock)(UpDeviceResult *result) = ^(UpDeviceResult *result) {
      if (!result.isSuccessful) {
          UPDeviceLogError(@"[%s][%d]逻辑引擎设备(%@)的命令执行失败！groupName:%@,attributes:%@,timeout:%lu,error:%@", __PRETTY_FUNCTION__, __LINE__, deviceId, groupName, attributes, timeout, result.extraInfo);
          if (completion) {
              completion([NSError errorWithDomain:[NSString stringWithFormat:@"%@", result.extraInfo] code:result.errorCode userInfo:nil]);
          }
          return;
      }
      UPDeviceLogDebug(@"[%s][%d]逻辑引擎设备(%@)的命令执行成功！groupName:%@,attributes:%@,timeout:%lu", __PRETTY_FUNCTION__, __LINE__, deviceId, groupName, attributes, timeout);
      if (completion) {
          completion(nil);
      }
    };
    UPDeviceLogDebug(@"[%s][%d]逻辑引擎设备(%@)的开始执行命令！groupName:%@,attributes:%@,timeout:%lu", __PRETTY_FUNCTION__, __LINE__, deviceId, groupName, attributes, timeout);
    [self executeCommand:UPEngineDevice_convertLEDeviceCMDToUpDeviceCommand(command) timeout:command.timeout finishBlock:completionBlock];
}

- (void)LE_executeCommandWithResult:(NSString *)deviceID command:(LEDeviceCMD *)command completion:(LECompletionWithResult)completion
{
    NSString *deviceId = self.getInfo.deviceId;
    if (![command isKindOfClass:[LEDeviceCMD class]]) {
        UPDeviceLogError(@"[%s][%d]逻辑引擎设备(%@)的命令执行失败！命令参数对象有误！cmd:%@", __PRETTY_FUNCTION__, __LINE__, deviceId, command);
        if (completion) {
            LEEngineResult *result = [[LEEngineResult alloc] initWithRetCode:[NSString stringWithFormat:@"%ld", ErrorCode_INVALID] extraData:@"" extraInfo:@"Invalid command"];
            completion([NSError errorWithDomain:[NSString stringWithFormat:@"%@", result.extraInfo] code:ErrorCode_INVALID userInfo:nil], result);
        }
        return;
    }
    NSString *groupName = command.groupName;
    NSDictionary<NSString *, NSString *> *attributes = command.attributes;
    NSUInteger timeout = command.timeout;
    void (^completionBlock)(UpDeviceResult *result) = ^(UpDeviceResult *result) {
      if (!result.isSuccessful) {
          UPDeviceLogError(@"[%s][%d]逻辑引擎设备(%@)的命令执行失败！groupName:%@,attributes:%@,timeout:%lu,error:%@", __PRETTY_FUNCTION__, __LINE__, deviceId, groupName, attributes, timeout, result.extraInfo);
          if (completion) {
              LEEngineResult *engineRet = [[LEEngineResult alloc] initWithRetCode:[NSString stringWithFormat:@"%ld", result.errorCode] extraData:result.extraData extraInfo:result.extraInfo];
              completion([NSError errorWithDomain:[NSString stringWithFormat:@"%@", result.extraInfo] code:result.errorCode userInfo:nil], engineRet);
          }
          return;
      }
      UPDeviceLogDebug(@"[%s][%d]逻辑引擎设备(%@)的命令执行成功！groupName:%@,attributes:%@,timeout:%lu", __PRETTY_FUNCTION__, __LINE__, deviceId, groupName, attributes, timeout);
      if (completion) {
          LEEngineResult *engineRet = [[LEEngineResult alloc] initWithRetCode:[NSString stringWithFormat:@"%ld", result.errorCode] extraData:result.extraData extraInfo:result.extraInfo];
          completion(nil, engineRet);
      }
    };
    UPDeviceLogDebug(@"[%s][%d]逻辑引擎设备(%@)的开始执行命令！groupName:%@,attributes:%@,timeout:%lu", __PRETTY_FUNCTION__, __LINE__, deviceId, groupName, attributes, timeout);
    [self executeCommandWithResult:UPEngineDevice_convertLEDeviceCMDToUpDeviceCommand(command) timeout:command.timeout finishBlock:completionBlock];
}

@end
