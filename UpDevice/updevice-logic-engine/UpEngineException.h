//
//  UpEngineException.h
//  UPDevice
//
//  Created by <PERSON> on 2018/12/28.
//  Copyright © 2018 海尔优家智能科技（北京）有限公司. All rights reserved.
//

#import "UpDeviceException.h"

extern NSString *const UP_EXCEPTION_CODE_CONFIG_NOT_INSTALLED;
extern NSString *const UP_EXCEPTION_CODE_CONFIG_PARSE_ERROR;
extern NSString *const UP_EXCEPTION_CODE_ENGINE_NOT_PREPARED;
extern NSString *const UP_EXCEPTION_INFO_CONFIG_NOT_INSTALLED;
extern NSString *const UP_EXCEPTION_INFO_CONFIG_PARSE_ERROR;
extern NSString *const UP_EXCEPTION_INFO_ENGINE_NOT_PREPARED;

NS_ASSUME_NONNULL_BEGIN

@class UpDeviceResult;
@class UpConfigNotInstalledException;
@class UpConfigParseException;
@class UpNotPreparedException;
@interface UpEngineException : UpDeviceException
+ (UpEngineException *)UpEngineException:(UpDeviceResult *)result;
+ (UpEngineException *)UpEngineExceptionWithExtraCode:(NSString *)extraCode info:(NSString *)extraInfo;
+ (UpConfigNotInstalledException *)ConfigNotInstalledException;
+ (UpConfigParseException *)ConfigParseException;
+ (UpNotPreparedException *)NotPreparedException;
@end

@interface UpConfigNotInstalledException : UpEngineException
@end

@interface UpConfigParseException : UpEngineException
@end

@interface UpNotPreparedException : UpEngineException
@end

NS_ASSUME_NONNULL_END
