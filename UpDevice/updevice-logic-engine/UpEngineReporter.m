//
//  UpEngineReporter.m
//  UPDevice
//
//  Created by <PERSON> on 2019/1/2.
//  Copyright © 2019 海尔优家智能科技（北京）有限公司. All rights reserved.
//

#import "UpEngineReporter.h"
#import "UPDeviceLog.h"

NSUInteger const UpEngineEvent_EVENT_STATE_CHANGE = 4096;
NSUInteger const UpEngineEvent_EVENT_CONNECTION_CHANGE = 4097;
NSUInteger const UpEngineEvent_EVENT_ATTRIBUTES_CHANGE = 4098;
NSUInteger const UpEngineEvent_EVENT_DEVICE_CAUTION = 4099;

@interface UpEngineReporter ()
@property (atomic, strong) NSMutableSet<id<LEEngineObserver>> *engineListenerSet;
- (void)notifyEngineStateChange:(LEEngine *)engine;
- (void)notifyEngineConnectionChange:(LEEngine *)engine;
- (void)notifyEngineAttributesChange:(LEEngine *)engine;
- (void)notifyEngineDeviceCaution:(LEEngine *)engine;
@end

@implementation UpEngineReporter
#pragma mark - Non-Public Methods
- (void)notifyEngineStateChange:(LEEngine *)engine
{
    UPDeviceLogWarning(@"%s[%d]该方法尚未实现，不应该被调用！", __PRETTY_FUNCTION__, __LINE__);
}

- (void)notifyEngineConnectionChange:(LEEngine *)engine
{
    NSString *deviceId = engine.deviceID;
    for (id<LEEngineObserver> observer in self.engineListenerSet) {
        if ([observer respondsToSelector:@selector(engine:deviceNetStatusDidChange:)]) {
            dispatch_async(dispatch_get_main_queue(), ^{
              [observer engine:engine deviceNetStatusDidChange:engine.deviceStatus];
            });
        }
    }
    UPDeviceLogDebug(@"%s[%d]逻辑引擎设备(%@)的连接状态变化已通知！", __PRETTY_FUNCTION__, __LINE__, deviceId);
}

- (void)notifyEngineAttributesChange:(LEEngine *)engine
{
    NSString *deviceId = engine.deviceID;
    for (id<LEEngineObserver> observer in self.engineListenerSet) {
        if ([observer respondsToSelector:@selector(engine:deviceAttributesDidChange:)]) {
            dispatch_async(dispatch_get_main_queue(), ^{
              [observer engine:engine deviceAttributesDidChange:engine.attributes];
            });
        }
    }
    UPDeviceLogDebug(@"%s[%d]逻辑引擎设备(%@)的属性变化已通知！", __PRETTY_FUNCTION__, __LINE__, deviceId);
}

- (void)notifyEngineDeviceCaution:(LEEngine *)engine
{
    NSString *deviceId = engine.deviceID;
    for (id<LEEngineObserver> observer in self.engineListenerSet) {
        if ([observer respondsToSelector:@selector(engine:deviceAlarmsDidChange:)]) {
            dispatch_async(dispatch_get_main_queue(), ^{
              [observer engine:engine deviceAlarmsDidChange:engine.alarms];
            });
        }
    }
    UPDeviceLogDebug(@"%s[%d]逻辑引擎设备(%@)的报警变化已通知！", __PRETTY_FUNCTION__, __LINE__, deviceId);
}

- (instancetype)init
{
    if (self = [super init]) {
        _engineListenerSet = [NSMutableSet set];
    }
    return self;
}

#pragma mark - Public Methods
+ (UpEngineReporter *)EngineReporter
{
    return [[UpEngineReporter alloc] init];
}

- (void)add:(id<LEEngineObserver>)listener
{
    if (![listener conformsToProtocol:@protocol(LEEngineObserver)]) {
        return;
    }
    [self.engineListenerSet addObject:listener];
}

- (void)remove:(id<LEEngineObserver>)listener
{
    if (![listener conformsToProtocol:@protocol(LEEngineObserver)]) {
        return;
    }
    [self.engineListenerSet removeObject:listener];
}

- (NSArray<id<LEEngineObserver>> *)getAll
{
    return self.engineListenerSet.allObjects;
}

- (void)clear
{
    [self.engineListenerSet removeAllObjects];
}

- (void)notifyLogicEngine:(LEEngine *)engine event:(NSUInteger)event
{
    switch (event) {
        case UpEngineEvent_EVENT_STATE_CHANGE:
            [self notifyEngineStateChange:engine];
            break;
        case UpEngineEvent_EVENT_CONNECTION_CHANGE:
            [self notifyEngineConnectionChange:engine];
            break;
        case UpEngineEvent_EVENT_ATTRIBUTES_CHANGE:
            [self notifyEngineAttributesChange:engine];
            break;
        case UpEngineEvent_EVENT_DEVICE_CAUTION:
            [self notifyEngineDeviceCaution:engine];
            break;
    }
}

@end
