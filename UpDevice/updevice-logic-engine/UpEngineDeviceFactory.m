//
//  UpEngineDeviceFactory.m
//  UPDevice
//
//  Created by <PERSON> on 2018/12/28.
//  Copyright © 2018 海尔优家智能科技（北京）有限公司. All rights reserved.
//

#import "UpEngineDeviceFactory.h"
#import "UpEngineDevice.h"
#import "UpEngineDataSource.h"

@interface UpEngineDeviceFactory ()
@property (nonatomic, strong) UPResourceManager *resourceManager;
- (instancetype)initEngineDeviceFactoryWithResourceManager:(UPResourceManager *)resourceManager;
@end

@implementation UpEngineDeviceFactory
#pragma mark - UpDeviceFactory
- (nullable id<UpDevice>)create:(NSString *)uniqueId deviceInfo:(id<UpDeviceInfo>)deviceInfo broker:(id<UpDeviceBroker>)broker factory:(id<UpDeviceFactory>)factory
{
    UpEngineDevice *engineDevice = [[UpEngineDevice alloc] initEngineDeviceWithUniqueID:uniqueId deviceInfo:deviceInfo broker:broker factory:factory];
    [engineDevice setResourceManager:self.resourceManager];
    return engineDevice;
}

#pragma mark - Non-Public Methods
- (instancetype)initEngineDeviceFactoryWithResourceManager:(UPResourceManager *)resourceManager
{
    if (self = [super init]) {
        self.resourceManager = resourceManager;
    }
    return self;
}

#pragma mark - Public Methods
+ (UpEngineDeviceFactory *)EngineDeviceFactory:(UPResourceManager *)resourceManager
{
    return [[UpEngineDeviceFactory alloc] initEngineDeviceFactoryWithResourceManager:resourceManager];
}

@end
