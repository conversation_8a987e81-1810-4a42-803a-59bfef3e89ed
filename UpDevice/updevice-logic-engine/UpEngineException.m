//
//  UpEngineException.m
//  UPDevice
//
//  Created by <PERSON> on 2018/12/28.
//  Copyright © 2018 海尔优家智能科技（北京）有限公司. All rights reserved.
//

#import "UpEngineException.h"

NSString *const UP_EXCEPTION_CODE_CONFIG_NOT_INSTALLED = @"110001";
NSString *const UP_EXCEPTION_CODE_CONFIG_PARSE_ERROR = @"110002";
NSString *const UP_EXCEPTION_CODE_ENGINE_NOT_PREPARED = @"110003";
NSString *const UP_EXCEPTION_INFO_CONFIG_NOT_INSTALLED = @"配置文件未安装";
NSString *const UP_EXCEPTION_INFO_CONFIG_PARSE_ERROR = @"配置文件解析失败";
NSString *const UP_EXCEPTION_INFO_ENGINE_NOT_PREPARED = @"逻辑引擎未准备好";

@implementation UpEngineException
#pragma mark - Public Methods
+ (UpEngineException *)UpEngineException:(UpDeviceResult *)result
{
    return [[UpEngineException alloc] initWithResult:result];
}

+ (UpEngineException *)UpEngineExceptionWithExtraCode:(NSString *)extraCode info:(NSString *)extraInfo
{
    return [[UpEngineException alloc] initWithExtraData:extraCode extraInfo:extraInfo];
}

+ (UpConfigNotInstalledException *)ConfigNotInstalledException
{
    return [[UpConfigNotInstalledException alloc] initWithExtraData:UP_EXCEPTION_CODE_CONFIG_NOT_INSTALLED extraInfo:UP_EXCEPTION_INFO_CONFIG_NOT_INSTALLED];
}

+ (UpConfigParseException *)ConfigParseException
{
    return [[UpConfigParseException alloc] initWithExtraData:UP_EXCEPTION_CODE_CONFIG_PARSE_ERROR extraInfo:UP_EXCEPTION_INFO_CONFIG_PARSE_ERROR];
}

+ (UpNotPreparedException *)NotPreparedException
{
    return [[UpNotPreparedException alloc] initWithExtraData:UP_EXCEPTION_CODE_ENGINE_NOT_PREPARED extraInfo:UP_EXCEPTION_INFO_ENGINE_NOT_PREPARED];
}

@end

@implementation UpConfigNotInstalledException
@end

@implementation UpConfigParseException
@end

@implementation UpNotPreparedException
@end
