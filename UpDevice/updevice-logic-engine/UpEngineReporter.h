//
//  UpEngineReporter.h
//  UPDevice
//
//  Created by <PERSON> on 2019/1/2.
//  Copyright © 2019 海尔优家智能科技（北京）有限公司. All rights reserved.
//

#import <Foundation/Foundation.h>
#import <LogicEngine/LEEngine.h>

extern NSUInteger const UpEngineEvent_EVENT_STATE_CHANGE;
extern NSUInteger const UpEngineEvent_EVENT_CONNECTION_CHANGE;
extern NSUInteger const UpEngineEvent_EVENT_ATTRIBUTES_CHANGE;
extern NSUInteger const UpEngineEvent_EVENT_DEVICE_CAUTION;

NS_ASSUME_NONNULL_BEGIN

@interface UpEngineReporter : NSObject
+ (UpEngineReporter *)EngineReporter;
- (void)add:(id<LEEngineObserver>)listener;
- (void)remove:(id<LEEngineObserver>)listener;
- (NSArray<id<LEEngineObserver>> *)getAll;
- (void)clear;
- (void)notifyLogicEngine:(LEEngine *)engine event:(NSUInteger)event;
@end

NS_ASSUME_NONNULL_END
