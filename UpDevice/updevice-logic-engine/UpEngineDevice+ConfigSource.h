//
//  UpEngineDevice+ConfigSource.h
//  UPDevice
//
//  Created by <PERSON> on 2019/1/9.
//  Copyright © 2019 海尔优家智能科技（北京）有限公司. All rights reserved.
//

#import "UpEngineDevice.h"
#import "UpLogicEngineApi.h"
#import "UPResourceManager.h"

NS_ASSUME_NONNULL_BEGIN

@class UPResourceInfo;
@interface UpEngineDevice (ConfigSource) <UPResourceListener, UPResourceCallback>
@property (nonatomic, assign) UpConfigState engineConfigFileState;
@property (atomic, strong) NSMutableDictionary<NSString *, void (^)(UPResourceInfo *_Nullable resource, NSError *_Nullable error)> *completionInfo;

- (void)prepareEngineDeviceConfigSource:(void (^)(UPResourceInfo *resource, NSError *error))completion;
- (void)installEngineDeviceResource:(UPResourceInfo *)resource withCompletion:(void (^)(UPResourceInfo *resource, NSError *error))completion;
- (void)prepareEngineWithResource:(UPResourceInfo *)resource withCompletion:(void (^)(LEEngine *engine, NSError *error))completion;

@end

NS_ASSUME_NONNULL_END
