//
//  UpEngineDevice.h
//  UPDevice
//
//  Created by <PERSON> on 2018/12/28.
//  Copyright © 2018 海尔优家智能科技（北京）有限公司. All rights reserved.
//

#import "UpDeviceBase.h"
#import "UpLogicEngineApi.h"
#import "UpEngineReloadHandler.h"
#import "UpEngineDataSource.h"
#import "UpDeviceInfo.h"
#import "UpDeviceBroker.h"
#import "UpDeviceFactory.h"

NS_ASSUME_NONNULL_BEGIN

@interface UpEngineDevice : UpDeviceBase <UpLogicEngineApi, UpEngineReloadHandler>
@property (nonatomic, strong) UpEngineDataSource *dataSource;
@property (nonatomic, strong) UPResourceManager *resManager;

- (instancetype)initEngineDeviceWithUniqueID:(NSString *)uniqueId deviceInfo:(id<UpDeviceInfo>)deviceInfo broker:(id<UpDeviceBroker>)broker factory:(id<UpDeviceFactory>)factory;

@end

NS_ASSUME_NONNULL_END
