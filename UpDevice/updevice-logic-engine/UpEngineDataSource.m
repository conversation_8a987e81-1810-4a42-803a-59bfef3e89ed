//
//  UpEngineDataSource.m
//  UPDevice
//
//  Created by <PERSON> on 2018/12/28.
//  Copyright © 2018 海尔优家智能科技（北京）有限公司. All rights reserved.
//

#import "UpEngineDataSource.h"
#import <LogicEngine/LEDeviceBaseInfo.h>
#import <LogicEngine/LECommonFuncs.h>
#import <LogicEngine/LEConfig.h>
#import "UPDeviceLog.h"

@interface UpEngineDataSource ()
@property (nonatomic, copy) NSString *resourceTaskId;
@property (nonatomic, strong) id<UpDeviceInfo> deviceInfo;
- (LEConfig *)loadConfigFromFile:(NSString *)filePath;
- (BOOL)isEqualToDeviceBaseInfo:(LEDeviceBaseInfo *)deviceBaseInfo;
@end

@implementation UpEngineDataSource
#pragma mark - LEConfigDataSource
- (LEConfig *)LE_configDataSourceLoadConfigWithDeviceBaseInfo:(LEDeviceBaseInfo *)deviceBaseInfo
{
    if (![self isEqualToDeviceBaseInfo:deviceBaseInfo]) {
        UPDeviceLogError(@"[%s][%d]要加载的配置文件信息与设备信息不一致！", __PRETTY_FUNCTION__, __LINE__);
        return nil;
    }
    return [self loadConfigFromFile:self.configFilePath];
}

#pragma mark - Non-Public Methods
- (BOOL)isEqualToDeviceBaseInfo:(LEDeviceBaseInfo *)deviceBaseInfo
{
    id<UpDeviceInfo> deviceInfo = self.deviceInfo;
    if (![deviceBaseInfo isKindOfClass:[LEDeviceBaseInfo class]] || ![deviceInfo conformsToProtocol:@protocol(UpDeviceInfo)]) {
        UPDeviceLogError(@"[%s][%d]设备信息比较失败！设备基本信息格式不符合要求！", __PRETTY_FUNCTION__, __LINE__);
        return NO;
    }
    return [self judgeDeviceModelIsEqual:deviceInfo.model str2:deviceBaseInfo.model] && LECommon_isValidStringEqual(deviceInfo.typeId, deviceBaseInfo.typeID);
}

- (BOOL)judgeDeviceModelIsEqual:(NSString *)str1 str2:(NSString *)str2
{
    if (str1 == nil && str2 == nil) {
        return YES;
    }

    return LECommon_isValidStringEqual(str1, str2);
}

- (LEConfig *)loadConfigFromFile:(NSString *)filePath
{
    NSError *error = nil;
    NSString *fileContent = [NSString stringWithContentsOfFile:filePath encoding:NSUTF8StringEncoding error:&error];
    if (error) {
        UPDeviceLogError(@"[%s][%d]配置文件加载失败！文件内容读取错误！filePath:%@，error:%@", __PRETTY_FUNCTION__, __LINE__, filePath, error.localizedDescription);
        return nil;
    }
    LEConfig *config = [[LEConfig alloc] initLEConfigWith:fileContent];
    if (![config isKindOfClass:[LEConfig class]]) {
        UPDeviceLogError(@"[%s][%d]配置文件对象初始化失败！filePath:%@，params:%@", __PRETTY_FUNCTION__, __LINE__, filePath, fileContent);
        return nil;
    }
    return config;
}

#pragma mark - Public Methods
- (instancetype)initWithDeviceInfo:(id<UpDeviceInfo>)deviceInfo
{
    if (![deviceInfo conformsToProtocol:@protocol(UpDeviceInfo)]) {
        UPDeviceLogError(@"[%s][%d]逻辑引擎数据源对象初始化失败！入参对象未实现UpDeviceInfo协议！deviceInfo:%@", __PRETTY_FUNCTION__, __LINE__, deviceInfo);
        return nil;
    }
    if (self = [super init]) {
        _deviceInfo = deviceInfo;
    }
    return self;
}

@end
