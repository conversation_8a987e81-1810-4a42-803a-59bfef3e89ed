//
//  UpEngineDevice+ResourceConfigSource.m
//  UPDevice
//
//  Created by xianwen<PERSON> on 2023/5/8.
//

#import "UpEngineDevice+ResourceConfigSource.h"

@implementation UpEngineDevice (ResourceConfigSource)

- (UpDeviceResourceManager *)getDeviceResourceManager
{
    return [UpDeviceResourceManager getInstance];
}

- (void)executeLoadingResourceConfigurationTaskWithDeviceId:(NSString *_Nullable)deviceId complete:(void (^)(NSString *_Nullable, NSError *_Nullable))completeBlock
{
    if (![deviceId isKindOfClass:[NSString class]]) {
        if (completeBlock == nil) {
            return;
        }
        completeBlock(nil, [NSError errorWithDomain:NSCocoaErrorDomain
                                               code:1001
                                           userInfo:
                                               @{
                                                   NSLocalizedDescriptionKey : @"Method executeLoadingResourceConfigurationTaskWithDeviceId Error",
                                                   NSLocalizedFailureReasonErrorKey : @"params error, deviceId is nil",
                                               }]);
        return;
    }

    UpDeviceResourceManager *manager = [self getDeviceResourceManager];
    if (![manager isKindOfClass:[UpDeviceResourceManager class]]) {
        if (completeBlock == nil) {
            return;
        }
        completeBlock(nil, [NSError errorWithDomain:NSCocoaErrorDomain
                                               code:1002
                                           userInfo:
                                               @{
                                                   NSLocalizedDescriptionKey : @"Method executeLoadingResourceConfigurationTaskWithDeviceId Error",
                                                   NSLocalizedFailureReasonErrorKey : @"internal error, manager is not UpDeviceResourceManager Class",
                                               }]);
        return;
    }
    [manager getAppFuncModelWithDeviceId:deviceId
                             finishBlock:^(UpDeviceResult *result) {
                               if (completeBlock == nil) {
                                   return;
                               }
                               NSString *resultJson = nil;
                               if ([result.extraData isKindOfClass:[NSString class]]) {
                                   resultJson = (NSString *)result.extraData;
                               }
                               completeBlock(resultJson, nil);
                             }];
}

@end
