//
//  UPNonNetworkDevice.m
//  UPDevice
//
//  Created by 闫达 on 2021/3/16.
//  Copyright © 2021 海尔优家智能科技（北京）有限公司. All rights reserved.
//

#import "UPNonNetworkDevice.h"
#import "UPDeviceLog.h"

@implementation UPNonNetworkDevice
- (instancetype)initWithDeviceInfo:(NSString *)uniqueId deviceInfo:(id<UpDeviceInfo>)deviceInfo broker:(id<UpDeviceBroker>)broker factory:(id<UpDeviceFactory>)factory
{
    if (self = [super initWithDeviceInfo:uniqueId deviceInfo:deviceInfo broker:broker factory:factory]) {
    }
    return self;
}

#pragma mark - baseDevice

- (id<UpDevice>)getExtApi
{
    return self;
}

- (BOOL)isExtApiPrepared
{
    return YES;
}

- (void)prepare:(void (^)(UpDeviceResult *))finishBlock
{
    if (finishBlock) {
        UpDeviceResult *result = [[UpDeviceResult alloc] initWithErrorCode:ErrorCode_SUCCESS extraData:nil extraCode:@"UPNonNetworkDevice doesn't need preparing." extraInfo:nil];
        UPDeviceLogDebug(@"%s[%d]UPNonNetworkDevice doesn't need preparing！", __PRETTY_FUNCTION__, __LINE__);
        finishBlock(result);
    }
}

- (void)release:(void (^)(UpDeviceResult *))finishBlock
{
    if (finishBlock) {
        UpDeviceResult *result = [[UpDeviceResult alloc] initWithErrorCode:ErrorCode_SUCCESS extraData:nil extraCode:@"UPNonNetworkDevice doesn't need release." extraInfo:nil];
        UPDeviceLogDebug(@"%s[%d]UPNonNetworkDevice doesn't need release！", __PRETTY_FUNCTION__, __LINE__);
        finishBlock(result);
    }
}

- (void)onPrepareExtApi:(void (^)(UpDeviceResult *result))finishblock
{
    if (finishblock) {
        UpDeviceResult *result = [[UpDeviceResult alloc] initWithErrorCode:ErrorCode_SUCCESS extraData:nil extraCode:@"UPNonNetworkDevice doesn't need preparing ExtApi." extraInfo:nil];
        UPDeviceLogDebug(@"%s[%d]UPNonNetworkDevice doesn't need preparing ExtApi.！", __PRETTY_FUNCTION__, __LINE__);
        finishblock(result);
    }
}

- (void)onReleaseExtApi:(void (^)(UpDeviceResult *result))finishblock
{
    if (finishblock) {
        UpDeviceResult *result = [[UpDeviceResult alloc] initWithErrorCode:ErrorCode_SUCCESS extraData:nil extraCode:@"UPNonNetworkDevice doesn't need preparing ExtApi." extraInfo:nil];
        UPDeviceLogDebug(@"%s[%d]UPNonNetworkDevice doesn't need preparing ExtApi.！", __PRETTY_FUNCTION__, __LINE__);
        finishblock(result);
    }
}

- (void)onReloadExtApi:(void (^)(UpDeviceResult *result))finishblock
{
    if (finishblock) {
        UpDeviceResult *result = [[UpDeviceResult alloc] initWithErrorCode:ErrorCode_SUCCESS extraData:nil extraCode:@"UPNonNetworkDevice doesn't need preparing ExtApi." extraInfo:nil];
        UPDeviceLogDebug(@"%s[%d]UPNonNetworkDevice doesn't need preparing ExtApi.！", __PRETTY_FUNCTION__, __LINE__);
        finishblock(result);
    }
}

- (id<UpDevice>)getDeviceById:(NSString *)deviceId
{
    UPDeviceLogWarning(@"%s[%d]该方法为空实现方法，不应该被调用！", __PRETTY_FUNCTION__, __LINE__);
    return nil;
}

- (BOOL)isReady
{
    return UpDeviceConnection_READY;
}

- (UpDeviceConfigState)configState
{
    return UpDeviceConfigState_NonNetDevice;
}
- (NSArray *)getEngineAttributeList
{
    return @[];
}
- (NSArray *)getEngineCautionList
{
    return @[];
}

@end
