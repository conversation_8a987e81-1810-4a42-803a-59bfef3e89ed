//
//  UpNonNetworkDeviceFactory.m
//  UPDevice
//
//  Created by 闫达 on 2021/3/16.
//  Copyright © 2021 海尔优家智能科技（北京）有限公司. All rights reserved.
//

#import "UpNonNetworkDeviceFactory.h"
#import "UPNonNetworkDevice.h"

@implementation UpNonNetworkDeviceFactory
#pragma mark - UpDeviceFactory
- (id<UpDevice>)create:(NSString *)uniqueId deviceInfo:(id<UpDeviceInfo>)deviceInfo broker:(id<UpDeviceBroker>)broker factory:(id<UpDeviceFactory>)factory
{
    NSString *deviceNetType = deviceInfo.getExtras[@"DI-Basic.deviceNetType"];
    if ([deviceNetType isEqualToString:@"nonNetDevice"]) {
        UPNonNetworkDevice *device = [[UPNonNetworkDevice alloc] initWithDeviceInfo:uniqueId deviceInfo:deviceInfo broker:broker factory:factory];
        return device;
    }
    return nil;
}

@end
