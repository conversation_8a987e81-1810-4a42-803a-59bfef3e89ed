//
//  UpWashDevice.m
//  UPDevice
//
//  Created by gump on 14/4/2021.
//  Copyright © 2021 海尔优家智能科技（北京）有限公司. All rights reserved.
//

#import "UpWashDevice.h"
#import "UpWashAdapterApi.h"
#import "UPDeviceLog.h"
#import "UpWashModel.h"
#import "UpWashDataResponseParser.h"
#import <UPStorage/UPStorage+Observer.h>

static NSString *const Wash_Storage_RemainTime_Keys = @"washRemainTimeKeys"; //剩余时间所需keys
static NSString *const Wash_UserDefaults_UpdateTime = @"washUpdateTime"; //洗衣机数据更新时间

@interface UpWashDevice ()

@end

@implementation UpWashDevice

- (instancetype)initWithDeviceInfo:(NSString *)uniqueId deviceInfo:(id<UpDeviceInfo>)deviceInfo broker:(id<UpDeviceBroker>)broker factory:(id<UpDeviceFactory>)factory
{
    self = [super initWithDeviceInfo:uniqueId deviceInfo:deviceInfo broker:broker factory:factory];
    if (self) {
        //        [self checkWashData];
    }
    return self;
}

#pragma mark - private
/**
 *  检查是否更新洗衣机数据
 */
- (void)checkWashData
{
    NSString *key = [NSString stringWithFormat:@"%@/%@", Wash_Storage_RemainTime_Keys, [self getInfo].typeId];
    NSString *str = [UPStorage getStringValue:key defaultValue:@""];
    if ([str isEqualToString:@""]) {
        [self requestWashAdapterData];
        [self saveUpdateTime];
        return;
    }

    NSString *updateTimeKey = [NSString stringWithFormat:@"%@/%@", Wash_UserDefaults_UpdateTime, [self getInfo].typeId];
    float updateTime = [UPStorage getFloatValue:updateTimeKey defaultValue:0];
    NSDate *date = [NSDate dateWithTimeIntervalSinceNow:0];
    NSTimeInterval curTime = [date timeIntervalSince1970];
    if (curTime - updateTime >= 600) {
        [self requestWashAdapterData];
        [self saveUpdateTime];
    }
}

/**
 *  请求洗衣机适配数据
 */
- (void)requestWashAdapterData
{
    NSMutableDictionary *body = [NSMutableDictionary dictionary];
    NSArray *typeids = @[ self.getInfo.typeId ?: @"" ];
    [body setObject:typeids forKey:@"typeIds"];
    UpWashAdapterApi *api = [[UpWashAdapterApi alloc] initWithWashAdapterParam:body];
    api.responseParser = [UpWashDataResponseParser new];
    [api startRequestWithSuccess:^(NSObject *_Nonnull responseObject) {
      if (![responseObject isKindOfClass:[UpWashModel class]]) {
          UPDeviceLogError(@"%s[%d]request wash  adapter data fail,data:%@", __PRETTY_FUNCTION__, __LINE__, responseObject);
          return;
      }
      [self storageWashKeys:(UpWashModel *)responseObject];
    }
        failure:^(NSError *_Nonnull error, NSDictionary *_Nullable info) {
          UPDeviceLogError(@"%s[%d]request wash  adapter data error,error:%@", __PRETTY_FUNCTION__, __LINE__, error);
        }];
}

- (void)storageWashKeys:(UpWashModel *)model
{
    if (!model) {
        return;
    }
    NSString *jsonString = [model JSONStringConversion];
    if (!jsonString) {
        return;
    }
    [UPStorage putStringValue:jsonString name:[NSString stringWithFormat:@"%@/%@", Wash_Storage_RemainTime_Keys, [self getInfo].typeId]];
}

- (void)saveUpdateTime
{
    NSDate *date = [NSDate dateWithTimeIntervalSinceNow:0];
    NSTimeInterval curTime = [date timeIntervalSince1970];
    NSString *updateTimeKey = [NSString stringWithFormat:@"%@/%@", Wash_UserDefaults_UpdateTime, [self getInfo].typeId];
    [UPStorage putFloatValue:curTime name:updateTimeKey];
}

@end
