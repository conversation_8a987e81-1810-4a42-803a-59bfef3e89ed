//
//  UpWashDevieManager.h
//  UPDevice
//
//  Created by 王杰 on 2023/2/1.
//  Copyright © 2023 海尔优家智能科技（北京）有限公司. All rights reserved.
///

#import "UpWashDeviceManager.h"
#import "UpWashAdapterApi.h"
#import "UPDeviceLog.h"
#import "UpWashModel.h"
#import "UpWashDataResponseParser.h"
#import <UPStorage/UPStorage+Observer.h>

static NSString *const WashManager_Storage_RemainTime_Keys = @"washRemainTimeKeys"; //剩余时间所需keys
@implementation UpWashDeviceManager

+ (UpWashDeviceManager *)getInstance
{
    static dispatch_once_t once_t;
    static UpWashDeviceManager *instance = nil;
    dispatch_block_t block = ^{
      instance = [[self alloc] init];
    };
    dispatch_once(&once_t, block);
    return instance;
}

- (void)getWashDeviceModelWithTypeId:(NSString *)typeId finishBlock:(void (^)(UpDeviceResult *result))finishBlock
{
    if (![typeId isKindOfClass:[NSString class]] || typeId.length == 0) {
        UPDeviceLogError(@"[%s][%d]typeId is empty.", __PRETTY_FUNCTION__, __LINE__);
        UpDeviceResult *result = [[UpDeviceResult alloc] initWithErrorCode:ErrorCode_FAILURE extraData:nil extraCode:nil extraInfo:@"typeId is empty"];
        finishBlock ? finishBlock(result) : nil;
        return;
    }
    NSString *key = [NSString stringWithFormat:@"%@/%@", WashManager_Storage_RemainTime_Keys, typeId];
    //从本地读取缓存
    NSString *json = [UPStorage getStringValue:key defaultValue:@""];
    if (![json isKindOfClass:[NSString class]] || json.length == 0) {
        [self requestWashAdapterDataWithTypeId:typeId finishBlock:finishBlock];
    }
    else {
        UpWashModel *washModel = [[UpWashModel alloc] initWithJSONString:json];
        if ([washModel isKindOfClass:[UpWashModel class]]) {
            UpDeviceResult *result = [UpDeviceResult UpDeviceResult:ErrorCode_SUCCESS extraData:washModel];
            finishBlock ? finishBlock(result) : nil;
        }
        else {
            //如果本地数据转模型失败,则请求服务器数据
            [self requestWashAdapterDataWithTypeId:typeId finishBlock:finishBlock];
        }
    }
}

/**
 *  请求洗衣机配置数据
 */
- (void)requestWashAdapterDataWithTypeId:(NSString *)typeId finishBlock:(void (^)(UpDeviceResult *result))finishBlock
{
    NSMutableDictionary *body = [NSMutableDictionary dictionary];
    NSArray *typeids = @[ typeId ?: @"" ];
    [body setObject:typeids forKey:@"typeIds"];
    UpWashAdapterApi *api = [[UpWashAdapterApi alloc] initWithWashAdapterParam:body];
    api.responseParser = [UpWashDataResponseParser new];
    [api startRequestWithSuccess:^(NSObject *_Nonnull responseObject) {
      if (![responseObject isKindOfClass:[UpWashModel class]]) {
          UPDeviceLogError(@"%s[%d]request wash  adapter data fail,data:%@", __PRETTY_FUNCTION__, __LINE__, responseObject);
          UpDeviceResult *result = [[UpDeviceResult alloc] initWithErrorCode:ErrorCode_FAILURE extraData:nil extraCode:nil extraInfo:@"request wash  adapter data fail"];
          finishBlock ? finishBlock(result) : nil;
          return;
      }
      //将数据存储至本地
      [self storageWashKeys:(UpWashModel *)responseObject typeId:typeId];
      UpDeviceResult *result = [UpDeviceResult UpDeviceResult:ErrorCode_SUCCESS extraData:(UpWashModel *)responseObject];
      finishBlock ? finishBlock(result) : nil;
    }
        failure:^(NSError *_Nonnull error, NSDictionary *_Nullable info) {
          UPDeviceLogError(@"%s[%d]request wash  adapter data error,error:%@", __PRETTY_FUNCTION__, __LINE__, error);
          UpDeviceResult *result = [[UpDeviceResult alloc] initWithErrorCode:ErrorCode_FAILURE extraData:nil extraCode:nil extraInfo:error.domain.description ?: @"request wash  adapter data error"];
          finishBlock ? finishBlock(result) : nil;
        }];
}

- (void)storageWashKeys:(UpWashModel *)model typeId:(NSString *)typeId
{
    if (![model isKindOfClass:[UpWashModel class]]) {
        return;
    }
    NSString *jsonString = [model JSONStringConversion];
    if (!jsonString) {
        return;
    }
    [UPStorage putStringValue:jsonString name:[NSString stringWithFormat:@"%@/%@", WashManager_Storage_RemainTime_Keys, typeId]];
}

@end
