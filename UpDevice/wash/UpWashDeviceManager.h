//
//  UpWashDevieManager.h
//  UPDevice
//
//  Created by 王杰 on 2023/2/1.
//  Copyright © 2023 海尔优家智能科技（北京）有限公司. All rights reserved.
//

#import <Foundation/Foundation.h>
#import "UpDeviceResult.h"
NS_ASSUME_NONNULL_BEGIN

@interface UpWashDeviceManager : NSObject
+ (UpWashDeviceManager *)getInstance;

/// 根据typeId 获取washDeviceModel
/// @param typeId  设备typeId
/// @param finishBlock 回调结果 redata为 UpWashModel
- (void)getWashDeviceModelWithTypeId:(NSString *)typeId finishBlock:(void (^)(UpDeviceResult *result))finishBlock;
@end

NS_ASSUME_NONNULL_END
