//
//  UpWashDataResponseParser.m
//  UPDevice
//
//  Created by gump on 20/4/2021.
//  Copyright © 2021 海尔优家智能科技（北京）有限公司. All rights reserved.
//

#import "UpWashDataResponseParser.h"
#import "UpWashModel.h"

static NSString *const WASH_COMMAND_STATE = @"35"; //运行状态
static NSString *const WASH_COMMAND_STATUS_STANDBY = @"1"; //待机
static NSString *const WASH_COMMAND_HOUR = @"36"; //小时
static NSString *const WASH_COMMAND_MINUTE = @"37"; //分钟
static NSString *const WASH_COMMAND_OPEN = @"5"; //开机
static NSString *const WASH_COMMAND_CLOSE = @"6"; //关机
static NSString *const WASH_COMMAND_End1 = @"21"; //程序结束
static NSString *const WASH_COMMAND_End2 = @"22"; //程序结束
static NSString *const WASH_COMMAND_End3 = @"26"; //程序结束
static NSString *const WASH_COMMAND_RUNNING_MODE = @"20"; //运行模式
static NSString *const WASH_COMMAND_LAUNDRY_CYCLE = @"34"; //洗衣状态
static NSString *const WASH_COMMAND_DRYER_POWER_STATUS = @"300"; //干衣启动
static NSString *const WASH_COMMAND_DRYER_REMOTE = @"302"; //远程干衣
static NSString *const WASH_COMMAND_DRYER_REMAIN_TIME = @"167"; //干衣剩余时间
static NSString *const WASH_COMMAND_CLOTHES_WEIGHT = @"48"; //衣物重量
static NSString *const WASH_COMMAND_APPOINTMENT_OPEN = @"47"; //预约开启

@interface UpWashDataResponseParser ()
@property (nonatomic, copy) NSDictionary<NSString *, NSString *> *numberDic;
@property (nonatomic, copy) NSDictionary<NSString *, NSNumber *> *judgeDic;

@end

@implementation UpWashDataResponseParser

- (instancetype)init
{
    self = [super init];
    if (self) {
        _numberDic = @{
            WASH_COMMAND_STATE : WASHMODE_KEY_STATE,
            WASH_COMMAND_HOUR : WASHMODE_KEY_HOUR,
            WASH_COMMAND_MINUTE : WASHMODE_KEY_MINUTE,
            WASH_COMMAND_RUNNING_MODE : WASHMODE_KEY_RUNNING_MODE,
            WASH_COMMAND_LAUNDRY_CYCLE : WASHMODE_KEY_LAUNDRY_CYCLE,
            WASH_COMMAND_DRYER_POWER_STATUS : WASHMODE_KEY_DRYER_POWER,
            WASH_COMMAND_DRYER_REMOTE : WASHMODE_KEY_DRYER_REMOTE_CTR,
            WASH_COMMAND_DRYER_REMAIN_TIME : WASHMODE_KEY_DRYER_REMAIN_TIME,
            WASH_COMMAND_APPOINTMENT_OPEN : WASHMODE_KEY_APPOINTMENT_OPEN,
        };
        _judgeDic = @{
            WASH_COMMAND_STATE : @(true),
            WASH_COMMAND_HOUR : @(false),
            WASH_COMMAND_MINUTE : @(false),
            WASH_COMMAND_RUNNING_MODE : @(true),
            WASH_COMMAND_LAUNDRY_CYCLE : @(false),
            WASH_COMMAND_DRYER_POWER_STATUS : @(true),
            WASH_COMMAND_DRYER_REMOTE : @(true),
            WASH_COMMAND_DRYER_REMAIN_TIME : @(false),
            WASH_COMMAND_APPOINTMENT_OPEN : @(true),
        };
    }
    return self;
}

- (NSObject *)parseResponseObject:(NSObject *)object
{
    id responseObject = object;
    if (!responseObject || ![responseObject isKindOfClass:NSDictionary.class]) {
        return nil;
    }
    NSDictionary *dic = [self processWashAdapterData:(NSDictionary *)responseObject];
    if (!dic) {
        return nil;
    }
    UpWashModel *model = [[UpWashModel alloc] initWithDic:dic];
    return model;
}

#pragma mark - private
/**
 *  处理洗衣机适配数据
 */
- (NSDictionary *)processWashAdapterData:(NSDictionary *)dic
{
    NSArray *settings = [dic objectForKey:@"settings"];
    if (!settings) {
        return nil;
    }
    NSMutableDictionary *washDic = [NSMutableDictionary new];
    for (NSDictionary *washDataDic in settings) {
        //type
        NSDictionary *baseSetting = [washDataDic objectForKey:@"baseSetting"];
        NSString *type = [baseSetting objectForKey:@"type"];
        [washDic setObject:type ?: @"" forKey:@"type"];
        //supportFreshAir
        NSDictionary *categorieDic = baseSetting[@"categories"];
        bool bSupport = [self isCategoriesSupport:categorieDic key:WASHMODE_KEY_SUPPORT_FRESH_AIR];
        [washDic setObject:bSupport ? @"true" : @"false" forKey:WASHMODE_KEY_SUPPORT_FRESH_AIR];
        //units
        NSDictionary *programmeSetting = [washDataDic objectForKey:@"programmeSetting"];
        NSArray *units = [programmeSetting objectForKey:@"units"];
        int unitsCount = units ? (int)units.count : 0;
        [washDic setObject:[NSString stringWithFormat:@"%d", unitsCount] forKey:@"units"];
        //alarmcode
        [self saveAlarmFrom:washDataDic to:washDic];
        //poweroncode
        NSArray *commonCommands = [programmeSetting objectForKey:@"commonCommands"];
        [self save:WASH_COMMAND_OPEN withKey:WASHMODE_KEY_POWER_ON from:commonCommands to:washDic];
        [self save:WASH_COMMAND_CLOSE withKey:WASHMODE_KEY_POWER_OFF from:commonCommands to:washDic];
        for (int i = 0; i < unitsCount; ++i) {
            //runstate,runstatestandby,endStates,
            NSDictionary *unitsDic = units[i];
            NSArray *commonCommands = [unitsDic objectForKey:@"commonCommands"];
            [self saveStates:commonCommands to:washDic index:i];
            //commonCommands
            [self saveCommonCommands:commonCommands to:washDic index:i];
            //clothes weight
            NSArray *programmes = [unitsDic objectForKey:@"programmes"];
            [self saveProgrammes:programmes num:WASH_COMMAND_CLOTHES_WEIGHT key:WASHMODE_KEY_CLOTHES_WEIGHT to:washDic index:i];
        }
    }
    NSArray *keys = washDic.allKeys;
    if (keys.count == 0) {
        return nil;
    }
    return washDic;
}

- (void)saveStates:(NSArray *)commonCommands to:(NSMutableDictionary *)typedic index:(int)i
{
    if (!commonCommands) {
        return;
    }

    BOOL isFind = NO;
    for (NSDictionary *dic in commonCommands) {
        isFind = [self saveEveryStates:dic to:typedic index:i];
        if (isFind) {
            break;
        }
    }
    if (!isFind) {
        [typedic setObject:@"no" forKey:[NSString stringWithFormat:@"runstate%d", i]];
    }
}

- (BOOL)saveEveryStates:(NSDictionary *)dic to:(NSMutableDictionary *)typedic index:(int)i
{
    NSString *num = [dic objectForKey:@"number"] ?: @"";
    BOOL isFind = NO;
    if ([num isEqualToString:WASH_COMMAND_STATE]) {
        NSArray *values = [dic objectForKey:@"values"];
        NSMutableArray *endStatus = [NSMutableArray new];
        for (NSDictionary *dic in values) {
            [self saveEndStatus:dic to:typedic endarr:endStatus index:i];
        }
        [typedic setObject:endStatus forKey:[NSString stringWithFormat:@"%@%d", WASHMODE_KEY_ENDSTATES, i]];
        isFind = YES;
    }

    return isFind;
}

- (void)saveEndStatus:(NSDictionary *)dic to:(NSMutableDictionary *)typedic endarr:(NSMutableArray *)endStatus index:(int)i
{
    NSString *value = [dic objectForKey:@"value"] ?: @"";
    if ([value isEqualToString:WASH_COMMAND_STATUS_STANDBY]) {
        NSString *code = [dic objectForKey:@"code"] ?: @"";
        [typedic setObject:code forKey:[NSString stringWithFormat:@"%@%d", WASHMODE_KEY_STATE_STANDBY, i]];
    }
    else if ([value isEqualToString:WASH_COMMAND_End1] || [value isEqualToString:WASH_COMMAND_End2] || [value isEqualToString:WASH_COMMAND_End3]) {
        NSString *code = [dic objectForKey:@"code"];
        if ([code isKindOfClass:[NSString class]] && code.length > 0) {
            [endStatus addObject:code];
        }
    }
}

- (void)save:(NSString *)number withKey:(NSString *)key from:(NSArray *)commonCommands to:(NSMutableDictionary *)typedic
{
    if (!commonCommands || !typedic) {
        return;
    }
    BOOL isFind = NO;
    for (NSDictionary *dic in commonCommands) {
        NSString *num = [dic objectForKey:@"number"] ?: @"";
        if ([num isEqualToString:number]) {
            NSString *code = [dic objectForKey:@"code"] ?: @"";
            [typedic setObject:code forKey:key];
            isFind = YES;
            NSArray *values = [dic objectForKey:@"values"];
            [self saveValue:values withKey:key to:typedic];
            break;
        }
    }
    if (!isFind) {
        [typedic setObject:@"no" forKey:key];
    }
}

- (void)saveValue:(NSArray *)values withKey:(NSString *)key to:(NSMutableDictionary *)typedic
{
    if (!values) {
        return;
    }
    if (!values.count) {
        return;
    }
    BOOL isFind = NO;
    for (NSDictionary *dic in values) {
        NSString *value = [dic objectForKey:@"value"] ?: @"";
        if ([value isEqualToString:@"1"]) {
            NSString *code = [dic objectForKey:@"code"] ?: @"";
            [typedic setObject:code forKey:[NSString stringWithFormat:@"%@yes", key]];
            isFind = YES;
            break;
        }
    }
    if (!isFind) {
        [typedic setObject:@"no" forKey:[NSString stringWithFormat:@"%@yes", key]];
    }
}

- (void)saveAlarmFrom:(NSDictionary *)dic to:(NSMutableDictionary *)typedic
{
    NSArray *alarms = [dic objectForKey:@"alarms"];
    if (!alarms) {
        return;
    }
    BOOL isFindAlarm = NO;
    for (NSDictionary *dic in alarms) {
        NSString *tag = [dic objectForKey:@"tag"] ?: @"";
        tag = [tag lowercaseStringWithLocale:[NSLocale currentLocale]];
        if ([tag isEqualToString:@"ok"]) {
            isFindAlarm = YES;
            NSString *code = [dic objectForKey:@"code"] ?: @"";
            [typedic setObject:code forKey:WASHMODE_KEY_ALARM_CODE];
            break;
        }
    }
    if (!isFindAlarm) {
        [typedic setObject:@"no" forKey:WASHMODE_KEY_ALARM_CODE];
    }
}

- (void)saveCommonCommands:(NSArray *)commands to:(NSMutableDictionary *)typedic index:(int)i
{
    if (!commands || !typedic) {
        return;
    }
    for (NSDictionary *dic in commands) {
        NSString *num = [dic objectForKey:@"number"] ?: @"";
        if (![self.numberDic.allKeys containsObject:num]) {
            continue;
        }
        NSString *value = self.numberDic[num] ?: @"";
        NSString *code = [dic objectForKey:@"code"] ?: @"";
        [typedic setObject:code forKey:[NSString stringWithFormat:@"%@%d", value, i]];
        bool takeValues = self.judgeDic[num] ? self.judgeDic[num].boolValue : false;
        if (!takeValues) {
            continue;
        }
        NSArray *values = [dic objectForKey:@"values"];
        if (![values isKindOfClass:NSArray.class] || values.count == 0) {
            [typedic setObject:@[] forKey:[NSString stringWithFormat:@"%@%@%d", value, @"Values", i]];
            continue;
        }
        NSMutableArray<NSDictionary *> *codes = [NSMutableArray new];
        for (NSDictionary *valueDic in values) {
            NSString *value = [valueDic objectForKey:@"value"] ?: @"";
            NSString *code = [valueDic objectForKey:@"code"] ?: @"";
            [codes addObject:@{ @"value" : value,
                                @"code" : code }];
        }
        [typedic setObject:codes forKey:[NSString stringWithFormat:@"%@%@%d", value, @"Values", i]];
    }
}

- (void)saveProgrammes:(NSArray *)programmes num:(NSString *)num key:(NSString *)key to:(NSMutableDictionary *)typedic index:(int)i
{
    if (![programmes isKindOfClass:NSArray.class] || ![typedic isKindOfClass:NSMutableDictionary.class]) {
        return;
    }
    for (NSDictionary *programme in programmes) {
        NSArray *commands = programme[@"commands"];
        NSString *code = [self findCodeFromCommands:commands num:num];
        if (![code isEqualToString:@""]) {
            [typedic setObject:code forKey:[NSString stringWithFormat:@"%@%d", key, i]];
            break;
        }
    }
}

- (NSString *__nonnull)findCodeFromCommands:(NSArray *)commands num:(NSString *)num
{
    if (![commands isKindOfClass:NSArray.class]) {
        return @"";
    }
    for (NSDictionary *command in commands) {
        NSString *number = command[@"number"];
        if ([number isEqualToString:num]) {
            NSString *code = command[@"code"] ?: @"";
            return code;
        }
    }
    return @"";
}

- (bool)isCategoriesSupport:(NSDictionary *)categorieDic key:(NSString *)key
{
    if (![categorieDic isKindOfClass:NSDictionary.class]) {
        return false;
    }
    NSNumber *isSupport = [categorieDic objectForKey:key] ?: @(false);
    return isSupport.boolValue;
}

@end
