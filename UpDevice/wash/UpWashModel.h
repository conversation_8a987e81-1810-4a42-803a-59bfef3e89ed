//
//  UpWashModel.h
//  UPDevice
//
//  Created by gump on 14/4/2021.
//  Copyright © 2021 海尔优家智能科技（北京）有限公司. All rights reserved.
//

#import <Foundation/Foundation.h>

extern NSString *const WASHMODE_KEY_STATE;
extern NSString *const WASHMODE_KEY_STATE_STANDBY;
extern NSString *const WASHMODE_KEY_HOUR;
extern NSString *const WASHMODE_KEY_MINUTE;
extern NSString *const WASHMODE_KEY_ENDSTATES;
extern NSString *const WASHMODE_KEY_ALARM_CODE;
extern NSString *const WASHMODE_KEY_POWER_ON;
extern NSString *const WASHMODE_KEY_POWER_OFF;
extern NSString *const WASHMODE_KEY_RUNNING_MODE;
extern NSString *const WASHMODE_KEY_LAUNDRY_CYCLE;
extern NSString *const WASHMODE_KEY_DRYER_POWER;
extern NSString *const WASHMODE_KEY_DRYER_REMAIN_TIME;
extern NSString *const WASHMODE_KEY_DRYER_REMOTE_CTR;
extern NSString *const WASHMODE_KEY_APPOINTMENT_OPEN;
extern NSString *const WASHMODE_KEY_CLOTHES_WEIGHT;
extern NSString *const WASHMODE_KEY_SUPPORT_FRESH_AIR;

NS_ASSUME_NONNULL_BEGIN
/**
 *  洗衣机数据模型
 */
@interface UpWashModel : NSObject
/**
 *  是否是滚筒洗衣机
 */
@property (nonatomic, assign, readonly) BOOL isRoller;
/**
 *  是否双桶
 */
@property (nonatomic, assign, readonly) BOOL isDouble;
/**
 *  上桶洗涤状态key
 */
@property (nonatomic, copy, readonly) NSString *washStateUpKey;
/**
 *  下桶洗涤状态key
 */
@property (nonatomic, copy, readonly) NSString *washStateDownKey;
/**
 *  上桶待机状态六位码
 */
@property (nonatomic, copy, readonly) NSString *washStateUpStandby;
/**
 *  下桶待机状态六位码
 */
@property (nonatomic, copy, readonly) NSString *washStateDownStandby;
/**
 *  上桶结束状态数组
 */
@property (nonatomic, copy, readonly) NSArray *endStatesUp;
/**
 *  下桶结束状态数组
 */
@property (nonatomic, copy, readonly) NSArray *endStatesDown;
/**
 *  上桶剩余洗涤时间小时六位码
 */
@property (nonatomic, copy, readonly) NSString *washTimeHourUp;
/**
 *  下桶剩余洗涤时间小时六位码
 */
@property (nonatomic, copy, readonly) NSString *washTimeHourDown;
/**
 *  上桶剩余洗涤时间分钟六位码
 */
@property (nonatomic, copy, readonly) NSString *washTimeMinUp;
/**
 *  下桶剩余洗涤时间分钟六位码
 */
@property (nonatomic, copy, readonly) NSString *washTimeMinDown;
/**
 *  报警解除六位码
 */
@property (nonatomic, copy, readonly) NSString *alarmCancel;
/**
 *  开机六位码key
 */
@property (nonatomic, copy, readonly) NSString *onStatusKey;
/**
 *  开机六位码value
 */
@property (nonatomic, copy, readonly) NSString *onStatusValue;
/**
 *  关机六位码key
 */
@property (nonatomic, copy, readonly) NSString *offStatusKey;
/**
 *  关机六位码value
 */
@property (nonatomic, copy, readonly) NSString *offStatusValue;
/**
 *  上桶程序values
 *  @since v7.11.0
 */
@property (nonatomic, copy, readonly) NSArray<NSDictionary *> *operationUpValues;
/**
 *  下桶程序values
 *  @since v7.11.0
 */
@property (nonatomic, copy, readonly) NSArray<NSDictionary *> *operationDownValues;
/**
 *  上桶运行模式
 *  @since v7.11.0
 */
@property (nonatomic, copy, readonly) NSString *runningModeUp;
/**
 *  下桶运行模式
 *  @since v7.11.0
 */
@property (nonatomic, copy, readonly) NSString *runningModeDown;
/**
 *  上桶运行模式values
 *  @since v7.11.0
 */
@property (nonatomic, copy, readonly) NSArray<NSDictionary *> *runningModeUpValues;
/**
 *  下桶运行模式values
 *  @since v7.11.0
 */
@property (nonatomic, copy, readonly) NSArray<NSDictionary *> *runningModeDownValues;
/**
 *  上桶干衣机状态
 *  @since v7.11.0
 */
@property (nonatomic, copy, readonly) NSString *laundryCycleUp;
/**
 *  下桶干衣机状态
 *  @since v7.11.0
 */
@property (nonatomic, copy, readonly) NSString *laundryCycleDown;
/**
 *  上桶干衣机电源状态
 *  @since v7.11.0
 */
@property (nonatomic, copy, readonly) NSString *dryerPowerStatusUp;
/**
 *  下桶干衣机电源状态
 *  @since v7.11.0
 */
@property (nonatomic, copy, readonly) NSString *dryerPowerStatusDown;
/**
 *  上桶干衣机电源状态values
 *  @since v7.11.0
 */
@property (nonatomic, copy, readonly) NSArray<NSDictionary *> *dryerPowerStatusUpValues;
/**
 *  下桶干衣机电源状态values
 *  @since v7.11.0
 */
@property (nonatomic, copy, readonly) NSArray<NSDictionary *> *dryerPowerStatusDownValues;
/**
 *  上桶干衣机远程控制
 *  @since v7.11.0
 */
@property (nonatomic, copy, readonly) NSString *dryerRemoteCtrValidUp;
/**
 *  下桶干衣机远程控制
 *  @since v7.11.0
 */
@property (nonatomic, copy, readonly) NSString *dryerRemoteCtrValidDown;
/**
 *  上桶干衣机远程控制values
 *  @since v7.11.0
 */
@property (nonatomic, copy, readonly) NSArray<NSDictionary *> *dryerRemoteCtrValidUpValues;
/**
 *  下桶干衣机远程控制values
 *  @since v7.11.0
 */
@property (nonatomic, copy, readonly) NSArray<NSDictionary *> *dryerRemoteCtrValidDownValues;
/**
 *  上桶干衣机剩余时间
 *  @since v7.11.0
 */
@property (nonatomic, copy, readonly) NSString *dryerRemainingResnTimeUp;
/**
 *  下桶干衣机剩余时间
 *  @since v7.11.0
 */
@property (nonatomic, copy, readonly) NSString *dryerRemainingResnTimeDown;
/**
 *  上桶预约开启
 *  @since v7.11.0
 */
@property (nonatomic, copy, readonly) NSString *appointmentNotOpenUp;
/**
 *  下桶预约开启
 *  @since v7.11.0
 */
@property (nonatomic, copy, readonly) NSString *appointmentNotOpenDown;
/**
 *  上桶预约开启values
 *  @since v7.11.0
 */
@property (nonatomic, copy, readonly) NSArray<NSDictionary *> *appointmentNotOpenUpValues;
/**
 *  下桶预约开启values
 *  @since v7.11.0
 */
@property (nonatomic, copy, readonly) NSArray<NSDictionary *> *appointmentNotOpenDownValues;
/**
 *  上桶衣重
 *  @since v7.11.0
 */
@property (nonatomic, copy, readonly) NSString *washClothesWeightUp;
/**
 *  下桶衣重
 *  @since v7.11.0
 */
@property (nonatomic, copy, readonly) NSString *washClothesWeightDown;
/**
 *  是否支持新风
 *  @since v7.11.0
 */
@property (nonatomic, copy, readonly) NSString *supportFreshAir;

- (instancetype)initWithDic:(NSDictionary *)dict;
- (nullable NSString *)JSONStringConversion;
//使用本地存储的json数据转模型方法
- (instancetype)initWithJSONString:(NSString *)json;
@end

NS_ASSUME_NONNULL_END
