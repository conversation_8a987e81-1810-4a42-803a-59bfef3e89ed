//
//  UpWashModel.m
//  UPDevice
//
//  Created by gump on 14/4/2021.
//  Copyright © 2021 海尔优家智能科技（北京）有限公司. All rights reserved.
//

#import "UpWashModel.h"
#import "UPDeviceLog.h"

NSString *const WASHMODE_KEY_STATE = @"state";
NSString *const WASHMODE_KEY_STATE_STANDBY = @"stateStandby";
NSString *const WASHMODE_KEY_HOUR = @"hour";
NSString *const WASHMODE_KEY_MINUTE = @"minute";
NSString *const WASHMODE_KEY_ENDSTATES = @"endStates";
NSString *const WASHMODE_KEY_ALARM_CODE = @"alarmcode";
NSString *const WASHMODE_KEY_POWER_ON = @"poweron";
NSString *const WASHMODE_KEY_POWER_OFF = @"poweroff";
NSString *const WASHMODE_KEY_RUNNING_MODE = @"runningMode";
NSString *const WASHMODE_KEY_LAUNDRY_CYCLE = @"laundryCycle";
NSString *const WASHMODE_KEY_DRYER_POWER = @"dryerPowerStatus";
NSString *const WASHMODE_KEY_DRYER_REMAIN_TIME = @"dryerRemainingResnTime";
NSString *const WASHMODE_KEY_DRYER_REMOTE_CTR = @"dryerRemoteCtrValid";
NSString *const WASHMODE_KEY_APPOINTMENT_OPEN = @"appointmentNotOpen";
NSString *const WASHMODE_KEY_CLOTHES_WEIGHT = @"clothesWeight";
NSString *const WASHMODE_KEY_SUPPORT_FRESH_AIR = @"supportFreshAir";

@implementation UpWashModel

- (instancetype)initWithDic:(NSDictionary *)dict
{
    self = [super init];
    if (self) {
        NSString *type = [dict objectForKey:@"type"] ?: @"";
        type = [type lowercaseStringWithLocale:[NSLocale currentLocale]];
        if ([type containsString:@"pulsator"]) {
            _isRoller = NO;
        }
        else {
            _isRoller = YES;
        }

        NSString *units = [dict objectForKey:@"units"] ?: @"";
        if (units.intValue > 1) {
            _isDouble = YES;
        }
        else {
            _isDouble = NO;
        }

        _washStateUpKey = [dict objectForKey:[NSString stringWithFormat:@"%@%d", WASHMODE_KEY_STATE, 0]] ?: @"";
        _washStateDownKey = [dict objectForKey:[NSString stringWithFormat:@"%@%d", WASHMODE_KEY_STATE, 1]] ?: @"";
        _washStateUpStandby = [dict objectForKey:[NSString stringWithFormat:@"%@%d", WASHMODE_KEY_STATE_STANDBY, 0]] ?: @"";
        _washStateDownStandby = [dict objectForKey:[NSString stringWithFormat:@"%@%d", WASHMODE_KEY_STATE_STANDBY, 1]] ?: @"";
        _endStatesUp = [dict objectForKey:[NSString stringWithFormat:@"%@%d", WASHMODE_KEY_ENDSTATES, 0]] ?: @[];
        _endStatesDown = [dict objectForKey:[NSString stringWithFormat:@"%@%d", WASHMODE_KEY_ENDSTATES, 1]] ?: @[];
        _washTimeHourUp = [dict objectForKey:[NSString stringWithFormat:@"%@%d", WASHMODE_KEY_HOUR, 0]] ?: @"";
        _washTimeHourDown = [dict objectForKey:[NSString stringWithFormat:@"%@%d", WASHMODE_KEY_HOUR, 1]] ?: @"";
        _washTimeMinUp = [dict objectForKey:[NSString stringWithFormat:@"%@%d", WASHMODE_KEY_MINUTE, 0]] ?: @"";
        _washTimeMinDown = [dict objectForKey:[NSString stringWithFormat:@"%@%d", WASHMODE_KEY_MINUTE, 1]] ?: @"";
        _alarmCancel = [dict objectForKey:WASHMODE_KEY_ALARM_CODE] ?: @"";
        _onStatusKey = [dict objectForKey:WASHMODE_KEY_POWER_ON] ?: @"";
        _onStatusValue = [dict objectForKey:[NSString stringWithFormat:@"%@%@", WASHMODE_KEY_POWER_ON, @"yes"]] ?: @"";
        _offStatusKey = [dict objectForKey:WASHMODE_KEY_POWER_OFF] ?: @"";
        _offStatusValue = [dict objectForKey:[NSString stringWithFormat:@"%@%@", WASHMODE_KEY_POWER_OFF, @"yes"]] ?: @"";
        _operationUpValues = [dict objectForKey:[NSString stringWithFormat:@"%@%@%d", WASHMODE_KEY_STATE, @"Values", 0]] ?: @[];
        _operationDownValues = [dict objectForKey:[NSString stringWithFormat:@"%@%@%d", WASHMODE_KEY_STATE, @"Values", 1]] ?: @[];
        _runningModeUp = [dict objectForKey:[NSString stringWithFormat:@"%@%d", WASHMODE_KEY_RUNNING_MODE, 0]] ?: @"";
        _runningModeDown = [dict objectForKey:[NSString stringWithFormat:@"%@%d", WASHMODE_KEY_RUNNING_MODE, 1]] ?: @"";
        _runningModeUpValues = [dict objectForKey:[NSString stringWithFormat:@"%@%@%d", WASHMODE_KEY_RUNNING_MODE, @"Values", 0]] ?: @[];
        _runningModeDownValues = [dict objectForKey:[NSString stringWithFormat:@"%@%@%d", WASHMODE_KEY_RUNNING_MODE, @"Values", 1]] ?: @[];
        _laundryCycleUp = [dict objectForKey:[NSString stringWithFormat:@"%@%d", WASHMODE_KEY_LAUNDRY_CYCLE, 0]] ?: @"";
        _laundryCycleDown = [dict objectForKey:[NSString stringWithFormat:@"%@%d", WASHMODE_KEY_LAUNDRY_CYCLE, 1]] ?: @"";
        _dryerPowerStatusUp = [dict objectForKey:[NSString stringWithFormat:@"%@%d", WASHMODE_KEY_DRYER_POWER, 0]] ?: @"";
        _dryerPowerStatusDown = [dict objectForKey:[NSString stringWithFormat:@"%@%d", WASHMODE_KEY_DRYER_POWER, 1]] ?: @"";
        _dryerPowerStatusUpValues = [dict objectForKey:[NSString stringWithFormat:@"%@%@%d", WASHMODE_KEY_DRYER_POWER, @"Values", 0]] ?: @[];
        _dryerPowerStatusDownValues = [dict objectForKey:[NSString stringWithFormat:@"%@%@%d", WASHMODE_KEY_DRYER_POWER, @"Values", 1]] ?: @[];
        _dryerRemoteCtrValidUp = [dict objectForKey:[NSString stringWithFormat:@"%@%d", WASHMODE_KEY_DRYER_REMOTE_CTR, 0]] ?: @"";
        _dryerRemoteCtrValidDown = [dict objectForKey:[NSString stringWithFormat:@"%@%d", WASHMODE_KEY_DRYER_REMOTE_CTR, 1]] ?: @"";
        _dryerRemoteCtrValidUpValues = [dict objectForKey:[NSString stringWithFormat:@"%@%@%d", WASHMODE_KEY_DRYER_REMOTE_CTR, @"Values", 0]] ?: @[];
        _dryerRemoteCtrValidDownValues = [dict objectForKey:[NSString stringWithFormat:@"%@%@%d", WASHMODE_KEY_DRYER_REMOTE_CTR, @"Values", 1]] ?: @[];
        _dryerRemainingResnTimeUp = [dict objectForKey:[NSString stringWithFormat:@"%@%d", WASHMODE_KEY_DRYER_REMAIN_TIME, 0]] ?: @"";
        _dryerRemainingResnTimeDown = [dict objectForKey:[NSString stringWithFormat:@"%@%d", WASHMODE_KEY_DRYER_REMAIN_TIME, 1]] ?: @"";
        _appointmentNotOpenUp = [dict objectForKey:[NSString stringWithFormat:@"%@%d", WASHMODE_KEY_APPOINTMENT_OPEN, 0]] ?: @"";
        _appointmentNotOpenDown = [dict objectForKey:[NSString stringWithFormat:@"%@%d", WASHMODE_KEY_APPOINTMENT_OPEN, 1]] ?: @"";
        _appointmentNotOpenUpValues = [dict objectForKey:[NSString stringWithFormat:@"%@%@%d", WASHMODE_KEY_APPOINTMENT_OPEN, @"Values", 0]] ?: @[];
        _appointmentNotOpenDownValues = [dict objectForKey:[NSString stringWithFormat:@"%@%@%d", WASHMODE_KEY_APPOINTMENT_OPEN, @"Values", 1]] ?: @[];
        _washClothesWeightUp = [dict objectForKey:[NSString stringWithFormat:@"%@%d", WASHMODE_KEY_CLOTHES_WEIGHT, 0]] ?: @"";
        _washClothesWeightDown = [dict objectForKey:[NSString stringWithFormat:@"%@%d", WASHMODE_KEY_CLOTHES_WEIGHT, 1]] ?: @"";
        _supportFreshAir = [dict objectForKey:WASHMODE_KEY_SUPPORT_FRESH_AIR] ?: @"";
    }
    return self;
}

- (nullable NSString *)JSONStringConversion
{
    NSDictionary *dic = @{
        @"isRoller" : self.isRoller ? @"true" : @"false",
        @"isDouble" : self.isDouble ? @"true" : @"false",
        @"operationUp" : self.washStateUpKey ?: @"",
        @"operationDown" : self.washStateDownKey ?: @"",
        @"standbyUp" : self.washStateUpStandby ?: @"",
        @"standbyDown" : self.washStateDownStandby ?: @"",
        @"endStatesUp" : self.endStatesUp ?: @[],
        @"endStatesDown" : self.endStatesDown ?: @[],
        @"hourUp" : self.washTimeHourUp ?: @"",
        @"hourDown" : self.washTimeHourDown ?: @"",
        @"minUp" : self.washTimeMinUp ?: @"",
        @"minDown" : self.washTimeMinDown ?: @"",
        @"alarmCancel" : self.alarmCancel ?: @"",
        @"onStatusKey" : self.onStatusKey ?: @"",
        @"onStatusValue" : self.onStatusValue ?: @"",
        @"offStatusKey" : self.offStatusKey ?: @"",
        @"offStatusValue" : self.offStatusValue ?: @"",
        @"operationUpValues" : self.operationUpValues ?: @[],
        @"operationDownValues" : self.operationDownValues ?: @[],
        @"runningModeUp" : self.runningModeUp ?: @"",
        @"runningModeDown" : self.runningModeDown ?: @"",
        @"runningModeUpValues" : self.runningModeUpValues ?: @[],
        @"runningModeDownValues" : self.runningModeDownValues ?: @[],
        @"laundryCycleUp" : self.laundryCycleUp ?: @"",
        @"laundryCycleDown" : self.laundryCycleDown ?: @"",
        @"dryerPowerStatusUp" : self.dryerPowerStatusUp ?: @"",
        @"dryerPowerStatusDown" : self.dryerPowerStatusDown ?: @"",
        @"dryerPowerStatusUpValues" : self.dryerPowerStatusUpValues ?: @[],
        @"dryerPowerStatusDownValues" : self.dryerPowerStatusDownValues ?: @[],
        @"dryerRemoteCtrValidUp" : self.dryerRemoteCtrValidUp ?: @"",
        @"dryerRemoteCtrValidDown" : self.dryerRemoteCtrValidDown ?: @"",
        @"dryerRemoteCtrValidUpValues" : self.dryerRemoteCtrValidUpValues ?: @[],
        @"dryerRemoteCtrValidDownValues" : self.dryerRemoteCtrValidDownValues ?: @[],
        @"dryerRemainingResnTimeUp" : self.dryerRemainingResnTimeUp ?: @"",
        @"dryerRemainingResnTimeDown" : self.dryerRemainingResnTimeDown ?: @"",
        @"appointmentNotOpenUp" : self.appointmentNotOpenUp ?: @"",
        @"appointmentNotOpenDown" : self.appointmentNotOpenDown ?: @"",
        @"appointmentNotOpenUpValues" : self.appointmentNotOpenUpValues ?: @[],
        @"appointmentNotOpenDownValues" : self.appointmentNotOpenDownValues ?: @[],
        @"washClothesWeightUp" : self.washClothesWeightUp ?: @"",
        @"washClothesWeightDown" : self.washClothesWeightDown ?: @"",
        @"supportFreshAir" : self.supportFreshAir ?: @"",
    };
    NSError *error = NULL;
    NSData *dictionaryData = [NSJSONSerialization dataWithJSONObject:dic options:0 error:&error];
    if (error) {
        return nil;
    }
    return [[NSString alloc] initWithData:dictionaryData encoding:NSUTF8StringEncoding];
}

- (instancetype)initWithJSONString:(NSString *)json
{
    if (![json isKindOfClass:[NSString class]] || json.length == 0) {
        return nil;
    }
    NSData *jsonData = [json dataUsingEncoding:NSUTF8StringEncoding];
    if (!jsonData) {
        UPLogDebug(kUPDevicePrefix, @"%s[%d]json data is null", __PRETTY_FUNCTION__, __LINE__);
        return nil;
    }
    NSDictionary *dict = [NSJSONSerialization JSONObjectWithData:jsonData options:0 error:nil];
    if (![dict isKindOfClass:[NSDictionary class]]) {
        UPLogDebug(kUPDevicePrefix, @"%s[%d]json json to dict fail", __PRETTY_FUNCTION__, __LINE__);
        return nil;
    }
    self = [super init];
    if (self) {
        NSString *isRoller = [dict[@"isRoller"] isKindOfClass:[NSString class]] ? dict[@"isRoller"] : @"";
        _isRoller = [isRoller isEqualToString:@"true"];
        NSString *isDouble = [dict[@"isDouble"] isKindOfClass:[NSString class]] ? dict[@"isDouble"] : @"";
        _isDouble = [isDouble isEqualToString:@"true"];
        _washStateUpKey = [dict[@"operationUp"] isKindOfClass:[NSString class]] ? dict[@"operationUp"] : @"";
        _washStateDownKey = [dict[@"operationDown"] isKindOfClass:[NSString class]] ? dict[@"operationDown"] : @"";
        _washStateUpStandby = [dict[@"standbyUp"] isKindOfClass:[NSString class]] ? dict[@"standbyUp"] : @"";
        _washStateDownStandby = [dict[@"standbyDown"] isKindOfClass:[NSString class]] ? dict[@"standbyDown"] : @"";
        _endStatesUp = [dict[@"endStatesUp"] isKindOfClass:[NSArray class]] ? dict[@"endStatesUp"] : @[];
        _endStatesDown = [dict[@"endStatesDown"] isKindOfClass:[NSArray class]] ? dict[@"endStatesDown"] : @[];
        _washTimeHourUp = [dict[@"hourUp"] isKindOfClass:[NSString class]] ? dict[@"hourUp"] : @"";
        _washTimeHourDown = [dict[@"hourDown"] isKindOfClass:[NSString class]] ? dict[@"hourDown"] : @"";
        _washTimeMinUp = [dict[@"minUp"] isKindOfClass:[NSString class]] ? dict[@"minUp"] : @"";
        _washTimeMinDown = [dict[@"minDown"] isKindOfClass:[NSString class]] ? dict[@"minDown"] : @"";
        _alarmCancel = [dict[@"alarmCancel"] isKindOfClass:[NSString class]] ? dict[@"alarmCancel"] : @"";
        _onStatusKey = [dict[@"onStatusKey"] isKindOfClass:[NSString class]] ? dict[@"onStatusKey"] : @"";
        _onStatusValue = [dict[@"onStatusValue"] isKindOfClass:[NSString class]] ? dict[@"onStatusValue"] : @"";
        _offStatusKey = [dict[@"offStatusKey"] isKindOfClass:[NSString class]] ? dict[@"offStatusKey"] : @"";
        _offStatusValue = [dict[@"offStatusValue"] isKindOfClass:[NSString class]] ? dict[@"offStatusValue"] : @"";
        _operationUpValues = [dict[@"operationUpValues"] isKindOfClass:[NSArray class]] ? dict[@"operationUpValues"] : @[];
        _operationDownValues = [dict[@"operationDownValues"] isKindOfClass:[NSArray class]] ? dict[@"operationDownValues"] : @[];
        _runningModeUp = [dict[@"runningModeUp"] isKindOfClass:[NSString class]] ? dict[@"runningModeUp"] : @"";
        _runningModeDown = [dict[@"runningModeDown"] isKindOfClass:[NSString class]] ? dict[@"runningModeDown"] : @"";
        _runningModeUpValues = [dict[@"runningModeUpValues"] isKindOfClass:[NSArray class]] ? dict[@"runningModeUpValues"] : @[];
        _runningModeDownValues = [dict[@"runningModeDownValues"] isKindOfClass:[NSArray class]] ? dict[@"runningModeDownValues"] : @[];
        _laundryCycleUp = [dict[@"laundryCycleUp"] isKindOfClass:[NSString class]] ? dict[@"laundryCycleUp"] : @"";
        _laundryCycleDown = [dict[@"laundryCycleDown"] isKindOfClass:[NSString class]] ? dict[@"laundryCycleDown"] : @"";
        _dryerPowerStatusUp = [dict[@"dryerPowerStatusUp"] isKindOfClass:[NSString class]] ? dict[@"dryerPowerStatusUp"] : @"";
        _dryerPowerStatusDown = [dict[@"dryerPowerStatusDown"] isKindOfClass:[NSString class]] ? dict[@"dryerPowerStatusDown"] : @"";
        _dryerPowerStatusUpValues = [dict[@"dryerPowerStatusUpValues"] isKindOfClass:[NSArray class]] ? dict[@"dryerPowerStatusUpValues"] : @[];
        _dryerPowerStatusDownValues = [dict[@"dryerPowerStatusDownValues"] isKindOfClass:[NSArray class]] ? dict[@"dryerPowerStatusDownValues"] : @[];
        _dryerRemoteCtrValidUp = [dict[@"dryerRemoteCtrValidUp"] isKindOfClass:[NSString class]] ? dict[@"dryerRemoteCtrValidUp"] : @"";
        _dryerRemoteCtrValidDown = [dict[@"dryerRemoteCtrValidDown"] isKindOfClass:[NSString class]] ? dict[@"dryerRemoteCtrValidDown"] : @"";
        _dryerRemoteCtrValidUpValues = [dict[@"dryerRemoteCtrValidUpValues"] isKindOfClass:[NSArray class]] ? dict[@"dryerRemoteCtrValidUpValues"] : @[];
        _dryerRemoteCtrValidDownValues = [dict[@"dryerRemoteCtrValidDownValues"] isKindOfClass:[NSArray class]] ? dict[@"dryerRemoteCtrValidDownValues"] : @[];
        _dryerRemainingResnTimeUp = [dict[@"dryerRemainingResnTimeUp"] isKindOfClass:[NSString class]] ? dict[@"dryerRemainingResnTimeUp"] : @"";
        _dryerRemainingResnTimeDown = [dict[@"dryerRemainingResnTimeDown"] isKindOfClass:[NSString class]] ? dict[@"dryerRemainingResnTimeDown"] : @"";
        _appointmentNotOpenUp = [dict[@"appointmentNotOpenUp"] isKindOfClass:[NSString class]] ? dict[@"appointmentNotOpenUp"] : @"";
        _appointmentNotOpenDown = [dict[@"appointmentNotOpenDown"] isKindOfClass:[NSString class]] ? dict[@"appointmentNotOpenDown"] : @"";
        _appointmentNotOpenUpValues = [dict[@"appointmentNotOpenUpValues"] isKindOfClass:[NSArray class]] ? dict[@"appointmentNotOpenUpValues"] : @[];
        _appointmentNotOpenDownValues = [dict[@"appointmentNotOpenDownValues"] isKindOfClass:[NSArray class]] ? dict[@"appointmentNotOpenDownValues"] : @[];
        _washClothesWeightUp = [dict[@"washClothesWeightUp"] isKindOfClass:[NSString class]] ? dict[@"washClothesWeightUp"] : @"";
        _washClothesWeightDown = [dict[@"washClothesWeightDown"] isKindOfClass:[NSString class]] ? dict[@"washClothesWeightDown"] : @"";
        _supportFreshAir = [dict[@"supportFreshAir"] isKindOfClass:[NSString class]] ? dict[@"supportFreshAir"] : @"";
    }
    return self;
}


@end
