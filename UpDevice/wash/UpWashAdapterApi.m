//
//  UpWashAdapterApi.m
//  UPDevice
//
//  Created by gump on 14/4/2021.
//  Copyright © 2021 海尔优家智能科技（北京）有限公司. All rights reserved.
//

#import "UpWashAdapterApi.h"

@interface UpWashAdapterApi ()
@property (nonatomic, strong) NSDictionary *param;
@end

@implementation UpWashAdapterApi

- (instancetype)initWithWashAdapterParam:(NSDictionary *)param
{
    if (self = [super init]) {
        self.param = param;
    }
    return self;
}

- (NSString *)name
{
    return @"查询适配洗衣机设置接口";
}

- (NSString *)baseURL
{
    return @"https://smartwasher.haier.net";
}

- (NSString *)path
{
    return @"/wash-adapter-rest/washingMachine/detail/1/0";
}

- (NSObject *)requestBody
{
    return self.param;
}

- (UPRequestMethod)method
{
    return UPRequestMethodPOST;
}

- (NSDictionary<NSString *, NSString *> *)requestHeaders
{
    NSDictionary *requestBody = (NSDictionary *)self.requestBody;
    return [UPCommonServerHeader signHeaderWithBody:requestBody];
}

@end
