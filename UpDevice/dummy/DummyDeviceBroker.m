//
//  DummyDeviceBroker.m
//  UPDevice
//
//  Created by gump on 8/1/2019.
//  Copyright © 2019 海尔优家智能科技（北京）有限公司. All rights reserved.
//

#import "DummyDeviceBroker.h"
#import "DummyDeviceToolkit.h"
#import "UPDeviceLog.h"

@interface DummyDeviceBroker ()

@property (nonatomic, strong) DummyDeviceToolkit *toolkit;

@end

@implementation DummyDeviceBroker

- (instancetype)init
{
    self = [super init];
    if (self) {
        self.toolkit = [[DummyDeviceToolkit alloc] init];
    }
    return self;
}

#pragma mark - UpDeviceBroker
- (BOOL)isToolkitReady
{
    return NO;
}

- (id<UpDeviceToolkit>)getToolkit
{
    return self.toolkit;
}

- (void)prepareToolkit:(void (^)(UpDeviceResult *result))finishBlock
{
    [self.toolkit execute:@"prepareToolkit"
                   params:nil
              finishBlock:^(UpDeviceResult *result) {

                if ([result isSuccessful]) {
                    UPDeviceLogDebug(@"%s[%d]prepareToolkit成功！", __PRETTY_FUNCTION__, __LINE__);
                }
                else {
                    UPDeviceLogError(@"%s[%d]prepareToolkit失败！error:%@", __PRETTY_FUNCTION__, __LINE__, result.extraInfo);
                }

                if (finishBlock) {
                    finishBlock(result);
                }
              }];
}

- (void)releaseToolkit:(void (^)(UpDeviceResult *result))finishBlock
{
    [self.toolkit execute:@"releaseToolkit"
                   params:nil
              finishBlock:^(UpDeviceResult *result) {

                if ([result isSuccessful]) {
                    UPDeviceLogDebug(@"%s[%d]releaseToolkit成功！", __PRETTY_FUNCTION__, __LINE__);
                }
                else {
                    UPDeviceLogError(@"%s[%d]releaseToolkit失败！error:%@", __PRETTY_FUNCTION__, __LINE__, result.extraInfo);
                }

                if (finishBlock) {
                    finishBlock(result);
                }
              }];
}

- (void)attachToolkit:(id<UpDeviceToolkitListener>)deviceToolkitListener detectListener:(id<UpDeviceDetectListener>)deviceDetectListener immediate:(BOOL)immediate finishBlock:(void (^)(UpDeviceResult *result))finishBlock
{
    [self.toolkit attachToolkit:deviceToolkitListener
                 detectListener:deviceDetectListener
                    finishBlock:^(UpDeviceResult *result) {

                      if ([result isSuccessful]) {
                          UPDeviceLogDebug(@"%s[%d]attachToolkit成功！", __PRETTY_FUNCTION__, __LINE__);
                      }
                      else {
                          UPDeviceLogError(@"%s[%d]attachToolkit失败！error:%@", __PRETTY_FUNCTION__, __LINE__, result.extraInfo);
                      }

                      if (finishBlock) {
                          finishBlock(result);
                      }
                    }];
}

- (void)detachToolkit:(id<UpDeviceToolkitListener>)deviceToolkitListener detectListener:(id<UpDeviceDetectListener>)deviceDetectListener finishBlock:(void (^)(UpDeviceResult *result))finishBlock
{
    [self.toolkit detachToolkit:^(UpDeviceResult *result) {

      if ([result isSuccessful]) {
          UPDeviceLogDebug(@"%s[%d]detachToolkit成功！", __PRETTY_FUNCTION__, __LINE__);
      }
      else {
          UPDeviceLogError(@"%s[%d]detachToolkit失败！error:%@", __PRETTY_FUNCTION__, __LINE__, result.extraInfo);
      }

      if (finishBlock) {
          finishBlock(result);
      }
    }];
}

- (void)attachDevice:(NSString *)deviceId reportListener:(id<UpDeviceReportListener>)listener immediate:(BOOL)immediate finishBlock:(void (^)(UpDeviceResult *result))finishBlock
{
    [self.toolkit attachDevice:deviceId
                reportListener:listener
                   finishBlock:^(UpDeviceResult *result) {

                     if ([result isSuccessful]) {
                         UPDeviceLogDebug(@"%s[%d]attachDevice成功！", __PRETTY_FUNCTION__, __LINE__);
                     }
                     else {
                         UPDeviceLogError(@"%s[%d]attachDevice失败！error:%@", __PRETTY_FUNCTION__, __LINE__, result.extraInfo);
                     }

                     if (finishBlock) {
                         finishBlock(result);
                     }
                   }];
}

- (void)detachDevice:(NSString *)deviceId reportListener:(id<UpDeviceReportListener>)listener finishBlock:(void (^)(UpDeviceResult *result))finishBlock
{
    [self.toolkit detachDevice:deviceId
                   finishBlock:^(UpDeviceResult *result) {

                     if ([result isSuccessful]) {
                         UPDeviceLogDebug(@"%s[%d]detachDevice成功！", __PRETTY_FUNCTION__, __LINE__);
                     }
                     else {
                         UPDeviceLogError(@"%s[%d]detachDevice失败！error:%@", __PRETTY_FUNCTION__, __LINE__, result.extraInfo);
                     }

                     if (finishBlock) {
                         finishBlock(result);
                     }
                   }];
}

- (NSMutableDictionary *)gatewayParams
{
    return nil;
}

- (void)setGatewayParams:(NSMutableDictionary *)params
{
}

@end
