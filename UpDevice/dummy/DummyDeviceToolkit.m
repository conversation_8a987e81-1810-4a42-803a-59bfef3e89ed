//
//  DummyDeviceToolkit.m
//  UPDevice
//
//  Created by gump on 11/1/2019.
//  Copyright © 2019 海尔优家智能科技（北京）有限公司. All rights reserved.
//

#import "DummyDeviceToolkit.h"

static NSString *const PROTOCOL = @"updevice-dummy";
static NSString *const VERSION = @"4.0.0";

@implementation DummyDeviceToolkit

- (instancetype)init
{
    self = [super init];
    if (self) {
    }
    return self;
}

#pragma mark - UpDeviceToolkit

- (NSString *)getSupportProtocol
{
    return PROTOCOL;
}

- (NSString *)getToolkitVersion
{
    return VERSION;
}

@end
