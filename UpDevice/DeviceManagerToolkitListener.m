//
//  DeviceManagerToolkitListener.m
//  UPDevice
//
//  Created by gump on 15/1/2019.
//  Copyright © 2019 海尔优家智能科技（北京）有限公司. All rights reserved.
//

#import "DeviceManagerToolkitListener.h"
#import "UPDeviceLog.h"

@implementation DeviceManagerToolkitListener

- (void)onStateChange:(NSString *)protocol state:(UpDeviceToolkitState)state
{
    UPDeviceLogInfo(@"%s[%d]onStateChange: %@-> %ld", __PRETTY_FUNCTION__, __LINE__, protocol, state);
}

@end
