//
//  UpDeviceInjection.h
//  UPDevice
//
//  Created by gump on 8/1/2019.
//  Copyright © 2019 海尔优家智能科技（北京）有限公司. All rights reserved.
//

#import <Foundation/Foundation.h>
#import "UpDeviceManager.h"
#import "UpDeviceDataSource.h"
#import "WifiDeviceToolkit.h"

NS_ASSUME_NONNULL_BEGIN

// 存储Manager初始化，通过存储Key发生变化，
// 发送通知，Flutter B层 - C层 重新建立链接
static NSString *const UP_DEVICE_MANGER_INIT_KEY = @"UP_DEVICE_MANGER_INIT";
static NSString *const UP_DEVICE_MANGER_INIT_VALUE = @"INIT";

@interface UpDeviceInjection : NSObject
@property (nonatomic, strong, readonly) UpDeviceManager *deviceManager;

+ (UpDeviceInjection *)getInstance;
- (void)initDeviceManagerWithWifi:(id<WifiDeviceToolkit>)toolkit dataSource:(id<UpDeviceDataSource>)dataSource;

@end

NS_ASSUME_NONNULL_END
