//
//  UpDeviceDaemon.h
//  UPDevice
//
//  Created by gump on 8/1/2019.
//  Copyright © 2019 海尔优家智能科技（北京）有限公司. All rights reserved.
//

#import <Foundation/Foundation.h>
#import "UpDevice.h"
#import "UpDeviceCarrier.h"
#import "UpDeviceFilter.h"

NS_ASSUME_NONNULL_BEGIN

@interface UpDeviceDaemon : NSObject
- (NSMutableArray<id<UpDevice>> *)list:(nullable id<UpDeviceFilter>)filter;
- (id<UpDevice>)get:(NSString *)uniqueId;
- (void)put:(NSString *)uniqueId device:(id<UpDevice>)device;
- (void)putScannerDevice:(NSString *)uniqueId device:(id<UpDevice>)device;
- (nullable id<UpDevice>)remove:(NSString *)uniqueId;
- (nullable id<UpDevice>)removeScannerDevice:(NSString *)uniqueId;
- (void)start;
- (void)stop;
- (void)moveDeviceToQueueHead:(NSString *)uniqueId;
- (void)moveDevicesToQueueHead:(NSArray<NSString *> *)uniqueIds;
- (NSArray<NSString *> *)getPriorityPrepareQueue;
- (NSArray<NSString *> *)getPrepareQueue;
- (void)clearPrepareQueue;
- (id<UpDevice>)getControlDevice:(NSString *)uniqueId;
- (BOOL)isScannerDevice:(NSString *)uniqueId; //todo
- (id<UpDevice>)getScannerDevice:(NSString *)uniqueId; //todo
/**
 *  设置当前家庭优先准备队列
 *  @param deviceIds 加入队列的设备id数组
 *  @since  7.5.0
 */
- (void)setCurFamilyPriorityPrepareQueue:(NSArray<NSString *> *)deviceIds;

/**
 *  获取当前家庭优先准备队列
 *  @since  7.5.0
 */
- (NSArray<NSString *> *)curFamilyPriorityPrepareQueue;

/**
 *  原函数moveDeviceToQueueHead的实现中，混入了将deviceId加入优先准备队列的操作。此函数只移动队列位置，不夹杂其他功能
 *  @param uniqueId 唯一设备id
 *  @since  7.5.0
 */
- (void)moveDeviceToQueueHeadPure:(NSString *)uniqueId;

/**
 *  @brief 添加处理配置文件线程任务到任务队列
 *  @param operation 线程任务
 *  @since  7.22.0
 */
- (void)addConfigOperation:(NSBlockOperation *)operation;

/**
 *  @brief 开始GIO打点统计
 *  @param devices 设备数组
 *  @since  7.8.1
 */
- (void)beginGIO:(NSArray<id<UpDeviceInfo>> *)devices;

/**
 *  @brief gio打点，统计本地已有配置文件的设备数目
 *  @param deviceId 设备id
 *  @since  7.9.2
 */
- (void)GIOCountActiveDevice:(NSString *)deviceId;

/**
 *  @brief gio打点，统计设备attach完成时间
 *  @since  7.10.0
 */
- (void)GIOCountAttachTime;

/**
 *  @brief gio打点，统计设备查找逻辑约束文件完成时间
 *  @since  7.10.0
 */
- (void)GIOCountFindConfigFileTime;

/**
 *  @brief gio打点，统计设备解析逻辑约束文件完成时间
 *  @since  7.10.0
 */
- (void)GIOCountParseConfigFileTime;
@end

NS_ASSUME_NONNULL_END
