//
//  UpDeviceDataSourceWrapper.m
//  UPDevice
//
//  Created by gump on 8/1/2019.
//  Copyright © 2019 海尔优家智能科技（北京）有限公司. All rights reserved.
//

#import "UpDeviceDataSourceWrapper.h"
#import "UPDeviceLog.h"

@interface UpDeviceDataSourceWrapper () {
    id<UpDeviceDataSource> _dataSourceRef;
}

@end

@implementation UpDeviceDataSourceWrapper

- (void)setDataSource:(id<UpDeviceDataSource>)dataSource
{
    _dataSourceRef = dataSource;
}

- (void)getDeviceList:(BOOL)immediate finishBlock:(void (^)(UpDeviceResult *result))finishBlock
{
    if (_dataSourceRef == nil) {
        UpDeviceResult *result = [[UpDeviceResult alloc] initWithErrorCode:ErrorCode_FAILURE extraData:nil extraCode:nil extraInfo:@"dataSourceRef为空"];
        UPDeviceLogError(@"%s[%d]获取设备列表失败,dataSourceRef为空", __PRETTY_FUNCTION__, __LINE__);
        if (finishBlock) {
            finishBlock(result);
        }

        return;
    }

    [_dataSourceRef getDeviceList:immediate
                      finishBlock:^(UpDeviceResult *result) {

                        if ([result isSuccessful]) {
                            UPDeviceLogDebug(@"%s[%d]getDeviceList成功！", __PRETTY_FUNCTION__, __LINE__);
                        }
                        else {
                            UPDeviceLogError(@"%s[%d]getDeviceList失败！error:%@", __PRETTY_FUNCTION__, __LINE__, result.extraInfo);
                        }

                        if (finishBlock) {
                            finishBlock(result);
                        }
                      }];
}

- (void)getGroupMemberListWithFamilyId:(NSString *)familyId finishBlock:(void (^)(UpDeviceResult *))finishBlock
{
    if (_dataSourceRef == nil) {
        UpDeviceResult *result = [[UpDeviceResult alloc] initWithErrorCode:ErrorCode_FAILURE extraData:nil extraCode:nil extraInfo:@"dataSourceRef为空"];
        UPDeviceLogError(@"%s[%d]getGroupMemberListWithFamilyId,dataSourceRef为空", __PRETTY_FUNCTION__, __LINE__);
        if (finishBlock) {
            finishBlock(result);
        }

        return;
    }
    [_dataSourceRef getGroupMemberListWithFamilyId:familyId
                                       finishBlock:^(UpDeviceResult *result) {
                                         if ([result isSuccessful]) {
                                             UPDeviceLogDebug(@"%s[%d]getGroupMemberListWithFamilyId成功！", __PRETTY_FUNCTION__, __LINE__);
                                         }
                                         else {
                                             UPDeviceLogError(@"%s[%d]getGroupMemberListWithFamilyId失败！error:%@", __PRETTY_FUNCTION__, __LINE__, result.extraInfo);
                                         }

                                         if (finishBlock) {
                                             finishBlock(result);
                                         }
                                       }];
}


@end
