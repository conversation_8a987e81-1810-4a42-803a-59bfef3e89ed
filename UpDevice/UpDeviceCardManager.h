//
//  UpDeviceCardManager.h
//  UPDevice
//
//  Created by <PERSON><PERSON><PERSON>   on 2025/3/31.
//  Copyright © 2025 海尔优家智能科技（北京）有限公司. All rights reserved.
//

#import <Foundation/Foundation.h>
#import "UpDeviceCardInfo.h"

NS_ASSUME_NONNULL_BEGIN

@protocol UpDeviceCard <NSObject>

- (NSString *)getDeviceCardDeviceId;

- (NSString *)getDeviceCardFamilyId;
- (BOOL)getDeviceCardIsSharedDevice;
- (BOOL)getDeviceCardIsAggregateDevice;

- (UpDeviceCardInfo *)getDeviceCardInfo:(NSString *)familyId;
@end

@interface UpDeviceCardManager : NSObject
+ (NSArray<id<UpDeviceCard>> *)filterDeviceCardList:(NSArray<id<UpDeviceCard>> *)deviceCardList with:(NSString *)familyId;
+ (NSArray<id<UpDeviceCard>> *)sortDeviceCardList:(NSArray<id<UpDeviceCard>> *)deviceCardList with:(NSString *)familyId;

/// 过滤当前家庭的设备ID
+ (NSArray<NSString *> *)filterAndSortDeviceCardList:(NSArray<id<UpDeviceCard>> *)deviceCardList with:(NSString *)familyId limitCount:(NSInteger)limitCount;

/// 是否是洗衣机设备
+ (BOOL)isWashingMachineWithTypeId:(NSString *)typeId;
@end

NS_ASSUME_NONNULL_END
