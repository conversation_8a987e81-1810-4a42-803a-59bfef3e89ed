//
//  UpDeviceManager.h
//  UPDevice
//
//  Created by gump on 8/1/2019.
//  Copyright © 2019 海尔优家智能科技（北京）有限公司. All rights reserved.
//

#import <Foundation/Foundation.h>
#import "UpDeviceDataSource.h"
#import "UpDeviceBroker.h"
#import "UpDeviceFactory.h"
#import "UpDeviceCenter.h"
#import "WifiDeviceToolkit.h"
#import "UpDeviceTracker.h"
#import "UpDeviceCommandIntercepter.h"

@class uSDKDeviceInfo;
@interface UpDeviceManager : NSObject <UpDeviceCenter, UpDeviceListener>

/// 设备列表是否完成创建
@property (nonatomic, assign) BOOL isDeviceListCreateComplete;

/// 命令拦截器数组
@property (nonatomic, strong) NSArray<id<UpDeviceCommandIntercepter>> *commandIntercepters;

/// 初始化设备管理类
/// @param toolkit 设备监听经纪人
/// @param source 设备数据源
- (instancetype)initDeviceManagerWithToolkit:(id<WifiDeviceToolkit>)toolkit dataSource:(id<UpDeviceDataSource>)source;

/// 添加工厂
/// @param deviceFactory 设备工厂
- (void)appendDeviceFactory:(id<UpDeviceFactory>)deviceFactory;

/// 删除工厂
/// @param deviceFactory 设备工厂
- (void)removeDeviceFactory:(id<UpDeviceFactory>)deviceFactory;

/// 设置默认工厂
/// @param deviceFactory 设备工厂
- (void)setupDefaultFactory:(id<UpDeviceFactory>)deviceFactory;

/// 得到支持的协议
- (NSString *)getSupportProtocol;

/// 设置设备比较算法
/// @param deviceComparator 设备比较算法
- (void)setDeviceComparator:(NSComparator)deviceComparator;

/// 远程连接所有设备
- (void)connectRemoteDevices;

/// 从守护进程中删除设备
/// @param deviceId 设备ID
- (id<UpDevice>)destroyDevice:(NSString *)deviceId;

/// 断开设备远程连接
- (void)disconnectRemoteDevices;

/// 设备是否被绑定
/// @param deviceId 设备ID
- (BOOL)isBound:(NSString *)deviceId;

/// 获取设备绑定信息
/// @param deviceId 设备ID
/// @param finishBlock 完成回调
- (void)getDeviceBindInfo:(NSString *)deviceId finishBlock:(void (^)(UpDeviceResult *result))finishBlock;

/// 将待prepare设备移到队列头
/// @param deviceId 设备ID
- (void)moveDeviceToQueueHead:(NSString *)deviceId;

/**
 *  原函数moveDeviceToQueueHead的实现中，混入了将deviceId加入优先准备队列的操作。此函数只移动队列位置，不夹杂其他功能
 *  @param deviceId 设备id
 *  @since  7.5.0
 */
- (void)moveDeviceToQueueHeadPure:(NSString *)deviceId;

/// 将待prepare设备数组移到队列头
/// @param deviceIds 设备ID数组
- (void)moveDevicesToQueueHead:(NSArray<NSString *> *)deviceIds;

/// 获取优先准备队列
- (NSArray<NSString *> *)getPriorityPrepareQueue;

/// 获取准备队列
- (NSArray<NSString *> *)getPrepareQueue;

/// 设置设备数据源
/// @param deviceDataSource 设备数据源
- (void)setDataSource:(id<UpDeviceDataSource>)deviceDataSource;

/// 添加命令拦截器
/// @param intercepter 命令拦截器
- (void)addCommandIntercepter:(id<UpDeviceCommandIntercepter>)intercepter;

/// 为设备添加命令拦截器
- (void)addCommandInterceptersForDevices;

/// GIO打点
/// @param tracker 打点的数据
- (void)setTracker:(id<UpDeviceTracker>)tracker;

/**
 *  设置当前家庭优先准备队列
 *  @param deviceIds 加入队列的设备id数组
 *  @since  7.5.0
 */
- (void)setCurFamilyPriorityPrepareQueue:(NSArray<NSString *> *)deviceIds;

/**
 *  获取当前家庭优先准备队列
 *  @since  7.5.0
 */
- (NSArray<NSString *> *)curFamilyPriorityPrepareQueue;

/**
 *  @brief 添加处理配置文件线程任务到任务队列
 *  @param operation 线程任务
 *  @since  7.22.0
 */
- (void)addConfigOperation:(NSBlockOperation *)operation;

/**
 *  @brief gio打点，统计本地已有配置文件的设备数目
 *  @param deviceId 设备id
 *  @since  7.9.2
 */
- (void)GIOCountActiveDevice:(NSString *)deviceId;

/**
 *  @brief gio打点，统计设备attach完成时间
 *  @since  7.10.2
 */
- (void)GIOCountAttachTime;

/**
 *  @brief gio打点，统计设备查找逻辑约束文件完成时间
 *  @since  7.10.0
 */
- (void)GIOCountFindConfigFileTime;

/**
 *  @brief gio打点，统计设备解析逻辑约束文件完成时间
 *  @since  7.10.0
 */
- (void)GIOCountParseConfigFileTime;

/**
 *  @brief 更新当前家庭设备信息
 *  @since  7.24.1
 */
- (void)updateCurrentFamilyDeviceInfo;
@end
