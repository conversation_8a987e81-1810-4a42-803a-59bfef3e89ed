//
//  UpDeviceResourceManager.m
//  UPDevice
//
//  Created by 王杰 on 2022/12/15.
//  Copyright © 2022 海尔优家智能科技（北京）有限公司. All rights reserved.
//

#import "UpDeviceResourceManager.h"
#import "UpDeviceObjectCommonHelper.h"
#import <UPResource/UPResourceInjection.h>
#import "UpDeviceGetDeviceResourceTool.h"
#import "UPDeviceLog.h"
#import "UpDeviceInjection.h"
#import "UpDeviceStringCommonHelper.h"
@interface UpDeviceResourceManager ()
@property (nonatomic, strong) UPResourceManager *resourceManager;
@end

@implementation UpDeviceResourceManager
+ (UpDeviceResourceManager *)getInstance
{
    static dispatch_once_t once_t;
    static UpDeviceResourceManager *instance = nil;
    dispatch_block_t block = ^{
      instance = [[self alloc] init];
    };
    dispatch_once(&once_t, block);
    return instance;
}


- (void)getAppFuncModelWithDeviceId:(NSString *)deviceId finishBlock:(void (^)(UpDeviceResult *))finishBlock
{
    if (!UPDevice_isValidString(deviceId)) {
        UpDeviceResult *result = [[UpDeviceResult alloc] initWithErrorCode:ErrorCode_FAILURE extraData:nil extraCode:nil extraInfo:@"deivceId is null"];
        UPDeviceLogError(@"%s[%d]getAppFuncModel deivceId is null ", __PRETTY_FUNCTION__, __LINE__);
        finishBlock ? finishBlock(result) : nil;
        return;
    }
    if (!self.resourceManager) {
        UpDeviceResult *result = [[UpDeviceResult alloc] initWithErrorCode:ErrorCode_FAILURE extraData:nil extraCode:nil extraInfo:@"resourceManager is null"];
        UPDeviceLogError(@"%s[%d]getAppFuncModel resourceManager is null ", __PRETTY_FUNCTION__, __LINE__);
        finishBlock ? finishBlock(result) : nil;
        return;
    }
    UpDeviceManager *deviceManager = [UpDeviceInjection getInstance].deviceManager;
    if (!deviceManager) {
        UpDeviceResult *result = [[UpDeviceResult alloc] initWithErrorCode:ErrorCode_FAILURE extraData:nil extraCode:nil extraInfo:@"deviceManager is null"];
        UPDeviceLogError(@"%s[%d]getAppFuncModel deviceManager is null ", __PRETTY_FUNCTION__, __LINE__);
        finishBlock ? finishBlock(result) : nil;
        return;
    }
    id<UpDevice> device = [deviceManager getDevice:deviceId];
    NSString *prodNo = device.getInfo.prodNo;
    if (!UPDevice_isValidString(prodNo)) {
        UpDeviceResult *result = [[UpDeviceResult alloc] initWithErrorCode:ErrorCode_FAILURE extraData:nil extraCode:nil extraInfo:@"prodNo is null"];
        UPDeviceLogError(@"%s[%d]getAppFuncModel prodNo is null deviceId:%@", __PRETTY_FUNCTION__, __LINE__, deviceId);
        finishBlock ? finishBlock(result) : nil;
        return;
    }
    UPResourceDeviceCondition *condition = [[UPResourceDeviceCondition alloc] initResourceType:UPResourceTypeAppFuncModel];
    condition.prodNo = prodNo;
    //查找本地资源
    NSArray<UPResourceInfo *> *infoList = [self.resourceManager searchDeviceResList:condition];
    UPResourceInfo *resourceInfo = infoList.firstObject;
    if ([self validInstallResource:resourceInfo]) { //判断本地资源是否安装
        //读取资源 若成功直接返回  失败则请求服务器资源
        __weak typeof(self) weakSelf = self;
        [self getAppFuncModelWithResource:resourceInfo
                              finishBlock:^(UpDeviceResult *localResult) {
                                if (localResult.isSuccessful) {
                                    [weakSelf asyncUpDateServerAppFuncModelWithCondition:condition]; //异步更新服务器数据
                                    finishBlock ? finishBlock(localResult) : nil;
                                }
                                else {
                                    [weakSelf getServerAppFuncModelWithCondition:condition finishBlock:finishBlock];
                                }
                              }];
    }
    else {
        [self getServerAppFuncModelWithCondition:condition finishBlock:finishBlock];
    }
}

//更新服务器数据
- (void)asyncUpDateServerAppFuncModelWithCondition:(UPResourceDeviceCondition *)condition
{
    UPDeviceLogDebug(@"[%s][%d]asyncUpDateServerAppFuncModel prodNo:%@", __PRETTY_FUNCTION__, __LINE__, condition.prodNo);
    dispatch_async(dispatch_get_global_queue(DISPATCH_QUEUE_PRIORITY_DEFAULT, 0), ^{
      UpDeviceGetDeviceResourceTool *resourceTool = [[UpDeviceGetDeviceResourceTool alloc] initWithResoureManager:self.resourceManager condition:condition];
      [resourceTool getResourceWithCompleteion:nil];
    });
}


//获取服务器资源
- (void)getServerAppFuncModelWithCondition:(UPResourceDeviceCondition *)condition finishBlock:(void (^)(UpDeviceResult *))finishBlock
{
    __weak typeof(self) weakSelf = self;
    UpDeviceGetDeviceResourceTool *resourceTool = [[UpDeviceGetDeviceResourceTool alloc] initWithResoureManager:self.resourceManager condition:condition];
    [resourceTool getResourceWithCompleteion:^(UpDeviceResult *resouceResult) {
      if (resouceResult.isSuccessful && [self validInstallResource:resouceResult.extraData]) {
          [weakSelf getAppFuncModelWithResource:resouceResult.extraData finishBlock:finishBlock];
      }
      else {
          UpDeviceResult *result = [[UpDeviceResult alloc] initWithErrorCode:ErrorCode_FAILURE extraData:nil extraCode:nil extraInfo:resouceResult.extraInfo];
          UPDeviceLogError(@"[%s][%d]getAppFuncModel fail prodNo:%@ error:%@", __PRETTY_FUNCTION__, __LINE__, condition.prodNo, result.extraInfo);
          finishBlock ? finishBlock(result) : nil;
      }
    }];
}

//根据resourceInfo 读取资源并返回
- (void)getAppFuncModelWithResource:(UPResourceInfo *)resource finishBlock:(void (^)(UpDeviceResult *))finishBlock
{
    if (!UPDevice_isValidString(resource.path)) {
        UpDeviceResult *result = [[UpDeviceResult alloc] initWithErrorCode:ErrorCode_FAILURE extraData:nil extraCode:nil extraInfo:@"path is null"];
        UPDeviceLogError(@"[%s][%d]getAppFuncModel path is null prodNo:%@", __PRETTY_FUNCTION__, __LINE__, resource.prodNo);
        finishBlock ? finishBlock(result) : nil;
        return;
    }
    NSString *content = [UpDeviceStringCommonHelper getContentFromFilePath:resource.path];
    if (!UPDevice_isValidString(content)) {
        UpDeviceResult *result = [[UpDeviceResult alloc] initWithErrorCode:ErrorCode_FAILURE extraData:nil extraCode:nil extraInfo:@"content is null"];
        UPDeviceLogError(@"[%s][%d]getAppFuncModel content is null prodNo:%@", __PRETTY_FUNCTION__, __LINE__, resource.prodNo);
        finishBlock ? finishBlock(result) : nil;
        return;
    }
    UpDeviceResult *result = [UpDeviceResult UpDeviceResult:ErrorCode_SUCCESS extraData:content];
    UPDeviceLogDebug(@"[%s][%d]getAppFuncModel success! prodNo:%@", __PRETTY_FUNCTION__, __LINE__, resource.prodNo);
    finishBlock ?
        finishBlock(result) :
        nil;
}


- (BOOL)validInstallResource:(UPResourceInfo *)resourceInfo
{
    if ([resourceInfo isKindOfClass:[UPResourceInfo class]] && resourceInfo.active && UPDevice_isValidString(resourceInfo.path)) {
        return YES;
    }
    else {
        return NO;
    }
}

- (UPResourceManager *)resourceManager
{
    if (!_resourceManager) {
        _resourceManager = [UPResourceInjection getInstance].resourceManager;
    }
    return _resourceManager;
}

@end
