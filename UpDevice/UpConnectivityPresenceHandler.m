//
//  UpConnectivityPresenceHandler.m
//  UPDevice
//
//  Created by lichen on 2025/4/17.
//  Copyright © 2025 海尔优家智能科技（北京）有限公司. All rights reserved.
//

#import "UpConnectivityPresenceHandler.h"
#import <upuserdomain/UpUserDomainHolder.h>
#import "UpDeviceInjection.h"
#import "UpDeviceBase.h"
#import "UPDeviceLog.h"

@implementation UpConnectivityPresenceHandler

+ (nullable UpPresenceInfo *)getConnectivityPresenceInfoBy:(NSString *)deviceId
{
    if (![deviceId isKindOfClass:[NSString class]] || deviceId.length < 1) {

        UPDeviceLogError(@"%s[%d]getConnectivityPresenceInfo! error: device is empty", __PRETTY_FUNCTION__, __LINE__);
        return nil;
    }

    id<UpDevice> device = [[UpDeviceInjection getInstance].deviceManager getDevice:deviceId];

    if (![device isKindOfClass:[UpDeviceBase class]]) {
        return nil;
    }

    UpDeviceBase *deviceBase = (UpDeviceBase *)device;

    // 未绑定的设备不做计算
    if (deviceBase.getControlState != UpDeviceControlState_None) {
        return nil;
    }

    UPDeviceLogDebug(@"%s[%d]getConnectivityPresenceInfo! device:%@", __PRETTY_FUNCTION__, __LINE__, deviceId);

    id<UpDeviceToolkit> deviceToolKit = [deviceBase getToolkit];

    UpDeviceRealOnline wifiOnlineState = [deviceToolKit getDeviceWifiOnlineState:deviceId];
    // wifi在线显示在线
    if (wifiOnlineState == UpDeviceRealOnline_ONLINE) {
        return nil;
    }

    BOOL isSharedDevice = [deviceBase getDeviceCardIsSharedDevice];
    BOOL isBleOnline = [self isBleOnline:deviceBase.getBleState];

    UPDeviceLogDebug(@"%s[%d]getConnectivityPresenceInfo! isSharedDevice:%i, isBleOnline:%i", __PRETTY_FUNCTION__, __LINE__, isSharedDevice, isBleOnline);
    if (isSharedDevice) {
        // 共享设备
        return [self getCanNotReBindPresenceInfoBy:isBleOnline];
    }

    BOOL isSupportOnlyConfig = [deviceToolKit isSupportOnlyConfig:deviceId];
    if (!isSupportOnlyConfig) {
        // 不支持仅配网
        return [self getPresenceInfoBasedOnDeviceStates:deviceId
                                            isBleOnline:isBleOnline
                                    isSupportOnlyConfig:isSupportOnlyConfig];
    }

    NSInteger faultInformationCode = deviceBase.getFaultInformationCode;
    if (faultInformationCode == 0) {
        return [self getPresenceInfoBasedOnDeviceStates:deviceId
                                            isBleOnline:isBleOnline
                                    isSupportOnlyConfig:isSupportOnlyConfig];
    }

    if (isBleOnline) {
        // 蓝牙在线
        return [self getPresenceInfoBy:UpPresenceStateBleOnlineWifiSetting];
    }

    BOOL isOnlyConfigFlow = [deviceToolKit isOnlyConfigFlow:deviceId];
    if (!isOnlyConfigFlow) {
        return [self getPresenceInfoBasedOnDeviceStates:deviceId
                                            isBleOnline:isBleOnline
                                    isSupportOnlyConfig:isSupportOnlyConfig];
    }

    return [self getPresenceInfoBy:UpPresenceStateOfflineBleMonitor];
}

+ (nullable UpPresenceInfo *)getPresenceInfoBasedOnDeviceStates:(NSString *)deviceId
                                                    isBleOnline:(BOOL)isBleOnline
                                            isSupportOnlyConfig:(BOOL)isSupportOnlyConfig
{
    id<UDUserDelegate> user = self.currentUserDomain.user;
    id<UDDeviceDelegate> device = user.devices[deviceId];

    if (device == nil) {
        UPDeviceLogError(@"%s[%d]getPresenceInfoBasedOnDeviceStates! error: userdomain device is empty deviceId:%@", __PRETTY_FUNCTION__, __LINE__, deviceId);
        return nil;
    }

    id<UDFamilyDelegate> currentFamily = user.currentFamily;
    // 当前用户在家庭中的角色类型,0-创建者,1-管理员,2-普通成员
    NSInteger memberType = currentFamily.memberType;

    UPDeviceLogDebug(@"%s[%d]getPresenceInfoBasedOnDeviceStates! deviceId:%@ memberType:%ld, reBind:%i", __PRETTY_FUNCTION__, __LINE__, deviceId, memberType, device.reBind);
    if (!device.reBind) {
        // 高安全设备

        BOOL isOwned = device.isOwned;
        if (isOwned || memberType == 0) {
            // 家庭创建者及设备所有者
            return [self getCanReBindPresenceInfoBy:isBleOnline isSupportOnlyConfig:isSupportOnlyConfig];
        }

        return [self getCanNotReBindPresenceInfoBy:isBleOnline];
    }

    // 普通设备
    if (memberType == 0 || memberType == 1) {
        // 家庭管理员及家庭创建者
        return [self getCanReBindPresenceInfoBy:isBleOnline isSupportOnlyConfig:isSupportOnlyConfig];
    }

    return [self getCanNotReBindPresenceInfoBy:isBleOnline];
}

+ (UpPresenceInfo *)getCanReBindPresenceInfoBy:(BOOL)isBleOnline
                           isSupportOnlyConfig:(BOOL)isSupportOnlyConfig
{
    UpPresenceCode presenceCode;
    if (isBleOnline) {
        presenceCode = isSupportOnlyConfig ? UpPresenceStateBleOnlineWifiBind : UpPresenceStateBleOnlineGuideBind;
    }
    else {
        presenceCode = isSupportOnlyConfig ? UpPresenceStateOfflineWifiBind : UpPresenceStateOfflineGuideBind;
    }

    return [self getPresenceInfoBy:presenceCode];
}

+ (nullable UpPresenceInfo *)getCanNotReBindPresenceInfoBy:(BOOL)isBleOnline
{
    if (isBleOnline) {
        return nil;
    }

    return [self getPresenceInfoBy:UpPresenceStateOfflineNormal];
}

+ (BOOL)isBleOnline:(UpDeviceConnection)bleState
{
    return (bleState == UpDeviceConnection_READY || bleState == UpDeviceConnection_CONNECTED);
}

+ (UpPresenceInfo *)getPresenceInfoBy:(UpPresenceCode)presenceCode
{
    UpPresenceInfo *presenceInfo = [[UpPresenceInfo alloc] init];
    presenceInfo.presenceCode = presenceCode;
    presenceInfo.popupTipInfo = [self getOfflineTipInfoBy:presenceInfo.presenceCode];

    return presenceInfo;
}

+ (UpPopupTipInfo *)getOfflineTipInfoBy:(UpPresenceCode)presenceCode
{
    NSString *title = @"";
    NSString *subTitle = @"";
    NSString *toConnectWifi = @"去连接Wi-Fi";

    if (presenceCode >= 9000) {

        title = @"设备已离线";
        subTitle = presenceCode == UpPresenceStateOfflineBleMonitor ? toConnectWifi : @"请检查设备是否接通电源";
    }
    else {

        title = @"设备Wi-Fi已断开，部分功能不可用";
        subTitle = toConnectWifi;
    }

    UpPopupTipInfo *popupTip = [[UpPopupTipInfo alloc] initWithTitle:title
                                                            subTitle:subTitle];

    UPDeviceLogDebug(@"%s[%d]getOfflineTipInfoBy! presenceCode:%ld", __PRETTY_FUNCTION__, __LINE__, presenceCode);
    return popupTip;
}

+ (UpUserDomain *)currentUserDomain
{
    return UpUserDomainHolder.instance.userDomain;
}

@end
