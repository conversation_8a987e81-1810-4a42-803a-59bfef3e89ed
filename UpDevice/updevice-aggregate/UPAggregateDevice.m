//
//  UPAggregateDevice.m
//  UPDevice
//
//  Created by pc on 2025/3/10.
//  Copyright © 2025 海尔优家智能科技（北京）有限公司. All rights reserved.
//

#import "UPAggregateDevice.h"
#import "UPDeviceLog.h"

@implementation UPAggregateDevice
- (instancetype)initWithDeviceInfo:(NSString *)uniqueId deviceInfo:(id<UpDeviceInfo>)deviceInfo broker:(id<UpDeviceBroker>)broker factory:(id<UpDeviceFactory>)factory
{
    if (self = [super initWithDeviceInfo:uniqueId deviceInfo:deviceInfo broker:broker factory:factory]) {
    }
    return self;
}

#pragma mark - baseDevice
- (id<UpDevice>)getExtApi
{
    return self;
}
- (BOOL)isExtApiPrepared
{
    return YES;
}
- (void)prepare:(void (^)(UpDeviceResult *))finishBlock
{
    if (finishBlock) {
        UpDeviceResult *result = [[UpDeviceResult alloc] initWithErrorCode:ErrorCode_SUCCESS extraData:nil extraCode:@"UPAggregateDevice doesn't need preparing." extraInfo:nil];
        UPDeviceLogDebug(@"%s[%d]UPAggregateDevice doesn't need preparing！", __PRETTY_FUNCTION__, __LINE__);
        finishBlock(result);
    }
}
- (void)release:(void (^)(UpDeviceResult *))finishBlock
{
    if (finishBlock) {
        UpDeviceResult *result = [[UpDeviceResult alloc] initWithErrorCode:ErrorCode_SUCCESS extraData:nil extraCode:@"UPAggregateDevice doesn't need release." extraInfo:nil];
        UPDeviceLogDebug(@"%s[%d]UPAggregateDevice doesn't need release！", __PRETTY_FUNCTION__, __LINE__);
        finishBlock(result);
    }
}
- (void)onPrepareExtApi:(void (^)(UpDeviceResult *result))finishblock
{
    if (finishblock) {
        UpDeviceResult *result = [[UpDeviceResult alloc] initWithErrorCode:ErrorCode_SUCCESS extraData:nil extraCode:@"UPAggregateDevice doesn't need preparing ExtApi." extraInfo:nil];
        UPDeviceLogDebug(@"%s[%d]UPAggregateDevice doesn't need preparing ExtApi.！", __PRETTY_FUNCTION__, __LINE__);
        finishblock(result);
    }
}
- (void)onReleaseExtApi:(void (^)(UpDeviceResult *result))finishblock
{
    if (finishblock) {
        UpDeviceResult *result = [[UpDeviceResult alloc] initWithErrorCode:ErrorCode_SUCCESS extraData:nil extraCode:@"UPAggregateDevice doesn't need preparing ExtApi." extraInfo:nil];
        UPDeviceLogDebug(@"%s[%d]UPAggregateDevice doesn't need preparing ExtApi.！", __PRETTY_FUNCTION__, __LINE__);
        finishblock(result);
    }
}
- (void)onReloadExtApi:(void (^)(UpDeviceResult *result))finishblock
{
    if (finishblock) {
        UpDeviceResult *result = [[UpDeviceResult alloc] initWithErrorCode:ErrorCode_SUCCESS extraData:nil extraCode:@"UPAggregateDevice doesn't need preparing ExtApi." extraInfo:nil];
        UPDeviceLogDebug(@"%s[%d]UPAggregateDevice doesn't need preparing ExtApi.！", __PRETTY_FUNCTION__, __LINE__);
        finishblock(result);
    }
}
- (id<UpDevice>)getDeviceById:(NSString *)deviceId
{
    UPDeviceLogDebug(@"%s[%d]This method is an empty implementation！", __PRETTY_FUNCTION__, __LINE__);
    return nil;
}
- (BOOL)isReady
{
    return UpDeviceConnection_READY;
}
- (UpDeviceConfigState)configState
{
    return UpDeviceConfigState_Unknown;
}
- (NSArray *)getEngineAttributeList
{
    return @[];
}
- (NSArray *)getEngineCautionList
{
    return @[];
}
@end
