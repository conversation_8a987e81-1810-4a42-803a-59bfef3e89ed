//
//  UPAggregateDeviceFactory.m
//  UPDevice
//
//  Created by pc on 2025/3/10.
//  Copyright © 2025 海尔优家智能科技（北京）有限公司. All rights reserved.
//

#import "UPAggregateDeviceFactory.h"
#import "UPAggregateDevice.h"

@implementation UPAggregateDeviceFactory
- (id<UpDevice>)create:(NSString *)uniqueId deviceInfo:(id<UpDeviceInfo>)deviceInfo broker:(id<UpDeviceBroker>)broker factory:(id<UpDeviceFactory>)factory
{
    NSString *deviceAggregateType = deviceInfo.getExtras[@"DI-Basic.deviceAggregateType"];
    if ([deviceAggregateType isKindOfClass:NSString.class] && deviceAggregateType.length > 0) {
        UPAggregateDevice *device = [[UPAggregateDevice alloc] initWithDeviceInfo:uniqueId deviceInfo:deviceInfo broker:broker factory:factory];
        return device;
    }
    return nil;
}
@end
