//
//  UpCompatDevice.m
//  UPDevice
//
//  Created by gump on 8/1/2019.
//  Copyright © 2019 海尔优家智能科技（北京）有限公司. All rights reserved.
//

#import "UpCompatDevice.h"
#import "UpDeviceType.h"
#import "UpDeviceNetworkType.h"
#import "UpDeviceStatus.h"
#import "UpCompatDeviceStore.h"
#import "UpDeviceFactory.h"
#import "UpDeviceAttribute.h"
#import "UpCompatCommand.h"
#import "UpCompatCallbackWrapper.h"
#import "UPDeviceLog.h"

@interface UpCompatDevice () {
    UpDeviceType *_deviceType;
    UpDeviceWifiType _networkType;
    UpDeviceStatus _deviceStatus;
    NSString *_deviceIp;
    int _internalStatus;
    UpCompatDeviceStore *_deviceStore;
    UpCompatCallbackWrapper *_callbackWrapper;
}

@end

@implementation UpCompatDevice

- (instancetype)initWithDeviceInfo:(NSString *)uniqueId
                        deviceInfo:(id<UpDeviceInfo>)deviceInfo
                            broker:(id<UpDeviceBroker>)broker
                           factory:(id<UpDeviceFactory>)factory
{
    if (self = [super initWithDeviceInfo:uniqueId
                              deviceInfo:deviceInfo
                                  broker:broker
                                 factory:factory]) {
        _deviceStatus = OFFLINE;
        _internalStatus = ST_DISCONNECT;
        _deviceStore = [UpCompatDeviceStore new];
    }
    return self;
}

#pragma mark - baseDevice

- (id<UpCompatApi>)getExtApi
{
    return self;
}

- (BOOL)isExtApiPrepared
{
    return YES;
}

- (void)onPrepareExtApi:(void (^)(UpDeviceResult *result))finishblock
{
    if (finishblock) {
        UpDeviceResult *result = [[UpDeviceResult alloc]
            initWithErrorCode:ErrorCode_SUCCESS
                    extraData:nil
                    extraCode:@"UpCompatDevice doesn't need preparing ExtApi."
                    extraInfo:nil];

        UPDeviceLogDebug(@"%s[%d]UpCompatDevice doesn't need preparing ExtApi.！",
                         __PRETTY_FUNCTION__, __LINE__);
        finishblock(result);
    }
}

- (void)onReleaseExtApi:(void (^)(UpDeviceResult *result))finishblock
{
    if (finishblock) {

        UpDeviceResult *result = [[UpDeviceResult alloc]
            initWithErrorCode:ErrorCode_SUCCESS
                    extraData:nil
                    extraCode:@"UpCompatDevice doesn't need preparing ExtApi."
                    extraInfo:nil];

        UPDeviceLogDebug(@"%s[%d]UpCompatDevice doesn't need preparing ExtApi.！",
                         __PRETTY_FUNCTION__, __LINE__);
        finishblock(result);
    }
}

- (void)onReloadExtApi:(void (^)(UpDeviceResult *result))finishblock
{
    if (finishblock) {
        UpDeviceResult *result = [[UpDeviceResult alloc]
            initWithErrorCode:ErrorCode_SUCCESS
                    extraData:nil
                    extraCode:@"UpCompatDevice doesn't need preparing ExtApi."
                    extraInfo:nil];

        UPDeviceLogDebug(@"%s[%d]UpCompatDevice doesn't need preparing ExtApi.！",
                         __PRETTY_FUNCTION__, __LINE__);
        finishblock(result);
    }
}

- (void)onPrepared
{
    if ([self getState] != UpDeviceState_PREPARED) {
        return;
    }
    NSArray<id<UpDeviceBaseInfo>> *subDevInfoList =
        [_deviceStore getSubDevInfoList];
    for (id<UpDeviceBaseInfo> subDevInfo in subDevInfoList) {
        id<UpDevice> subDev = [self getDeviceById:[subDevInfo deviceId]];
        if ([subDev isKindOfClass:[UpCompatDevice class]]) {
            UpCompatDevice *compatDevice = (UpCompatDevice *)subDev;
        }
    }

    for (id<UpDeviceBaseInfo> subDevInfo in subDevInfoList) {
        id<UpDevice> subDev = [self getDeviceById:[subDevInfo deviceId]];
        if ([subDev isKindOfClass:[UpCompatDevice class]]) {
            UpCompatDevice *compatDevice = (UpCompatDevice *)subDev;
        }
    }

    [self attach:_callbackWrapper];
}

- (id<UpDevice>)getDeviceById:(NSString *)deviceId
{
    UPDeviceLogWarning(@"%s[%d]该方法为空实现方法，不应该被调用！",
                       __PRETTY_FUNCTION__, __LINE__);
    return nil;
}

- (void)onReleased
{
    UPDeviceLogWarning(@"%s[%d]该方法为空实现方法，不应该被调用！",
                       __PRETTY_FUNCTION__, __LINE__);
    if ([self getState] != UpDeviceState_RELEASED) {
        return;
    }
}

- (BOOL)isReady
{
    UpDeviceConnection connectionStatus = [self getConnection];
    UpDeviceConnection requiredStatus = [self getConnection];
    if (requiredStatus == UpDeviceConnection_READY) {
        return connectionStatus == UpDeviceConnection_READY &&
               ST_PREPARED == _internalStatus;
    }
    else {
        return (connectionStatus == UpDeviceConnection_READY ||
                connectionStatus == requiredStatus) &&
               ST_PREPARED == _internalStatus;
    }
}

- (UpDeviceConfigState)configState
{
    return UpDeviceConfigState_NativeDevice;
}

- (NSArray *)getEngineAttributeList
{
    return @[];
}

- (NSArray *)getEngineCautionList
{
    return @[];
}
#pragma mark - <UpCompatApi> - start

#pragma mark - <UpCompatApi> - end

@end
