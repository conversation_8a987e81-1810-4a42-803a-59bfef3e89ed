//
//  UpCompatApi.h
//  UPDevice
//
//  Created by gump on 8/1/2019.
//  Copyright © 2019 海尔优家智能科技（北京）有限公司. All rights reserved.
//

#import <Foundation/Foundation.h>
#import "UpDeviceToolkit.h"
#import "UpDeviceCaution.h"
#import "UpDeviceAttribute.h"
#import "UpDeviceConnection.h"
#import "UpDeviceType.h"
#import "UpDeviceNetworkType.h"
#import "UpDeviceConnection.h"
#import "UpDeviceStatus.h"
#import "UpDeviceChangeCallback.h"
#import "UpCompatCommand.h"

@class UpCompatSubDev;

#define OPERATION_TIMEOUT TIMEOUT_DEFAULT

#define ST_DISCONNECT 0
#define ST_DISCONNECTING 1
#define ST_CONNECTING 10
#define ST_CONNECTED 11
#define ST_PREPARING 20
#define ST_PREPARED 21

@protocol UpCompatApi <NSObject>


@end
