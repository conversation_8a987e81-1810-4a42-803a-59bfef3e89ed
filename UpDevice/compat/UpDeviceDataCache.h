//
//  UpDeviceDataCache.h
//  UPDevice
//
//  Created by gump on 8/1/2019.
//  Copyright © 2019 海尔优家智能科技（北京）有限公司. All rights reserved.
//

#import <Foundation/Foundation.h>
#import "UpDeviceCaution.h"
#import "UpDeviceConnection.h"
#import "UpDeviceAttribute.h"

NS_ASSUME_NONNULL_BEGIN

@interface UpDeviceDataCache : NSObject

- (void)storeConnection:(UpDeviceConnection)connection;

- (UpDeviceConnection)getConnectionStatus;

- (void)setConnectionStatus:(UpDeviceConnection)connection;

- (nullable NSArray<id<UpDeviceCaution>> *)storeAlarmList:(NSArray<id<UpDeviceCaution>> *)cautionList isClear:(BOOL)isClear;

- (nullable NSArray<id<UpDeviceAttribute>> *)storeAttributeList:(NSArray<id<UpDeviceAttribute>> *)attributeList isClear:(BOOL)isClear;


- (void)updateAttribute:(id<UpDeviceAttribute>)attribute;

- (NSArray<id<UpDeviceCaution>> *)cloneAlarmList;

- (NSArray<id<UpDeviceAttribute>> *)cloneAttributeList;

- (nullable id<UpDeviceAttribute>)cloneAttributeByName:(NSString *)name;


@end

NS_ASSUME_NONNULL_END
