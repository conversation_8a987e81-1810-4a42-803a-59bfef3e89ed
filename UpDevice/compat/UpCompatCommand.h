//
//  UpCompatCommand.h
//  UPDevice
//
//  Created by gump on 8/1/2019.
//  Copyright © 2019 海尔优家智能科技（北京）有限公司. All rights reserved.
//

#import <Foundation/Foundation.h>
#import "DeviceCommand.h"

NS_ASSUME_NONNULL_BEGIN

@interface UpCompatCommand : DeviceCommand

- (instancetype)initWithGroupName:(NSString *)groupName Attributes:(NSDictionary<NSString *, NSString *> *)attributes ExpectAttributeMap:(NSDictionary<NSString *, NSString *> *)expectAttributeMap Timeout:(int)timeout;

- (int)getTimeout;

- (NSDictionary<NSString *, NSString *> *)getExpectAttributes;

- (void)putExpectAttribute:(NSString *)name Value:(NSString *)value;

- (void)putExpectAttributes:(NSDictionary<NSString *, NSString *> *)attrCmdMap;

- (NSArray<NSString *> *)getExpectAttrNameSet;

@end

NS_ASSUME_NONNULL_END
