//
//  UpCompatCallbackWrapper.m
//  UPDevice
//
//  Created by gump on 8/1/2019.
//  Copyright © 2019 海尔优家智能科技（北京）有限公司. All rights reserved.
//

#import "UpCompatCallbackWrapper.h"
#import "UpDeviceListener.h"
#import "UPDeviceLog.h"

@interface UpCompatCallbackWrapper () {
    NSMutableArray<id<UpDeviceChangeCallback>> *_deviceCallbackList;
    NSMutableArray<id<UpSubDevChangeCallback>> *_subDevCallbackList;
}

@end

@implementation UpCompatCallbackWrapper

- (instancetype)init
{
    if (self = [super init]) {
        _deviceCallbackList = [NSMutableArray array];
        _subDevCallbackList = [NSMutableArray array];
    }

    return self;
}

- (void)addDeviceCallback:(id<UpDeviceChangeCallback>)callback
{
    @synchronized(self)
    {
        if (callback == nil) {
            UPDeviceLogWarning(@"%s[%d]callback参数为空！", __PRETTY_FUNCTION__, __LINE__);
            return;
        }

        if (![_deviceCallbackList containsObject:callback]) {
            [_deviceCallbackList addObject:callback];
        }
    }
}

- (void)removeDeviceCallback:(id<UpDeviceChangeCallback>)callback
{
    @synchronized(self)
    {
        if (callback == nil) {
            UPDeviceLogWarning(@"%s[%d]callback参数为空！", __PRETTY_FUNCTION__, __LINE__);
            return;
        }
        [_deviceCallbackList removeObject:callback];
    }
}

- (void)clearDeviceCallback
{
    @synchronized(self)
    {
        [_deviceCallbackList removeAllObjects];
    }
}

- (void)addSubDevCallback:(id<UpSubDevChangeCallback>)callback
{
    @synchronized(self)
    {
        if (callback == nil) {
            UPDeviceLogWarning(@"%s[%d]callback参数为空！", __PRETTY_FUNCTION__, __LINE__);
            return;
        }
        if (![_subDevCallbackList containsObject:callback]) {
            [_subDevCallbackList addObject:callback];
        }
    }
}

- (void)removeSubDevCallback:(id<UpSubDevChangeCallback>)callback
{
    @synchronized(self)
    {
        if (callback == nil) {
            UPDeviceLogWarning(@"%s[%d]callback参数为空！", __PRETTY_FUNCTION__, __LINE__);
            return;
        }
        [_subDevCallbackList removeObject:callback];
    }
}

- (void)clearSubDevCallback
{
    @synchronized(self)
    {
        [_subDevCallbackList removeAllObjects];
    }
}

#pragma mark - delegate

- (void)onDeviceReport:(NSInteger)event device:(id)device
{
    UPDeviceLogDebug(@"%s[%d]onDeviceReport event=%ld,device=%@！", __PRETTY_FUNCTION__, __LINE__, event, [device getInfo].deviceId);
    switch (event) {
        case EVENT_BASE_INFO_CHANGE:
        case EVENT_DEVICE_STATE_CHANGE:
        case EVENT_ATTRIBUTES_CHANGE:
            [self notifyAttributesChange:device];
            break;
        case EVENT_DEVICE_CAUTION:
            [self notifyDeviceCaution:device];
            break;
        case EVENT_SUB_DEV_LIST_CHANGE:
            [self notifySubDevListChange:device];
            break;
        default:
            break;
    }
}

#pragma mark - private
- (void)notifySubDevListChange:(id<UpDevice>)device
{
    UPDeviceLogDebug(@"%s[%d]notifySubDevListChange device=%@！", __PRETTY_FUNCTION__, __LINE__, [device getInfo].deviceId);
    @synchronized(self)
    {
        id<UpDevice> parent = [device getParent];
        if (parent == nil) {
            UPDeviceLogWarning(@"%s[%d]parent参数为空！", __PRETTY_FUNCTION__, __LINE__);
            return;
        }
        NSArray<id<UpDevice>> *subDevList = [device getSubDevList];
        NSArray<id<UpSubDevChangeCallback>> *callbacks = [NSArray arrayWithArray:_subDevCallbackList];
        for (id<UpSubDevChangeCallback> callback in callbacks) {
            [callback onSubDevListChange:parent subDevList:subDevList];
        }
    }
}

- (void)notifyAttributesChange:(id<UpDevice>)device
{
    UPDeviceLogDebug(@"%s[%d]notifyAttributesChange device=%@！", __PRETTY_FUNCTION__, __LINE__, [device getInfo].deviceId);
    @synchronized(self)
    {
        id<UpDevice> parent = [device getParent];
        if (parent == nil) {
            NSArray<id<UpDeviceChangeCallback>> *callbacks = [NSArray arrayWithArray:_deviceCallbackList];
            for (id<UpDeviceChangeCallback> callback in callbacks) {
                [callback onDeviceChange:device];
            }
        }
        else {
            NSArray<id<UpSubDevChangeCallback>> *callbacks = [NSArray arrayWithArray:_subDevCallbackList];
            for (id<UpSubDevChangeCallback> callback in callbacks) {
                [callback onSubDevChange:parent subDev:device];
            }
        }
    }
}

- (void)notifyDeviceCaution:(id<UpDevice>)device
{
    UPDeviceLogDebug(@"%s[%d]notifyDeviceCaution device=%@！", __PRETTY_FUNCTION__, __LINE__, [device getInfo].deviceId);
    @synchronized(self)
    {
        id<UpDevice> parent = [device getParent];
        if (parent == nil) {
            NSArray<id<UpDeviceChangeCallback>> *callbacks = [NSArray arrayWithArray:_deviceCallbackList];
            for (id<UpDeviceChangeCallback> callback in callbacks) {
                [callback onDeviceAlarm:device];
            }
        }
        else {
            NSArray<id<UpSubDevChangeCallback>> *callbacks = [NSArray arrayWithArray:_subDevCallbackList];
            for (id<UpSubDevChangeCallback> callback in callbacks) {
                [callback onSubDevAlarm:parent subDev:device];
            }
        }
    }
}

@end
