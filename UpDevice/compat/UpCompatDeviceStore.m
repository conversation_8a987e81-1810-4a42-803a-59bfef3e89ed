//
//  UpCompatDeviceStore.m
//  UPDevice
//
//  Created by gump on 8/1/2019.
//  Copyright © 2019 海尔优家智能科技（北京）有限公司. All rights reserved.
//

#import "UpCompatDeviceStore.h"
#import "UpDeviceBaseInfo.h"
#import "UpDeviceAttribute.h"
#import "UpDeviceDataCache.h"
#import "UpDeviceBaseInfo.h"

@interface UpCompatDeviceStore () {
    NSMutableDictionary<NSString *, UpDeviceDataCache *> *_dataCacheMap;
    NSMutableDictionary<NSString *, id<UpDeviceBaseInfo>> *_subDevInfoMap;
}

@end

@implementation UpCompatDeviceStore

- (instancetype)init
{
    if (self = [super init]) {
        _dataCacheMap = [NSMutableDictionary dictionary];
        _subDevInfoMap = [NSMutableDictionary dictionary];
    }

    return self;
}

- (BOOL)hasSubDevList
{
    @synchronized(self)
    {
        return _subDevInfoMap.allKeys.count > 0;
    }
}

- (id<UpDeviceBaseInfo>)getSubDevInfo:(NSString *)deviceId
{
    @synchronized(self)
    {
        return [_subDevInfoMap objectForKey:deviceId];
    }
}

- (NSArray<id<UpDeviceBaseInfo>> *)getSubDevInfoList
{
    @synchronized(self)
    {
        return _subDevInfoMap.allValues;
    }
}

- (void)putSubDevInfoList:(NSArray<id<UpDeviceBaseInfo>> *)subDevInfoList
{
    @synchronized(self)
    {
        [_subDevInfoMap removeAllObjects];
        if (subDevInfoList != nil) {
            for (id<UpDeviceBaseInfo> baseInfo in subDevInfoList) {
                [_subDevInfoMap setObject:baseInfo forKey:baseInfo.deviceId];
            }
        }
    }
}

- (UpDeviceConnection)getConnectionStatus:(NSString *)deviceId
{
    return [self getConnectionStatus:deviceId fallback:UpDeviceConnection_OFFLINE];
}

- (UpDeviceConnection)getConnectionStatus:(NSString *)deviceId fallback:(UpDeviceConnection)fallback
{
    UpDeviceDataCache *dataCache = [self getDataCache:deviceId];
    UpDeviceConnection connectionStatus = dataCache != nil ? [dataCache getConnectionStatus] : UpDeviceConnection_OFFLINE;
    if (connectionStatus == UpDeviceConnection_OFFLINE) {
        connectionStatus = fallback;
    }
    return connectionStatus;
}

- (void)putConnection:(NSString *)deviceId Connection:(UpDeviceConnection)connection
{
    UpDeviceDataCache *dataCache = [self addDataCache:deviceId];
    [dataCache storeConnection:connection];
}

- (void)setConnectionStatus:(NSString *)deviceId ConnectionStatus:(UpDeviceConnection)connectionStatus
{
    UpDeviceDataCache *dataCache = [self addDataCache:deviceId];
    [dataCache setConnectionStatus:connectionStatus];
}

- (NSArray<id<UpDeviceCaution>> *)getAlarmList:(NSString *)deviceId
{
    return [self getAlarmList:deviceId fallback:@[]];
}
- (NSArray<id<UpDeviceCaution>> *)getAlarmList:(NSString *)deviceId fallback:(NSArray<id<UpDeviceCaution>> *)fallback
{
    UpDeviceDataCache *dataCache = [self getDataCache:deviceId];
    NSArray<id<UpDeviceCaution>> *alarmList;
    if (dataCache == nil) {
        alarmList = fallback;
    }
    else {
        alarmList = [dataCache cloneAlarmList];
    }
    return alarmList;
}

- (NSArray<id<UpDeviceAttribute>> *)getAttributeList:(NSString *)deviceId fallback:(NSArray<id<UpDeviceAttribute>> *)fallback
{
    UpDeviceDataCache *dataCache = [self getDataCache:deviceId];
    NSArray<id<UpDeviceAttribute>> *attributeList;
    if (dataCache == nil) {
        attributeList = fallback;
    }
    else {
        attributeList = [dataCache cloneAttributeList];
    }
    return attributeList;
}

- (NSArray<id<UpDeviceAttribute>> *)getAttributeList:(NSString *)deviceId
{
    return [self getAttributeList:deviceId fallback:@[]];
}


- (NSArray<id<UpDeviceAttribute>> *)putAttributeList:(NSString *)deviceId originalAttributeList:(NSArray<id<UpDeviceAttribute>> *)originalAttributeList isClear:(BOOL)isClear
{
    UpDeviceDataCache *dataCache = [self addDataCache:deviceId];
    return [dataCache storeAttributeList:originalAttributeList isClear:isClear];
}

- (id<UpDeviceAttribute>)getAttributeByName:(NSString *)deviceId Name:(NSString *)name fallback:(id<UpDeviceAttribute>)fallback
{
    UpDeviceDataCache *dataCache = [self getDataCache:deviceId];
    id<UpDeviceAttribute> attribute = dataCache != nil ? [dataCache cloneAttributeByName:name] : nil;
    if (attribute == nil) {
        attribute = fallback;
    }
    return attribute;
}


- (id<UpDeviceAttribute>)getAttributeByName:(NSString *)deviceId Name:(NSString *)name
{
    return [self getAttributeByName:deviceId Name:name fallback:nil];
}

- (void)updateAttribute:(NSString *)deviceId attribute:(id<UpDeviceAttribute>)attribute
{
    UpDeviceDataCache *dataCache = [self addDataCache:deviceId];
    [dataCache updateAttribute:attribute];
}

- (UpDeviceDataCache *)getDataCache:(NSString *)deviceId
{
    @synchronized(self)
    {
        return [_dataCacheMap objectForKey:deviceId];
    }
}

- (UpDeviceDataCache *)addDataCache:(NSString *)deviceId
{
    @synchronized(self)
    {
        UpDeviceDataCache *dataCache = [_dataCacheMap objectForKey:deviceId];
        if (dataCache == nil) {
            dataCache = [UpDeviceDataCache new];
            [_dataCacheMap setObject:dataCache forKey:deviceId];
        }
        return dataCache;
    }
}


@end
