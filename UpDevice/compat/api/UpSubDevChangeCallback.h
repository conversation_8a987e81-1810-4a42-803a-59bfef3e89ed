//
//  UpSubDevChangeCallback.h
//  UPDevice
//
//  Created by gump on 8/1/2019.
//  Copyright © 2019 海尔优家智能科技（北京）有限公司. All rights reserved.
//


#import <Foundation/Foundation.h>
#import "UpDevice.h"

@protocol UpSubDevChangeCallback <NSObject>

- (void)onSubDevListChange:(id<UpDevice>)parent subDevList:(NSArray<id<UpDevice>> *)subDevList;

- (void)onSubDevAlarm:(id<UpDevice>)parent subDev:(id<UpDevice>)subDev;

- (void)onSubDevChange:(id<UpDevice>)parent subDev:(id<UpDevice>)subDev;

@end
