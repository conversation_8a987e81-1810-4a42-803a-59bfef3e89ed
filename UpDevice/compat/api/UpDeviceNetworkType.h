//
//  UpDeviceNetworkType.h
//  UPDevice
//
//  Created by gump on 8/1/2019.
//  Copyright © 2019 海尔优家智能科技（北京）有限公司. All rights reserved.
//

#import <Foundation/Foundation.h>
#import "UpDevice.h"
#import <uSDK/uSDK.h>

NS_ASSUME_NONNULL_BEGIN

typedef NS_ENUM(NSUInteger, UpDeviceWifiType) {
    UpDeviceWifiTypeLocal,
    UpDeviceWifiTypeRemote,
    UpDeviceWifiTypeOneSelf,
};


@interface UpDeviceNetworkType : NSObject

+ (UpDeviceWifiType)from:(uSDKDeviceNetTypeConst)type;

@end

NS_ASSUME_NONNULL_END
