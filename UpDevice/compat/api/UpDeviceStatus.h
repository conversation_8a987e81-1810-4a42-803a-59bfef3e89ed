//
//  UpDeviceStatus.h
//  UPDevice
//
//  Created by gump on 8/1/2019.
//  Copyright © 2019 海尔优家智能科技（北京）有限公司. All rights reserved.
//

#ifndef UpDeviceStatus_h
#define UpDeviceStatus_h

typedef enum UpDeviceStatus {
    /**
     * SDK的不可用、离线、在线状态。
     */
    OFFLINE,
    /**
     * SDK的就绪状态。设备有开关机功能，并处于关机状态
     */
    STANDBY,
    /**
     * SDK的就绪状态。设备有开关机功能，并处于开机状态；或设备无开关机功能，且已上电
     */
    RUNNING,
    /**
     * SDK的就绪状态。设备处于报警状态
     */
    ALARM
} UpDeviceStatus;

#endif /* UpDeviceStatus_h */
