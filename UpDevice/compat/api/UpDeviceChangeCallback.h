//
//  UpDeviceChangeCallback.h
//  UPDevice
//
//  Created by gump on 8/1/2019.
//  Copyright © 2019 海尔优家智能科技（北京）有限公司. All rights reserved.
//

#import <Foundation/Foundation.h>
#import "UpDevice.h"

@protocol UpDeviceChangeCallback <NSObject>

/**
 * 设备报警消息反馈。<br/>
 * <p>
 * 调用者实现该方法，从报警消息列表获取报警数据并做具体处理，可以通过device参数方便访问到发生报警的设备。<br/>
 * <p>
 * <strong>调用者不需要手动发送解除报警命令({@link UpCompatApi#disarmCurrentAlarm()})</strong>
 *
 * @param device 设备对象
 */
- (void)onDeviceAlarm:(id<UpDevice>)device;

/**
 * 设备属性状态变化消息反馈。<br/>
 * <p>
 * 调用者通过device参数访问当前设备，查询感兴趣的相关参数，进行UI刷新等操作。
 *
 * @param device 设备对象
 */
- (void)onDeviceChange:(id<UpDevice>)device;

@end
