//
//  UpDeviceType.m
//  UPDevice
//
//  Created by gump on 8/1/2019.
//  Copyright © 2019 海尔优家智能科技（北京）有限公司. All rights reserved.
//

#import "UpDeviceType.h"
#import "WifiDeviceType.h"

@interface UpDeviceType () {
    uSDKDeviceTypeConst _value;
}

@end

@implementation UpDeviceType

+ (NSString *)convertuSDKDeviceTypeIntoStr:(uSDKDeviceTypeConst)type
{
    NSDictionary *typeNameInfo = @{ @(ALL_TYPE) : @"ALL_TYPE", //所有类型
                                    @(FRIDGE) : @"FRIDGE", //冰箱
                                    @(SPLIT_AIRCONDITION) : @"SPLIT_AIRCONDITION", //分体空调
                                    @(PACKAGE_AIRCONDITION) : @"PACKAGE_AIRCONDITION", //柜机空调
                                    @(PULSATOR_WASHING_MACHINE) : @"PULSATOR_WASHING_MACHINE", //波轮洗衣机
                                    @(DRUM_WASHING_MACHINE) : @"DRUM_WASHING_MACHINE", //滚筒洗衣机
                                    @(WATER_HEATER) : @"WATER_HEATER", //热水器
                                    @(MICRO_WAVE_OVEN) : @"MICRO_WAVE_OVEN", //微波炉
                                    @(WINE_CABINET) : @"WINE_CABINET", //酒柜
                                    @(RANGE_HOOD) : @"RANGE_HOOD", //油烟机
                                    @(DISH_WASHING_MACHINE) : @"DISH_WASHING_MACHINE", //洗碗机
                                    @(DISINFECTION_CABINET) : @"DISINFECTION_CABINET", //消毒柜
                                    @(RESERVE) : @"RESERVE", //保留
                                    @(COMMERCIAL_AIRCONDITION) : @"COMMERCIAL_AIRCONDITION", //商用空调
                                    @(TV) : @"TV", //电视
                                    @(HOME_ENTERTAINMENT_EQUIPMENT) : @"HOME_ENTERTAINMENT_EQUIPMENT", //娱乐设备：媒体中心，音响等
                                    @(LIGHTING) : @"LIGHTING", //灯光照明
                                    @(SECURITY_EQUIPMENT) : @"SECURITY_EQUIPMENT", //安防设备
                                    @(VIDEO_SURVEILLANCE) : @"VIDEO_SURVEILLANCE", //视频监控
                                    @(SENSOR) : @"SENSOR", //传感器：各类环境传感器
                                    @(SMART_HOME) : @"SMART_HOME", //智能家居：智能窗帘，三表等
                                    @(MEDICAL_CARE) : @"MEDICAL_CARE", //医疗保健：各种家庭医疗监护，远程医疗等
                                    @(REFRIDGERATOR) : @"REFRIDGERATOR", //冷柜
                                    @(MEDICAL_CABINET) : @"MEDICAL_CABINET", //医用柜
                                    @(GAS_WATER_HEATER) : @"GAS_WATER_HEATER", //燃气热水器
                                    @(HEATING_FURNACE) : @"HEATING_FURNACE", //采暖炉
                                    @(STEAM_BOX) : @"STEAM_BOX", //蒸箱
                                    @(COFFEE_MAKER) : @"COFFEE_MAKER", //咖啡机
                                    @(WATER_MACHINE) : @"WATER_MACHINE", //饮水机
                                    @(COOKING) : @"COOKING", //灶具
                                    @(OVEN) : @"OVEN", //烤箱
                                    @(SOLAR_WATER_HEATER) : @"SOLAR_WATER_HEATER", //太阳能热水器
                                    @(AIR_PURIFIER) : @"AIR_PURIFIER", //空气净化器
                                    @(WATER_PURIFIER) : @"WATER_PURIFIER", //净水机
                                    @(AIR_CUBE) : @"AIR_CUBE", //空气魔方
                                    @(ROUTING_MODULE) : @"ROUTING_MODULE", //路由模块
                                    @(SMART_ROUTER) : @"SMART_ROUTER", //智能路由器
                                    @(CONTROL_TERMINAL) : @"CONTROL_TERMINAL", //控制终端
                                    @(NEW_WIND_DEVICE) : @"NEW_WIND_DEVICE", //新风设备
                                    @(KITCHEN_APPLIANCE) : @"KITCHEN_APPLIANCE", //小厨电
                                    @(PUBLIC_SERVICE) : @"PUBLIC_SERVICE", //公共服务类
                                    @(ENV_MONITOR) : @"ENV_MONITOR", //环境检测设备
                                    @(OTHER_DEVICE) : @"OTHER_DEVICE", //其他
                                    @(DUST_PROOF) : @"DUST_PROOF", //除尘设备
                                    @(WEARABLE_DEVICE) : @"WEARABLE_DEVICE", //可穿戴设备
                                    @(HEAT_PUMP) : @"HEAT_PUMP", //热泵
                                    @(KETTLE) : @"KETTLE", //水壶
                                    @(FLOOR_HEATING_DEVICE) : @"FLOOR_HEATING_DEVICE", //地暖设备
                                    @(GATEWAY) : @"GATEWAY", //网关
                                    @(ROBOT) : @"ROBOT", //机器人
                                    @(DRYER) : @"DRYER", //干衣机
                                    @(AIR_HEATER) : @"AIR_HEATER", //浴霸及暖风机
                                    @(PERSONAL_CLEANER) : @"PERSONAL_CLEANER", //个人护理类
    };
    NSString *typeName = typeNameInfo[@(type)];
    if (![typeName isKindOfClass:[NSString class]] || typeName.length == 0) {
        typeName = UPDevice_UnKnownTypeName;
    }
    return typeName;
}

- (instancetype)initWithValue:(uSDKDeviceTypeConst)value
{
    if (self = [super init]) {
        _value = value;
    }

    return self;
}

- (NSString *)getTypeName
{
    return [[self class] convertuSDKDeviceTypeIntoStr:_value];
}

+ (UpDeviceType *)from:(uSDKDeviceTypeConst)type
{
    UpDeviceType *deviceType = [[UpDeviceType alloc] initWithValue:type];
    return deviceType;
}

@end
