//
//  UpDeviceNetworkType.m
//  UPDevice
//
//  Created by gump on 8/1/2019.
//  Copyright © 2019 海尔优家智能科技（北京）有限公司. All rights reserved.
//

#import "UpDeviceNetworkType.h"

@implementation UpDeviceNetworkType

+ (UpDeviceWifiType)from:(uSDKDeviceNetTypeConst)type
{
    switch (type) {
        case NET_TYPE_REMOTE:
            return UpDeviceWifiTypeRemote;
        default:
            return UpDeviceWifiTypeLocal;
    }
}

@end
