//
//  UpCompatEngineDevice.m
//  UPDevice
//
//  Created by gump on 14/4/2021.
//  Copyright © 2021 海尔优家智能科技（北京）有限公司. All rights reserved.
//

#import "UpCompatEngineDevice.h"
#import <UPResource/UPResourceInjection.h>
#import "UpEngineDevice+ConfigSource.h"

@implementation UpCompatEngineDevice

- (instancetype)initWithDeviceInfo:(NSString *)uniqueId deviceInfo:(id<UpDeviceInfo>)deviceInfo broker:(id<UpDeviceBroker>)broker factory:(id<UpDeviceFactory>)factory
{
    self = [super initEngineDeviceWithUniqueID:uniqueId deviceInfo:deviceInfo broker:broker factory:factory];
    if (self) {
        [self setResourceManager:[UPResourceInjection getInstance].resourceManager];
    }

    return self;
}

#pragma mark - override
- (UpDeviceConfigState)configState
{
    if (self.engineConfigFileState == UpConfigStateNotSupport) {
        return UpDeviceConfigState_NativeDevice;
    }
    return (UpDeviceConfigState)self.engineConfigFileState;
}

@end
