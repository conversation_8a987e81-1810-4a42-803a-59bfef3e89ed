//
//  UpDeviceDataCache.m
//  UPDevice
//
//  Created by gump on 8/1/2019.
//  Copyright © 2019 海尔优家智能科技（北京）有限公司. All rights reserved.
//

#import "UpDeviceDataCache.h"
#import "DeviceCaution.h"
#import "DeviceAttribute.h"
#import "UPDeviceLog.h"

@interface UpDeviceDataCache () {
    UpDeviceConnection _connectionStatus;
    NSMutableArray<id<UpDeviceCaution>> *_alarmList;
    NSMutableDictionary<NSString *, id<UpDeviceAttribute>> *_attributeMap;
}

@end

@implementation UpDeviceDataCache

- (instancetype)init
{
    if (self = [super init]) {
        _connectionStatus = UpDeviceConnection_OFFLINE;
        _alarmList = [NSMutableArray array];
        _attributeMap = [NSMutableDictionary dictionary];
    }

    return self;
}

- (void)storeConnection:(UpDeviceConnection)connection
{
    [self setConnectionStatus:connection];
}

- (UpDeviceConnection)getConnectionStatus
{
    @synchronized(self)
    {
        return _connectionStatus;
    }
}

- (void)setConnectionStatus:(UpDeviceConnection)connection
{
    _connectionStatus = connection;
}

- (nullable NSArray<id<UpDeviceCaution>> *)storeAlarmList:(NSArray<id<UpDeviceCaution>> *)cautionList isClear:(BOOL)isClear
{
    @synchronized(self)
    {
        if (cautionList == nil || cautionList.count == 0) {
            UPDeviceLogWarning(@"%s[%d]cautionList参数为空！", __PRETTY_FUNCTION__, __LINE__);
            return nil;
        }

        NSMutableArray<id<UpDeviceCaution>> *list = [NSMutableArray array];
        for (id<UpDeviceCaution> alarm in cautionList) {
            if (alarm != nil) {
                [list addObject:alarm];
            }
        }

        if (isClear) {
            [_alarmList removeAllObjects];
        }
        [_alarmList addObjectsFromArray:list];
        return list;
    }
}

- (nullable NSArray<id<UpDeviceAttribute>> *)storeAttributeList:(NSArray<id<UpDeviceAttribute>> *)attributeList isClear:(BOOL)isClear
{
    @synchronized(self)
    {
        if (attributeList == nil || attributeList.count == 0) {
            UPDeviceLogWarning(@"%s[%d]attributeList参数为空！", __PRETTY_FUNCTION__, __LINE__);
            return nil;
        }
        NSMutableDictionary<NSString *, id<UpDeviceAttribute>> *map = [NSMutableDictionary dictionary];
        for (id<UpDeviceAttribute> attribute in attributeList) {
            if (attribute != nil) {
                [map setObject:attribute forKey:attribute.name];
            }
        }

        if (isClear) {
            [_attributeMap removeAllObjects];
        }
        [_attributeMap setValuesForKeysWithDictionary:map];
        return map.allValues;
    }
}

- (void)updateAttribute:(id<UpDeviceAttribute>)attribute
{
    @synchronized(self)
    {
        if (attribute == nil) {
            UPDeviceLogWarning(@"%s[%d]attribute参数为空！", __PRETTY_FUNCTION__, __LINE__);
            return;
        }
        [_attributeMap setObject:attribute forKey:attribute.name];
    }
}


- (NSArray<id<UpDeviceCaution>> *)cloneAlarmList
{
    @synchronized(self)
    {
        NSMutableArray<id<UpDeviceCaution>> *copyList = [NSMutableArray array];
        for (id<UpDeviceCaution> alarm in _alarmList) {
            DeviceCaution *deviceCaution = [[DeviceCaution alloc] initWithName:alarm.name Value:alarm.value Time:alarm.time];
            [copyList addObject:deviceCaution];
        }

        return copyList;
    }
}

- (NSArray<id<UpDeviceAttribute>> *)cloneAttributeList
{
    @synchronized(self)
    {
        NSMutableArray<id<UpDeviceAttribute>> *copyList = [NSMutableArray array];
        for (id<UpDeviceAttribute> attr in _attributeMap) {
            DeviceAttribute *deviceAttr = [[DeviceAttribute alloc] init];
            deviceAttr.value = attr.value;
            deviceAttr.name = attr.name;
            [copyList addObject:deviceAttr];
        }

        return copyList;
    }
}

- (nullable id<UpDeviceAttribute>)cloneAttributeByName:(NSString *)name
{
    @synchronized(self)
    {
        id<UpDeviceAttribute> attribute = [_attributeMap objectForKey:name];
        if (attribute == nil) {
            UPDeviceLogWarning(@"%s[%d]attribute参数为空！", __PRETTY_FUNCTION__, __LINE__);
            return nil;
        }

        DeviceAttribute *deviceAttr = [[DeviceAttribute alloc] init];
        deviceAttr.value = attribute.value;
        deviceAttr.name = attribute.name;

        return deviceAttr;
    }
}


@end
