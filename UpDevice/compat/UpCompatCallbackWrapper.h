//
//  UpCompatCallbackWrapper.h
//  UPDevice
//
//  Created by gump on 8/1/2019.
//  Copyright © 2019 海尔优家智能科技（北京）有限公司. All rights reserved.
//

#import <Foundation/Foundation.h>
#import "UpDeviceListener.h"
#import "UpDeviceChangeCallback.h"
#import "UpSubDevChangeCallback.h"

NS_ASSUME_NONNULL_BEGIN

@interface UpCompatCallbackWrapper : NSObject <UpDeviceListener>

- (void)addDeviceCallback:(id<UpDeviceChangeCallback>)callback;

- (void)removeDeviceCallback:(id<UpDeviceChangeCallback>)callback;

- (void)clearDeviceCallback;

- (void)addSubDevCallback:(id<UpSubDevChangeCallback>)callback;

- (void)removeSubDevCallback:(id<UpSubDevChangeCallback>)callback;

- (void)clearSubDevCallback;

@end

NS_ASSUME_NONNULL_END
