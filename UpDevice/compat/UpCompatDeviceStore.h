//
//  UpCompatDeviceStore.h
//  UPDevice
//
//  Created by gump on 8/1/2019.
//  Copyright © 2019 海尔优家智能科技（北京）有限公司. All rights reserved.
//

#import <Foundation/Foundation.h>
#import "UpDeviceBaseInfo.h"
#import "UpDeviceConnection.h"
#import "UpDeviceCaution.h"
#import "UpDeviceAttribute.h"

NS_ASSUME_NONNULL_BEGIN

@interface UpCompatDeviceStore : NSObject

- (BOOL)hasSubDevList;

- (id<UpDeviceBaseInfo>)getSubDevInfo:(NSString *)deviceId;

- (NSArray<id<UpDeviceBaseInfo>> *)getSubDevInfoList;

- (void)putSubDevInfoList:(NSArray<id<UpDeviceBaseInfo>> *)subDevInfoList;

- (UpDeviceConnection)getConnectionStatus:(NSString *)deviceId;

- (void)putConnection:(NSString *)deviceId Connection:(UpDeviceConnection)connection;

- (void)setConnectionStatus:(NSString *)deviceId ConnectionStatus:(UpDeviceConnection)connectionStatus;

- (NSArray<id<UpDeviceCaution>> *)getAlarmList:(NSString *)deviceId;

- (NSArray<id<UpDeviceAttribute>> *)getAttributeList:(NSString *)deviceId;

- (NSArray<id<UpDeviceAttribute>> *)putAttributeList:(NSString *)deviceId originalAttributeList:(NSArray<id<UpDeviceAttribute>> *)originalAttributeList isClear:(BOOL)isClear;

- (id<UpDeviceAttribute>)getAttributeByName:(NSString *)deviceId Name:(NSString *)name;

- (void)updateAttribute:(NSString *)deviceId attribute:(id<UpDeviceAttribute>)attribute;
@end

NS_ASSUME_NONNULL_END
