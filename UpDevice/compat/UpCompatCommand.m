//
//  UpCompatCommand.m
//  UPDevice
//
//  Created by gump on 8/1/2019.
//  Copyright © 2019 海尔优家智能科技（北京）有限公司. All rights reserved.
//

#import "UpCompatCommand.h"
#import "UPDeviceLog.h"

@interface UpCompatCommand () {
    NSMutableDictionary<NSString *, NSString *> *_expectAttrs;
    int _timeout;
}

@end

@implementation UpCompatCommand

- (instancetype)init
{
    if (self = [super init]) {
        _expectAttrs = [NSMutableDictionary dictionary];
    }
    return self;
}

- (instancetype)initWithGroupName:(NSString *)groupName Attributes:(NSDictionary<NSString *, NSString *> *)attributes ExpectAttributeMap:(NSDictionary<NSString *, NSString *> *)expectAttributeMap Timeout:(int)timeout;
{
    if (self = [super initWithGroupName:groupName Attributes:[NSMutableDictionary dictionaryWithDictionary:attributes]]) {
        _expectAttrs = [NSMutableDictionary dictionary];
        _timeout = timeout;
        [self putExpectAttributes:expectAttributeMap];
    }

    return self;
}

- (int)getTimeout
{
    return _timeout;
}

- (NSDictionary<NSString *, NSString *> *)getExpectAttributes
{
    return _expectAttrs;
}

- (void)putExpectAttribute:(NSString *)name Value:(NSString *)value
{
    if (name == nil || value == nil) {
        UPDeviceLogWarning(@"%s[%d]参数为空！name:%@,value:%@", __PRETTY_FUNCTION__, __LINE__, name, value);
        return;
    }

    [_expectAttrs setObject:value forKey:name];
}

- (void)putExpectAttributes:(NSDictionary<NSString *, NSString *> *)attrCmdMap
{
    if (attrCmdMap == nil) {
        UPDeviceLogWarning(@"%s[%d]attrCmdMap参数为空！", __PRETTY_FUNCTION__, __LINE__);
        return;
    }

    for (NSString *key in attrCmdMap.allKeys) {
        NSString *value = [attrCmdMap objectForKey:key];
        [self putExpectAttribute:key Value:value];
    }
}

- (NSArray<NSString *> *)getExpectAttrNameSet
{
    return _expectAttrs.allKeys;
}

@end
