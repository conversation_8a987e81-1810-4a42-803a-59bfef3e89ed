//
//  UpConnectivityPresenceHandler.h
//  UPDevice
//
//  Created by lichen on 2025/4/17.
//  Copyright © 2025 海尔优家智能科技（北京）有限公司. All rights reserved.
//

#import <Foundation/Foundation.h>
#import "UpPresenceInfo.h"

NS_ASSUME_NONNULL_BEGIN

@interface UpConnectivityPresenceHandler : NSObject

/// 获取连接离线信息
///
/// @Param deviceId 设备id
/// @Return 连接离线信息，可为空
+ (nullable UpPresenceInfo *)getConnectivityPresenceInfoBy:(NSString *)deviceId;

@end

NS_ASSUME_NONNULL_END
