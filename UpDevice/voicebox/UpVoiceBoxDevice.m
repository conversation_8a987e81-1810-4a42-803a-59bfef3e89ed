//
//  UpVoiceBoxDevice.m
//  UPDevice
//
//  Created by gump on 19/4/2021.
//  Copyright © 2021 海尔优家智能科技（北京）有限公司. All rights reserved.
//

#import "UpVoiceBoxDevice.h"
#import "UPDeviceLog.h"
#import "DeviceCommand.h"
#import <upnetwork/UPNetwork.h>
#import "UpDeviceInjection.h"

static int const VoiceBox_RepeatTimes = 6; //命令重试次数
static NSString *const VoiceBox_First_TYPEID = @"203c920238e04d240f02199e9d795c0000009098f4aa29ecdd392f1f621eb2c0"; //一代音箱

@interface UpVoiceBoxDevice ()
@property (nonatomic, assign) int repeatTimes; //命令重试次数
@end

@implementation UpVoiceBoxDevice

- (instancetype)initWithDeviceInfo:(NSString *)uniqueId deviceInfo:(id<UpDeviceInfo>)deviceInfo broker:(id<UpDeviceBroker>)broker factory:(id<UpDeviceFactory>)factory
{
    self = [super initWithDeviceInfo:uniqueId deviceInfo:deviceInfo broker:broker factory:factory];
    if (self) {
        self.repeatTimes = VoiceBox_RepeatTimes;
    }
    return self;
}

#pragma mark - override
- (void)processAttributes:(NSArray<id<UpDeviceAttribute>> *)attributeList
{
    [super processAttributes:attributeList];
    [self sendCommand];
}
#pragma mark - private
/**
 *  向音箱底板发送命令
 */
- (void)sendCommand
{
    if (self.repeatTimes <= 0) {
        return;
    }
    self.repeatTimes--;
    if ([self.getInfo.typeId isEqualToString:VoiceBox_First_TYPEID]) {
        [self sendV1TokenAndClientID];
    }
    else {
        [self sendV2TokenAndClientID];
    }
}

- (void)execToken
{
    NSString *accessToken = [UPNetworkSettings sharedSettings].accessToken;
    NSString *uhomeAccessToken = [UPNetworkSettings sharedSettings].uhomeAccessToken;
    if (!accessToken || !uhomeAccessToken || accessToken.length == 0 || uhomeAccessToken.length == 0) {
        UPDeviceLogDebug(@"%s[%d]voiceBox:Token is empty. accessToken=%@ uhomeAccessToken=%@", __PRETTY_FUNCTION__, __LINE__, accessToken, uhomeAccessToken);
        return;
    }
    NSMutableArray *cmdList = [NSMutableArray array];
    NSString *tokenValue = [NSString stringWithFormat:@"%@&&%@", accessToken, uhomeAccessToken];
    [cmdList addObject:@{ @"token" : tokenValue }];
    NSMutableDictionary *cmDic = [NSMutableDictionary dictionary];
    for (NSDictionary *dic in cmdList) {
        if (dic) {
            [cmDic addEntriesFromDictionary:dic];
        }
    }
    UPDeviceLogDebug(@"%s[%d]voiceBox: execToken -- start token =%@", __PRETTY_FUNCTION__, __LINE__, tokenValue);
    NSString *groupName = nil;
    id<UpDeviceCommand> command = [[DeviceCommand alloc] initWithGroupName:groupName Attributes:cmDic];
    [self executeCommand:command
                 timeout:5.0
             finishBlock:^(UpDeviceResult *result) {
               UpDeviceResultErrorCode errorCode = result.errorCode;
               if (errorCode == ErrorCode_SUCCESS) {
                   UPDeviceLogDebug(@"%s[%d] voiceBox: execToken -- success", __PRETTY_FUNCTION__, __LINE__);
               }
               else {
                   [self sendCommand];
                   UPDeviceLogDebug(@"%s[%d] voiceBox: execToken -- failure:%ld", __PRETTY_FUNCTION__, __LINE__, result.errorCode);
               }
             }];
}

- (void)execClientID
{
    UPDeviceLogDebug(@"%s[%d] voiceBox: execClientID -- start", __PRETTY_FUNCTION__, __LINE__);
    NSMutableArray *cmdList = [NSMutableArray array];
    NSString *clientId = [UPCommonServerHeader clientId];
    [cmdList addObject:@{ @"ClientID" : clientId }];
    NSMutableDictionary *cmDic = [NSMutableDictionary dictionary];
    for (NSDictionary *dic in cmdList) {
        if (dic) {
            [cmDic addEntriesFromDictionary:dic];
        }
    }
    NSString *groupName = nil;
    id<UpDeviceCommand> command = [[DeviceCommand alloc] initWithGroupName:groupName Attributes:cmDic];
    if ([command isKindOfClass:[DeviceCommand class]]) {
        [self executeCommand:command
                     timeout:5.0
                 finishBlock:^(UpDeviceResult *result) {
                   UpDeviceResultErrorCode errorCode = result.errorCode;
                   if (errorCode == ErrorCode_SUCCESS) {
                       UPDeviceLogDebug(@"%s[%d] voiceBox: execClientID -- success", __PRETTY_FUNCTION__, __LINE__);
                   }
                   else {
                       [self sendCommand];
                       UPDeviceLogDebug(@"%s[%d] voiceBox: execClientID -- failure:%ld", __PRETTY_FUNCTION__, __LINE__, result.errorCode);
                   }
                 }];
    }
}
/**
 *  向2代音箱底板发送命令
 */
- (void)sendV2TokenAndClientID
{
    NSString *ownerid = (NSString *)[self.getInfo getExtra:@"DI-Relation.ownerId"];
    id<UpDeviceBroker> broker = [[UpDeviceInjection getInstance].deviceManager getBroker];
    NSString *userid = [broker.gatewayParams objectForKey:@"usdk-access-userId"];
    if (![userid isEqualToString:ownerid]) {
        UPDeviceLogDebug(@"%s[%d] voiceBox: userid:%@ ownerid:%@", __PRETTY_FUNCTION__, __LINE__, userid, ownerid);
        return;
    }
    id<UpDeviceAttribute> authVersionAttr = [self getAttributeByName:@"authVersion"];
    BOOL auBool = [authVersionAttr.value isEqualToString:@"1"];
    id<UpDeviceAttribute> voTokenAttr = [self getAttributeByName:@"token"];
    if (auBool && voTokenAttr.value.length == 0 && self.getConnection == UpDeviceConnection_READY) {
        [self execToken];
    }
    id<UpDeviceAttribute> voClientidAttr = [self getAttributeByName:@"ClientID"];
    if (auBool && voClientidAttr.value.length == 0 && self.getConnection == UpDeviceConnection_READY) {
        [self execClientID];
    }
}

/**
 *  向1代音箱底板发送命令
 */
- (void)sendV1TokenAndClientID
{
    id<UpDeviceAttribute> voTokenAttr = [self getAttributeByName:@"token"];
    if (voTokenAttr.value.length == 0 && self.getConnection == UpDeviceConnection_READY) {
        [self execToken];
    }
    id<UpDeviceAttribute> voClientidAttr = [self getAttributeByName:@"ClientID"];
    if (voClientidAttr.value.length == 0 && self.getConnection == UpDeviceConnection_READY) {
        [self execClientID];
    }
}

@end
