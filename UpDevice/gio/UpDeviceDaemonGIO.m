//
//  UpDeviceDaemonGIO.m
//  UPDevice
//
//  Created by MAC on 2023/6/14.
//  Copyright © 2023 海尔优家智能科技（北京）有限公司. All rights reserved.
//

#import "UpDeviceDaemonGIO.h"
#import "UDSafeMutableArray.h"
#import "UDSafeMutableDictionary.h"
#import "UPDeviceLog.h"
#import <UpTrace/UPEventTrace.h>

const NSString *DaemonGIO_FamilyID_Key = @"DI-Relation.familyId";
const NSString *DaemonGIO_ProductType_Key = @"DI-Basic.apptypeCode";
NSString *const DaemonGIO_NULL = @"NULL";

@interface UpDeviceDaemonGIO ()
@property (nonatomic, assign) BOOL alreadyGIO; //已经打点统计
@property (nonatomic, strong) NSDate *beginPrepareTime; //第一个设备开始准备时间
@property (nonatomic, strong) UDSafeMutableArray<NSString *> *devices; //总设备数组
@property (nonatomic, strong) UDSafeMutableArray<NSString *> *familyDevices; //当前家庭设备数组
@property (nonatomic, copy) NSString *curFamilyId; //当前家庭id
@property (nonatomic, strong) UDSafeMutableDictionary<NSString *, NSDate *> *alreadyPrepareDeviceMap; //已准备好的设备字典
@property (nonatomic, strong) UDSafeMutableDictionary<NSString *, NSDate *> *alreadyPrepareFamilyDeviceMap; //当前家庭已准备设备字典
@property (nonatomic, assign) int totalVMDeviceCount; //虚拟设备总数
@property (nonatomic, assign) int curFamilyVMDeviceCount; //当前家庭虚拟设备数
@property (nonatomic, assign) int totalActiveDeviceCount; //本地已有配置文件的设备总数
@property (nonatomic, assign) int curFamilyActiveDeviceCount; //当前家庭已有配置文件的设备数
@property (nonatomic, copy) NSString *deviceId; //设备id
@property (nonatomic, copy) NSString *productType; //设备品类
@property (nonatomic, assign) int prepareCount; //设备准备次数
@property (nonatomic, strong) NSDate *attachTime; //attach完成时间
@property (nonatomic, strong) NSDate *findConfigFileTime; //查找逻辑约束文件完成时间
@property (nonatomic, strong) NSDate *parseConfigFileTime; //解析逻辑约束文件完成时间
@property (nonatomic, strong) UDSafeMutableArray<NSString *> *activeDevices; //本地已有配置文件设备数组
@end

@implementation UpDeviceDaemonGIO
- (instancetype)init
{
    self = [super init];
    if (self) {
        self.alreadyGIO = NO;
        self.devices = [UDSafeMutableArray new];
        self.familyDevices = [UDSafeMutableArray new];
        self.curFamilyId = @"";
        self.beginPrepareTime = nil;
        self.alreadyPrepareDeviceMap = [UDSafeMutableDictionary new];
        self.alreadyPrepareFamilyDeviceMap = [UDSafeMutableDictionary new];
        self.totalVMDeviceCount = 0;
        self.curFamilyVMDeviceCount = 0;
        self.totalActiveDeviceCount = 0;
        self.curFamilyActiveDeviceCount = 0;
        self.deviceId = DaemonGIO_NULL;
        self.prepareCount = 0;
        self.activeDevices = [UDSafeMutableArray new];
    }
    return self;
}

#pragma mark - public

- (void)beginPrepare:(NSArray<id<UpDeviceInfo>> *)devices
{
    if (self.devices.count > 0) {
        return;
    }
    if (devices.count == 0) {
        return;
    }
    if (self.curFamilyId.length <= 0) {
        id<UpDeviceInfo> info = devices.firstObject;
        if (info) {
            NSString *fid = info.getExtras[DaemonGIO_FamilyID_Key] ?: @"";
            self.curFamilyId = fid;
        }
    }

    if (devices.count == 1) {
        id<UpDeviceInfo> info = devices.firstObject;
        self.deviceId = info.deviceId ?: DaemonGIO_NULL;
        self.productType = info.getExtras[DaemonGIO_ProductType_Key];
    }

    for (id<UpDeviceInfo> info in devices) {
        NSString *deviceId = info.deviceId ?: @"";
        [self.devices addObject:deviceId];
        [self isRealDevice:deviceId] ?: self.totalVMDeviceCount++;
        NSString *familyId = info.getExtras[DaemonGIO_FamilyID_Key];
        if ([familyId isEqualToString:self.curFamilyId]) {
            [self.familyDevices addObject:deviceId];
            [self isRealDevice:deviceId] ?: self.curFamilyVMDeviceCount++;
        }
    }
}

- (void)devicePrepareBegin
{
    if (self.alreadyGIO) {
        return;
    }
    if (self.beginPrepareTime) {
        return;
    }
    self.beginPrepareTime = [NSDate date];
}

- (void)devicePrepareEnd:(NSString *)deviceId
{
    if (self.alreadyGIO) {
        return;
    }
    if (deviceId.length == 0) {
        return;
    }
    if (self.alreadyPrepareFamilyDeviceMap.allValues.count < self.familyDevices.count) {
        if ([self.familyDevices containsObject:deviceId]) {
            [self processFamilyDevicePrepareEnd:deviceId];
        }
    }
    if (self.alreadyPrepareDeviceMap.allValues.count < self.devices.count) {
        if ([self.devices containsObject:deviceId]) {
            [self processDevicePrepareEnd:deviceId];
        }
    }
}

- (void)countActiveDevice:(NSString *)deviceId
{
    if ([self.activeDevices containsObject:deviceId]) {
        return;
    }
    [self.activeDevices addObject:deviceId];
    if ([self.familyDevices containsObject:deviceId]) {
        self.curFamilyActiveDeviceCount++;
        self.totalActiveDeviceCount++;
        return;
    }
    if ([self.devices containsObject:deviceId]) {
        self.totalActiveDeviceCount++;
    }
}

- (void)countPrepareTimes
{
    if (self.devices.count != 1) {
        return;
    }
    if (self.alreadyGIO) {
        return;
    }
    self.prepareCount++;
}

- (void)countAttachTime
{
    if (self.devices.count != 1) {
        return;
    }
    if (self.alreadyGIO) {
        return;
    }
    self.attachTime = [NSDate date];
}

- (void)countFindConfigFileTime
{
    if (self.devices.count != 1) {
        return;
    }
    if (self.alreadyGIO) {
        return;
    }
    if (self.findConfigFileTime != nil) {
        return;
    }
    self.findConfigFileTime = [NSDate date];
}

- (void)countParseConfigFileTime
{
    if (self.devices.count != 1) {
        return;
    }
    if (self.alreadyGIO) {
        return;
    }
    self.parseConfigFileTime = [NSDate date];
}

#pragma mark - private
- (void)judgePrepareEnd
{
    if (self.alreadyPrepareDeviceMap.allValues.count < self.devices.count) {
        return;
    }
    self.alreadyGIO = YES;
    [self trace];
}

- (void)processFamilyDevicePrepareEnd:(NSString *)deviceId
{
    if ([self.alreadyPrepareFamilyDeviceMap.allKeys containsObject:deviceId]) {
        return;
    }
    NSDate *date = [NSDate date];
    [self.alreadyPrepareFamilyDeviceMap setObject:date forKey:deviceId];
}

- (void)processDevicePrepareEnd:(NSString *)deviceId
{
    if ([self.alreadyPrepareDeviceMap.allKeys containsObject:deviceId]) {
        return;
    }
    NSDate *date = [NSDate date];
    [self.alreadyPrepareDeviceMap setObject:date forKey:deviceId];
    [self judgePrepareEnd];
}

- (NSDate *)calculateLastTime:(NSArray<NSDate *> *)times
{
    if (times.count == 0) {
        return nil;
    }
    NSDate *maxDate = self.beginPrepareTime;
    if (!maxDate) {
        return nil;
    }
    for (NSDate *time in times) {
        if ([time compare:maxDate] == NSOrderedDescending) {
            maxDate = time;
        }
    }
    return maxDate;
}

- (void)trace
{
    NSDate *lastCurFamilyDevicePrepareDate = [self calculateLastTime:self.alreadyPrepareFamilyDeviceMap.allValues];
    NSDate *lastDevicePrepareDate = [self calculateLastTime:self.alreadyPrepareDeviceMap.allValues];

    NSInteger beginPerpareInterval = [self toMillisecondTimeIntervalByDate:self.beginPrepareTime];
    NSInteger lastCurFamilyDevicePerpareInterval = [self toMillisecondTimeIntervalByDate:lastCurFamilyDevicePrepareDate];
    NSInteger lastDevicePrepareInterval = [self toMillisecondTimeIntervalByDate:lastDevicePrepareDate];

    NSNumber *totalDeviceCount = [[NSNumber alloc] initWithInteger:self.devices.count];
    NSNumber *curFamilyDeviceCount = [[NSNumber alloc] initWithInteger:self.familyDevices.count];
    NSNumber *virtualDeviceCount = [[NSNumber alloc] initWithInt:self.totalVMDeviceCount];
    NSNumber *familyPrepareCost = [[NSNumber alloc] initWithInteger:(lastCurFamilyDevicePerpareInterval - beginPerpareInterval)];
    NSNumber *allPrepareCost = [[NSNumber alloc] initWithInteger:(lastDevicePrepareInterval - beginPerpareInterval)];

    NSDictionary *oneDeviceParam = @{
        @"count_total_device" : totalDeviceCount,
        @"have_virtual_device" : virtualDeviceCount,
        @"count_family_device" : curFamilyDeviceCount,
        @"cost_family_prepare" : familyPrepareCost,
        @"cost_prepare" : allPrepareCost
    };

    [[UPEventTrace getInstance] trace:@"MB36632" withVariable:oneDeviceParam];

    NSString *log = [NSString stringWithFormat:@"DeviceDaemonGIO totalcount:%@,curfamilycount:%@,begin:%ld,familylast:%ld,last:%ld,deviceId:%@,productType:%@,prepareCount:%d",
                                               totalDeviceCount,
                                               curFamilyDeviceCount,
                                               beginPerpareInterval,
                                               lastCurFamilyDevicePerpareInterval,
                                               lastDevicePrepareInterval,
                                               self.deviceId,
                                               self.productType,
                                               self.prepareCount];
    UPDeviceLogDebug(@"%s[%d]%@", __PRETTY_FUNCTION__, __LINE__, log);

    if (self.devices.count == 1) {

        NSInteger attachInterval = [self toMillisecondTimeIntervalByDate:self.attachTime];
        NSInteger findConfigFileInterval = [self toMillisecondTimeIntervalByDate:self.findConfigFileTime];
        NSInteger parseConfigFileInterval = [self toMillisecondTimeIntervalByDate:self.parseConfigFileTime];

        NSNumber *perpareTimes = [[NSNumber alloc] initWithInt:self.prepareCount];
        NSNumber *deviceAttachCost = [[NSNumber alloc] initWithInteger:(attachInterval - beginPerpareInterval)];
        NSNumber *logicSearchCost = [[NSNumber alloc] initWithInteger:(findConfigFileInterval - attachInterval)];
        NSNumber *logicParseCost = [[NSNumber alloc] initWithInteger:(parseConfigFileInterval - findConfigFileInterval)];

        NSDictionary *oneDeviceParam = @{
            @"deviceid" : self.deviceId ?: DaemonGIO_NULL,
            @"product_type" : self.productType ?: DaemonGIO_NULL,
            @"cost_device_attach" : deviceAttachCost,
            @"cost_logic_search" : logicSearchCost,
            @"cost_logic_parse" : logicParseCost,
            @"times_prepare" : perpareTimes
        };

        [[UPEventTrace getInstance] trace:@"MB36633" withVariable:oneDeviceParam];
    }
}

- (BOOL)isRealDevice:(NSString *)deviceId
{
    if (![deviceId isKindOfClass:NSString.class] || deviceId.length < 2) {
        return NO;
    }
    NSString *head = [deviceId substringWithRange:NSMakeRange(0, 2)];
    if ([@"VM" isEqualToString:head]) {
        return NO;
    }
    return YES;
}

- (NSInteger)toMillisecondTimeIntervalByDate:(NSDate *)date
{
    if ([date isKindOfClass:[NSDate class]]) {
        return [date timeIntervalSince1970] * 1000;
    }

    return 0;
}

@end
