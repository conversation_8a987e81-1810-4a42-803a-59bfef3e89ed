//
//  UpDeviceDaemonGIO.h
//  UPDevice
//
//  Created by MAC on 2023/6/14.
//  Copyright © 2023 海尔优家智能科技（北京）有限公司. All rights reserved.
//

#import <Foundation/Foundation.h>
#import "UpDeviceInfo.h"

/**
 * description: 设备准备阶段，生成GIO打点数据的管理类
 */
NS_ASSUME_NONNULL_BEGIN

@interface UpDeviceDaemonGIO : NSObject

/**
 *  @brief 开始准备设备列表时调用，传入设备列表
 *  @param devices 设备信息对象数组
 *  @since  7.8.1
 */
- (void)beginPrepare:(NSArray<id<UpDeviceInfo>> *)devices;

/**
 *  @brief 设备开始准备
 *  @since  7.8.1
 */
- (void)devicePrepareBegin;

/**
 *  @brief 设备结束准备
 *  @param deviceId 设备Id
 *  @since  7.8.1
 */
- (void)devicePrepareEnd:(NSString *)deviceId;

/**
 *  @brief 统计本地已有配置文件的设备
 *  @param deviceId 设备Id
 *  @since  7.9.2
 */
- (void)countActiveDevice:(NSString *)deviceId;

/**
 *  @brief 统计设备准备次数
 *  @since  7.10.0
 */
- (void)countPrepareTimes;

/**
 *  @brief 统计设备attach完成时间
 *  @since  7.10.0
 */
- (void)countAttachTime;

/**
 *  @brief 统计设备查找逻辑约束文件完成时间
 *  @since  7.10.0
 */
- (void)countFindConfigFileTime;

/**
 *  @brief 统计设备解析逻辑约束文件完成时间
 *  @since  7.10.0
 */
- (void)countParseConfigFileTime;

@end

NS_ASSUME_NONNULL_END
