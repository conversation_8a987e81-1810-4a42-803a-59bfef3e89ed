//
//  WifiDeviceHelper.h
//  UPDeviceDebugger
//
//  Created by f on 2/1/2019.
//  Copyright © 2019 海尔优家智能科技（北京）有限公司. All rights reserved.
//

#import <Foundation/Foundation.h>
#import "UpDeviceToolkitState.h"
#import <uSDK/uSDK.h>
#import "UpDeviceResult.h"
#import "UpStringResult.h"
#import "GatewayConnection.h"
#import "UpDeviceConnection.h"
#import "WifiDeviceNetworkType.h"
#import "UpDeviceBaseInfo.h"
#import "DeviceBaseInfo.h"
#import "UpDeviceCaution.h"
#import "DeviceCaution.h"
#import "DeviceAttribute.h"
#import "UpDeviceAttribute.h"
#import "UpDeviceControlState.h"
#import "UpDeviceOnlineStatus.h"
#import "UpDeviceOnlineStateV2.h"
#import "UpDeviceOnlyConfigState.h"
#import "UpDeviceNetworkLevel.h"
#import "UpDeviceQCConnectTimeoutType.h"
#import "UpDeviceOfflineCause.h"
NS_ASSUME_NONNULL_BEGIN

@interface WifiDeviceHelper : NSObject

+ (NSString *)formatError:(uSDKErrorConst)error;

+ (UpDeviceToolkitState)parseState:(uSDKState)state;

+ (UpDeviceRealOnline)paresOnlineState:(uSDKDeviceOnlineState)state;

+ (UpDeviceRealOnlineV2)paresOnlineStateV2:(uSDKDeviceOnlineStateV2)state;

+ (UpDeviceOnlyConfigState)parseOnlyConfigState:(uSDKDeviceOnlyConfigState)state;

+ (UpDeviceSleepState)parseSleepState:(uSDKDeviceSleepState)state;

+ (UpDeviceNetworkLevel)parseNetworkLevel:(uSDKDeviceNetQualityLevel)level;

+ (UpDeviceResultErrorCode)parseErrorCode:(uSDKErrorConst)error;

+ (UpStringResult *)parseError:(uSDKErrorConst)error;

+ (GatewayConnection)parseGatewayConnection:(uSDKCloudConnectionState)connection;

+ (UpDeviceConnection)parseDeviceConnection:(uSDKDeviceState)connection;

+ (UpDeviceControlState)parseDeviceControlState:(uSDKDeviceControlState)state;

+ (WifiDeviceNetworkType)parseNetworkType:(uSDKDeviceNetTypeConst)networkType;


/// 解析本地Wi-Fi连接状态
/// @param state uSDK连接状态
+ (UpDeviceConnection)parseWifiLocalState:(uSDKDeviceState)state;

/// 解析设备蓝牙连接状态
/// @param state uSDK设备蓝牙连接状态
+ (UpDeviceConnection)parseBleState:(uSDKDeviceState)state;

+ (UpDeviceQCConnectTimeoutType)parseQCConnectTimeoutType:(uSDKQCConnectTimeoutType)timeoutType;
/// 解析蓝牙连接状态
/// @param state uSDK连接状态
+ (UpDeviceConnection)parseBleState:(uSDKDeviceState)state;

+ (DeviceBaseInfo *)createDeviceBaseInfo:(uSDKDevice *)device;

+ (NSArray<id<UpDeviceBaseInfo>> *)createDeviceBaseInfoList:(NSArray<uSDKDevice *> *)deviceList;

+ (nullable DeviceCaution *)parseDeviceCaution:(uSDKDeviceAlarm *)alarm;

+ (NSArray<id<UpDeviceCaution>> *)parseDeviceCautionList:(NSArray<uSDKDeviceAlarm *> *)alarmList;

+ (nullable DeviceAttribute *)parseDeviceAttribute:(uSDKDeviceAttribute *)attribute;

+ (NSArray<id<UpDeviceAttribute>> *)parseDeviceAttributeList:(NSArray<uSDKDeviceAttribute *> *)attributes;

+ (uSDKArgument *)createArgument:(NSString *)name Value:(NSString *)value;

+ (NSArray<uSDKArgument *> *)createArgumentList:(NSDictionary<NSString *, NSString *> *)attributeMap;

+ (nullable DeviceBaseInfo *)createDeviceBaseInfoBy:(uSDKDeviceInfo *)deviceInfo;

+ (nullable uSDKDeviceInfo *)getUSDKDeviceInfo:(NSString *)deviceId;

+ (void)saveUSDKDeviceInfo:(uSDKDeviceInfo *)info;

+ (UpDeviceOfflineCause)parseDeviceOfflineCause:(uSDKDeviceActiveOfflineCause)offlineCause;

@end

NS_ASSUME_NONNULL_END
