//
//  GatewayMessageListener.h
//  UPDeviceDebugger
//
//  Created by f on 2/1/2019.
//  Copyright © 2019 海尔优家智能科技（北京）有限公司. All rights reserved.
//

#import <Foundation/Foundation.h>
#import "GatewayConnection.h"

NS_ASSUME_NONNULL_BEGIN

@protocol GatewayMessageListener <NSObject>

- (void)onGatewayConnectionChange:(GatewayConnection)connection;

- (void)onDeviceBound:(NSString *)deviceId;

- (void)onDeviceUnbound:(NSString *)deviceId;

@end

NS_ASSUME_NONNULL_END
