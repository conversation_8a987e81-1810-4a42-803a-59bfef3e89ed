//
//  GetDeviceNetworkLevel.h
//  UPDevice
//
//  Created by 王杰 on 2021/12/1.
//  Copyright © 2021 海尔优家智能科技（北京）有限公司. All rights reserved.
//

#import "WifiDeviceAction.h"

NS_ASSUME_NONNULL_BEGIN
extern NSString *const GetDeviceNetworkLevel_NAME;
@interface GetDeviceNetworkLevel : WifiDeviceAction
+ (GetDeviceNetworkLevel *)GetDeviceNetworkLevel:(uSDKManager *)manager DeviceManager:(uSDKDeviceManager *)deviceManager;

@end

NS_ASSUME_NONNULL_END
