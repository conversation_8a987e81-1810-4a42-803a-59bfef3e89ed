//
//  GetBleState.m
//  UPDevice
//
//  Created by 王杰 on 2022/5/25.
//  Copyright © 2022 海尔优家智能科技（北京）有限公司. All rights reserved.
//

#import "GetDeviceBleState.h"
#import "WifiDeviceHelper.h"
#import "UPDeviceLog.h"

NSString *const GetDeviceBleState_NAME = @"GetDeviceBleState";

@implementation GetDeviceBleState
+ (GetDeviceBleState *)GetDeviceBleState:(uSDKManager *)manager DeviceManager:(uSDKDeviceManager *)deviceManager
{
    return [[GetDeviceBleState alloc] initWith:manager DeviceManager:deviceManager];
}

- (instancetype)initWith:(uSDKManager *)manager DeviceManager:(uSDKDeviceManager *)deviceManager
{
    if (self = [super initWithName:GetDeviceBleState_NAME Manager:manager DeviceManager:deviceManager]) {
    }
    return self;
}

- (void)execute:(NSDictionary *)params finishBlock:(void (^)(UpDeviceResult *result))finishBlock
{
    NSString *deviceId = nil;
    if (params != nil) {
        deviceId = [self getParam:params Key:WifiDeviceAction_KEY_DEVICE_ID Class:[NSObject class]];
    }

    if (deviceId == nil) {
        UpDeviceResult *result = [UpDeviceResult UpDeviceResult:ErrorCode_FAILURE extraData:nil];
        UPDeviceLogError(@"%s[%d]device(%@)GetDeviceBleState!error:%@", __PRETTY_FUNCTION__, __LINE__, deviceId, result.extraInfo);
        if (finishBlock) {
            finishBlock(result);
        }
        return;
    }

    uSDKDevice *device = [self getDevice:deviceId];
    if (!device) {
        UpDeviceResult *result = [UpDeviceResult UpDeviceResult:ErrorCode_FAILURE extraData:nil];
        UPDeviceLogError(@"%s[%d]devcie(%@)GetDeviceBleState fail,no usdkDevice!error:%@", __PRETTY_FUNCTION__, __LINE__, deviceId, result.extraInfo);
        if (finishBlock) {
            finishBlock(result);
        }
        return;
    }
    UpDeviceResult *result = [UpDeviceResult UpDeviceResult:ErrorCode_SUCCESS extraData:nil];
    UpDeviceConnection bleState = [WifiDeviceHelper parseBleState:device.bleState];
    result.extraData = [NSNumber numberWithInt:(int)bleState];
    UPDeviceLogDebug(@"%s[%d]device(%@)GetDeviceBleState success！status:%ld", __PRETTY_FUNCTION__, __LINE__, deviceId, device.localState);
    if (finishBlock) {
        finishBlock(result);
    }
}

@end
