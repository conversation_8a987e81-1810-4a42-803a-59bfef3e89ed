//
//  AttachToolkit.m
//  UPDeviceDebugger
//
//  Created by f on 2/1/2019.
//  Copyright © 2019 海尔优家智能科技（北京）有限公司. All rights reserved.
//

#import "AttachToolkit.h"
#import "UPDeviceLog.h"

NSString *const AttachToolkit_NAME = @"attachToolkit";

NSString *const AttachToolkit_KEY_MANAGER_LISTENER = @"usdk-manager-listener";
NSString *const AttachToolkit_KEY_DEVICE_MANAGER_LISTENER = @"usdk-device-manager-listener";
NSString *const AttachToolkit_APP_ID = @"usdk-app-id";
NSString *const AttachToolkit_APP_KEY = @"usdk-app-key";
NSString *const AttachToolkit_SECRET_KEY = @"usdk-secret-key";
NSString *const AttachToolkit_AREA_KEY = @"usdk-area-key";
NSString *const AttachToolkit_FEATURES_KEY = @"usdk-features-key";
NSString *const AttachToolkit_ENABLE_BLE_KEY = @"usdk-enable-ble-control-key";
NSString *const AttachToolkit_ENABLE_WIFI_KEY = @"usdk-enable-wifi-search-key";

@implementation AttachToolkit

+ (AttachToolkit *)AttachToolkit:(uSDKManager *)manager DeviceManager:(uSDKDeviceManager *)deviceManager
{
    return [[AttachToolkit alloc] initWith:manager DeviceManager:deviceManager];
}

- (instancetype)initWith:(uSDKManager *)manager DeviceManager:(uSDKDeviceManager *)deviceManager
{
    if (self = [super initWithName:AttachToolkit_NAME Manager:manager DeviceManager:deviceManager]) {
    }

    return self;
}

- (void)execute:(NSDictionary *)params finishBlock:(void (^)(UpDeviceResult *result))finishBlock
{
    id<uSDKManagerDelegate> managerListener = nil;
    id<uSDKDeviceManagerDelegate> deviceManagerListener = nil;
    NSString *appID = nil;
    NSString *appKey = nil;
    NSString *secretKey = nil;
    NSInteger aera = 0;
    NSUInteger feastures = 0;
    BOOL enableBLEControllableSearch = NO;
    BOOL enableWifiSearch = NO;
    if ([params isKindOfClass:[NSDictionary class]] && params.count > 0) {
        managerListener = [self getParam:params Key:AttachToolkit_KEY_MANAGER_LISTENER Class:[NSObject class]];
        deviceManagerListener = [self getParam:params Key:AttachToolkit_KEY_DEVICE_MANAGER_LISTENER Class:[NSObject class]];
        appID = [self getParam:params Key:AttachToolkit_APP_ID Class:[NSString class]];
        appKey = [self getParam:params Key:AttachToolkit_APP_KEY Class:[NSString class]];
        secretKey = [self getParam:params Key:AttachToolkit_SECRET_KEY Class:[NSString class]];
        aera = [[self getParam:params Key:AttachToolkit_AREA_KEY Class:[NSNumber class]] integerValue];
        feastures = [[self getParam:params Key:AttachToolkit_FEATURES_KEY Class:[NSNumber class]] unsignedIntegerValue];

        id enableBle = [self getParam:params Key:AttachToolkit_ENABLE_BLE_KEY Class:[NSNumber class]];
        id enableWifi = [self getParam:params Key:AttachToolkit_ENABLE_WIFI_KEY Class:[NSNumber class]];

        if (enableBle != nil) {
            enableBLEControllableSearch = [enableBle boolValue];
        }
        if (enableWifi != nil) {
            enableWifiSearch = [enableWifi boolValue];
        }
    }

    if (managerListener == nil || deviceManagerListener == nil || appID == nil || appKey == nil || secretKey == nil) {
        UpDeviceResult *result = [[UpDeviceResult alloc] initWithErrorCode:ErrorCode_FAILURE extraData:nil extraCode:nil extraInfo:@"参数为空！"];
        UPDeviceLogError(@"%s[%d]执行AttachToolkit失败!managerListener:%@,deviceManagerListener:%@,appID:%@,appKey:%@,secretKey:%@,error:%@", __PRETTY_FUNCTION__, __LINE__, managerListener, deviceManagerListener, appID, appKey, secretKey, result.extraInfo);
        if (finishBlock) {
            finishBlock(result);
        }
        return;
    }
    [self getManager].delegate = managerListener;
    [self getDeviceManager].delegate = deviceManagerListener;

    uSDKStartOptions *uSDKOption = [uSDKStartOptions new];
    uSDKOption.appKey = appKey;
    uSDKOption.secretKey = secretKey;
    uSDKOption.appID = appID;
    uSDKOption.area = aera;
    uSDKOption.features = feastures;
    uSDKOption.enableBLEControllableSearch = enableBLEControllableSearch;
    uSDKOption.enableWifiSearch = enableWifiSearch;
    uSDKOption.useDevListLocalCache = YES;
    //    uSDKOption.callback_queue = dispatch_queue_create("USDK_REPORT_SERIAL_QUEUE#", DISPATCH_QUEUE_SERIAL);
    [[self getManager] startSDKWithOptions:uSDKOption
        success:^{
          UpDeviceResult *result = [UpDeviceResult UpDeviceResult:ErrorCode_SUCCESS extraData:nil];
          UPDeviceLogDebug(@"%s[%d]usdk启动成功！", __PRETTY_FUNCTION__, __LINE__);
          if (finishBlock) {
              finishBlock(result);
          }
        }
        failure:^(NSError *error) {
          UpDeviceResult *result = [[UpDeviceResult alloc] initWithErrorCode:ErrorCode_FAILURE extraData:nil extraCode:nil extraInfo:error.localizedDescription];
          UPDeviceLogError(@"%s[%d]usdk启动失败！error:%@", __PRETTY_FUNCTION__, __LINE__, result.extraInfo);
          if (finishBlock) {
              finishBlock(result);
          }
        }];
}

@end
