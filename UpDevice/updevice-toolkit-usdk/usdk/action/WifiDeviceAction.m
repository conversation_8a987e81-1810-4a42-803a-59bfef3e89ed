//
//  WifiDeviceAction.m
//  UPDeviceDebugger
//
//  Created by f on 2/1/2019.
//  Copyright © 2019 海尔优家智能科技（北京）有限公司. All rights reserved.
//

#import "WifiDeviceAction.h"
#import "WifiDeviceHelper.h"
#import "UPDeviceLog.h"

NSString *const WifiDeviceAction_KEY_DEVICE_ID = @"usdk-device-id";

@implementation WifiDeviceAction

- (nullable uSDKDevice *)getDevice:(NSString *)deviceId
{
    uSDKDevice *device = [[self getDeviceManager] getDeviceWithID:deviceId];
    if (device) {
        UPDeviceLogInfo(@"%s[%d]开始查询USDK设备 找到usdk设备对象 deviceId %@", __PRETTY_FUNCTION__, __LINE__, deviceId);
        return device;
    }
    UPDeviceLogInfo(@"%s[%d]开始查询USDK设备 未找到usdk设备对象 deviceId %@", __PRETTY_FUNCTION__, __LINE__, deviceId);
    return nil;
}
- (NSArray<uSDKDevice *> *)createuSDKDevice:(NSArray *)deviceIds
{
    NSMutableArray *devicesArray = [NSMutableArray array];
    for (NSString *deviceId in deviceIds) {
        uSDKDevice *device = [self getDevice:deviceId];
        if (device) {
            [devicesArray addObject:device];
        }
    }
    return devicesArray;
}
@end
