//
//  DisconnectDevice.m
//  UPDevice
//
//  Created by MAC on 2021/10/13.
//  Copyright © 2021 海尔优家智能科技（北京）有限公司. All rights reserved.
//

#import "DisconnectDevice.h"
#import "UPDeviceLog.h"
#import "WifiDeviceHelper.h"
#import <uSDK/uSDKBinding.h>

NSString *const DisconnectDevice_NAME = @"DisconnectDevice";

@implementation DisconnectDevice

+ (DisconnectDevice *)DisconnectDevice:(uSDKManager *)manager DeviceManager:(uSDKDeviceManager *)deviceManager
{
    return [[DisconnectDevice alloc] initWith:manager DeviceManager:deviceManager];
}

- (instancetype)initWith:(uSDKManager *)manager DeviceManager:(uSDKDeviceManager *)deviceManager
{
    if (self = [super initWithName:DisconnectDevice_NAME Manager:manager DeviceManager:deviceManager]) {
    }

    return self;
}

- (void)execute:(NSDictionary *)params finishBlock:(void (^)(UpDeviceResult *result))finishBlock
{
    NSString *deviceId = nil;
    if (params != nil) {
        deviceId = [self getParam:params Key:WifiDeviceAction_KEY_DEVICE_ID Class:[NSString class]];
    }
    if (deviceId.length == 0) {
        UpDeviceResult *result = [[UpDeviceResult alloc] initWithErrorCode:ErrorCode_FAILURE extraData:nil extraCode:nil extraInfo:@"deviceId empty！"];
        UPDeviceLogError(@"%s[%d]execute DisconnectDevice!deviceId:%@,error:%@", __PRETTY_FUNCTION__, __LINE__, deviceId, result.extraInfo);
        if (finishBlock) {
            finishBlock(result);
        }
        return;
    }
    uSDKDevice *device = [self getDevice:deviceId];
    if (!device) {
        UpDeviceResult *result = [UpDeviceResult UpDeviceResult:ErrorCode_FAILURE extraData:nil];
        UPDeviceLogError(@"%s[%d]devcie(%@)get DisconnectDevice fail,no usdkDevice!error:%@", __PRETTY_FUNCTION__, __LINE__, deviceId, result.extraInfo);
        if (finishBlock) {
            finishBlock(result);
        }
        return;
    }

    if (device.controlState == uSDKDeviceControlStateNone) {
        UpDeviceResult *result = [UpDeviceResult UpDeviceResult:ErrorCode_FAILURE extraData:nil];
        UPDeviceLogError(@"%s[%d]devcie(%@)already bind,noneed disconnect!error:%@", __PRETTY_FUNCTION__, __LINE__, deviceId, result.extraInfo);
        if (finishBlock) {
            finishBlock(result);
        }
        return;
    }

    [device disconnectWithSuccess:^{
      UpDeviceResult *result = [UpDeviceResult UpDeviceResult:ErrorCode_SUCCESS extraData:nil];
      UPDeviceLogDebug(@"%s[%d]DisconnectDevice success！", __PRETTY_FUNCTION__, __LINE__);
      if (finishBlock) {
          finishBlock(result);
      }
    }
        failure:^(NSError *error) {
          UpDeviceResult *result = [[UpDeviceResult alloc] initWithErrorCode:ErrorCode_FAILURE extraData:nil extraCode:error.description extraInfo:error.description];
          UPDeviceLogError(@"%s[%d]DisconnectDevice fail！error:%@", __PRETTY_FUNCTION__, __LINE__, error.description);
          if (finishBlock) {
              finishBlock(result);
          }
        }];
}

@end
