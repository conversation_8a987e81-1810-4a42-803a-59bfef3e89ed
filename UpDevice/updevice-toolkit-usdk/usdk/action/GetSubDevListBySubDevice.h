//
//  GetSubDevListBySubDevice.h
//  UPDevice
//
//  Created by gump on 4/12/2019.
//  Copyright © 2019 海尔优家智能科技（北京）有限公司. All rights reserved.
//

#import "WifiDeviceAction.h"
#import <uSDK/uSDK.h>

/**
 * description: 通过子机获取子机列表
 */

extern NSString *const GetSubDevListBySubDevice_NAME;

@interface GetSubDevListBySubDevice : WifiDeviceAction

+ (GetSubDevListBySubDevice *)GetSubDevListBySubDevice:(uSDKManager *)manager DeviceManager:(uSDKDeviceManager *)deviceManager;

- (instancetype)initWith:(uSDKManager *)manager DeviceManager:(uSDKDeviceManager *)deviceManager;

@end
