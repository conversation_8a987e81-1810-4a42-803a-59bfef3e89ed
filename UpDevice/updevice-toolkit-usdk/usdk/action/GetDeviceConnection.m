//
//  GetDeviceConnection.m
//  UPDeviceDebugger
//
//  Created by f on 2/1/2019.
//  Copyright © 2019 海尔优家智能科技（北京）有限公司. All rights reserved.
//

#import "GetDeviceConnection.h"
#import "WifiDeviceHelper.h"
#import "UPDeviceLog.h"

NSString *const GetDeviceConnection_NAME = @"getDeviceConnection";

@implementation GetDeviceConnection

+ (GetDeviceConnection *)GetDeviceConnection:(uSDKManager *)manager DeviceManager:(uSDKDeviceManager *)deviceManager
{
    return [[GetDeviceConnection alloc] initWith:manager DeviceManager:deviceManager];
}

- (instancetype)initWith:(uSDKManager *)manager DeviceManager:(uSDKDeviceManager *)deviceManager
{
    if (self = [super initWithName:GetDeviceConnection_NAME Manager:manager DeviceManager:deviceManager]) {
    }
    return self;
}

- (void)execute:(NSDictionary *)params finishBlock:(void (^)(UpDeviceResult *result))finishBlock
{
    NSString *deviceId = nil;
    if (params != nil) {
        deviceId = [self getParam:params Key:WifiDeviceAction_KEY_DEVICE_ID Class:[NSObject class]];
    }

    if (deviceId == nil) {
        UpDeviceResult *result = [UpDeviceResult UpDeviceResult:ErrorCode_FAILURE extraData:nil];
        UPDeviceLogError(@"%s[%d]设备(%@)连接信息获取失败!error:%@", __PRETTY_FUNCTION__, __LINE__, deviceId, result.extraInfo);
        if (finishBlock) {
            finishBlock(result);
        }
        return;
    }

    uSDKDevice *device = [self getDevice:deviceId];
    UpDeviceConnection connection = [WifiDeviceHelper parseDeviceConnection:device.state];
    UpDeviceResult *result = [UpDeviceResult UpDeviceResult:ErrorCode_SUCCESS extraData:nil];
    result.extraData = [NSNumber numberWithInt:connection];
    UPDeviceLogDebug(@"%s[%d]device(%@)GetDeviceConnection success connection:%ld！", __PRETTY_FUNCTION__, __LINE__, deviceId, connection);
    if (finishBlock) {
        finishBlock(result);
    }
}


@end
