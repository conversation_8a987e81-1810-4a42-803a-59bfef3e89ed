//
//  fetchBoardFOTAStatus.h
//  UPDevice
//
//  Created by gump on 12/6/2019.
//  Copyright © 2019 海尔优家智能科技（北京）有限公司. All rights reserved.
//

#import <Foundation/Foundation.h>
#import "WifiDeviceAction.h"
#import <uSDK/uSDK.h>

extern NSString *const FetchBoardFOTAStatus_NAME;
/*
 *查询当前设备底板固件的升级状态
 */
@interface FetchBoardFOTAStatus : WifiDeviceAction

+ (FetchBoardFOTAStatus *)FetchBoardFOTAStatus:(uSDKManager *)manager DeviceManager:(uSDKDeviceManager *)deviceManager;

- (instancetype)initWith:(uSDKManager *)manager DeviceManager:(uSDKDeviceManager *)deviceManager;

@end
