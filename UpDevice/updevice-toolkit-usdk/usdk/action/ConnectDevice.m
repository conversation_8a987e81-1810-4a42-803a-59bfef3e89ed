//
//  ConnectDevice.m
//  UPDevice
//
//  Created by MAC on 2021/10/13.
//  Copyright © 2021 海尔优家智能科技（北京）有限公司. All rights reserved.
//

#import "ConnectDevice.h"
#import "UPDeviceLog.h"
#import "WifiDeviceHelper.h"
#import <uSDK/uSDKBinding.h>

NSString *const ConnectDevice_NAME = @"ConnectDevice";

@implementation ConnectDevice

+ (ConnectDevice *)ConnectDevice:(uSDKManager *)manager DeviceManager:(uSDKDeviceManager *)deviceManager
{
    return [[ConnectDevice alloc] initWith:manager DeviceManager:deviceManager];
}

- (instancetype)initWith:(uSDKManager *)manager DeviceManager:(uSDKDeviceManager *)deviceManager
{
    if (self = [super initWithName:ConnectDevice_NAME Manager:manager DeviceManager:deviceManager]) {
    }

    return self;
}

- (void)execute:(NSDictionary *)params finishBlock:(void (^)(UpDeviceResult *result))finishBlock
{
    NSString *deviceId = nil;
    if (params != nil) {
        deviceId = [self getParam:params Key:WifiDeviceAction_KEY_DEVICE_ID Class:[NSString class]];
    }
    if (deviceId.length == 0) {
        UpDeviceResult *result = [[UpDeviceResult alloc] initWithErrorCode:ErrorCode_FAILURE extraData:nil extraCode:nil extraInfo:@"deviceId empty！"];
        UPDeviceLogError(@"%s[%d]execute ConnectDevice!deviceId:%@,error:%@", __PRETTY_FUNCTION__, __LINE__, deviceId, result.extraInfo);
        if (finishBlock) {
            finishBlock(result);
        }
        return;
    }
    uSDKDevice *device = [self getDevice:deviceId];
    if (!device) {
        UpDeviceResult *result = [UpDeviceResult UpDeviceResult:ErrorCode_FAILURE extraData:nil];
        UPDeviceLogError(@"%s[%d]devcie(%@)get ConnectDevice fail,no usdkDevice!error:%@", __PRETTY_FUNCTION__, __LINE__, deviceId, result.extraInfo);
        if (finishBlock) {
            finishBlock(result);
        }
        return;
    }

    if (device.controlState == uSDKDeviceControlStateNone) {
        UpDeviceResult *result = [UpDeviceResult UpDeviceResult:ErrorCode_FAILURE extraData:nil];
        UPDeviceLogError(@"%s[%d]devcie(%@)already bind,noneed connect!error:%@", __PRETTY_FUNCTION__, __LINE__, deviceId, result.extraInfo);
        if (finishBlock) {
            finishBlock(result);
        }
        return;
    }

    [device connectNeedPropertiesWithSuccess:^{
      UpDeviceResult *result = [UpDeviceResult UpDeviceResult:ErrorCode_SUCCESS extraData:nil];
      UPDeviceLogDebug(@"%s[%d]device(%@)subscribe usdk success！", __PRETTY_FUNCTION__, __LINE__, deviceId);
      if (finishBlock) {
          finishBlock(result);
      }
    }
        failure:^(NSError *error) {
          UpDeviceResult *result = [[UpDeviceResult alloc] initWithErrorCode:ErrorCode_FAILURE extraData:nil extraCode:nil extraInfo:error.description];
          UPDeviceLogError(@"%s[%d]device(%@)subscribe usdk fail！error:%@", __PRETTY_FUNCTION__, __LINE__, deviceId, error);
          if (finishBlock) {
              finishBlock(result);
          }
        }];
}
@end
