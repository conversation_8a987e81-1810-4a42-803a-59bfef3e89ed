//
//  ExecuteCommandWithResult.h
//  UPDevice
//
//  Created by pc on 2024/2/23.
//  Copyright © 2024 海尔优家智能科技（北京）有限公司. All rights reserved.
//

#import "WifiDeviceAction.h"
#import <uSDK/uSDK.h>

NS_ASSUME_NONNULL_BEGIN

extern NSString *const ExecuteCommandWithResult_NAME;

@interface ExecuteCommandWithResult : WifiDeviceAction

+ (ExecuteCommandWithResult *)ExecuteCommandWithResult:(uSDKManager *)manager DeviceManager:(uSDKDeviceManager *)deviceManager;

- (instancetype)initWith:(uSDKManager *)manager DeviceManager:(uSDKDeviceManager *)deviceManager;

@end

NS_ASSUME_NONNULL_END
