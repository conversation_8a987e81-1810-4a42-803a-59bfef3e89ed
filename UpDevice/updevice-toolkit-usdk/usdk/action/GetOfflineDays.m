//
//  GetOfflineDays.m
//  UPDevice
//
//  Created by <PERSON><PERSON><PERSON>   on 2024/5/15.
//  Copyright © 2024 海尔优家智能科技（北京）有限公司. All rights reserved.
//

#import "GetOfflineDays.h"
#import "WifiDeviceHelper.h"
#import "UPDeviceLog.h"

NSString *const GetOfflineDays_NAME = @"GetOfflineDays";

@implementation GetOfflineDays

+ (GetOfflineDays *)getDeviceOfflineDays:(id)manager DeviceManager:(id)deviceManager
{
    return [[GetOfflineDays alloc] initWith:manager DeviceManager:deviceManager];
}

- (instancetype)initWith:(uSDKManager *)manager DeviceManager:(uSDKDeviceManager *)deviceManager
{
    if (self = [super initWithName:GetOfflineDays_NAME Manager:manager DeviceManager:deviceManager]) {
    }
    return self;
}

- (void)execute:(NSDictionary *)params finishBlock:(void (^)(UpDev<PERSON>Result *result))finishBlock
{
    NSString *deviceId = nil;
    if (params != nil) {
        deviceId = [self getParam:params Key:WifiDeviceAction_KEY_DEVICE_ID Class:[NSObject class]];
    }

    if (deviceId == nil) {
        UpDeviceResult *result = [UpDeviceResult UpDeviceResult:ErrorCode_FAILURE extraData:nil];
        UPDeviceLogError(@"%s[%d]device(%@)GetOfflineDays!error:%@", __PRETTY_FUNCTION__, __LINE__, deviceId, result.extraInfo);
        if (finishBlock) {
            finishBlock(result);
        }
        return;
    }

    uSDKDevice *device = [self getDevice:deviceId];
    if (!device) {
        UpDeviceResult *result = [UpDeviceResult UpDeviceResult:ErrorCode_FAILURE extraData:nil];
        UPDeviceLogError(@"%s[%d]devcie(%@)GetOfflineDays fail,no usdkDevice!error:%@", __PRETTY_FUNCTION__, __LINE__, deviceId, result.extraInfo);
        if (finishBlock) {
            finishBlock(result);
        }
        return;
    }
    UpDeviceResult *result = [UpDeviceResult UpDeviceResult:ErrorCode_SUCCESS extraData:nil];
    result.extraData = [NSNumber numberWithInteger:device.offlineDays];
    UPDeviceLogDebug(@"%s[%d]device(%@)GetOfflineDays success！offlineDays:%ld", __PRETTY_FUNCTION__, __LINE__, deviceId, device.offlineDays);
    if (finishBlock) {
        finishBlock(result);
    }
}

@end
