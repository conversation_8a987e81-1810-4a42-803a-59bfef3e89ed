//
//  GetDeviceAttributes.m
//  UPDeviceDebugger
//
//  Created by f on 2/1/2019.
//  Copyright © 2019 海尔优家智能科技（北京）有限公司. All rights reserved.
//

#import "GetDeviceAttributes.h"
#import "UpDeviceAttribute.h"
#import "WifiDeviceHelper.h"
#import "UPDeviceLog.h"

NSString *const GetDeviceAttributes_NAME = @"getDeviceAttributeList";

@implementation GetDeviceAttributes

+ (GetDeviceAttributes *)GetDeviceAttributes:(uSDKManager *)manager DeviceManager:(uSDKDeviceManager *)deviceManager
{
    return [[GetDeviceAttributes alloc] initWith:manager DeviceManager:deviceManager];
}

- (instancetype)initWith:(uSDKManager *)manager DeviceManager:(uSDKDeviceManager *)deviceManager
{
    if (self = [super initWithName:GetDeviceAttributes_NAME Manager:manager DeviceManager:deviceManager]) {
    }
    return self;
}

- (void)execute:(NSDictionary *)params finishBlock:(void (^)(UpDeviceResult *result))finishBlock
{
    NSString *deviceId = nil;
    if (params != nil) {
        deviceId = [self getParam:params Key:WifiDeviceAction_KEY_DEVICE_ID Class:[NSObject class]];
    }

    if (deviceId == nil) {
        UpDeviceResult *result = [UpDeviceResult UpDeviceResult:ErrorCode_FAILURE extraData:nil];
        UPDeviceLogError(@"%s[%d]设备(%@)属性获取失败!error:%@", __PRETTY_FUNCTION__, __LINE__, deviceId, result.extraInfo);
        if (finishBlock) {
            finishBlock(result);
        }
        return;
    }

    uSDKDevice *device = [self getDevice:deviceId];
    NSDictionary<NSString *, uSDKDeviceAttribute *> *attrMap = [device attributeDict];
    NSArray<id<UpDeviceAttribute>> *attributeList;

    @synchronized(attrMap)
    {
        if (attrMap.count > 0) {
            attributeList = [WifiDeviceHelper parseDeviceAttributeList:attrMap.allValues];
        }
        else {
            attributeList = [NSArray new];
        }
    }

    UpDeviceResult *result = [UpDeviceResult UpDeviceResult:ErrorCode_SUCCESS extraData:nil];
    UPDeviceLogDebug(@"%s[%d]设备(%@)属性获取成功！", __PRETTY_FUNCTION__, __LINE__, deviceId);
    result.extraData = attributeList;
    if (finishBlock) {
        finishBlock(result);
    }
}

@end
