//
//  GetOnlyConfigState.h
//  UPDevice
//
//  Created by <PERSON><PERSON><PERSON>   on 2024/6/18.
//  Copyright © 2024 海尔优家智能科技（北京）有限公司. All rights reserved.
//

#import "WifiDeviceAction.h"

NS_ASSUME_NONNULL_BEGIN
extern NSString *const GetOnlyConfigState_NAME;

@interface GetOnlyConfigState : WifiDeviceAction
+ (GetOnlyConfigState *)getDeviceOnlyConfigState:(uSDKManager *)manager DeviceManager:(uSDKDeviceManager *)deviceManager;
@end

NS_ASSUME_NONNULL_END
