//
//  SmartLinkSoftwareVersion.m
//  UPDevice
//
//  Created by gump on 15/11/2019.
//  Copyright © 2019 海尔优家智能科技（北京）有限公司. All rights reserved.
//

#import "SmartLinkSoftwareVersion.h"
#import "UPDeviceLog.h"

NSString *const SmartLinkSoftwareVersion_NAME = @"SmartLinkSoftwareVersion";

@implementation SmartLinkSoftwareVersion

+ (SmartLinkSoftwareVersion *)SmartLinkSoftwareVersion:(uSDKManager *)manager DeviceManager:(uSDKDeviceManager *)deviceManager
{
    return [[SmartLinkSoftwareVersion alloc] initWith:manager DeviceManager:deviceManager];
}

- (instancetype)initWith:(uSDKManager *)manager DeviceManager:(uSDKDeviceManager *)deviceManager
{
    if (self = [super initWithName:SmartLinkSoftwareVersion_NAME Manager:manager DeviceManager:deviceManager]) {
    }
    return self;
}

- (void)execute:(NSDictionary *)params finishBlock:(void (^)(UpDeviceResult *result))finishBlock
{
    NSString *deviceId = nil;
    if (params != nil) {
        deviceId = [self getParam:params Key:WifiDeviceAction_KEY_DEVICE_ID Class:[NSString class]];
    }
    if (deviceId == nil) {
        UpDeviceResult *result = [[UpDeviceResult alloc] initWithErrorCode:ErrorCode_FAILURE extraData:nil extraCode:nil extraInfo:@"deviceId 为空！"];
        UPDeviceLogError(@"%s[%d]设备命令执行失败!deviceId:%@,error:%@", __PRETTY_FUNCTION__, __LINE__, deviceId, result.extraInfo);
        if (finishBlock) {
            finishBlock(result);
        }
        return;
    }

    uSDKDevice *device = [self getDevice:deviceId];
    if (![device isKindOfClass:[uSDKDevice class]]) {
        NSString *msg = [NSString stringWithFormat:@"uSDK中未发现该设备对象：%@", deviceId];
        UpDeviceResult *result = [[UpDeviceResult alloc] initWithErrorCode:ErrorCode_FAILURE extraData:nil extraCode:nil extraInfo:msg];
        UPDeviceLogError(@"%s[%d]设备(%@)获取模块版本失败! error:%@", __PRETTY_FUNCTION__, __LINE__, deviceId, result.extraInfo);
        if (finishBlock) {
            finishBlock(result);
        }
        return;
    }

    UpDeviceResult *result = [UpDeviceResult UpDeviceResult:ErrorCode_SUCCESS extraData:device.smartLinkSoftwareVersion];
    UPDeviceLogDebug(@"%s[%d]获取模块版本号！deviceId:%@   smartLinkSoftwareVersion:%@", __PRETTY_FUNCTION__, __LINE__, deviceId, result.extraData); //用device.smartLinkSoftwareVersion 断言会走2次
    if (finishBlock) {
        finishBlock(result);
    }
}

@end
