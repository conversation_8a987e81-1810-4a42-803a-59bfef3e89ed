//
//  IsSupportOnlyConfig.h
//  UPDevice
//
//  Created by lichen on 2025/4/18.
//  Copyright © 2025 海尔优家智能科技（北京）有限公司. All rights reserved.
//

#import "WifiDeviceAction.h"

NS_ASSUME_NONNULL_BEGIN
extern NSString *const IsSupportOnlyConfig_NAME;

@interface IsSupportOnlyConfig : WifiDeviceAction

+ (IsSupportOnlyConfig *)IsSupportOnlyConfig:(uSDKManager *)manager DeviceManager:(uSDKDeviceManager *)deviceManager;

@end

NS_ASSUME_NONNULL_END
