//
//  AttachDecodeResource.m
//  UPDevice
//
//  Created by 冉东军 on 2021/3/17.
//  Copyright © 2021 海尔优家智能科技（北京）有限公司. All rights reserved.
//

#import "AttachDecodeResource.h"
#import "UPDeviceLog.h"
NSString *const AttachDecodeResource_NAME = @"attachDecodeResource";
extern NSString *const KEY_RESOURCE_NAME;
@implementation AttachDecodeResource

+ (AttachDecodeResource *)AttachDecodeResource:(uSDKManager *)manager DeviceManager:(uSDKDeviceManager *)deviceManager
{
    return [[AttachDecodeResource alloc] initWith:manager DeviceManager:deviceManager];
}

- (instancetype)initWith:(uSDKManager *)manager DeviceManager:(uSDKDeviceManager *)deviceManager
{
    if (self = [super initWithName:AttachDecodeResource_NAME Manager:manager DeviceManager:deviceManager]) {
    }

    return self;
}
- (void)execute:(NSDictionary *)params finishBlock:(void (^)(UpDeviceResult *result))finishBlock
{
    NSString *deviceId = nil;
    NSString *resourceName = nil;

    if (params != nil) {
        deviceId = [self getParam:params Key:WifiDeviceAction_KEY_DEVICE_ID Class:[NSString class]];

        resourceName = [self getParam:params Key:KEY_RESOURCE_NAME Class:[NSString class]];
    }

    if (resourceName.length == 0 || deviceId.length == 0) {
        UpDeviceResult *result = [[UpDeviceResult alloc] initWithErrorCode:ErrorCode_FAILURE extraData:nil extraCode:nil extraInfo:@"resourceName或者deviceId为空"];
        UPDeviceLogError(@"%s[%d]执行失败! resourceName:%@ deviceId:%@ error:%@", __PRETTY_FUNCTION__, __LINE__, resourceName, deviceId, result.extraInfo);
        if (finishBlock) {
            finishBlock(result);
        }
        return;
    }

    uSDKDevice *device = [self getDevice:deviceId];
    if (![device isKindOfClass:[uSDKDevice class]]) {
        NSString *msg = [NSString stringWithFormat:@"uSDK中未发现该设备对象：%@", deviceId];
        UpDeviceResult *result = [[UpDeviceResult alloc] initWithErrorCode:ErrorCode_FAILURE extraData:nil extraCode:nil extraInfo:msg];
        UPDeviceLogError(@"%s[%d]设备(%@)订阅失败! error:%@", __PRETTY_FUNCTION__, __LINE__, deviceId, result.extraInfo);
        if (finishBlock) {
            finishBlock(result);
        }
        return;
    }

    [device subscribeResourceWithDecode:resourceName
        success:^{
          UpDeviceResult *result = [UpDeviceResult UpDeviceResult:ErrorCode_SUCCESS extraData:nil];
          UPDeviceLogDebug(@"%s[%d]设备(%@)订阅资源上报成功！", __PRETTY_FUNCTION__, __LINE__, deviceId);
          if (finishBlock) {
              finishBlock(result);
          }
        }
        failure:^(NSError *error) {
          UpDeviceResult *result = [[UpDeviceResult alloc] initWithErrorCode:ErrorCode_FAILURE extraData:nil extraCode:nil extraInfo:error.description];
          UPDeviceLogError(@"%s[%d]设备(%@)订阅资源上报失败！error:%@", __PRETTY_FUNCTION__, __LINE__, deviceId, error);
          if (finishBlock) {
              finishBlock(result);
          }
        }];
}
@end
