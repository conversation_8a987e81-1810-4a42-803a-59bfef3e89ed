//
//  isGroup.m
//  UPDevice
//
//  Created by 冉东军 on 2021/5/26.
//  Copyright © 2021 海尔优家智能科技（北京）有限公司. All rights reserved.
//

#import "IsGroup.h"
#import "UPDeviceLog.h"
NSString *const IsGroup_NAME = @"IsGroupNAME";

@implementation IsGroup
+ (IsGroup *)IsGroup:(uSDKManager *)manager DeviceManager:(uSDKDeviceManager *)deviceManager
{
    return [[IsGroup alloc] initWith:manager DeviceManager:deviceManager];
}
- (instancetype)initWith:(uSDKManager *)manager DeviceManager:(uSDKDeviceManager *)deviceManager
{
    if (self = [super initWithName:IsGroup_NAME Manager:manager DeviceManager:deviceManager]) {
    }
    return self;
}
- (void)execute:(NSDictionary *)params finishBlock:(void (^)(UpDeviceResult *result))finishBlock
{
    NSString *deviceId = nil;
    if (params != nil) {
        deviceId = [self getParam:params Key:WifiDeviceAction_KEY_DEVICE_ID Class:[NSString class]];
    }

    if (deviceId == nil) {
        UpDeviceResult *result = [[UpDeviceResult alloc] initWithErrorCode:ErrorCode_FAILURE extraData:nil extraCode:nil extraInfo:@"deviceId 为空！"];
        UPDeviceLogError(@"%s[%d]设备命令执行失败!deviceId:%@,error:%@", __PRETTY_FUNCTION__, __LINE__, deviceId, result.extraInfo);
        if (finishBlock) {
            finishBlock(result);
        }
        return;
    }
    uSDKDevice *device = [self getDevice:deviceId];
    UpDeviceResult *result = [UpDeviceResult UpDeviceResult:ErrorCode_SUCCESS extraData:[NSNumber numberWithBool:device.isGroup]];
    UPDeviceLogDebug(@"%s[%d]是否为组设备成功！deviceId:%@   isBound:%d", __PRETTY_FUNCTION__, __LINE__, deviceId, device.isBound);
    if (finishBlock) {
        finishBlock(result);
    }
}
@end
