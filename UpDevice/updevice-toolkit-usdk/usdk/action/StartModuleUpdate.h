//
//  StartModuleUpdate.h
//  UPDevice
//
//  Created by gump on 20/8/2019.
//  Copyright © 2019 海尔优家智能科技（北京）有限公司. All rights reserved.
//

#import <Foundation/Foundation.h>
#import "WifiDeviceAction.h"
#import <uSDK/uSDK.h>

extern NSString *const StartModuleUpdate_NAME;

/*
 *开始设备模块升级
 */
@interface StartModuleUpdate : WifiDeviceAction

+ (StartModuleUpdate *)StartModuleUpdate:(uSDKManager *)manager DeviceManager:(uSDKDeviceManager *)deviceManager;

- (instancetype)initWith:(uSDKManager *)manager DeviceManager:(uSDKDeviceManager *)deviceManager;

@end
