//
//  CheckBoardFOTAInfo.h
//  UPDevice
//
//  Created by gump on 12/6/2019.
//  Copyright © 2019 海尔优家智能科技（北京）有限公司. All rights reserved.
//

#import <Foundation/Foundation.h>
#import "WifiDeviceAction.h"
#import <uSDK/uSDK.h>

extern NSString *const CheckBoardFOTAInfo_NAME;
/*
 *检查设备底板固件的版本信息
 */
@interface CheckBoardFOTAInfo : WifiDeviceAction

+ (CheckBoardFOTAInfo *)CheckBoardFOTAInfo:(uSDKManager *)manager DeviceManager:(uSDKDeviceManager *)deviceManager;

- (instancetype)initWith:(uSDKManager *)manager DeviceManager:(uSDKDeviceManager *)deviceManager;

@end
