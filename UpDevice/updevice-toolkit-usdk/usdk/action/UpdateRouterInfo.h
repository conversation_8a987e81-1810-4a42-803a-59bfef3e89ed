//
//  UpdateRouterInfo.h
//  UPDevice
//
//  Created by MAC on 2021/9/30.
//  Copyright © 2021 海尔优家智能科技（北京）有限公司. All rights reserved.
//

#import "WifiDeviceAction.h"

NS_ASSUME_NONNULL_BEGIN
extern NSString *const UpdateRouterInfo_NAME;
extern NSString *const UPDATE_SSID_LISTENER;
extern NSString *const UPDATE_SSID_SSID;
extern NSString *const UPDATE_SSID_PASSWORD;
extern NSString *const UPDATE_SSID_BSSID;
/**
 * 修改设备连接的路由器或密码
 */
@interface UpdateRouterInfo : WifiDeviceAction

+ (UpdateRouterInfo *)UpdateRouterInfo:(uSDKManager *)manager DeviceManager:(uSDKDeviceManager *)deviceManager;

@end

NS_ASSUME_NONNULL_END
