//
//  GetNetType.m
//  UPDevice
//
//  Created by <PERSON><PERSON><PERSON><PERSON> on 2021/2/20.
//  Copyright © 2021 海尔优家智能科技（北京）有限公司. All rights reserved.
//

#import "GetNetType.h"
#import "UPDeviceLog.h"
NSString *const GetNetType_NAME = @"getNetType";

@implementation GetNetType

+ (GetNetType *)getNetType:(uSDKManager *)manager DeviceManager:(uSDKDeviceManager *)deviceManager
{
    return [[GetNetType alloc] initWith:manager DeviceManager:deviceManager];
}

- (instancetype)initWith:(uSDKManager *)manager DeviceManager:(uSDKDeviceManager *)deviceManager
{
    if (self = [super initWithName:GetNetType_NAME Manager:manager DeviceManager:deviceManager]) {
    }
    return self;
}

- (void)execute:(NSDictionary *)params finishBlock:(void (^)(UpDeviceResult *result))finishBlock
{
    NSString *deviceId = nil;
    if (params != nil) {
        deviceId = [self getParam:params Key:WifiDeviceAction_KEY_DEVICE_ID Class:[NSString class]];
    }
    if (deviceId == nil) {
        UpDeviceResult *result = [[UpDeviceResult alloc] initWithErrorCode:ErrorCode_FAILURE extraData:nil extraCode:nil extraInfo:@"deviceId 为空！"];
        UPDeviceLogError(@"%s[%d]设备命令执行失败!deviceId:%@,error:%@", __PRETTY_FUNCTION__, __LINE__, deviceId, result.extraInfo);
        if (finishBlock) {
            finishBlock(result);
        }
        return;
    }
    uSDKDevice *device = [self getDevice:deviceId];
    if (![device isKindOfClass:[uSDKDevice class]]) {
        NSString *msg = [NSString stringWithFormat:@"uSDK中未发现该设备对象：%@", deviceId];
        UpDeviceResult *result = [[UpDeviceResult alloc] initWithErrorCode:ErrorCode_FAILURE extraData:nil extraCode:nil extraInfo:msg];
        UPDeviceLogError(@"%s[%d]设备(%@)获取网络类型失败! error:%@", __PRETTY_FUNCTION__, __LINE__, deviceId, result.extraInfo);
        if (finishBlock) {
            finishBlock(result);
        }
        return;
    }
    UpDeviceResult *result = [UpDeviceResult UpDeviceResult:ErrorCode_SUCCESS extraData:@(device.netType)];
    UPDeviceLogDebug(@"%s[%d]获取设备网络类型成功！deviceId:%@  netType:%@", __PRETTY_FUNCTION__, __LINE__, deviceId, result.extraData);
    if (finishBlock) {
        finishBlock(result);
    }
}

@end
