//
//  GetSubDevList.m
//  UPDeviceDebugger
//
//  Created by f on 2/1/2019.
//  Copyright © 2019 海尔优家智能科技（北京）有限公司. All rights reserved.
//

#import "GetSubDevList.h"
#import "UpDeviceBaseInfo.h"
#import "DeviceBaseInfo.h"
#import "WifiDeviceHelper.h"
#import "UPDeviceLog.h"

NSString *const GetSubDevList_NAME = @"getSubDevList";

@implementation GetSubDevList

+ (GetSubDevList *)GetSubDevList:(uSDKManager *)manager DeviceManager:(uSDKDeviceManager *)deviceManager
{
    return [[GetSubDevList alloc] initWith:manager DeviceManager:deviceManager];
}

- (instancetype)initWith:(uSDKManager *)manager DeviceManager:(uSDKDeviceManager *)deviceManager
{
    if (self = [super initWithName:GetSubDevList_NAME Manager:manager DeviceManager:deviceManager]) {
    }
    return self;
}

- (void)execute:(NSDictionary *)params finishBlock:(void (^)(UpDeviceResult *result))finishBlock
{
    NSString *deviceId = nil;
    if (params != nil) {
        deviceId = [self getParam:params Key:WifiDeviceAction_KEY_DEVICE_ID Class:[NSObject class]];
    }

    if (deviceId == nil) {
        UpDeviceResult *result = [UpDeviceResult UpDeviceResult:ErrorCode_FAILURE extraData:nil];
        UPDeviceLogError(@"%s[%d]子设备列表获取失败!error:%@", __PRETTY_FUNCTION__, __LINE__, result.extraInfo);
        if (finishBlock) {
            finishBlock(result);
        }
        return;
    }

    //    uSDKDevice device = getDevice(deviceId);
    //    ArrayList<uSDKDevice> subDevList = device.getSubDeviceList();

    uSDKDevice *device = [self getDevice:deviceId];
    if (![device isKindOfClass:[uSDKDevice class]]) {
        NSString *msg = [NSString stringWithFormat:@"uSDK中未发现该设备对象：%@", deviceId];
        UpDeviceResult *result = [[UpDeviceResult alloc] initWithErrorCode:ErrorCode_FAILURE extraData:nil extraCode:nil extraInfo:msg];
        UPDeviceLogError(@"%s[%d]设备(%@)子设备列表获取失败! error:%@", __PRETTY_FUNCTION__, __LINE__, deviceId, result.extraInfo);
        if (finishBlock) {
            finishBlock(result);
        }
        return;
    }
    NSArray<uSDKSubDevice *> *subDevList = [device subDeviceList];
    NSMutableArray<id<UpDeviceBaseInfo>> *baseInfoList = [NSMutableArray array];
    if (subDevList == nil) {
        UpDeviceResult *result = [UpDeviceResult UpDeviceResult:ErrorCode_FAILURE extraData:nil];
        UPDeviceLogError(@"%s[%d]子设备列表获取失败!error:%@", __PRETTY_FUNCTION__, __LINE__, result.extraInfo);
        if (finishBlock) {
            finishBlock(result);
        }
        return;
    }

    for (uSDKDevice *device in subDevList) {
        DeviceBaseInfo *baseInfo = [WifiDeviceHelper createDeviceBaseInfo:device];
        if (baseInfo) {
            [baseInfoList addObject:baseInfo];
        }
    }

    UpDeviceResult *result = [UpDeviceResult UpDeviceResult:ErrorCode_SUCCESS extraData:nil];

    result.extraData = baseInfoList;
    UPDeviceLogDebug(@"%s[%d]子设备列表获取成功！", __PRETTY_FUNCTION__, __LINE__);
    if (finishBlock) {
        finishBlock(result);
    }
}

@end
