//
//  GetDeviceBleState.h
//  UPDevice
//
//  Created by 王杰 on 2022/6/16.
//  Copyright © 2022 海尔优家智能科技（北京）有限公司. All rights reserved.
//

#import "WifiDeviceAction.h"
//设备蓝牙连接状态
NS_ASSUME_NONNULL_BEGIN
extern NSString *const GetDeviceBleState_NAME;


@interface GetDeviceBleState : WifiDeviceAction
+ (GetDeviceBleState *)GetDeviceBleState:(uSDKManager *)manager DeviceManager:(uSDKDeviceManager *)deviceManager;
@end

NS_ASSUME_NONNULL_END
