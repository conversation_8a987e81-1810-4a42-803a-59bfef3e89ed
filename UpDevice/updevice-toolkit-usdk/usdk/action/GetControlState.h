//
//  GetControlState.h
//  UPDevice
//
//  Created by MAC on 2021/9/29.
//  Copyright © 2021 海尔优家智能科技（北京）有限公司. All rights reserved.
//

#import "WifiDeviceAction.h"

NS_ASSUME_NONNULL_BEGIN

extern NSString *const GetControlState_NAME;
/**
 * 获取设备状态
 */
@interface GetControlState : WifiDeviceAction

+ (GetControlState *)GetControlState:(uSDKManager *)manager DeviceManager:(uSDKDeviceManager *)deviceManager;

@end

NS_ASSUME_NONNULL_END
