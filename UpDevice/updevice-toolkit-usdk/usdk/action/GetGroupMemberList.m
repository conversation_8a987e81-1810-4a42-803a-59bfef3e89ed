//
//  GetGroupMemberList.m
//  UPDevice
//
//  Created by 冉东军 on 2021/4/6.
//  Copyright © 2021 海尔优家智能科技（北京）有限公司. All rights reserved.
//

#import "GetGroupMemberList.h"
#import "UPDeviceLog.h"
#import "UpDeviceBaseInfo.h"
#import "WifiDeviceHelper.h"

NSString *const GetGroupMemberList_NAME = @"GetGroupMemberListNAME";

@implementation GetGroupMemberList
+ (GetGroupMemberList *)GetGroupMemberList:(uSDKManager *)manager DeviceManager:(uSDKDeviceManager *)deviceManager
{
    return [[GetGroupMemberList alloc] initWith:manager DeviceManager:deviceManager];
}
- (instancetype)initWith:(uSDKManager *)manager DeviceManager:(uSDKDeviceManager *)deviceManager
{
    if (self = [super initWithName:GetGroupMemberList_NAME Manager:manager DeviceManager:deviceManager]) {
    }
    return self;
}

- (void)execute:(NSDictionary *)params finishBlock:(void (^)(UpDeviceResult *result))finishBlock
{
    NSString *deviceId = nil;
    if (params != nil) {
        deviceId = [self getParam:params Key:WifiDeviceAction_KEY_DEVICE_ID Class:[NSString class]];
    }

    if (deviceId == nil) {
        UpDeviceResult *result = [[UpDeviceResult alloc] initWithErrorCode:ErrorCode_FAILURE extraData:nil extraCode:nil extraInfo:@"deviceId 为空！"];
        UPDeviceLogError(@"%s[%d]GetGroupMemberList deviceId is nil", __PRETTY_FUNCTION__, __LINE__);
        if (finishBlock) {
            finishBlock(result);
        }
        return;
    }

    uSDKDevice *device = [self getDevice:deviceId];
    NSArray<uSDKDevice *> *groupMemberList = device.groupMembers;
    if (groupMemberList.count == 0) {
        UpDeviceResult *result = [UpDeviceResult UpDeviceResult:ErrorCode_FAILURE extraData:nil];
        UPDeviceLogError(@"%s[%d]device(%@)GetGroupMemberList fail！no groupMembers", __PRETTY_FUNCTION__, __LINE__, deviceId);
        if (finishBlock) {
            finishBlock(result);
        }
        return;
    }
    NSArray<id<UpDeviceBaseInfo>> *baseInfoList = [WifiDeviceHelper createDeviceBaseInfoList:groupMemberList];
    UpDeviceResult *result = [UpDeviceResult UpDeviceResult:ErrorCode_SUCCESS extraData:nil];
    result.extraData = baseInfoList;
    UPDeviceLogDebug(@"%s[%d]device(%@)GetGroupMemberList success！", __PRETTY_FUNCTION__, __LINE__, deviceId);
    if (finishBlock) {
        finishBlock(result);
    }
}
@end
