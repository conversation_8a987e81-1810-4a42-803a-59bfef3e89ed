//
//  ConnectRemoteDevices.m
//  UPDeviceDebugger
//
//  Created by f on 2/1/2019.
//  Copyright © 2019 海尔优家智能科技（北京）有限公司. All rights reserved.
//

#import "ConnectRemoteDevices.h"
#import "WifiDeviceToolkit.h"
#import "UpDeviceBaseInfo.h"
#import "WifiDeviceBaseInfo.h"
#import "UPDeviceLog.h"
#import "UpDeviceInjection.h"
NSString *const ConnectRemoteDevices_NAME = @"connectRemoteDevices";

@implementation ConnectRemoteDevices

+ (ConnectRemoteDevices *)ConnectRemoteDevices:(uSDKManager *)manager DeviceManager:(uSDKDeviceManager *)deviceManager
{
    return [[ConnectRemoteDevices alloc] initWith:manager DeviceManager:deviceManager];
}

- (instancetype)initWith:(uSDKManager *)manager DeviceManager:(uSDKDeviceManager *)deviceManager
{
    if (self = [super initWithName:ConnectRemoteDevices_NAME Manager:manager DeviceManager:deviceManager]) {
    }

    return self;
}

- (void)execute:(NSDictionary *)params finishBlock:(void (^)(UpDeviceResult *result))finishBlock
{
    NSString *accessToken = nil;
    NSString *userId = nil;

    if (params != nil) {
        accessToken = [self getParam:params Key:WifiDeviceToolkit_KEY_ACCESS_TOKEN Class:[NSString class]];
        userId = [self getParam:params Key:WifiDeviceToolkit_KEY_USERID Class:[NSString class]];
    }
    if (accessToken == nil || userId == nil) {
        UpDeviceResult *result = [[UpDeviceResult alloc] initWithErrorCode:ErrorCode_FAILURE extraData:nil extraCode:@"远程登录参数错误！" extraInfo:nil];
        UPDeviceLogError(@"%s[%d]connectRemoteDevices parameter error！accessToken:%@,userId:%@ result:%@", __PRETTY_FUNCTION__, __LINE__, accessToken, userId, result.extraInfo);
        if (finishBlock) {
            finishBlock(result);
        }
        return;
    }
    uSDKUserInfo *userInfo = [uSDKUserInfo new];
    userInfo.userToken = accessToken;
    userInfo.userID = userId;
    NSError *error;
    BOOL setUserInfoResult = [[self getDeviceManager] setUserInfo:userInfo error:&error];
    if (setUserInfoResult) {
        UpDeviceResult *result = [UpDeviceResult UpDeviceResult:ErrorCode_SUCCESS extraData:nil];
        UPDeviceLogInfo(@"%s[%d]connectRemoteDevices call usdk interface success!", __PRETTY_FUNCTION__, __LINE__);
        if (finishBlock) {
            finishBlock(result);
        }
    }
    else {
        UpDeviceResult *result = [[UpDeviceResult alloc] initWithErrorCode:ErrorCode_FAILURE extraData:nil extraCode:error.description extraInfo:nil];
        UPDeviceLogInfo(@"%s[%d]connectRemoteDevices call usdk interface fail！error:%@", __PRETTY_FUNCTION__, __LINE__, error.description);
        if (finishBlock) {
            finishBlock(result);
        }
    }
}

@end
