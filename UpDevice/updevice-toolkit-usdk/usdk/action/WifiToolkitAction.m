//
//  WifiToolkitAction.m
//  UPDeviceDebugger
//
//  Created by f on 2/1/2019.
//  Copyright © 2019 海尔优家智能科技（北京）有限公司. All rights reserved.
//

#import "WifiToolkitAction.h"

NSString *const WifiToolkitAction_KEY_TIMEOUT = @"usdk-timeout";
NSTimeInterval const TIMEOUT_DEFAULT_GROUPDEVICE = 90;
NSTimeInterval const TIMEOUT_ADD_GROUPDEVICE = 180;

@interface WifiToolkitAction () {
    uSDKManager *_manager;
    uSDKDeviceManager *_deviceManager;
}

@end

@implementation WifiToolkitAction

- (instancetype)initWithName:(NSString *)name Manager:(uSDKManager *)manager DeviceManager:(uSDKDeviceManager *)deviceManager
{
    if (self = [super init]) {
        self.name = name;
        _manager = manager;
        _deviceManager = deviceManager;
    }

    return self;
}

- (uSDKManager *)getManager
{
    return _manager;
}

- (uSDKDeviceManager *)getDeviceManager
{
    return _deviceManager;
}

- (int)getTimeout:(NSDictionary<NSString *, id> *)params
{
    NSNumber *timeout = [self getParam:params Key:WifiToolkitAction_KEY_TIMEOUT Class:[NSNumber class]];
    if (timeout == nil) {
        return 0;
    }

    return 0;
}

@end
