//
//  StartBoardFOTA.h
//  UPDevice
//
//  Created by gump on 12/6/2019.
//  Copyright © 2019 海尔优家智能科技（北京）有限公司. All rights reserved.
//

#import <Foundation/Foundation.h>
#import "WifiDeviceAction.h"
#import <uSDK/uSDK.h>

extern NSString *const StartBoardFOTA_NAME;

/*
 *开始设备底板固件的升级
 */
@interface StartBoardFOTA : WifiDeviceAction

+ (StartBoardFOTA *)StartBoardFOTA:(uSDKManager *)manager DeviceManager:(uSDKDeviceManager *)deviceManager;

- (instancetype)initWith:(uSDKManager *)manager Devi<PERSON>Manager:(uSDKDeviceManager *)deviceManager;

@end
