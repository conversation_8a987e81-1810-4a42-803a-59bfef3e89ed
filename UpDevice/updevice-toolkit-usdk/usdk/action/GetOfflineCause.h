//
//  GetOfflineCause.h
//  UPDevice
//
//  Created by pc on 2023/11/10.
//  Copyright © 2023 海尔优家智能科技（北京）有限公司. All rights reserved.
//

#import "WifiDeviceAction.h"
/*
 *从usdk获取设备离线原因
 *@since v7.1.1
 */
NS_ASSUME_NONNULL_BEGIN
extern NSString *const GetOfflineCause_NAME;
@interface GetOfflineCause : WifiDeviceAction
+ (GetOfflineCause *)getDeviceOfflineCause:(uSDKManager *)manager DeviceManager:(uSDKDeviceManager *)deviceManager;
@end

NS_ASSUME_NONNULL_END
