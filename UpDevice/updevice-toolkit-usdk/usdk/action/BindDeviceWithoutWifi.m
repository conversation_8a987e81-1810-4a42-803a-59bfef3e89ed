//
//  BindDeviceWithoutWifi.m
//  UPDevice
//
//  Created by MAC on 2021/9/29.
//  Copyright © 2021 海尔优家智能科技（北京）有限公司. All rights reserved.
//

#import "BindDeviceWithoutWifi.h"
#import "UPDeviceLog.h"
#import "WifiDeviceHelper.h"
#import <uSDK/uSDKBinding.h>

NSString *const BindDeviceWithoutWifi_NAME = @"BindDeviceWithoutWifi";
NSString *const BIND_WITHOUT_WIFI_LISTENER = @"bind-without-wifi-listener";

@implementation BindDeviceWithoutWifi

+ (BindDeviceWithoutWifi *)BindDeviceWithoutWifi:(uSDKManager *)manager DeviceManager:(uSDKDeviceManager *)deviceManager
{
    return [[BindDeviceWithoutWifi alloc] initWith:manager DeviceManager:deviceManager];
}

- (instancetype)initWith:(uSDKManager *)manager DeviceManager:(uSDKDeviceManager *)deviceManager
{
    if (self = [super initWithName:BindDeviceWithoutWifi_NAME Manager:manager DeviceManager:deviceManager]) {
    }
    return self;
}

- (void)execute:(NSDictionary *)params finishBlock:(void (^)(UpDeviceResult *result))finishBlock
{
    NSString *deviceId = nil;
    void (^progressBlock)(UpDeviceResult *result);
    if (params != nil) {
        deviceId = [self getParam:params Key:WifiDeviceAction_KEY_DEVICE_ID Class:[NSObject class]];
        progressBlock = [self getParam:params Key:BIND_WITHOUT_WIFI_LISTENER Class:[NSObject class]];
    }

    if (deviceId == nil) {
        UpDeviceResult *result = [UpDeviceResult UpDeviceResult:ErrorCode_FAILURE extraData:nil];
        UPDeviceLogError(@"%s[%d]devcie(%@) BindDeviceWithoutWifi fail!error:%@", __PRETTY_FUNCTION__, __LINE__, deviceId, result.extraInfo);
        if (finishBlock) {
            finishBlock(result);
        }
        return;
    }

    uSDKDeviceInfo *deviceInfo = [WifiDeviceHelper getUSDKDeviceInfo:deviceId];
    if (!deviceInfo) {
        UpDeviceResult *result = [UpDeviceResult UpDeviceResult:ErrorCode_FAILURE extraData:nil];
        UPDeviceLogError(@"%s[%d]devcie(%@)get BindDeviceWithoutWifi fail,no usdkDeviceInfo!error:%@", __PRETTY_FUNCTION__, __LINE__, deviceId, result.extraInfo);
        if (finishBlock) {
            finishBlock(result);
        }
        return;
    }
    uSDKWithoutWifiBindInfo *info = [uSDKWithoutWifiBindInfo new];
    info.deviceInfo = deviceInfo;
    info.timeoutInterval = 30;
    info.traceNodeCS = [uTraceNode new];

    [uSDKBinding bindDeviceWithoutWifi:info
        progressNotify:^(uSDKBindProgressInfo *bindProgressInfo) {

          NSString *bindProgress = @"";
          if (self) {
              bindProgress = [self transformBindProgressToString:bindProgressInfo.bindProgress];
          }
          NSDictionary *progressDic = @{ @"deviceId" : bindProgressInfo.deviceID,
                                         @"bindProgress" : bindProgress,
          };
          UPDeviceLogDebug(@"%s[%d]devcie(%@) BindDeviceWithoutWifi progress！progressDic:%@", __PRETTY_FUNCTION__, __LINE__, deviceId, progressDic);
          UpDeviceResult *result = [UpDeviceResult UpDeviceResult:ErrorCode_SUCCESS extraData:progressDic];
          if (progressBlock) {
              progressBlock(result);
          }
        }
        completionHandler:^(uSDKDevice *device, NSError *error) {
          UpDeviceResult *result;
          if (!error) {
              result = [UpDeviceResult UpDeviceResult:ErrorCode_SUCCESS extraData:nil];
              UPDeviceLogDebug(@"%s[%d]devcie(%@) BindDeviceWithoutWifi success！", __PRETTY_FUNCTION__, __LINE__, deviceId);
          }
          else {
              result = [[UpDeviceResult alloc] initWithErrorCode:ErrorCode_FAILURE extraData:nil extraCode:[NSString stringWithFormat:@"%ld", error.code] extraInfo:error.description];
              UPDeviceLogError(@"%s[%d]devcie(%@) BindDeviceWithoutWifi fail！error:%@", __PRETTY_FUNCTION__, __LINE__, deviceId, error.description);
          }
          if (finishBlock) {
              finishBlock(result);
          }
        }];
}

- (NSString *)transformBindProgressToString:(uSDKBindProgress)bindProgress
{
    switch (bindProgress) {
        case uSDKBindProgressConnectDevice:
            return @"connectDevice";
        case uSDKBindProgressSendConfigInfo:
            return @"progressSendConfigInfo";
        case uSDKBindProgressVerification:
            return @"progressVerification";
        case uSDKBindProgressBindDevice:
            return @"progressBindDevice";
        case uSDKBindProgressStartEnterNetworkingMode:
            return @"startEnterNetworkingMode";
        case uSDKBindProgressStartNetworking:
            return @"startNetworking";
        case uSDKBindProgressStartBindSlaveDevice:
            return @"startBindSlaveDevice";
        case uSDKBindProgressBindDeviceSuccess:
            return @"bindDeviceSuccess";
        case uSDKBindProgressConfigDeviceFailure:
            return @"configDeviceFailure";
        case uSDKBindProgressBindDeviceFailure:
            return @"bindDeviceFailure";
        default:
            return @"";
    }
}

@end
