//
//  GetDeviceAttribute.h
//  UPDeviceDebugger
//
//  Created by f on 2/1/2019.
//  Copyright © 2019 海尔优家智能科技（北京）有限公司. All rights reserved.
//

#import <Foundation/Foundation.h>
#import "WifiDeviceAction.h"
#import <uSDK/uSDK.h>

NS_ASSUME_NONNULL_BEGIN

extern NSString *const GetDeviceAttribute_NAME;
extern NSString *const GetDeviceAttribute_KEY_ATTR_NAME;

@interface GetDeviceAttribute : WifiDeviceAction

+ (GetDeviceAttribute *)GetDeviceAttribute:(uSDKManager *)manager Devi<PERSON><PERSON>anager:(uSDKDeviceManager *)deviceManager;

- (instancetype)initWith:(uSDKManager *)manager Devi<PERSON><PERSON>anager:(uSDKDeviceManager *)deviceManager;

@end

NS_ASSUME_NONNULL_END
