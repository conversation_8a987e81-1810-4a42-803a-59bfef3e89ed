//
//  GetConfigRouterInfo.h
//  UPDevice
//
//  Created by MAC on 2021/9/30.
//  Copyright © 2021 海尔优家智能科技（北京）有限公司. All rights reserved.
//

#import "WifiDeviceAction.h"

extern NSString *const GetConfigRouterInfo_NAME;

NS_ASSUME_NONNULL_BEGIN
/**
 * 获取已入网设备WiFi网络的路由器SSID和密码
 */
@interface GetConfigRouterInfo : WifiDeviceAction

+ (GetConfigRouterInfo *)GetConfigRouterInfo:(uSDKManager *)manager DeviceManager:(uSDKDeviceManager *)deviceManager;

@end

NS_ASSUME_NONNULL_END
