//
//  ExecuteCommand.m
//  UPDeviceDebugger
//
//  Created by f on 2/1/2019.
//  Copyright © 2019 海尔优家智能科技（北京）有限公司. All rights reserved.
//

#import "ExecuteCommand.h"
#import "UpDeviceCommand.h"
#import "WifiDeviceHelper.h"
#import "UPDeviceLog.h"

NSString *const ExecuteCommand_NAME = @"executeDeviceCommand";
NSString *const ExecuteCommand_KEY_DEVICE_COMMAND = @"usdk-device-command";

@implementation ExecuteCommand

+ (ExecuteCommand *)ExecuteCommand:(uSDKManager *)manager DeviceManager:(uSDKDeviceManager *)deviceManager
{
    return [[ExecuteCommand alloc] initWith:manager DeviceManager:deviceManager];
}

- (instancetype)initWith:(uSDKManager *)manager DeviceManager:(uSDKDeviceManager *)deviceManager
{
    if (self = [super initWithName:ExecuteCommand_NAME Manager:manager DeviceManager:deviceManager]) {
    }
    return self;
}

- (void)execute:(NSDictionary *)params finishBlock:(void (^)(UpDeviceResult *result))finishBlock
{
    NSString *deviceId = nil;
    id<UpDeviceCommand> command = nil;

    if (params != nil) {
        deviceId = [self getParam:params Key:WifiDeviceAction_KEY_DEVICE_ID Class:[NSString class]];
        command = [self getParam:params Key:ExecuteCommand_KEY_DEVICE_COMMAND Class:[NSObject class]];
    }
    if (deviceId == nil || command == nil) {
        UpDeviceResult *result = [[UpDeviceResult alloc] initWithErrorCode:ErrorCode_FAILURE extraData:nil extraCode:nil extraInfo:@"deviceId or command为空！"];
        UPDeviceLogError(@"%s[%d]设备命令执行失败!deviceId:%@,command:%@,error:%@", __PRETTY_FUNCTION__, __LINE__, deviceId, command, result.extraInfo);
        if (finishBlock) {
            finishBlock(result);
        }
        return;
    }

    //int timeout = [self getTimeout:params];
    uSDKDevice *device = [self getDevice:deviceId];
    NSString *groupName = [command groupName];
    NSDictionary<NSString *, NSString *> *attributes = [command attributes];

    if (groupName == nil && attributes.count != 0) {
        NSString *name = nil;
        NSString *value = nil;

        for (NSString *key in attributes.allKeys) {
            name = key;
            value = attributes[key];
            if (name && value) {
                break;
            }
        }
        [device writeAttributeWithName:name
            value:value
            success:^{
              UpDeviceResult *result = [UpDeviceResult UpDeviceResult:ErrorCode_SUCCESS extraData:nil];
              UPDeviceLogDebug(@"%s[%d]device(%@)writeAttributeWithName success！name:%@", __PRETTY_FUNCTION__, __LINE__, deviceId, name);
              if (finishBlock) {
                  finishBlock(result);
              }
            }
            failure:^(NSError *error) {
              UpDeviceResult *result = [[UpDeviceResult alloc] initWithErrorCode:ErrorCode_FAILURE extraData:nil extraCode:error.description extraInfo:error.description];
              UPDeviceLogError(@"%s[%d]device(%@)writeAttributeWithName fail！name:%@ error:%@", __PRETTY_FUNCTION__, __LINE__, deviceId, name, error.description);
              if (finishBlock) {
                  finishBlock(result);
              }
            }];
    }
    else if (groupName == nil && attributes.count == 0) {
        UpDeviceResult *result = [[UpDeviceResult alloc] initWithErrorCode:ErrorCode_FAILURE extraData:nil extraCode:nil extraInfo:@"groupName & attributes 为空"];
        UPDeviceLogError(@"%s[%d]deivce(%@)writeAttributeWithName fail,error:groupName & attributes 为空", __PRETTY_FUNCTION__, __LINE__, deviceId);
        if (finishBlock) {
            finishBlock(result);
        }
    }
    else {
        NSArray<uSDKArgument *> *argumentList = [WifiDeviceHelper createArgumentList:attributes];
        [device executeOperation:groupName
            args:argumentList
            success:^{
              UpDeviceResult *result = [UpDeviceResult UpDeviceResult:ErrorCode_SUCCESS extraData:nil];
              UPDeviceLogDebug(@"%s[%d]device(%@)executeOperation success！name:%@,argumentList:%@", __PRETTY_FUNCTION__, __LINE__, deviceId, groupName, argumentList);
              if (finishBlock) {
                  finishBlock(result);
              }
            }
            failure:^(NSError *error) {

              UpDeviceResult *result = [[UpDeviceResult alloc] initWithErrorCode:ErrorCode_FAILURE extraData:nil extraCode:error.description extraInfo:nil];

              UPDeviceLogError(@"%s[%d]device(%@)executeOperation fail！name:%@,argumentList:%@,error:%@", __PRETTY_FUNCTION__, __LINE__, deviceId, groupName, argumentList, result.extraInfo);

              if (finishBlock) {
                  finishBlock(result);
              }
            }];
    }
}

@end
