//
//  GetDeviceList.m
//  UPDeviceDebugger
//
//  Created by f on 2/1/2019.
//  Copyright © 2019 海尔优家智能科技（北京）有限公司. All rights reserved.
//

#import "GetDeviceList.h"
#import "UpDeviceBaseInfo.h"
#import "WifiDeviceHelper.h"
#import "UPDeviceLog.h"

NSString *const GetDeviceList_NAME = @"getDeviceList";

@implementation GetDeviceList

+ (GetDeviceList *)GetDeviceList:(uSDKManager *)manager DeviceManager:(uSDKDeviceManager *)deviceManager
{
    return [[GetDeviceList alloc] initWith:manager DeviceManager:deviceManager];
}

- (instancetype)initWith:(uSDKManager *)manager DeviceManager:(uSDKDeviceManager *)deviceManager
{
    if (self = [super initWithName:GetDeviceList_NAME Manager:manager DeviceManager:deviceManager]) {
    }
    return self;
}

- (void)execute:(NSDictionary *)params finishBlock:(void (^)(UpDeviceResult *result))finishBlock
{
    NSArray<uSDKDevice *> *deviceList = [[self getDeviceManager] getDeviceList];
    NSMutableArray<id<UpDeviceBaseInfo>> *baseInfoList = [NSMutableArray array];
    if (deviceList == nil) {
        UpDeviceResult *result = [UpDeviceResult UpDeviceResult:ErrorCode_FAILURE extraData:nil];
        UPDeviceLogError(@"%s[%d]设备列表获取失败!error:%@", __PRETTY_FUNCTION__, __LINE__, result.extraInfo);
        if (finishBlock) {
            finishBlock(result);
        }
        return;
    }

    for (uSDKDevice *device in deviceList) {
        DeviceBaseInfo *baseInfo = [WifiDeviceHelper createDeviceBaseInfo:device];
        if (baseInfo) {
            [baseInfoList addObject:baseInfo];
        }
    }
    UpDeviceResult *result = [UpDeviceResult UpDeviceResult:ErrorCode_SUCCESS extraData:nil];

    result.extraData = baseInfoList;
    UPDeviceLogDebug(@"%s[%d]设备列表获取成功！", __PRETTY_FUNCTION__, __LINE__);
    if (finishBlock) {
        finishBlock(result);
    }
}

@end
