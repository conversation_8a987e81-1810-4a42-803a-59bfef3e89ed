//
//  GetDeviceAttribute.m
//  UPDeviceDebugger
//
//  Created by f on 2/1/2019.
//  Copyright © 2019 海尔优家智能科技（北京）有限公司. All rights reserved.
//

#import "GetDeviceAttribute.h"
#import "UPDeviceLog.h"

NSString *const GetDeviceAttribute_NAME = @"getDeviceAttribute";

NSString *const GetDeviceAttribute_KEY_ATTR_NAME = @"usdk-device-attr-name";

@implementation GetDeviceAttribute

+ (GetDeviceAttribute *)GetDeviceAttribute:(uSDKManager *)manager DeviceManager:(uSDKDeviceManager *)deviceManager
{
    return [[GetDeviceAttribute alloc] initWith:manager DeviceManager:deviceManager];
}

- (instancetype)initWith:(uSDKManager *)manager DeviceManager:(uSDKDeviceManager *)deviceManager
{
    if (self = [super initWithName:GetDeviceAttribute_NAME Manager:manager DeviceManager:deviceManager]) {
    }
    return self;
}

- (void)execute:(NSDictionary *)params finishBlock:(void (^)(UpDeviceResult *result))finishBlock
{
    NSString *deviceId = nil;
    NSString *attrName = nil;
    if (params != nil) {
        deviceId = [self getParam:params Key:WifiDeviceAction_KEY_DEVICE_ID Class:[NSObject class]];
        attrName = [self getParam:params Key:GetDeviceAttribute_KEY_ATTR_NAME Class:[NSObject class]];
    }

    if (deviceId == nil || attrName == nil) {
        UpDeviceResult *result = [UpDeviceResult UpDeviceResult:ErrorCode_FAILURE extraData:nil];
        UPDeviceLogError(@"%s[%d]读取设备的属性失败!deviceId:%@,attrName:%@,error:%@", __PRETTY_FUNCTION__, __LINE__, deviceId, attrName, result.extraInfo);
        if (finishBlock) {
            finishBlock(result);
        }
        return;
    }
    int timeout = [self getTimeout:params];
    uSDKDevice *device = [self getDevice:deviceId];
    [device readAttributeWithName:attrName
        timeoutInterval:timeout
        success:^(NSString *value) {
          UpDeviceResult *result = [UpDeviceResult UpDeviceResult:ErrorCode_SUCCESS extraData:nil];
          UPDeviceLogDebug(@"%s[%d]读取设备的属性成功！", __PRETTY_FUNCTION__, __LINE__);
          if (finishBlock) {
              finishBlock(result);
          }
        }
        failure:^(NSError *error) {
          UpDeviceResult *result = [[UpDeviceResult alloc] initWithErrorCode:ErrorCode_FAILURE extraData:nil extraCode:error.description extraInfo:nil];
          UPDeviceLogError(@"%s[%d]读取设备的属性失败！error:%@", __PRETTY_FUNCTION__, __LINE__, error.description);
          if (finishBlock) {
              finishBlock(result);
          }
        }];
}

@end
