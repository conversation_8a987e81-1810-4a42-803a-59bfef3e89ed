//
//  DisconnectDevice.h
//  UPDevice
//
//  Created by MAC on 2021/10/13.
//  Copyright © 2021 海尔优家智能科技（北京）有限公司. All rights reserved.
//

#import "WifiDeviceAction.h"

NS_ASSUME_NONNULL_BEGIN

extern NSString *const DisconnectDevice_NAME;

@interface DisconnectDevice : WifiDeviceAction

+ (DisconnectDevice *)DisconnectDevice:(uSDKManager *)manager DeviceManager:(uSDKDeviceManager *)deviceManager;

@end

NS_ASSUME_NONNULL_END
