//
//  GetNetworkQuality.m
//  UPDevice
//
//  Created by gump on 7/8/2020.
//  Copyright © 2020 海尔优家智能科技（北京）有限公司. All rights reserved.
//

#import "GetNetworkQuality.h"
#import "UPDeviceLog.h"
#import "DeviceNetWorkQualityInfo.h"

NSString *const GetNetworkQuality_NAME = @"GetNetworkQuality";

@implementation GetNetworkQuality

+ (GetNetworkQuality *)GetNetworkQuality:(uSDKManager *)manager DeviceManager:(uSDKDeviceManager *)deviceManager
{
    return [[GetNetworkQuality alloc] initWith:manager DeviceManager:deviceManager];
}

- (instancetype)initWith:(uSDKManager *)manager DeviceManager:(uSDKDeviceManager *)deviceManager
{
    if (self = [super initWithName:GetNetworkQuality_NAME Manager:manager DeviceManager:deviceManager]) {
    }
    return self;
}

- (void)execute:(NSDictionary *)params finishBlock:(void (^)(UpDeviceResult *result))finishBlock
{
    NSString *deviceId = nil;
    if (params != nil) {
        deviceId = [self getParam:params Key:WifiDeviceAction_KEY_DEVICE_ID Class:[NSString class]];
    }
    if (deviceId == nil) {
        UpDeviceResult *result = [[UpDeviceResult alloc] initWithErrorCode:ErrorCode_FAILURE extraData:nil extraCode:nil extraInfo:@"deviceId is empty！"];
        UPDeviceLogError(@"%s[%d]device command fail!deviceId:%@,error:%@", __PRETTY_FUNCTION__, __LINE__, deviceId, result.extraInfo);
        if (finishBlock) {
            finishBlock(result);
        }
        return;
    }

    uSDKDevice *device = [self getDevice:deviceId];
    [device getNetworkQualityV2Success:^(uSDKNetworkQualityInfoV2 *networkQuality) {
      if (!networkQuality) {
          UpDeviceResult *result = [[UpDeviceResult alloc] initWithErrorCode:ErrorCode_FAILURE extraData:nil extraCode:nil extraInfo:@"networkQuality is empty"];
          UPDeviceLogError(@"%s[%d]get net work quality fail！networkQuality is empty. deviceId:%@", __PRETTY_FUNCTION__, __LINE__, deviceId);
          if (finishBlock) {
              finishBlock(result);
          }
          return;
      }

      DeviceNetWorkQualityInfo *info = [DeviceNetWorkQualityInfo DeviceNetWorkQualityInfo];
      info.connectStatus = (int)networkQuality.connectStatus;
      info.machineId = networkQuality.machineId;
      info.isOnLine = networkQuality.isOnLine;
      info.statusLastChangeTime = networkQuality.statusLastChangeTime;
      info.netType = networkQuality.netType;
      info.ssid = networkQuality.ssid;
      info.rssi = networkQuality.rssi;
      info.prssi = networkQuality.prssi;
      info.signalLevel = networkQuality.signalLevel;
      info.ilostRatio = networkQuality.ilostRatio;
      info.its = networkQuality.its;
      info.lanIP = networkQuality.lanIP;
      info.moduleVersion = networkQuality.moduleVersion;

      UpDeviceResult *result = [UpDeviceResult UpDeviceResult:ErrorCode_SUCCESS extraData:info];
      UPDeviceLogDebug(@"%s[%d]get net work quality success！", __PRETTY_FUNCTION__, __LINE__);
      if (finishBlock) {
          finishBlock(result);
      }
    }
        failure:^(NSError *error) {
          UpDeviceResult *result = [[UpDeviceResult alloc] initWithErrorCode:ErrorCode_FAILURE extraData:nil extraCode:error.description extraInfo:error.description];
          UPDeviceLogError(@"%s[%d]get net work quality fail！error:%@ deviceId:%@", __PRETTY_FUNCTION__, __LINE__, error.description, deviceId);
          if (finishBlock) {
              finishBlock(result);
          }
        }];
}
@end
