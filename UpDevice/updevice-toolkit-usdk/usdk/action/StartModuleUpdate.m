//
//  StartModuleUpdate.m
//  UPDevice
//
//  Created by gump on 20/8/2019.
//  Copyright © 2019 海尔优家智能科技（北京）有限公司. All rights reserved.
//

#import "StartModuleUpdate.h"
#import "UPDeviceLog.h"
#import "DeviceOTAStatusInfo.h"
#import <SystemConfiguration/CaptiveNetwork.h>

NSString *const StartModuleUpdate_NAME = @"StartModuleUpdateNAME";

@implementation StartModuleUpdate

+ (StartModuleUpdate *)StartModuleUpdate:(uSDKManager *)manager
                           DeviceManager:(uSDKDeviceManager *)deviceManager
{
    return [[StartModuleUpdate alloc] initWith:manager DeviceManager:deviceManager];
}

- (instancetype)initWith:(uSDKManager *)manager
           DeviceManager:(uSDKDeviceManager *)deviceManager
{
    if (self = [super initWithName:StartModuleUpdate_NAME
                           Manager:manager
                     DeviceManager:deviceManager]) {
    }
    return self;
}

- (void)execute:(NSDictionary *)params
    finishBlock:(void (^)(UpDeviceResult *result))finishBlock
{
    NSString *deviceId = nil;
    if (params != nil) {
        deviceId = [self getParam:params
                              Key:WifiDeviceAction_KEY_DEVICE_ID
                            Class:[NSString class]];
    }
    if (deviceId == nil) {
        UpDeviceResult *result =
            [[UpDeviceResult alloc] initWithErrorCode:ErrorCode_FAILURE
                                            extraData:nil
                                            extraCode:nil
                                            extraInfo:@"deviceId 为空！"];
        UPDeviceLogError(@"%s[%d]StartModuleUpdate deviceId is nil",
                         __PRETTY_FUNCTION__, __LINE__);
        if (finishBlock) {
            finishBlock(result);
        }
        return;
    }

    uSDKDevice *device = [self getDevice:deviceId];
    NSDate *nowDate = [NSDate date];
    NSTimeInterval startTime = [nowDate timeIntervalSince1970];
    __weak typeof(self) weakSelf = self;
    [device moduleOTAWithProgress:^(uSDKOTAStatusInfo *upgradeStatus) {
      DeviceOTAStatusInfo *info = [DeviceOTAStatusInfo
          DeviceOTAStatusInfo:(UPDeviceOTAStage)upgradeStatus.upgradeStatus
              upgradeProgress:upgradeStatus.upgradeProgress];
      UpDeviceResult *result =
          [UpDeviceResult UpDeviceResult:ErrorCode_SUCCESS
                               extraData:info];
      UPDeviceLogDebug(@"%s[%d]设备OTA升级开始！deviceId:%@ 状态:%ld 进度:%f",
                       __PRETTY_FUNCTION__, __LINE__, deviceId,
                       upgradeStatus.upgradeStatus,
                       upgradeStatus.upgradeProgress);
      if (finishBlock) {
          finishBlock(result);
      }
    }
        success:^() {
          UpDeviceResult *result =
              [UpDeviceResult UpDeviceResult:ErrorCode_SUCCESS
                                   extraData:nil];
          UPDeviceLogDebug(@"%s[%d]设备OTA升级成功！deviceId:%@",
                           __PRETTY_FUNCTION__, __LINE__, deviceId);
          if (finishBlock) {
              finishBlock(result);
          }
          NSTimeInterval spantime = [weakSelf spanTime:startTime];
          [weakSelf traceIot:device code:@"success" spanTime:spantime];

        }
        failure:^(NSError *error) {
          UpDeviceResult *result =
              [[UpDeviceResult alloc] initWithErrorCode:ErrorCode_FAILURE
                                              extraData:nil
                                              extraCode:error.description
                                              extraInfo:error.description];
          UPDeviceLogError(@"%s[%d]设备OTA升级失败！error:%@ deviceId:%@",
                           __PRETTY_FUNCTION__, __LINE__, error.description,
                           deviceId);
          if (finishBlock) {
              finishBlock(result);
          }
          NSTimeInterval spantime = [weakSelf spanTime:startTime];
          [weakSelf traceIot:device
                        code:[NSString stringWithFormat:@"%ld", (long)error.code]
                    spanTime:spantime];
        }];
}
- (NSTimeInterval)spanTime:(NSTimeInterval)oldTime
{
    NSDate *nowDate = [NSDate date];
    NSTimeInterval now = [nowDate timeIntervalSince1970];
    NSTimeInterval spaTime = now - oldTime;
    return spaTime;
}
- (void)traceIot:(uSDKDevice *)device
            code:(NSString *)code
        spanTime:(NSTimeInterval)spanTime
{
    uTrace *trace = [uTrace createDITrace];
    uTraceNodeDI *traceNode = [uTraceNodeDI new];
    traceNode.bId = @"common";
    traceNode.bName = @"upgrade"; // bname
    traceNode.dId = device.deviceID;
    traceNode.code = code; //升级结果
    traceNode.span = spanTime; //升级耗时
    traceNode.prot = @"wifi"; //
    traceNode.args = [self getArgs:device];
    NSError *error;
    [trace addTraceNodeDI:traceNode error:&error];
    if (error) {
        UPDeviceLogError(@"traceIot！upgrade埋点添加失败：error:%@", error);
    }
}
- (NSMutableDictionary *)getArgs:(uSDKDevice *)device
{
    NSMutableDictionary *args = [NSMutableDictionary dictionary];
    NSString *deviceName = [[UIDevice currentDevice] systemName];
    NSString *phoneVersion = [[UIDevice currentDevice] systemVersion];
    args[@"mdl"] = deviceName; //手机品牌和型号
    args[@"mver"] = phoneVersion; //手机系统版本
    args[@"typeid"] = device.uplusID; // typeid
    args[@"ssid"] = [self ssid]; //网络名称
    args[@"hver"] = device.smartLinkHardwareVersion; //硬件版本
    args[@"curver"] = device.smartLinkSoftwareVersion; //升级前版本
    args[@"destver"] = device.smartLinkSoftwareVersion; //目标版本
    args[@"wifi"] = @"prot";
    return args;
}
- (NSString *)ssid
{
    NSArray *ifs = (__bridge id)CNCopySupportedInterfaces();
    id info = nil;
    for (NSString *ifnam in ifs) {
        info = (__bridge id)CNCopyCurrentNetworkInfo((__bridge CFStringRef)ifnam);
        if (info) {
            break;
        }
    }
    NSDictionary *dctySSID = (NSDictionary *)info;
    return [dctySSID objectForKey:@"SSID"];
}
@end
