//
//  FetchBLEHistoryData.m
//  UPDevice
//
//  Created by gump on 27/8/2019.
//  Copyright © 2019 海尔优家智能科技（北京）有限公司. All rights reserved.
//

#import "FetchBLEHistoryData.h"
#import "UPDeviceLog.h"

NSString *const FetchBLEHistoryData_NAME = @"FetchBLEHistoryDataNAME";

@implementation FetchBLEHistoryData

+ (FetchBLEHistoryData *)FetchBLEHistoryData:(uSDKManager *)manager DeviceManager:(uSDKDeviceManager *)deviceManager
{
    return [[FetchBLEHistoryData alloc] initWith:manager DeviceManager:deviceManager];
}

- (instancetype)initWith:(uSDKManager *)manager DeviceManager:(uSDKDeviceManager *)deviceManager
{
    if (self = [super initWithName:FetchBLEHistoryData_NAME Manager:manager DeviceManager:deviceManager]) {
    }
    return self;
}

- (void)execute:(NSDictionary *)params finishBlock:(void (^)(UpDeviceResult *result))finishBlock
{
    NSString *deviceId = nil;
    if (params != nil) {
        deviceId = [self getParam:params Key:WifiDeviceAction_KEY_DEVICE_ID Class:[NSString class]];
    }
    if (deviceId == nil) {
        UpDeviceResult *result = [[UpDeviceResult alloc] initWithErrorCode:ErrorCode_FAILURE extraData:nil extraCode:nil extraInfo:@"deviceId 为空！"];
        UPDeviceLogError(@"%s[%d]设备命令执行失败!deviceId:%@,error:%@", __PRETTY_FUNCTION__, __LINE__, deviceId, result.extraInfo);
        if (finishBlock) {
            finishBlock(result);
        }
        return;
    }

    uSDKDevice *device = [self getDevice:deviceId];
    [device fetchBLEHistoryDataSuccess:^() {
      UpDeviceResult *result = [UpDeviceResult UpDeviceResult:ErrorCode_SUCCESS extraData:nil];
      UPDeviceLogDebug(@"%s[%d]获取蓝牙历史数据成功！deviceId:%@", __PRETTY_FUNCTION__, __LINE__, deviceId);
      if (finishBlock) {
          finishBlock(result);
      }
    }
        failure:^(NSError *error) {
          UpDeviceResult *result = [[UpDeviceResult alloc] initWithErrorCode:ErrorCode_FAILURE extraData:nil extraCode:error.description extraInfo:error.description];
          UPDeviceLogError(@"%s[%d]获取蓝牙历史数据失败！error:%@ deviceId:%@", __PRETTY_FUNCTION__, __LINE__, error.description, deviceId);
          if (finishBlock) {
              finishBlock(result);
          }
        }];
}

@end
