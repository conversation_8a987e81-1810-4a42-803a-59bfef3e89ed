//
//  AttachToolkit.h
//  UPDeviceDebugger
//
//  Created by f on 2/1/2019.
//  Copyright © 2019 海尔优家智能科技（北京）有限公司. All rights reserved.
//

#import <Foundation/Foundation.h>
#import "WifiToolkitAction.h"
#import <uSDK/uSDK.h>

NS_ASSUME_NONNULL_BEGIN

extern NSString *const AttachToolkit_NAME;

extern NSString *const AttachToolkit_KEY_MANAGER_LISTENER;
extern NSString *const AttachToolkit_KEY_DEVICE_MANAGER_LISTENER;
extern NSString *const AttachToolkit_APP_ID;
extern NSString *const AttachToolkit_APP_KEY;
extern NSString *const AttachToolkit_SECRET_KEY;
extern NSString *const AttachToolkit_AREA_KEY;
extern NSString *const AttachToolkit_FEATURES_KEY;
extern NSString *const AttachToolkit_ENABLE_BLE_KEY;
extern NSString *const AttachToolkit_ENABLE_WIFI_KEY;

@interface AttachToolkit : WifiToolkitAction

+ (AttachToolkit *)AttachToolkit:(uSDKManager *)manager DeviceManager:(uSDKDeviceManager *)deviceManager;

- (instancetype)initWith:(uSDKManager *)manager DeviceManager:(uSDKDeviceManager *)deviceManager;


@end

NS_ASSUME_NONNULL_END
