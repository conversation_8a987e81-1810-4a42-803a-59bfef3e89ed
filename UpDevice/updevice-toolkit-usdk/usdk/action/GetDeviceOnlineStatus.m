//
//  GetDeviceOnlineStatus.m
//  UPDevice
//
//  Created by gump on 2021/9/6.
//  Copyright © 2021 海尔优家智能科技（北京）有限公司. All rights reserved.
//

#import "GetDeviceOnlineStatus.h"
#import "WifiDeviceHelper.h"
#import "UPDeviceLog.h"

NSString *const GetDeviceOnlineStatus_NAME = @"GetDeviceOnlineStatus";

@implementation GetDeviceOnlineStatus

+ (GetDeviceOnlineStatus *)GetDeviceOnlineStatus:(uSDKManager *)manager DeviceManager:(uSDKDeviceManager *)deviceManager
{
    return [[GetDeviceOnlineStatus alloc] initWith:manager DeviceManager:deviceManager];
}

- (instancetype)initWith:(uSDKManager *)manager DeviceManager:(uSDKDeviceManager *)deviceManager
{
    if (self = [super initWithName:GetDeviceOnlineStatus_NAME Manager:manager DeviceManager:deviceManager]) {
    }
    return self;
}

- (void)execute:(NSDictionary *)params finishBlock:(void (^)(UpDeviceResult *result))finishBlock
{
    NSString *deviceId = nil;
    if (params != nil) {
        deviceId = [self getParam:params Key:WifiDeviceAction_KEY_DEVICE_ID Class:[NSObject class]];
    }

    if (deviceId == nil) {
        UpDeviceResult *result = [UpDeviceResult UpDeviceResult:ErrorCode_FAILURE extraData:nil];
        UPDeviceLogError(@"%s[%d]device(%@)GetDeviceOnlineStatus!error:%@", __PRETTY_FUNCTION__, __LINE__, deviceId, result.extraInfo);
        if (finishBlock) {
            finishBlock(result);
        }
        return;
    }

    uSDKDevice *device = [self getDevice:deviceId];
    UpDeviceResult *result = [UpDeviceResult UpDeviceResult:ErrorCode_SUCCESS extraData:nil];
    UpDeviceRealOnline onlineStatus = [WifiDeviceHelper paresOnlineState:device.onlineState];
    result.extraData = [NSNumber numberWithInt:(int)onlineStatus];
    UPDeviceLogDebug(@"%s[%d]device(%@)GetDeviceOnlineStatus success！status:%lu", __PRETTY_FUNCTION__, __LINE__, deviceId, device.onlineState);
    if (finishBlock) {
        finishBlock(result);
    }
}

@end
