//
//  StartWashFota.h
//  UPDevice
//
//  Created by gump on 8/6/2021.
//  Copyright © 2021 海尔优家智能科技（北京）有限公司. All rights reserved.
//

#import "WifiDeviceAction.h"
#import <uSDK/uSDK.h>

extern NSString *_Nonnull const StartWashFota_NAME;
static NSString *_Nonnull const KEY_TRACE_ID = @"trace-id";
static NSString *_Nonnull const KEY_FIRM_WARE_ID = @"firm-ware-id";

NS_ASSUME_NONNULL_BEGIN
/*
 *开始设备固件升级接口
 */
@interface StartWashFota : WifiDeviceAction

+ (StartWashFota *)StartWashFota:(uSDKManager *)manager DeviceManager:(uSDKDeviceManager *)deviceManager;

@end

NS_ASSUME_NONNULL_END
