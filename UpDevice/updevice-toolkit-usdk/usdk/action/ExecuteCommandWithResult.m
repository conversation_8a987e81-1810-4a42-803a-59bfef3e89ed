//
//  ExecuteCommandWithResult.m
//  UPDevice
//
//  Created by pc on 2024/2/23.
//  Copyright © 2024 海尔优家智能科技（北京）有限公司. All rights reserved.
//

#import "ExecuteCommandWithResult.h"
#import "UpDeviceCommand.h"
#import "WifiDeviceHelper.h"
#import "UPDeviceLog.h"
#import "ExecuteCommand.h"

NSString *const ExecuteCommandWithResult_NAME = @"executeDeviceCommandWithResult";

@implementation ExecuteCommandWithResult

+ (ExecuteCommandWithResult *)ExecuteCommandWithResult:(uSDKManager *)manager DeviceManager:(uSDKDeviceManager *)deviceManager
{
    return [[ExecuteCommandWithResult alloc] initWith:manager DeviceManager:deviceManager];
}

- (instancetype)initWith:(uSDKManager *)manager DeviceManager:(uSDKDeviceManager *)deviceManager
{
    if (self = [super initWithName:ExecuteCommandWithResult_NAME Manager:manager DeviceManager:deviceManager]) {
    }
    return self;
}

- (void)execute:(NSDictionary *)params finishBlock:(void (^)(UpDeviceResult *result))finishBlock
{
    NSString *deviceId = nil;
    id<UpDeviceCommand> command = nil;

    if (params != nil) {
        deviceId = [self getParam:params Key:WifiDeviceAction_KEY_DEVICE_ID Class:[NSString class]];
        command = [self getParam:params Key:ExecuteCommand_KEY_DEVICE_COMMAND Class:[NSObject class]];
    }
    if (nil == deviceId || nil == command) {
        UpDeviceResult *result = [[UpDeviceResult alloc] initWithErrorCode:ErrorCode_FAILURE extraData:nil extraCode:nil extraInfo:@"deviceId or command is empty！"];
        UPDeviceLogError(@"%s[%d]command fail!deviceId:%@,command:%@,error:%@", __PRETTY_FUNCTION__, __LINE__, deviceId, command, result.extraInfo);
        if (finishBlock) {
            finishBlock(result);
        }
        return;
    }
    int timeout = [self getTimeoutValue:params];
    uSDKDevice *device = [self getDevice:deviceId];
    NSString *groupName = [command groupName];
    NSDictionary<NSString *, NSString *> *attributes = [command attributes];
    long startMilliseconds = [self getCurrentTimestamp];
    uTrace *currentTrace = [uTrace createTraceWithBusinessID:@"app-operate"];
    __weak typeof(self) weakSelf = self;
    if (groupName == nil && attributes.count != 0) {
        NSString *name = nil;
        NSString *value = nil;
        for (NSString *key in attributes.allKeys) {
            name = key;
            value = attributes[key];
            if (name && value) {
                break;
            }
        }
        [device writeAttributeWithName:name
            value:value
            uTrace:currentTrace
            timeoutInterval:timeout
            success:^{
              long endMilliseconds = [weakSelf getCurrentTimestamp];
              NSString *strData = [weakSelf generateResultString:currentTrace.traceID startTime:startMilliseconds endTime:endMilliseconds];
              UpDeviceResult *result = [UpDeviceResult UpDeviceResult:ErrorCode_SUCCESS extraData:strData];
              UPDeviceLogDebug(@"%s[%d]device(%@)writeAttributeWithName success！name:%@", __PRETTY_FUNCTION__, __LINE__, deviceId, name);
              if (finishBlock) {
                  finishBlock(result);
              }
            }
            failure:^(NSError *error) {
              long endMilliseconds = [weakSelf getCurrentTimestamp];
              NSString *strData = [weakSelf generateResultString:currentTrace.traceID startTime:startMilliseconds endTime:endMilliseconds];
              UpDeviceResult *result = [[UpDeviceResult alloc] initWithErrorCode:ErrorCode_FAILURE extraData:strData extraCode:error.description extraInfo:error.description];
              UPDeviceLogError(@"%s[%d]device(%@)writeAttributeWithName fail！name:%@ error:%@", __PRETTY_FUNCTION__, __LINE__, deviceId, name, error.description);
              if (finishBlock) {
                  finishBlock(result);
              }
            }];
    }
    else if (nil == groupName && 0 == attributes.count) {
        UpDeviceResult *result = [[UpDeviceResult alloc] initWithErrorCode:ErrorCode_FAILURE extraData:@"groupName & attributes isEmpty" extraCode:[NSString stringWithFormat:@"%ld", ErrorCode_FAILURE] extraInfo:@"groupName & attributes isEmpty"];
        UPDeviceLogError(@"%s[%d]deivce(%@)writeAttributeWithName fail,error:groupName & attributes isEmpty", __PRETTY_FUNCTION__, __LINE__, deviceId);
        if (finishBlock) {
            finishBlock(result);
        }
    }
    else {
        NSArray<uSDKArgument *> *argumentList = [WifiDeviceHelper createArgumentList:attributes];
        [device executeOperation:groupName
            args:argumentList
            uTrace:currentTrace
            timeoutInterval:timeout
            success:^{
              long endMilliseconds = [weakSelf getCurrentTimestamp];
              NSString *strData = [weakSelf generateResultString:currentTrace.traceID startTime:startMilliseconds endTime:endMilliseconds];
              UpDeviceResult *result = [UpDeviceResult UpDeviceResult:ErrorCode_SUCCESS extraData:strData];
              UPDeviceLogDebug(@"%s[%d]device(%@)executeOperation success！name:%@,argumentList:%@", __PRETTY_FUNCTION__, __LINE__, deviceId, groupName, argumentList);
              if (finishBlock) {
                  finishBlock(result);
              }
            }
            failure:^(NSError *error) {
              long endMilliseconds = [weakSelf getCurrentTimestamp];
              NSString *strData = [weakSelf generateResultString:currentTrace.traceID startTime:startMilliseconds endTime:endMilliseconds];
              UpDeviceResult *result = [[UpDeviceResult alloc] initWithErrorCode:ErrorCode_FAILURE extraData:strData extraCode:error.description extraInfo:error.description];
              UPDeviceLogError(@"%s[%d]device(%@)executeOperation fail！name:%@,argumentList:%@,error:%@", __PRETTY_FUNCTION__, __LINE__, deviceId, groupName, argumentList, result.extraInfo);

              if (finishBlock) {
                  finishBlock(result);
              }
            }];
    }
}

- (long)getCurrentTimestamp
{
    NSDate *now = [NSDate date];
    NSTimeInterval secondsSince1970 = [now timeIntervalSince1970];
    return (long)(secondsSince1970 * 1000.0);
}

- (NSString *)generateResultString:(NSString *)traceId startTime:(long)startTime endTime:(long)endTime
{
    return [NSString stringWithFormat:@"traceId:%@;usdkOperateTime:%@;usdkOperateCallbackTime:%@", traceId ?: @"", [NSString stringWithFormat:@"%ld", startTime], [NSString stringWithFormat:@"%ld", endTime]];
}

- (int)getTimeoutValue:(NSDictionary<NSString *, id> *)params
{
    NSNumber *timeout = [self getParam:params Key:WifiToolkitAction_KEY_TIMEOUT Class:[NSNumber class]];
    if (timeout == nil) {
        return 0;
    }
    return timeout.intValue;
}
@end
