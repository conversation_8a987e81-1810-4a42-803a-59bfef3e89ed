//
//  DetachResource.h
//  UPDevice
//
//  Created by 振兴郑 on 2019/4/23.
//  Copyright © 2019 海尔优家智能科技（北京）有限公司. All rights reserved.
//

#import <Foundation/Foundation.h>
#import "WifiDeviceAction.h"
#import <uSDK/uSDK.h>
NS_ASSUME_NONNULL_BEGIN

extern NSString *const DetachResource_NAME;
@interface DetachResource : WifiDeviceAction
+ (DetachResource *)DetachResource:(uSDKManager *)manager DeviceManager:(uSDKDeviceManager *)deviceManager;
@end

NS_ASSUME_NONNULL_END
