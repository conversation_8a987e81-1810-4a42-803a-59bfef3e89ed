//
//  FetchGroupableDeviceList.m
//  UPDevice
//
//  Created by 冉东军 on 2021/4/6.
//  Copyright © 2021 海尔优家智能科技（北京）有限公司. All rights reserved.
//

#import "FetchGroupableDeviceList.h"
#import "UPDeviceLog.h"
#import "UpDeviceBaseInfo.h"
#import "WifiDeviceHelper.h"

NSString *const FetchGroupableDeviceList_NAME = @"FetchGroupableDeviceListNAME";
int const FetchGroup_NoDevice_errCode = -14056;

@implementation FetchGroupableDeviceList
+ (FetchGroupableDeviceList *)FetchGroupableDeviceList:(uSDKManager *)manager DeviceManager:(uSDKDeviceManager *)deviceManager
{
    return [[FetchGroupableDeviceList alloc] initWith:manager DeviceManager:deviceManager];
}

- (instancetype)initWith:(uSDKManager *)manager DeviceManager:(uSDKDeviceManager *)deviceManager
{
    if (self = [super initWithName:FetchGroupableDeviceList_NAME Manager:manager DeviceManager:deviceManager]) {
    }
    return self;
}

- (void)execute:(NSDictionary *)params finishBlock:(void (^)(UpDeviceResult *result))finishBlock
{
    NSString *deviceId = nil;
    if (params != nil) {
        deviceId = [self getParam:params Key:WifiDeviceAction_KEY_DEVICE_ID Class:[NSString class]];
    }

    if (deviceId == nil) {
        UpDeviceResult *result = [[UpDeviceResult alloc] initWithErrorCode:ErrorCode_FAILURE extraData:nil extraCode:nil extraInfo:@"deviceId 为空！"];
        UPDeviceLogError(@"%s[%d]FetchGroupableDeviceList deviceId is nil", __PRETTY_FUNCTION__, __LINE__);
        if (finishBlock) {
            finishBlock(result);
        }
        return;
    }
    uSDKDevice *device = [self getDevice:deviceId];
    [device fetchGroupableDeviceListCompletionHandler:^(NSArray<uSDKDevice *> *devices, NSError *error) {
      if (error == nil) {
          NSArray<id<UpDeviceBaseInfo>> *baseInfoList = [WifiDeviceHelper createDeviceBaseInfoList:devices];
          UpDeviceResult *result = [UpDeviceResult UpDeviceResult:ErrorCode_SUCCESS extraData:baseInfoList];
          UPDeviceLogDebug(@"%s[%d]device(%@)FetchGroupableDeviceList success！number:%lu", __PRETTY_FUNCTION__, __LINE__, deviceId, baseInfoList.count);
          if (finishBlock) {
              finishBlock(result);
          }
          return;
      }
      UpDeviceResult *result = [[UpDeviceResult alloc] initWithErrorCode:ErrorCode_FAILURE extraData:nil extraCode:error.description extraInfo:error.description];
      UPDeviceLogError(@"%s[%d]device(%@)FetchGroupableDeviceList fail！error:%@", __PRETTY_FUNCTION__, __LINE__, deviceId, error.description);
      if (error.code == FetchGroup_NoDevice_errCode) {
          result = [UpDeviceResult UpDeviceResult:ErrorCode_SUCCESS extraData:@[]];
          UPDeviceLogDebug(@"%s[%d]device(%@)FetchGroupableDeviceList success！with error:%ld", __PRETTY_FUNCTION__, __LINE__, deviceId, error.code);
      }
      if (finishBlock) {
          finishBlock(result);
      }
    }];
}
@end
