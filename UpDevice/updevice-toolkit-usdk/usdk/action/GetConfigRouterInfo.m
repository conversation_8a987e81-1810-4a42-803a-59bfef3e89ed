//
//  GetConfigRouterInfo.m
//  UPDevice
//
//  Created by MAC on 2021/9/30.
//  Copyright © 2021 海尔优家智能科技（北京）有限公司. All rights reserved.
//

#import "GetConfigRouterInfo.h"
#import "UPDeviceLog.h"
#import "WifiDeviceHelper.h"
#import <uSDK/uSDKBinding.h>

NSString *const GetConfigRouterInfo_NAME = @"GetConfigRouterInfo";

@implementation GetConfigRouterInfo

+ (GetConfigRouterInfo *)GetConfigRouterInfo:(uSDKManager *)manager DeviceManager:(uSDKDeviceManager *)deviceManager
{
    return [[GetConfigRouterInfo alloc] initWith:manager DeviceManager:deviceManager];
}

- (instancetype)initWith:(uSDKManager *)manager DeviceManager:(uSDKDeviceManager *)deviceManager
{
    if (self = [super initWithName:GetConfigRouterInfo_NAME Manager:manager DeviceManager:deviceManager]) {
    }
    return self;
}

- (void)execute:(NSDictionary *)params finishBlock:(void (^)(UpDeviceResult *result))finishBlock
{
    [uSDKBinding getConfigRouterInfo:30
        success:^(uSDKConfigRouterInfo *routerInfo) {
          NSDictionary *info = @{ @"ssid" : routerInfo.ssid,
                                  @"bssid" : routerInfo.bssid,
                                  @"password" : routerInfo.password,
          };
          UpDeviceResult *result = [UpDeviceResult UpDeviceResult:ErrorCode_SUCCESS extraData:info];
          UPDeviceLogDebug(@"%s[%d]GetConfigRouterInfo success！info:%@", __PRETTY_FUNCTION__, __LINE__, info);
          if (finishBlock) {
              finishBlock(result);
          }
        }
        failure:^(NSError *error) {
          UpDeviceResult *result = [[UpDeviceResult alloc] initWithErrorCode:ErrorCode_FAILURE extraData:nil extraCode:error.description extraInfo:error.description];
          UPDeviceLogError(@"%s[%d]GetConfigRouterInfo fail！error:%@", __PRETTY_FUNCTION__, __LINE__, error.description);
          if (finishBlock) {
              finishBlock(result);
          }
        }];
}

@end
