//
//  AttachDeviceWithoutConnect.h
//  UPDevice
//
//  Created by 闫达 on 2021/9/28.
//  Copyright © 2021 海尔优家智能科技（北京）有限公司. All rights reserved.
//

#import "WifiDeviceAction.h"
#import <uSDK/uSDK.h>
extern NSString *_Nonnull const AttachDeviceWithoutConnect_NAME;
extern NSString *_Nonnull const AttachDeviceWithoutConnect_KEY_DEVICE_LISTENER;

NS_ASSUME_NONNULL_BEGIN

@interface AttachDeviceWithoutConnect : WifiDeviceAction
+ (AttachDeviceWithoutConnect *)AttachDeviceWithoutConnect:(uSDKManager *)manager DeviceManager:(uSDKDeviceManager *)deviceManager;


@end

NS_ASSUME_NONNULL_END
