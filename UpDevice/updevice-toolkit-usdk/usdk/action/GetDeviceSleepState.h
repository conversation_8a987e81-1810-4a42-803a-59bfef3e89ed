//
//  GetDeviceSleepState.h
//  UPDevice
//
//  Created by 闫达 on 2021/11/2.
//  Copyright © 2021 海尔优家智能科技（北京）有限公司. All rights reserved.
//

#import "WifiDeviceAction.h"

NS_ASSUME_NONNULL_BEGIN
extern NSString *const GetDeviceSleepState_NAME;
@interface GetDeviceSleepState : WifiDeviceAction
+ (GetDeviceSleepState *)GetDeviceSleepState:(uSDKManager *)manager DeviceManager:(uSDKDeviceManager *)deviceManager;
@end

NS_ASSUME_NONNULL_END
