//
//  RefresUsdkDeviceList.m
//  UPDevice
//
//  Created by gump on 23/9/2019.
//  Copyright © 2019 海尔优家智能科技（北京）有限公司. All rights reserved.
//

#import "RefresUsdkDeviceList.h"
#import "UPDeviceLog.h"

NSString *const RefresUsdkDeviceList_NAME = @"RefresUsdkDeviceList";

@implementation RefresUsdkDeviceList

+ (RefresUsdkDeviceList *)RefresUsdkDeviceList:(uSDKManager *)manager DeviceManager:(uSDKDeviceManager *)deviceManager
{
    return [[RefresUsdkDeviceList alloc] initWith:manager DeviceManager:deviceManager];
}

- (instancetype)initWith:(uSDKManager *)manager DeviceManager:(uSDKDeviceManager *)deviceManager
{
    if (self = [super initWithName:RefresUsdkDeviceList_NAME Manager:manager DeviceManager:deviceManager]) {
    }
    return self;
}

- (void)execute:(NSDictionary *)params finishBlock:(void (^)(UpDeviceResult *result))finishBlock
{
    [[self getDeviceManager] refreshDeviceListWithSuccess:^{
      UpDeviceResult *result = [UpDeviceResult UpDeviceResult:ErrorCode_SUCCESS extraData:nil];
      UPDeviceLogDebug(@"%s[%d]刷新已绑定的设备列表!", __PRETTY_FUNCTION__, __LINE__);
      if (finishBlock) {
          finishBlock(result);
      }
    }
        failure:^(NSError *error) {
          UpDeviceResult *result = [[UpDeviceResult alloc] initWithErrorCode:ErrorCode_FAILURE extraData:nil extraCode:error.description extraInfo:nil];
          UPDeviceLogError(@"%s[%d]刷新已绑定的设备列表！error:%@", __PRETTY_FUNCTION__, __LINE__, error.description);
          if (finishBlock) {
              finishBlock(result);
          }
        }];
}

@end
