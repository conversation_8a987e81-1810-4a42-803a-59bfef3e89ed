//
//  BindDeviceWithoutWifi.h
//  UPDevice
//
//  Created by MAC on 2021/9/29.
//  Copyright © 2021 海尔优家智能科技（北京）有限公司. All rights reserved.
//

#import "WifiDeviceAction.h"

NS_ASSUME_NONNULL_BEGIN
extern NSString *const BindDeviceWithoutWifi_NAME;
extern NSString *const BIND_WITHOUT_WIFI_LISTENER;
/**
 * 无路由先绑定接口
 */
@interface BindDeviceWithoutWifi : WifiDeviceAction

+ (BindDeviceWithoutWifi *)BindDeviceWithoutWifi:(uSDKManager *)manager Devi<PERSON>Manager:(uSDKDeviceManager *)deviceManager;

@end

NS_ASSUME_NONNULL_END
