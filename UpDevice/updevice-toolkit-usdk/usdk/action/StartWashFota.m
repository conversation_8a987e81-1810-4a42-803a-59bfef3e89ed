//
//  StartWashFota.m
//  UPDevice
//
//  Created by gump on 8/6/2021.
//  Copyright © 2021 海尔优家智能科技（北京）有限公司. All rights reserved.
//

#import "StartWashFota.h"
#import "UPDeviceLog.h"
#import <uSDK/uSDKFOTAInfo.h>

NSString *const StartWashFota_NAME = @"StartWashFota";

@implementation StartWashFota

+ (StartWashFota *)StartWashFota:(uSDKManager *)manager DeviceManager:(uSDKDeviceManager *)deviceManager
{
    return [[StartWashFota alloc] initWith:manager DeviceManager:deviceManager];
}

- (instancetype)initWith:(uSDKManager *)manager DeviceManager:(uSDKDeviceManager *)deviceManager
{
    if (self = [super initWithName:StartWashFota_NAME Manager:manager DeviceManager:deviceManager]) {
    }
    return self;
}

- (void)execute:(NSDictionary *)params finishBlock:(void (^)(UpDeviceResult *result))finishBlock
{
    NSString *deviceId = nil;
    NSString *traceId = nil;
    NSString *firmwareId = nil;
    if (params != nil) {
        deviceId = [self getParam:params Key:WifiDeviceAction_KEY_DEVICE_ID Class:[NSString class]];
        traceId = [self getParam:params Key:KEY_TRACE_ID Class:[NSString class]];
        firmwareId = [self getParam:params Key:KEY_FIRM_WARE_ID Class:[NSString class]];
    }
    if (deviceId.length == 0) {
        UpDeviceResult *result = [[UpDeviceResult alloc] initWithErrorCode:ErrorCode_FAILURE extraData:nil extraCode:nil extraInfo:@"deviceId empty！"];
        UPDeviceLogError(@"%s[%d]execute StartWashFota!deviceId:%@,error:%@", __PRETTY_FUNCTION__, __LINE__, deviceId, result.extraInfo);
        if (finishBlock) {
            finishBlock(result);
        }
        return;
    }

    uSDKDevice *device = [self getDevice:deviceId];
    uSDKDeviceFOTAInfo *deviceFOTAInfo = [uSDKDeviceFOTAInfo new];
    deviceFOTAInfo.firmwareID = firmwareId;
    deviceFOTAInfo.traceID = traceId;
    [device startFOTAWithDeviceFOTAInfo:(uSDKDeviceFOTAInfo *)deviceFOTAInfo
                      completionHandler:^(NSError *error) {
                        if (!error) {
                            UpDeviceResult *result = [UpDeviceResult UpDeviceResult:ErrorCode_SUCCESS extraData:nil];
                            UPDeviceLogDebug(@"%s[%d]StartWashFota succeed！deviceId:%@", __PRETTY_FUNCTION__, __LINE__, deviceId);
                            if (finishBlock) {
                                finishBlock(result);
                            }
                            return;
                        }

                        UpDeviceResult *result = [[UpDeviceResult alloc] initWithErrorCode:ErrorCode_FAILURE extraData:nil extraCode:error.description extraInfo:error.description];
                        UPDeviceLogError(@"%s[%d]StartWashFota fail！deviceId:%@ error:%@", __PRETTY_FUNCTION__, __LINE__, deviceId, error.description);
                        if (finishBlock) {
                            finishBlock(result);
                        }
                      }];
}

@end
