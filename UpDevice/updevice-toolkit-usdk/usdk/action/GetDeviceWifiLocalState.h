//
//  GetDeviceWifiLocalState.h
//  UPDevice
//
//  Created by 王炜 on 2021/12/7.
//  Copyright © 2021 海尔优家智能科技（北京）有限公司. All rights reserved.
//

#import "WifiDeviceAction.h"

NS_ASSUME_NONNULL_BEGIN
extern NSString *const GetDeviceWifiLocalState_NAME;

/*
 *获取设备本地Wi-Fi连接状态
 */
@interface GetDeviceWifiLocalState : WifiDeviceAction

+ (GetDeviceWifiLocalState *)GetDeviceWifiLocalState:(uSDKManager *)manager DeviceManager:(uSDKDeviceManager *)deviceManager;

@end

NS_ASSUME_NONNULL_END
