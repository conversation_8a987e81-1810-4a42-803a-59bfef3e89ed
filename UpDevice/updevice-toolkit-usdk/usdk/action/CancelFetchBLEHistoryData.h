//
//  CancelFetchBLEHistoryData.h
//  UPDevice
//
//  Created by gump on 27/8/2019.
//  Copyright © 2019 海尔优家智能科技（北京）有限公司. All rights reserved.
//

#import "WifiDeviceAction.h"
#import <uSDK/uSDK.h>

extern NSString *const CancelFetchBLEHistoryData_NAME;

/*
 *取消获取蓝牙历史数据
 */

@interface CancelFetchBLEHistoryData : WifiDeviceAction

+ (CancelFetchBLEHistoryData *)CancelFetchBLEHistoryData:(uSDKManager *)manager DeviceManager:(uSDKDeviceManager *)deviceManager;

- (instancetype)initWith:(uSDKManager *)manager DeviceManager:(uSDKDeviceManager *)deviceManager;

@end
