//
//  DetachDevice.m
//  UPDeviceDebugger
//
//  Created by f on 2/1/2019.
//  Copyright © 2019 海尔优家智能科技（北京）有限公司. All rights reserved.
//

#import "DetachDevice.h"
#import "UPDeviceLog.h"

NSString *const DetachDevice_NAME = @"detachDevice";

@implementation DetachDevice

+ (DetachDevice *)DetachDevice:(uSDKManager *)manager DeviceManager:(uSDKDeviceManager *)deviceManager
{
    return [[DetachDevice alloc] initWith:manager DeviceManager:deviceManager];
}

- (instancetype)initWith:(uSDKManager *)manager DeviceManager:(uSDKDeviceManager *)deviceManager
{
    if (self = [super initWithName:DetachDevice_NAME Manager:manager DeviceManager:deviceManager]) {
    }
    return self;
}

- (void)execute:(NSDictionary *)params finishBlock:(void (^)(UpDeviceResult *result))finishBlock
{
    NSString *deviceId = nil;
    if (params != nil) {
        deviceId = [self getParam:params Key:WifiDeviceAction_KEY_DEVICE_ID Class:[NSString class]];
    }

    if (deviceId == nil) {
        UpDeviceResult *result = [[UpDeviceResult alloc] initWithErrorCode:ErrorCode_FAILURE extraData:nil extraCode:nil extraInfo:@"deviceId 为空！"];
        UPDeviceLogError(@"%s[%d]DetachDevice执行失败!deviceId为空，error: mac为空！", __PRETTY_FUNCTION__, __LINE__);
        if (finishBlock) {
            finishBlock(result);
        }
        return;
    }

    uSDKDevice *device = [self getDevice:deviceId];
    if (!device) {
        UpDeviceResult *result = [UpDeviceResult UpDeviceResult:ErrorCode_SUCCESS extraData:nil];
        UPDeviceLogDebug(@"%s[%d]device(%@)DetachDevice success！", __PRETTY_FUNCTION__, __LINE__, deviceId);
        if (finishBlock) {
            finishBlock(result);
        }
    }

    [device disconnectWithSuccess:^{
      UpDeviceResult *result = [UpDeviceResult UpDeviceResult:ErrorCode_SUCCESS extraData:nil];
      UPDeviceLogDebug(@"%s[%d]device(%@)DetachDevice success！", __PRETTY_FUNCTION__, __LINE__, deviceId);
      if (finishBlock) {
          finishBlock(result);
      }
    }
        failure:^(NSError *error) {
          UpDeviceResult *result = [[UpDeviceResult alloc] initWithErrorCode:ErrorCode_FAILURE extraData:nil extraCode:error.description extraInfo:error.description];
          UPDeviceLogError(@"%s[%d]device(%@)DetachDevice fail！error:%@", __PRETTY_FUNCTION__, __LINE__, deviceId, error.description);
          if (finishBlock) {
              finishBlock(result);
          }
        }];
}

@end
