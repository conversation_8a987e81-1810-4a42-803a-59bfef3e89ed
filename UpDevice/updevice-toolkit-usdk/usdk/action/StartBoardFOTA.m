//
//  StartBoardFOTA.m
//  UPDevice
//
//  Created by gump on 12/6/2019.
//  Copyright © 2019 海尔优家智能科技（北京）有限公司. All rights reserved.
//

#import "StartBoardFOTA.h"
#import "DeviceFOTAInfo.h"
#import "UPDeviceLog.h"

NSString *const StartBoardFOTA_NAME = @"StartBoardFOTA";

@implementation StartBoardFOTA

+ (StartBoardFOTA *)StartBoardFOTA:(uSDKManager *)manager DeviceManager:(uSDKDeviceManager *)deviceManager
{
    return [[StartBoardFOTA alloc] initWith:manager DeviceManager:deviceManager];
}

- (instancetype)initWith:(uSDKManager *)manager DeviceManager:(uSDKDeviceManager *)deviceManager
{
    if (self = [super initWithName:StartBoardFOTA_NAME Manager:manager DeviceManager:deviceManager]) {
    }
    return self;
}

- (void)execute:(NSDictionary *)params finishBlock:(void (^)(UpDeviceResult *result))finishBlock
{
    NSString *deviceId = nil;

    if (params != nil) {
        deviceId = [self getParam:params Key:WifiDeviceAction_KEY_DEVICE_ID Class:[NSString class]];
    }
    if (deviceId == nil) {
        UpDeviceResult *result = [[UpDeviceResult alloc] initWithErrorCode:ErrorCode_FAILURE extraData:nil extraCode:nil extraInfo:@"deviceId or DeviceFOTAInfo为空！"];
        UPDeviceLogError(@"%s[%d]设备命令执行失败!deviceId:%@,error:%@", __PRETTY_FUNCTION__, __LINE__, deviceId, result.extraInfo);
        if (finishBlock) {
            finishBlock(result);
        }
        return;
    }

    uSDKDevice *device = [self getDevice:deviceId];

    [device startBoardFOTAWithSuccess:^{
      UpDeviceResult *result = [UpDeviceResult UpDeviceResult:ErrorCode_SUCCESS extraData:nil];
      UPDeviceLogDebug(@"%s[%d]设备startBoardFOTAWithFOTAInfo成功！", __PRETTY_FUNCTION__, __LINE__);
      if (finishBlock) {
          finishBlock(result);
      }
    }
        failure:^(NSError *error) {
          UpDeviceResult *result = [[UpDeviceResult alloc] initWithErrorCode:ErrorCode_FAILURE extraData:nil extraCode:error.description extraInfo:error.description];
          UPDeviceLogError(@"%s[%d]设备startBoardFOTAWithFOTAInfo失败！error:%@", __PRETTY_FUNCTION__, __LINE__, error.description);
          if (finishBlock) {
              finishBlock(result);
          }
        }];
}

@end
