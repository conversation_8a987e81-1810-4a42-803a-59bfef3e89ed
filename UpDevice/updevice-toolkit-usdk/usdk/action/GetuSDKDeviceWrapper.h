//
//  GetuSDKDeviceWrapper.h
//  UPDevice
//
//  Created by <PERSON><PERSON><PERSON>   on 2025/4/9.
//  Copyright © 2025 海尔优家智能科技（北京）有限公司. All rights reserved.
//

#import <Foundation/Foundation.h>
#import "WifiDeviceAction.h"

NS_ASSUME_NONNULL_BEGIN
extern NSString *const GetuSDKDeviceWrapper_NAME;

@interface GetuSDKDeviceWrapper : WifiDeviceAction
+ (GetuSDKDeviceWrapper *)getuSDKDeviceWrapper:(uSDKManager *)manager DeviceManager:(uSDKDeviceManager *)deviceManager;
@end

NS_ASSUME_NONNULL_END
