//
//  GetOnlyConfigState.m
//  UPDevice
//
//  Created by <PERSON><PERSON><PERSON>   on 2024/6/18.
//  Copyright © 2024 海尔优家智能科技（北京）有限公司. All rights reserved.
//

#import "GetOnlyConfigState.h"
#import "WifiDeviceHelper.h"
#import "UPDeviceLog.h"

NSString *const GetOnlyConfigState_NAME = @"GetOnlyConfigState";

@implementation GetOnlyConfigState

+ (GetOnlyConfigState *)getDeviceOnlyConfigState:(uSDKManager *)manager DeviceManager:(uSDKDeviceManager *)deviceManager
{
    return [[GetOnlyConfigState alloc] initWith:manager DeviceManager:deviceManager];
}

- (instancetype)initWith:(uSDKManager *)manager DeviceManager:(uSDKDeviceManager *)deviceManager
{
    if (self = [super initWithName:GetOnlyConfigState_NAME Manager:manager DeviceManager:deviceManager]) {
    }
    return self;
}

- (void)execute:(NSDictionary *)params finishBlock:(void (^)(UpDeviceResult *result))finishBlock
{
    NSString *deviceId = nil;
    if (params != nil) {
        deviceId = [self getParam:params Key:WifiDeviceAction_KEY_DEVICE_ID Class:[NSObject class]];
    }

    if (deviceId == nil) {
        UpDeviceResult *result = [UpDeviceResult UpDeviceResult:ErrorCode_FAILURE extraData:nil];
        UPDeviceLogError(@"%s[%d]device(%@)GetOnlyConfigState!error:%@", __PRETTY_FUNCTION__, __LINE__, deviceId, result.extraInfo);
        if (finishBlock) {
            finishBlock(result);
        }
        return;
    }

    uSDKDevice *device = [self getDevice:deviceId];
    if (!device) {
        UpDeviceResult *result = [UpDeviceResult UpDeviceResult:ErrorCode_FAILURE extraData:nil];
        UPDeviceLogError(@"%s[%d]devcie(%@)GetOnlyConfigState fail,no usdkDevice!error:%@", __PRETTY_FUNCTION__, __LINE__, deviceId, result.extraInfo);
        if (finishBlock) {
            finishBlock(result);
        }
        return;
    }
    UpDeviceResult *result = [UpDeviceResult UpDeviceResult:ErrorCode_SUCCESS extraData:nil];
    UpDeviceOnlyConfigState onlyConfigState = [WifiDeviceHelper parseOnlyConfigState:device.onlyConfigState];
    result.extraData = [NSNumber numberWithInteger:(int)onlyConfigState];
    UPDeviceLogDebug(@"%s[%d]device(%@)GetOnlyConfigState success！onlyConfigState:%ld", __PRETTY_FUNCTION__, __LINE__, deviceId, onlyConfigState);
    if (finishBlock) {
        finishBlock(result);
    }
}

@end
