//
//  GetSubDevList.h
//  UPDeviceDebugger
//
//  Created by f on 2/1/2019.
//  Copyright © 2019 海尔优家智能科技（北京）有限公司. All rights reserved.
//

#import <Foundation/Foundation.h>
#import <uSDK/uSDK.h>
#import "WifiDeviceAction.h"

NS_ASSUME_NONNULL_BEGIN

extern NSString *const GetSubDevList_NAME;

@interface GetSubDevList : WifiDeviceAction

+ (GetSubDevList *)GetSubDevList:(uSDKManager *)manager DeviceManager:(uSDKDeviceManager *)deviceManager;

- (instancetype)initWith:(uSDKManager *)manager DeviceManager:(uSDKDeviceManager *)deviceManager;

@end

NS_ASSUME_NONNULL_END
