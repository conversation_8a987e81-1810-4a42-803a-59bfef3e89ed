//
//  GetBindInfo.m
//  UPDeviceDebugger
//
//  Created by f on 2/1/2019.
//  Copyright © 2019 海尔优家智能科技（北京）有限公司. All rights reserved.
//

#import "GetBindInfo.h"
#import "UPDeviceLog.h"
#import "WifiDeviceToolkit.h"

NSString *const GetBindInfo_NAME = @"getBindInfo";
NSString *const GetBindInfo_KEY_ACCESS_TOKEN = @"usdk-access-token";

@implementation GetBindInfo

+ (GetBindInfo *)GetBindInfo:(uSDKManager *)manager DeviceManager:(uSDKDeviceManager *)deviceManager
{
    return [[GetBindInfo alloc] initWith:manager DeviceManager:deviceManager];
}

- (instancetype)initWith:(uSDKManager *)manager DeviceManager:(uSDKDeviceManager *)deviceManager
{
    if (self = [super initWithName:GetBindInfo_NAME Manager:manager DeviceManager:deviceManager]) {
    }

    return self;
}

- (void)execute:(NSDictionary *)params finishBlock:(void (^)(UpDeviceResult *result))finishBlock
{
    NSString *deviceId = nil;
    NSString *accessToken = nil;
    if (params != nil) {
        deviceId = [self getParam:params Key:WifiDeviceAction_KEY_DEVICE_ID Class:[NSString class]];
        accessToken = [self getParam:params Key:WifiDeviceToolkit_KEY_ACCESS_TOKEN Class:[NSString class]];
    }

    if (deviceId == nil || accessToken == nil) {
        UpDeviceResult *result = [UpDeviceResult UpDeviceResult:ErrorCode_FAILURE extraData:nil];
        UPDeviceLogError(@"%s[%d]获取设备绑定信息失败!deviceId:%@,accessToken:%@,error:%@", __PRETTY_FUNCTION__, __LINE__, deviceId, accessToken, result.extraInfo);
        if (finishBlock) {
            finishBlock(result);
        }
        return;
    }

    uSDKDevice *device = [self getDevice:deviceId];
    if (![device isKindOfClass:[uSDKDevice class]]) {
        NSString *msg = [NSString stringWithFormat:@"uSDK中未发现该设备对象：%@", deviceId];
        UpDeviceResult *result = [[UpDeviceResult alloc] initWithErrorCode:ErrorCode_FAILURE extraData:nil extraCode:nil extraInfo:msg];
        UPDeviceLogError(@"%s[%d]设备(%@)获取设备绑定信息失败! error:%@", __PRETTY_FUNCTION__, __LINE__, deviceId, result.extraInfo);
        if (finishBlock) {
            finishBlock(result);
        }
        return;
    }
    [device getDeviceBindInfoWithToken:accessToken
        timeoutInterval:TIMEOUT_DEFAULT
        traceNodeCS:nil
        success:^(NSString *info) {

          UpDeviceResult *result = [UpDeviceResult UpDeviceResult:ErrorCode_SUCCESS extraData:info];
          UPDeviceLogDebug(@"%s[%d]device(%@)GetBindInfo success！info :%@", __PRETTY_FUNCTION__, __LINE__, deviceId, result.extraData);

          if (finishBlock) {
              finishBlock(result);
          }
        }
        failure:^(NSError *error) {
          UpDeviceResult *result = [[UpDeviceResult alloc] initWithErrorCode:ErrorCode_FAILURE extraData:nil extraCode:error.description extraInfo:nil];
          UPDeviceLogError(@"%s[%d]device(%@)GetBindInfo fail！error:%@", __PRETTY_FUNCTION__, __LINE__, deviceId, error.description);
          if (finishBlock) {
              finishBlock(result);
          }
        }];
}

@end
