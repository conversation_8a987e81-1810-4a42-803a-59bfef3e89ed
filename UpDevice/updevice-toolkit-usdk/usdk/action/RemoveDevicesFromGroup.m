//
//  RemoveDevicesFromGroup.m
//  UPDevice
//
//  Created by 冉东军 on 2021/4/6.
//  Copyright © 2021 海尔优家智能科技（北京）有限公司. All rights reserved.
//

#import "RemoveDevicesFromGroup.h"
#import "UPDeviceLog.h"
#import "WifiDeviceHelper.h"

NSString *const RemoveDevicesFromGroup_NAME = @"RemoveDevicesFromGroupNAME";
extern NSString *const KEY_DEVICEIDS_NAME;

@implementation RemoveDevicesFromGroup
+ (RemoveDevicesFromGroup *)RemoveDevicesFromGroup:(uSDKManager *)manager DeviceManager:(uSDKDeviceManager *)deviceManager
{
    return [[RemoveDevicesFromGroup alloc] initWith:manager DeviceManager:deviceManager];
}

- (instancetype)initWith:(uSDKManager *)manager DeviceManager:(uSDKDeviceManager *)deviceManager
{
    if (self = [super initWithName:RemoveDevicesFromGroup_NAME Manager:manager DeviceManager:deviceManager]) {
    }
    return self;
}

- (void)execute:(NSDictionary *)params finishBlock:(void (^)(UpDeviceResult *result))finishBlock
{
    NSString *deviceId = nil;
    NSArray *deviceIds = nil;
    if (params != nil) {
        deviceId = [self getParam:params Key:WifiDeviceAction_KEY_DEVICE_ID Class:[NSString class]];
        deviceIds = [self getParam:params Key:KEY_DEVICEIDS_NAME Class:[NSArray class]];
    }

    if (deviceId == nil || deviceIds.count == 0) {
        UpDeviceResult *result = [[UpDeviceResult alloc] initWithErrorCode:ErrorCode_FAILURE extraData:nil extraCode:nil extraInfo:@"deviceId 或者 deviceIds 为空！"];
        UPDeviceLogError(@"%s[%d]RemoveDevicesFromGroup deviceId or deviceIds is nil", __PRETTY_FUNCTION__, __LINE__);
        if (finishBlock) {
            finishBlock(result);
        }
        return;
    }
    NSArray<uSDKDevice *> *devicesArray = [self createuSDKDevice:deviceIds];
    if (devicesArray.count == 0) {
        UpDeviceResult *result = [[UpDeviceResult alloc] initWithErrorCode:ErrorCode_FAILURE extraData:nil extraCode:nil extraInfo:@"uSDKDevice为空！"];
        UPDeviceLogError(@"%s[%d]device(%@)RemoveDevicesFromGroup fail!,error:%@", __PRETTY_FUNCTION__, __LINE__, deviceId, result.extraInfo);
        if (finishBlock) {
            finishBlock(result);
        }
        return;
    }
    NSMutableArray<DeviceBaseInfo *> *removeSuccess = [NSMutableArray array];
    NSMutableArray<DeviceBaseInfo *> *removeFail = [NSMutableArray array];
    NSMutableDictionary *deviceDict = [NSMutableDictionary dictionary];
    uSDKDevice *device = [self getDevice:deviceId];
    [device removeDevices:devicesArray
        fromGroupWithTimeoutInterval:TIMEOUT_DEFAULT_GROUPDEVICE
        progressNotify:^(uSDKDevice *device, NSError *error) {
          DeviceBaseInfo *baseInfo = [WifiDeviceHelper createDeviceBaseInfo:device];
          if (baseInfo == nil) {
              return;
          }
          if (error == nil) {
              [removeSuccess addObject:baseInfo];
          }
          else {
              [removeFail addObject:baseInfo];
          }
        }
        completionHandler:^(NSError *error) {
          if (error == nil) {
              deviceDict[@"success"] = removeSuccess;
              deviceDict[@"faliure"] = removeFail;
              UpDeviceResult *result = [UpDeviceResult UpDeviceResult:ErrorCode_SUCCESS extraData:deviceDict];
              UPDeviceLogDebug(@"%s[%d]device(%@)RemoveDevicesFromGroup success！", __PRETTY_FUNCTION__, __LINE__, deviceId);
              if (finishBlock) {
                  finishBlock(result);
              }
              return;
          }
          UpDeviceResult *result = [[UpDeviceResult alloc] initWithErrorCode:ErrorCode_FAILURE extraData:nil extraCode:error.description extraInfo:error.description];
          UPDeviceLogError(@"%s[%d]device(%@)RemoveDevicesFromGroup fail！error:%@", __PRETTY_FUNCTION__, __LINE__, deviceId, error.description);
          if (finishBlock) {
              finishBlock(result);
          }
        }];
}
@end
