//
//  GetOfflineCause.m
//  UPDevice
//
//  Created by pc on 2023/11/10.
//  Copyright © 2023 海尔优家智能科技（北京）有限公司. All rights reserved.
//

#import "GetOfflineCause.h"
#import "WifiDeviceHelper.h"
#import "UPDeviceLog.h"

NSString *const GetOfflineCause_NAME = @"GetOfflineCause";

@implementation GetOfflineCause

+ (GetOfflineCause *)getDeviceOfflineCause:(uSDKManager *)manager DeviceManager:(uSDKDeviceManager *)deviceManager
{
    return [[GetOfflineCause alloc] initWith:manager DeviceManager:deviceManager];
}

- (instancetype)initWith:(uSDKManager *)manager DeviceManager:(uSDKDeviceManager *)deviceManager
{
    if (self = [super initWithName:GetOfflineCause_NAME Manager:manager DeviceManager:deviceManager]) {
    }
    return self;
}

- (void)execute:(NSDictionary *)params finishBlock:(void (^)(UpDeviceResult *result))finishBlock
{
    NSString *deviceId = nil;
    if (params != nil) {
        deviceId = [self getParam:params Key:WifiDeviceAction_KEY_DEVICE_ID Class:[NSObject class]];
    }

    if (deviceId == nil) {
        UpDeviceResult *result = [UpDeviceResult UpDeviceResult:ErrorCode_FAILURE extraData:nil];
        UPDeviceLogError(@"%s[%d]device(%@)GetOfflineCause!error:%@", __PRETTY_FUNCTION__, __LINE__, deviceId, result.extraInfo);
        if (finishBlock) {
            finishBlock(result);
        }
        return;
    }

    uSDKDevice *device = [self getDevice:deviceId];
    if (!device) {
        UpDeviceResult *result = [UpDeviceResult UpDeviceResult:ErrorCode_FAILURE extraData:nil];
        UPDeviceLogError(@"%s[%d]devcie(%@)GetOfflineCause fail,no usdkDevice!error:%@", __PRETTY_FUNCTION__, __LINE__, deviceId, result.extraInfo);
        if (finishBlock) {
            finishBlock(result);
        }
        return;
    }
    UpDeviceResult *result = [UpDeviceResult UpDeviceResult:ErrorCode_SUCCESS extraData:nil];
    UpDeviceOfflineCause offlineCause = [WifiDeviceHelper parseDeviceOfflineCause:device.activeOfflineCause];
    result.extraData = [NSNumber numberWithInt:(int)offlineCause];
    UPDeviceLogDebug(@"%s[%d]device(%@)GetOfflineCause success！offlineCause:%ld", __PRETTY_FUNCTION__, __LINE__, deviceId, device.activeOfflineCause);
    if (finishBlock) {
        finishBlock(result);
    }
}

@end
