//
//  GetSubDevListBySubDevice.m
//  UPDevice
//
//  Created by gump on 4/12/2019.
//  Copyright © 2019 海尔优家智能科技（北京）有限公司. All rights reserved.
//

#import "GetSubDevListBySubDevice.h"
#import "UPDeviceLog.h"
#import "UpDeviceBaseInfo.h"
#import "DeviceBaseInfo.h"
#import "WifiDeviceHelper.h"

NSString *const GetSubDevListBySubDevice_NAME = @"GetSubDevListBySubDevice";

@implementation GetSubDevListBySubDevice

+ (GetSubDevListBySubDevice *)GetSubDevListBySubDevice:(uSDKManager *)manager DeviceManager:(uSDKDeviceManager *)deviceManager
{
    return [[GetSubDevListBySubDevice alloc] initWith:manager DeviceManager:deviceManager];
}

- (instancetype)initWith:(uSDKManager *)manager DeviceManager:(uSDKDeviceManager *)deviceManager
{
    if (self = [super initWithName:GetSubDevListBySubDevice_NAME Manager:manager DeviceManager:deviceManager]) {
    }
    return self;
}

- (void)execute:(NSDictionary *)params finishBlock:(void (^)(UpDeviceResult *result))finishBlock
{
    NSString *deviceId = nil;
    if (params != nil) {
        deviceId = [self getParam:params Key:WifiDeviceAction_KEY_DEVICE_ID Class:[NSString class]];
    }
    if (deviceId == nil) {
        [self returnErr:@"deviceId 为空!" deviceId:deviceId finishBlock:finishBlock];
        return;
    }

    uSDKDevice *device = [self getDevice:deviceId];
    if (!device || ![device isKindOfClass:[uSDKSubDevice class]]) {
        [self returnErr:@"子机usdk对象为空!" deviceId:deviceId finishBlock:finishBlock];
        return;
    }

    uSDKSubDevice *subDevice = (uSDKSubDevice *)device;
    uSDKDevice *mainDev = subDevice.parentDevice;
    if (!mainDev) {
        [self returnErr:@"子机的主机对象为空!" deviceId:deviceId finishBlock:finishBlock];
        return;
    }

    NSArray<uSDKSubDevice *> *subDevList = [mainDev subDeviceList];

    if (subDevList == nil || ![subDevList isKindOfClass:[NSArray class]]) {
        [self returnErr:@"子设备列表获取失败!" deviceId:deviceId finishBlock:finishBlock];
        return;
    }

    NSMutableArray<id<UpDeviceBaseInfo>> *baseInfoList = [NSMutableArray array];
    for (uSDKDevice *device in subDevList) {
        DeviceBaseInfo *baseInfo = [WifiDeviceHelper createDeviceBaseInfo:device];
        if (baseInfo) {
            [baseInfoList addObject:baseInfo];
        }
    }

    UpDeviceResult *result = [UpDeviceResult UpDeviceResult:ErrorCode_SUCCESS extraData:nil];

    result.extraData = baseInfoList;
    UPDeviceLogDebug(@"%s[%d]子设备列表获取成功！", __PRETTY_FUNCTION__, __LINE__);
    if (finishBlock) {
        finishBlock(result);
    }
}

- (void)returnErr:(NSString *)errDescribe deviceId:(NSString *)deviceId finishBlock:(void (^)(UpDeviceResult *result))finishBlock
{
    UpDeviceResult *result = [[UpDeviceResult alloc] initWithErrorCode:ErrorCode_FAILURE extraData:nil extraCode:nil extraInfo:errDescribe];
    UPDeviceLogError(@"%s[%d]通过子机获取子机列表失败!deviceId:%@,error:%@", __PRETTY_FUNCTION__, __LINE__, deviceId, result.extraInfo);
    if (finishBlock) {
        finishBlock(result);
    }
}

@end
