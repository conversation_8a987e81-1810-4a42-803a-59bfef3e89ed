//
//  IsBound.m
//  UPDevice
//
//  Created by gump on 20/9/2019.
//  Copyright © 2019 海尔优家智能科技（北京）有限公司. All rights reserved.
//

#import "IsBound.h"
#import "UPDeviceLog.h"

NSString *const IsBound_NAME = @"IsBound";

@implementation IsBound

+ (IsBound *)IsBound:(uSDKManager *)manager DeviceManager:(uSDKDeviceManager *)deviceManager
{
    return [[IsBound alloc] initWith:manager DeviceManager:deviceManager];
}

- (instancetype)initWith:(uSDKManager *)manager DeviceManager:(uSDKDeviceManager *)deviceManager
{
    if (self = [super initWithName:IsBound_NAME Manager:manager DeviceManager:deviceManager]) {
    }
    return self;
}

- (void)execute:(NSDictionary *)params finishBlock:(void (^)(UpDeviceResult *result))finishBlock
{
    NSString *deviceId = nil;
    if (params != nil) {
        deviceId = [self getParam:params Key:WifiDeviceAction_KEY_DEVICE_ID Class:[NSString class]];
    }
    if (deviceId == nil) {
        UpDeviceResult *result = [[UpDeviceResult alloc] initWithErrorCode:ErrorCode_FAILURE extraData:nil extraCode:nil extraInfo:@"deviceId 为空！"];
        UPDeviceLogError(@"%s[%d]设备命令执行失败!deviceId:%@,error:%@", __PRETTY_FUNCTION__, __LINE__, deviceId, result.extraInfo);
        if (finishBlock) {
            finishBlock(result);
        }
        return;
    }

    uSDKDevice *device = [self getDevice:deviceId];
    UpDeviceResult *result = [UpDeviceResult UpDeviceResult:ErrorCode_SUCCESS extraData:[NSNumber numberWithBool:device.isBound]];
    UPDeviceLogDebug(@"%s[%d]获取标识设备是否被绑定状态成功！deviceId:%@   isBound:%d", __PRETTY_FUNCTION__, __LINE__, deviceId, device.isBound);
    if (finishBlock) {
        finishBlock(result);
    }
}

@end
