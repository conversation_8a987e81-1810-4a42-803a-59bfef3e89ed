//
//  IsModuleNeedOta.h
//  UPDevice
//
//  Created by gump on 3/9/2019.
//  Copyright © 2019 海尔优家智能科技（北京）有限公司. All rights reserved.
//

#import "WifiDeviceAction.h"
#import <uSDK/uSDK.h>

extern NSString *const IsModuleNeedOta_NAME;

/**
 * description: 判断小循环升级状态
 */

@interface IsModuleNeedOta : WifiDeviceAction

+ (IsModuleNeedOta *)IsModuleNeedOta:(uSDKManager *)manager DeviceManager:(uSDKDeviceManager *)deviceManager;

- (instancetype)initWith:(uSDKManager *)manager DeviceManager:(uSDKDeviceManager *)deviceManager;

@end
