//
//  fetchBoardFOTAStatus.m
//  UPDevice
//
//  Created by gump on 12/6/2019.
//  Copyright © 2019 海尔优家智能科技（北京）有限公司. All rights reserved.
//

#import "FetchBoardFOTAStatus.h"
#import "UPDeviceLog.h"
#import "DeviceFOTAStatusInfo.h"

NSString *const FetchBoardFOTAStatus_NAME = @"FetchBoardFOTAStatusNAME";

@implementation FetchBoardFOTAStatus

+ (FetchBoardFOTAStatus *)FetchBoardFOTAStatus:(uSDKManager *)manager DeviceManager:(uSDKDeviceManager *)deviceManager
{
    return [[FetchBoardFOTAStatus alloc] initWith:manager DeviceManager:deviceManager];
}

- (instancetype)initWith:(uSDKManager *)manager DeviceManager:(uSDKDeviceManager *)deviceManager
{
    if (self = [super initWithName:FetchBoardFOTAStatus_NAME Manager:manager DeviceManager:deviceManager]) {
    }
    return self;
}

- (void)execute:(NSDictionary *)params finishBlock:(void (^)(UpDeviceResult *result))finishBlock
{
    NSString *deviceId = nil;
    if (params != nil) {
        deviceId = [self getParam:params Key:WifiDeviceAction_KEY_DEVICE_ID Class:[NSString class]];
    }
    if (deviceId == nil) {
        UpDeviceResult *result = [[UpDeviceResult alloc] initWithErrorCode:ErrorCode_FAILURE extraData:nil extraCode:nil extraInfo:@"deviceId 为空！"];
        UPDeviceLogError(@"%s[%d]FetchBoardFOTAStatus deviceId is nil", __PRETTY_FUNCTION__, __LINE__);
        if (finishBlock) {
            finishBlock(result);
        }
        return;
    }

    uSDKDevice *device = [self getDevice:deviceId];
    [device fetchBoardFOTAStatusWithSuccess:^(uSDKFOTAStatusInfo *FOTAInfo) {
      DeviceFOTAStatusInfo *info = [DeviceFOTAStatusInfo DeviceFOTAStatusInfo:FOTAInfo.upgradeStatus upgradeErrInfo:FOTAInfo.upgradeErrorInfo];
      UpDeviceResult *result = [UpDeviceResult UpDeviceResult:ErrorCode_SUCCESS extraData:info];
      UPDeviceLogDebug(@"%s[%d]device(%@)fetchBoardFOTAStatusWithSuccess success！", __PRETTY_FUNCTION__, __LINE__, deviceId);
      if (finishBlock) {
          finishBlock(result);
      }
    }
        failure:^(NSError *error) {
          UpDeviceResult *result = [[UpDeviceResult alloc] initWithErrorCode:ErrorCode_FAILURE extraData:nil extraCode:error.description extraInfo:error.description];
          UPDeviceLogError(@"%s[%d]device(%@)fetchBoardFOTAStatusWithSuccess fail！error:%@", __PRETTY_FUNCTION__, __LINE__, deviceId, error.description);
          if (finishBlock) {
              finishBlock(result);
          }
        }];
}

@end
