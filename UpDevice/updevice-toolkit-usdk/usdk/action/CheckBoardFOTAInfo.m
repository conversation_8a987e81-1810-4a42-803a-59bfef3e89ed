//
//  CheckBoardFOTAInfo.m
//  UPDevice
//
//  Created by gump on 12/6/2019.
//  Copyright © 2019 海尔优家智能科技（北京）有限公司. All rights reserved.
//

#import "CheckBoardFOTAInfo.h"
#import "DeviceFOTAInfo.h"
#import "UPDeviceLog.h"

NSString *const CheckBoardFOTAInfo_NAME = @"CheckBoardFOTAInfoNAME";

@implementation CheckBoardFOTAInfo

+ (CheckBoardFOTAInfo *)CheckBoardFOTAInfo:(uSDKManager *)manager DeviceManager:(uSDKDeviceManager *)deviceManager
{
    return [[CheckBoardFOTAInfo alloc] initWith:manager DeviceManager:deviceManager];
}

- (instancetype)initWith:(uSDKManager *)manager DeviceManager:(uSDKDeviceManager *)deviceManager
{
    if (self = [super initWithName:CheckBoardFOTAInfo_NAME Manager:manager DeviceManager:deviceManager]) {
    }
    return self;
}

- (void)execute:(NSDictionary *)params finishBlock:(void (^)(UpDeviceResult *result))finishBlock
{
    NSString *deviceId = nil;
    if (params != nil) {
        deviceId = [self getParam:params Key:WifiDeviceAction_KEY_DEVICE_ID Class:[NSString class]];
    }
    if (deviceId == nil) {
        UpDeviceResult *result = [[UpDeviceResult alloc] initWithErrorCode:ErrorCode_FAILURE extraData:nil extraCode:nil extraInfo:@"deviceId 为空！"];
        UPDeviceLogError(@"%s[%d]CheckBoardFOTAInfodeviceIdFail deviceId is nil", __PRETTY_FUNCTION__, __LINE__);
        if (finishBlock) {
            finishBlock(result);
        }
        return;
    }

    uSDKDevice *device = [self getDevice:deviceId];
    [device checkBoardFOTAInfoWithSuccess:^(uSDKFOTAInfo *FOTAInfo) {
      DeviceFOTAInfo *info = [DeviceFOTAInfo DeviceFOTAInfo:FOTAInfo.isNeedFOTA currentVersion:FOTAInfo.currentVersion newestVersion:FOTAInfo.newestVersion newestVersionDescription:FOTAInfo.newestVersionDescription model:FOTAInfo.model timeoutInterval:FOTAInfo.timeoutInterval];
      UpDeviceResult *result = [UpDeviceResult UpDeviceResult:ErrorCode_SUCCESS extraData:info];
      UPDeviceLogDebug(@"%s[%d]device(%@)checkBoardFOTAInfoWithSuccess success！", __PRETTY_FUNCTION__, __LINE__, deviceId);
      if (finishBlock) {
          finishBlock(result);
      }
    }
        failure:^(NSError *error) {
          UpDeviceResult *result = [[UpDeviceResult alloc] initWithErrorCode:ErrorCode_FAILURE extraData:error extraCode:nil extraInfo:nil];
          NSError *resultError = (NSError *)result.extraData;
          UPDeviceLogError(@"%s[%d]device(%@)checkBoardFOTAInfoWithSuccess fail！code:%ld  description:%@   userInfo:%@", __PRETTY_FUNCTION__, __LINE__, deviceId, (long)resultError.code, resultError.description, resultError.userInfo);
          if (finishBlock) {
              finishBlock(result);
          }
        }];
}
@end
