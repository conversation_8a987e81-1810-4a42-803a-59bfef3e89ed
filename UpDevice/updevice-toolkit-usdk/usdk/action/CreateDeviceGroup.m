//
//  CreateDeviceGroup.m
//  UPDevice
//
//  Created by 冉东军 on 2021/4/6.
//  Copyright © 2021 海尔优家智能科技（北京）有限公司. All rights reserved.
//

#import "CreateDeviceGroup.h"
#import "UPDeviceLog.h"
#import "UpDeviceBaseInfo.h"
#import "WifiDeviceHelper.h"
#import "UpDeviceBase.h"
#import "DeviceInfo.h"

NSString *const CreateDeviceGroup_NAME = @"CreateDeviceGroupNAME";

@implementation CreateDeviceGroup

+ (CreateDeviceGroup *)CreateDeviceGroup:(uSDKManager *)manager DeviceManager:(uSDKDeviceManager *)deviceManager
{
    return [[CreateDeviceGroup alloc] initWith:manager DeviceManager:deviceManager];
}
- (instancetype)initWith:(uSDKManager *)manager DeviceManager:(uSDKDeviceManager *)deviceManager
{
    if (self = [super initWithName:CreateDeviceGroup_NAME Manager:manager DeviceManager:deviceManager]) {
    }
    return self;
}

- (void)execute:(NSDictionary *)params finishBlock:(void (^)(UpDeviceResult *result))finishBlock
{
    NSString *deviceId = nil;
    if (params != nil) {
        deviceId = [self getParam:params Key:WifiDeviceAction_KEY_DEVICE_ID Class:[NSString class]];
    }

    if (deviceId == nil) {
        UpDeviceResult *result = [[UpDeviceResult alloc] initWithErrorCode:ErrorCode_FAILURE extraData:nil extraCode:nil extraInfo:@"deviceId 为空！"];
        UPDeviceLogError(@"%s[%d]CreateDeviceGroup deviceId is nil", __PRETTY_FUNCTION__, __LINE__);
        if (finishBlock) {
            finishBlock(result);
        }
        return;
    }

    uSDKDevice *device = [self getDevice:deviceId];
    [device createGroupWithTimeoutInterval:TIMEOUT_DEFAULT_GROUPDEVICE
                         completionHandler:^(uSDKDevice *device, NSError *error) {
                           if (error == nil) {
                               id<UpDeviceBaseInfo> baseInfo = [WifiDeviceHelper createDeviceBaseInfo:device];
                               id<UpDeviceInfo> deviceInfo = [[DeviceInfo alloc] initWithDeviceBaseInfo:baseInfo];
                               id<UpDevice> upDevice = [[UpDeviceBase alloc] initWithDeviceInfo:nil deviceInfo:deviceInfo broker:nil factory:nil];
                               UpDeviceResult *result = [UpDeviceResult UpDeviceResult:ErrorCode_SUCCESS extraData:upDevice];
                               UPDeviceLogDebug(@"%s[%d]device(%@)CreateDeviceGroup success！", __PRETTY_FUNCTION__, __LINE__, deviceId);
                               if (finishBlock) {
                                   finishBlock(result);
                               }
                               return;
                           }
                           UpDeviceResult *result = [[UpDeviceResult alloc] initWithErrorCode:ErrorCode_FAILURE extraData:nil extraCode:error.description extraInfo:error.description];
                           UPDeviceLogError(@"%s[%d]device(%@)CreateDeviceGroup fail！error:%@", __PRETTY_FUNCTION__, __LINE__, deviceId, error.description);
                           if (finishBlock) {
                               finishBlock(result);
                           }
                         }];
}

@end
