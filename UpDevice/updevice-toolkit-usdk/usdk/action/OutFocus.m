//
//  OutFocus.m
//  UPDevice
//
//  Created by gump on 7/2/2020.
//  Copyright © 2020 海尔优家智能科技（北京）有限公司. All rights reserved.
//

#import "OutFocus.h"

#import "UPDeviceLog.h"

NSString *const OutFocus_NAME = @"OutFocus";

@implementation OutFocus

+ (OutFocus *)OutFocus:(uSDKManager *)manager DeviceManager:(uSDKDeviceManager *)deviceManager
{
    return [[OutFocus alloc] initWith:manager DeviceManager:deviceManager];
}

- (instancetype)initWith:(uSDKManager *)manager DeviceManager:(uSDKDeviceManager *)deviceManager
{
    if (self = [super initWithName:OutFocus_NAME Manager:manager DeviceManager:deviceManager]) {
    }
    return self;
}

- (void)execute:(NSDictionary *)params finishBlock:(void (^)(UpDeviceResult *result))finishBlock
{
    NSString *deviceId = nil;
    if (params != nil) {
        deviceId = [self getParam:params Key:WifiDeviceAction_KEY_DEVICE_ID Class:[NSString class]];
    }
    if (deviceId == nil) {
        UpDeviceResult *result = [[UpDeviceResult alloc] initWithErrorCode:ErrorCode_FAILURE extraData:nil extraCode:nil extraInfo:@"deviceId 为空！"];
        UPDeviceLogInfo(@"%s[%d]Device command outfocus failed!deviceId:%@,error:%@", __PRETTY_FUNCTION__, __LINE__, deviceId, result.extraInfo);
        if (finishBlock) {
            finishBlock(result);
        }
        return;
    }

    uSDKDevice *device = [self getDevice:deviceId];
    BOOL outFocus = [device outFocus];
    UpDeviceResult *result = [UpDeviceResult UpDeviceResult:ErrorCode_SUCCESS extraData:[NSNumber numberWithBool:outFocus]];
    UPDeviceLogInfo(@"%s[%d]Device command outfocus success！deviceId:%@   isBound:%d", __PRETTY_FUNCTION__, __LINE__, deviceId, outFocus);
    if (finishBlock) {
        finishBlock(result);
    }
}

@end
