//
//  RefresUsdkDeviceList.h
//  UPDevice
//
//  Created by gump on 23/9/2019.
//  Copyright © 2019 海尔优家智能科技（北京）有限公司. All rights reserved.
//

#import "WifiDeviceAction.h"
#import <uSDK/uSDK.h>

extern NSString *const RefresUsdkDeviceList_NAME;

/**
 * description: 刷新已绑定的设备列表
 */

@interface RefresUsdkDeviceList : WifiDeviceAction

+ (RefresUsdkDeviceList *)RefresUsdkDeviceList:(uSDKManager *)manager DeviceManager:(uSDKDeviceManager *)deviceManager;

- (instancetype)initWith:(uSDKManager *)manager DeviceManager:(uSDKDeviceManager *)deviceManager;

@end
