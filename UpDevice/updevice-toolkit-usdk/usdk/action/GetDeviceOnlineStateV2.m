//
//  GetDeviceOnlineStateV2.m
//  UPDevice
//
//  Created by 王炜 on 2021/11/29.
//  Copyright © 2021 海尔优家智能科技（北京）有限公司. All rights reserved.
//

#import "GetDeviceOnlineStateV2.h"
#import "WifiDeviceHelper.h"
#import "UPDeviceLog.h"

NSString *const GetDeviceOnlineStateV2_NAME = @"GetDeviceOnlineStateV2";

@implementation GetDeviceOnlineStateV2

+ (GetDeviceOnlineStateV2 *)GetDeviceOnlineStatus:(uSDKManager *)manager DeviceManager:(uSDKDeviceManager *)deviceManager
{
    return [[GetDeviceOnlineStateV2 alloc] initWith:manager DeviceManager:deviceManager];
}

- (instancetype)initWith:(uSDKManager *)manager DeviceManager:(uSDKDeviceManager *)deviceManager
{
    if (self = [super initWithName:GetDeviceOnlineStateV2_NAME Manager:manager DeviceManager:deviceManager]) {
    }
    return self;
}

- (void)execute:(NSDictionary *)params finishBlock:(void (^)(UpDeviceResult *result))finishBlock
{
    NSString *deviceId = nil;
    if (params != nil) {
        deviceId = [self getParam:params Key:WifiDeviceAction_KEY_DEVICE_ID Class:[NSObject class]];
    }

    if (deviceId == nil) {
        UpDeviceResult *result = [UpDeviceResult UpDeviceResult:ErrorCode_FAILURE extraData:nil];
        UPDeviceLogError(@"%s[%d]device(%@)GetDeviceOnlineStateV2!error:%@", __PRETTY_FUNCTION__, __LINE__, deviceId, result.extraInfo);
        if (finishBlock) {
            finishBlock(result);
        }
        return;
    }

    uSDKDevice *device = [self getDevice:deviceId];
    UpDeviceResult *result = [UpDeviceResult UpDeviceResult:ErrorCode_SUCCESS extraData:nil];
    UpDeviceRealOnlineV2 onlineStatus = [WifiDeviceHelper paresOnlineStateV2:device.onlineStateV2];
    result.extraData = [NSNumber numberWithInt:(int)onlineStatus];
    UPDeviceLogDebug(@"%s[%d]device(%@)GetDeviceOnlineStateV2 success！status:%lu", __PRETTY_FUNCTION__, __LINE__, deviceId, device.onlineStateV2);
    if (finishBlock) {
        finishBlock(result);
    }
}

@end
