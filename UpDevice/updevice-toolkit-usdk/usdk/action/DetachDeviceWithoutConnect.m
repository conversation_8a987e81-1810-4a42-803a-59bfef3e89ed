//
//  DetachDeviceWithoutConnect.m
//  UPDevice
//
//  Created by 闫达 on 2021/9/28.
//  Copyright © 2021 海尔优家智能科技（北京）有限公司. All rights reserved.
//

#import "DetachDeviceWithoutConnect.h"
#import "UPDeviceLog.h"

NSString *const DetachDeviceWithoutConnect_NAME = @"DetachDeviceWithoutConnect";
@implementation DetachDeviceWithoutConnect
+ (DetachDeviceWithoutConnect *)DetachDeviceWithoutConnect:(uSDKManager *)manager DeviceManager:(uSDKDeviceManager *)deviceManager
{
    return [[DetachDeviceWithoutConnect alloc] initWith:manager DeviceManager:deviceManager];
}

- (instancetype)initWith:(uSDKManager *)manager DeviceManager:(uSDKDeviceManager *)deviceManager
{
    if (self = [super initWithName:DetachDeviceWithoutConnect_NAME Manager:manager DeviceManager:deviceManager]) {
    }
    return self;
}

- (void)execute:(NSDictionary *)params finishBlock:(void (^)(UpDeviceResult *_Nonnull))finishBlock
{
    NSString *deviceId = nil;
    if (params != nil) {
        deviceId = [self getParam:params Key:WifiDeviceAction_KEY_DEVICE_ID Class:[NSString class]];
    }

    if (deviceId == nil) {
        UpDeviceResult *result = [[UpDeviceResult alloc] initWithErrorCode:ErrorCode_FAILURE extraData:nil extraCode:nil extraInfo:@"deviceId 为空！"];
        UPDeviceLogError(@"%s[%d]DetachDevice执行失败!deviceId为空，error: mac为空！", __PRETTY_FUNCTION__, __LINE__);
        if (finishBlock) {
            finishBlock(result);
        }
        return;
    }

    uSDKDevice *device = [self getDevice:deviceId];
    device.delegate = nil;
    if (!device) {
        UpDeviceResult *result = [UpDeviceResult UpDeviceResult:ErrorCode_SUCCESS extraData:nil];
        UPDeviceLogDebug(@"%s[%d]断开快连设备连接成功！", __PRETTY_FUNCTION__, __LINE__);
        if (finishBlock) {
            finishBlock(result);
        }
    }
}
@end
