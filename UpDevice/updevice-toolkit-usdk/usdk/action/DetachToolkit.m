//
//  DetachToolkit.m
//  UPDeviceDebugger
//
//  Created by f on 2/1/2019.
//  Copyright © 2019 海尔优家智能科技（北京）有限公司. All rights reserved.
//

#import "DetachToolkit.h"
#import "WifiDeviceHelper.h"
#import "UPDeviceLog.h"

NSString *const DetachToolkit_NAME = @"detachToolkit";

@implementation DetachToolkit

+ (DetachToolkit *)DetachToolkit:(uSDKManager *)manager DeviceManager:(uSDKDeviceManager *)deviceManager
{
    return [[DetachToolkit alloc] initWith:manager DeviceManager:deviceManager];
}

- (instancetype)initWith:(uSDKManager *)manager DeviceManager:(uSDKDeviceManager *)deviceManager
{
    if (self = [super initWithName:DetachToolkit_NAME Manager:manager DeviceManager:deviceManager]) {
    }
    return self;
}

- (void)execute:(NSDictionary *)params finishBlock:(void (^)(UpDeviceResult *result))finishBlock
{
    uSDKErrorConst sdkError = [[self getManager] stopSDK];
    UpDeviceResult *result = [WifiDeviceHelper parseError:sdkError];

    if ([result isSuccessful]) {
        UPDeviceLogDebug(@"%s[%d]DetachToolkit执行成功！", __PRETTY_FUNCTION__, __LINE__);
    }
    else {
        UPDeviceLogError(@"%s[%d]DetachToolkit执行失败！error:%@", __PRETTY_FUNCTION__, __LINE__, result.extraInfo);
    }

    if (finishBlock) {
        finishBlock(result);
    }
}

@end
