//
//  DisconnectRemoteDevices.m
//  UPDeviceDebugger
//
//  Created by f on 2/1/2019.
//  Copyright © 2019 海尔优家智能科技（北京）有限公司. All rights reserved.
//

#import "DisconnectRemoteDevices.h"
#import "WifiDeviceToolkit.h"
#import "UPDeviceLog.h"
#import "UpDeviceInjection.h"

NSString *const DisconnectRemoteDevices_NAME = @"disconnectRemoteDevices";

@implementation DisconnectRemoteDevices

+ (DisconnectRemoteDevices *)DisconnectRemoteDevices:(uSDKManager *)manager DeviceManager:(uSDKDeviceManager *)deviceManager
{
    return [[DisconnectRemoteDevices alloc] initWith:manager DeviceManager:deviceManager];
}

- (instancetype)initWith:(uSDKManager *)manager DeviceManager:(uSDKDeviceManager *)deviceManager
{
    if (self = [super initWithName:DisconnectRemoteDevices_NAME Manager:manager DeviceManager:deviceManager]) {
    }
    return self;
}

- (void)execute:(NSDictionary *)params finishBlock:(void (^)(UpDeviceResult *result))finishBlock
{
    NSError *error;
    BOOL setUserInfoResult = [[self getDeviceManager] setUserInfo:nil error:&error];
    if (setUserInfoResult) {
        UpDeviceResult *result = [UpDeviceResult UpDeviceResult:ErrorCode_SUCCESS extraData:nil];
        UPDeviceLogInfo(@"%s[%d]usdk断开云的连接成功！", __PRETTY_FUNCTION__, __LINE__);
        if (finishBlock) {
            finishBlock(result);
        }
    }
    else {
        UpDeviceResult *result = [[UpDeviceResult alloc] initWithErrorCode:ErrorCode_FAILURE extraData:nil extraCode:error.description extraInfo:nil];
        UPDeviceLogError(@"%s[%d]usdk断开云的连接失败！error:%@", __PRETTY_FUNCTION__, __LINE__, error.description);
        if (finishBlock) {
            finishBlock(result);
        }
    }
}

@end
