//
//  GetDeviceWifiLocalState.m
//  UPDevice
//
//  Created by 王炜 on 2021/12/7.
//  Copyright © 2021 海尔优家智能科技（北京）有限公司. All rights reserved.
//

#import "GetDeviceWifiLocalState.h"
#import "WifiDeviceHelper.h"
#import "UPDeviceLog.h"

NSString *const GetDeviceWifiLocalState_NAME = @"GetDeviceWifiLocalState";

@implementation GetDeviceWifiLocalState

+ (GetDeviceWifiLocalState *)GetDeviceWifiLocalState:(uSDKManager *)manager DeviceManager:(uSDKDeviceManager *)deviceManager
{
    return [[GetDeviceWifiLocalState alloc] initWith:manager DeviceManager:deviceManager];
}

- (instancetype)initWith:(uSDKManager *)manager DeviceManager:(uSDKDeviceManager *)deviceManager
{
    if (self = [super initWithName:GetDeviceWifiLocalState_NAME Manager:manager DeviceManager:deviceManager]) {
    }
    return self;
}

- (void)execute:(NSDictionary *)params finishBlock:(void (^)(UpDeviceResult *result))finishBlock
{
    NSString *deviceId = nil;
    if (params != nil) {
        deviceId = [self getParam:params Key:WifiDeviceAction_KEY_DEVICE_ID Class:[NSObject class]];
    }

    if (deviceId == nil) {
        UpDeviceResult *result = [UpDeviceResult UpDeviceResult:ErrorCode_FAILURE extraData:nil];
        UPDeviceLogError(@"%s[%d]device(%@)GetDeviceWifiLocalState!error:%@", __PRETTY_FUNCTION__, __LINE__, deviceId, result.extraInfo);
        if (finishBlock) {
            finishBlock(result);
        }
        return;
    }

    uSDKDevice *device = [self getDevice:deviceId];
    UpDeviceResult *result = [UpDeviceResult UpDeviceResult:ErrorCode_SUCCESS extraData:nil];
    UpDeviceConnection localState = [WifiDeviceHelper parseWifiLocalState:device.localState];
    result.extraData = [NSNumber numberWithInt:(int)localState];
    UPDeviceLogDebug(@"%s[%d]device(%@)GetDeviceWifiLocalState success！status:%ld", __PRETTY_FUNCTION__, __LINE__, deviceId, device.localState);
    if (finishBlock) {
        finishBlock(result);
    }
}

@end
