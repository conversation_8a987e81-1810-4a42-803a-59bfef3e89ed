//
//  GetDeviceCautions.m
//  UPDeviceDebugger
//
//  Created by f on 2/1/2019.
//  Copyright © 2019 海尔优家智能科技（北京）有限公司. All rights reserved.
//

#import "GetDeviceCautions.h"
#import "UpDeviceCaution.h"
#import "WifiDeviceHelper.h"
#import "UPDeviceLog.h"

NSString *const GetDeviceCautions_NAME = @"getDeviceCautionList";

@implementation GetDeviceCautions

+ (GetDeviceCautions *)GetDeviceCautions:(uSDKManager *)manager DeviceManager:(uSDKDeviceManager *)deviceManager
{
    return [[GetDeviceCautions alloc] initWith:manager DeviceManager:deviceManager];
}

- (instancetype)initWith:(uSDKManager *)manager DeviceManager:(uSDKDeviceManager *)deviceManager
{
    if (self = [super initWithName:GetDeviceCautions_NAME Manager:manager DeviceManager:deviceManager]) {
    }
    return self;
}

- (void)execute:(NSDictionary *)params finishBlock:(void (^)(UpDeviceResult *result))finishBlock
{
    NSString *deviceId = nil;
    if (params != nil) {
        deviceId = [self getParam:params Key:WifiDeviceAction_KEY_DEVICE_ID Class:[NSObject class]];
    }

    if (deviceId == nil) {
        UpDeviceResult *result = [UpDeviceResult UpDeviceResult:ErrorCode_FAILURE extraData:nil];
        UPDeviceLogError(@"%s[%d]设备(%@)报警信息获取失败!error:%@", __PRETTY_FUNCTION__, __LINE__, deviceId, result.extraInfo);
        if (finishBlock) {
            finishBlock(result);
        }
        return;
    }

    uSDKDevice *device = [self getDevice:deviceId];
    NSArray<uSDKDeviceAlarm *> *alarmList = [device alarmList];
    NSArray<id<UpDeviceCaution>> *cautionList;

    @synchronized(alarmList)
    {
        cautionList = [WifiDeviceHelper parseDeviceCautionList:alarmList];
    }

    UpDeviceResult *result = [UpDeviceResult UpDeviceResult:ErrorCode_SUCCESS extraData:nil];
    result.extraData = cautionList;
    UPDeviceLogDebug(@"%s[%d]设备(%@)报警信息获取成功！", __PRETTY_FUNCTION__, __LINE__, deviceId);
    if (finishBlock) {
        finishBlock(result);
    }
}

@end
