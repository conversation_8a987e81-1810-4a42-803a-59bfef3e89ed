//
//  GetDeviceWifiOnlineState.m
//  UPDevice
//
//  Created by lichen on 2025/4/18.
//  Copyright © 2025 海尔优家智能科技（北京）有限公司. All rights reserved.
//

#import "GetDeviceWifiOnlineState.h"
#import "WifiDeviceHelper.h"
#import "UPDeviceLog.h"

NSString *const GetDeviceWifiOnlineState_NAME = @"GetDeviceWifiOnlineState";

@implementation GetDeviceWifiOnlineState

+ (GetDeviceWifiOnlineState *)GetDeviceWifiOnlineState:(uSDKManager *)manager DeviceManager:(uSDKDeviceManager *)deviceManager
{
    return [[GetDeviceWifiOnlineState alloc] initWith:manager DeviceManager:deviceManager];
}

- (instancetype)initWith:(uSDKManager *)manager DeviceManager:(uSDKDeviceManager *)deviceManager
{
    if (self = [super initWithName:GetDeviceWifiOnlineState_NAME Manager:manager DeviceManager:deviceManager]) {
    }
    return self;
}

- (void)execute:(NSDictionary *)params finishBlock:(void (^)(UpDeviceResult *result))finishBlock
{
    NSString *deviceId = nil;
    if (params != nil) {
        deviceId = [self getParam:params Key:WifiDeviceAction_KEY_DEVICE_ID Class:[NSObject class]];
    }

    if (deviceId == nil) {
        UpDeviceResult *result = [UpDeviceResult UpDeviceResult:ErrorCode_FAILURE extraData:nil];
        UPDeviceLogError(@"%s[%d]device(%@)GetDeviceWifiOnlineState!error:%@", __PRETTY_FUNCTION__, __LINE__, deviceId, result.extraInfo);
        if (finishBlock) {
            finishBlock(result);
        }
        return;
    }

    uSDKDevice *device = [self getDevice:deviceId];
    if (!device) {
        UpDeviceResult *result = [UpDeviceResult UpDeviceResult:ErrorCode_FAILURE extraData:nil];
        UPDeviceLogError(@"%s[%d]devcie(%@)GetDeviceWifiOnlineState fail,no usdkDevice!error:%@", __PRETTY_FUNCTION__, __LINE__, deviceId, result.extraInfo);
        if (finishBlock) {
            finishBlock(result);
        }
        return;
    }

    UpDeviceResult *result = [UpDeviceResult UpDeviceResult:ErrorCode_SUCCESS extraData:nil];
    UpDeviceRealOnline onlineStatus = [WifiDeviceHelper paresOnlineState:device.wifiOnlineState];
    result.extraData = [NSNumber numberWithInt:(int)onlineStatus];
    UPDeviceLogDebug(@"%s[%d]device(%@)GetDeviceWifiOnlineState success！status:%lu", __PRETTY_FUNCTION__, __LINE__, deviceId, device.wifiOnlineState);
    if (finishBlock) {
        finishBlock(result);
    }
}

@end
