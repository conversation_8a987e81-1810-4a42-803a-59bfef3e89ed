//
//  AttachDevice.m
//  UPDeviceDebugger
//
//  Created by f on 2/1/2019.
//  Copyright © 2019 海尔优家智能科技（北京）有限公司. All rights reserved.
//

#import "AttachDevice.h"
#import "WifiDeviceAction.h"
#import "UPDeviceLog.h"
#import "UpDeviceInjection.h"

NSString *const AttachDevice_NAME = @"attachDevice";

NSString *const AttachDevice_KEY_DEVICE_LISTENER = @"usdk-device-listener";


@implementation AttachDevice

+ (AttachDevice *)AttachDevice:(uSDKManager *)manager DeviceManager:(uSDKDeviceManager *)deviceManager
{
    return [[AttachDevice alloc] initWith:manager DeviceManager:deviceManager];
}

- (instancetype)initWith:(uSDKManager *)manager DeviceManager:(uSDKDeviceManager *)deviceManager
{
    if (self = [super initWithName:AttachDevice_NAME Manager:manager DeviceManager:deviceManager]) {
    }

    return self;
}

- (void)execute:(NSDictionary *)params finishBlock:(void (^)(UpDeviceResult *result))finishBlock
{
    NSString *deviceId = nil;
    id<uSDKDeviceDelegate> deviceListener = nil;

    if (params != nil) {
        deviceId = [self getParam:params Key:WifiDeviceAction_KEY_DEVICE_ID Class:[NSString class]];

        deviceListener = [self getParam:params Key:AttachDevice_KEY_DEVICE_LISTENER Class:[NSObject class]];
    }

    if (deviceListener == nil || deviceId == nil) {
        UpDeviceResult *result = [[UpDeviceResult alloc] initWithErrorCode:ErrorCode_FAILURE extraData:nil extraCode:nil extraInfo:@"deviceListener或者mac为空"];
        UPDeviceLogError(@"%s[%d]执行失败! deviceListener:%@ deviceId:%@ error:deviceListener或者mac为空", __PRETTY_FUNCTION__, __LINE__, deviceListener, deviceId);
        if (finishBlock) {
            finishBlock(result);
        }
        return;
    }

    uSDKDevice *device = [self getDevice:deviceId];
    if (![device isKindOfClass:[uSDKDevice class]]) {
        NSString *msg = [NSString stringWithFormat:@"uSDK中未发现该设备对象：%@", deviceId];
        UpDeviceResult *result = [[UpDeviceResult alloc] initWithErrorCode:ErrorCode_FAILURE extraData:nil extraCode:nil extraInfo:msg];
        UPDeviceLogError(@"%s[%d]设备(%@)订阅失败! error:%@", __PRETTY_FUNCTION__, __LINE__, deviceId, msg);
        if (finishBlock) {
            finishBlock(result);
        }
        return;
    }

    device.delegate = deviceListener;
    if (device.isSubscribed) {
        UpDeviceResult *result = [UpDeviceResult UpDeviceResult:ErrorCode_SUCCESS extraData:nil];
        UPDeviceLogDebug(@"%s[%d]设备(%@)订阅usdk成功！", __PRETTY_FUNCTION__, __LINE__, deviceId);
        if (finishBlock) {
            finishBlock(result);
        }
        return;
    }
    __weak typeof(self) weakSelf = self;
    [self traceIot:device code:@"00000" bName:@"connectNeedProperties"];
    // 修改为同步调用
    NSError *error = [device connectNeedPropertiesSync];
    if (!error) {
        UpDeviceResult *result = [UpDeviceResult UpDeviceResult:ErrorCode_SUCCESS extraData:nil];
        UPDeviceLogDebug(@"%s[%d]设备(%@)订阅usdk成功！", __PRETTY_FUNCTION__, __LINE__, deviceId);
        if (finishBlock) {
            finishBlock(result);
        }
        [weakSelf traceIot:device code:@"00000" bName:@"connectNeedPropertiesResult"];
    }
    else {
        UpDeviceResult *result = [[UpDeviceResult alloc] initWithErrorCode:ErrorCode_FAILURE extraData:nil extraCode:nil extraInfo:error.description];
        UPDeviceLogError(@"%s[%d]设备(%@)订阅usdk失败！error:%@", __PRETTY_FUNCTION__, __LINE__, deviceId, error);
        if (finishBlock) {
            finishBlock(result);
        }
        [weakSelf traceIot:device code:[NSString stringWithFormat:@"%ld", (long)error.code] bName:@"connectNeedPropertiesResult"];
    }


    //    [device connectNeedPropertiesWithSuccess:^{
    //      UpDeviceResult *result = [UpDeviceResult UpDeviceResult:ErrorCode_SUCCESS extraData:nil];
    //      UPDeviceLogDebug(@"%s[%d]设备(%@)订阅usdk成功！", __PRETTY_FUNCTION__, __LINE__, deviceId);
    //      if (finishBlock) {
    //          finishBlock(result);
    //      }
    //      [weakSelf traceIot:device code:@"00000" bName:@"connectNeedPropertiesResult"];
    //    }
    //        failure:^(NSError *error) {
    //          UpDeviceResult *result = [[UpDeviceResult alloc] initWithErrorCode:ErrorCode_FAILURE extraData:nil extraCode:nil extraInfo:error.description];
    //          UPDeviceLogError(@"%s[%d]设备(%@)订阅usdk失败！error:%@", __PRETTY_FUNCTION__, __LINE__, deviceId, error);
    //          if (finishBlock) {
    //              finishBlock(result);
    //          }
    //          [weakSelf traceIot:device code:[NSString stringWithFormat:@"%ld", (long)error.code] bName:@"connectNeedPropertiesResult"];
    //        }];
}

- (void)traceIot:(uSDKDevice *)device code:(NSString *)code bName:(NSString *)bName
{
    uTrace *trace = [uTrace createDITrace];
    uTraceNodeDI *traceNode = [uTraceNodeDI new];
    traceNode.bId = @"fun";
    traceNode.sys = @"APP";
    traceNode.subSys = @"updevice";
    traceNode.bName = bName;
    traceNode.dId = device.deviceID;
    traceNode.code = code;
    NSMutableDictionary *argsDict = [NSMutableDictionary dictionary];
    argsDict[@"mac"] = device.deviceID;
    argsDict[@"user"] = @"1";
    traceNode.args = argsDict;
    NSError *error;
    [trace addTraceNodeDI:traceNode error:&error];
    if (error) {
        UPDeviceLogError(@"traceIot！%@埋点添加失败：error:%@", bName, error);
    }
}
@end
