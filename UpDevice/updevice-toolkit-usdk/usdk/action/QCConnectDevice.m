//
//  QCConnectDevice.m
//  UPDevice
//
//  Created by 王杰 on 2022/1/4.
//  Copyright © 2022 海尔优家智能科技（北京）有限公司. All rights reserved.
//

#import "QCConnectDevice.h"
#import "UPDeviceLog.h"
#import "WifiDeviceHelper.h"
#import <uSDK/uSDKBinding.h>

NSString *const QCConnectDevice_NAME = @"QCConnectDevice_NAME";

@implementation QCConnectDevice
+ (QCConnectDevice *)QCConnectDevice:(uSDKManager *)manager DeviceManager:(uSDKDeviceManager *)deviceManager
{
    return [[QCConnectDevice alloc] initWith:manager DeviceManager:deviceManager];
}

- (instancetype)initWith:(uSDKManager *)manager DeviceManager:(uSDKDeviceManager *)deviceManager
{
    if (self = [super initWithName:QCConnectDevice_NAME Manager:manager DeviceManager:deviceManager]) {
    }

    return self;
}

- (void)execute:(NSDictionary *)params finishBlock:(void (^)(UpDeviceResult *result))finishBlock
{
    NSString *deviceId = nil;
    if (params != nil) {
        deviceId = [self getParam:params Key:WifiDeviceAction_KEY_DEVICE_ID Class:[NSString class]];
    }
    if (deviceId.length == 0) {
        UpDeviceResult *result = [[UpDeviceResult alloc] initWithErrorCode:ErrorCode_FAILURE extraData:nil extraCode:nil extraInfo:@"deviceId empty！"];
        UPDeviceLogError(@"%s[%d]execute QCConnectDevice!deviceId:%@,error:%@", __PRETTY_FUNCTION__, __LINE__, deviceId, result.extraInfo);
        if (finishBlock) {
            finishBlock(result);
        }
        return;
    }
    UPDeviceLogDebug(@"%s[%d] StartQCConnectDevice!deviceId:%@", __PRETTY_FUNCTION__, __LINE__, deviceId);
    uSDKDevice *device = [self getDevice:deviceId];
    if (!device) {
        UpDeviceResult *result = [UpDeviceResult UpDeviceResult:ErrorCode_FAILURE extraData:nil];
        UPDeviceLogError(@"%s[%d]devcie(%@)QCConnectDevice fail,no usdkDevice!error:%@", __PRETTY_FUNCTION__, __LINE__, deviceId, result.extraInfo);
        if (finishBlock) {
            finishBlock(result);
        }
        return;
    }

    if (device.controlState == uSDKDeviceControlStateNone) {
        UpDeviceResult *result = [UpDeviceResult UpDeviceResult:ErrorCode_FAILURE extraData:nil];
        UPDeviceLogError(@"%s[%d]devcie(%@)already bind,noneed QCConnect!error:%@", __PRETTY_FUNCTION__, __LINE__, deviceId, result.extraInfo);
        if (finishBlock) {
            finishBlock(result);
        }
        return;
    }

    [device QCConnectWithSuccess:^{
      UpDeviceResult *result = [UpDeviceResult UpDeviceResult:ErrorCode_SUCCESS extraData:nil];
      UPDeviceLogDebug(@"%s[%d]device(%@)QCSubscribe usdk success！", __PRETTY_FUNCTION__, __LINE__, deviceId);
      if (finishBlock) {
          finishBlock(result);
      }
    }
        failure:^(NSError *error) {
          UpDeviceResult *result = [[UpDeviceResult alloc] initWithErrorCode:ErrorCode_FAILURE extraData:nil extraCode:[NSString stringWithFormat:@"%ld", error.code] extraInfo:error.description];
          UPDeviceLogError(@"%s[%d]device(%@)QCSubscribe usdk fail！error:%@", __PRETTY_FUNCTION__, __LINE__, deviceId, error);
          if (finishBlock) {
              finishBlock(result);
          }
        }];
    //        onTimeOut:^(uSDKQCConnectTimeoutType timeoutType) {
    //          UpDeviceResult *result = [[UpDeviceResult alloc] initWithErrorCode:ErrorCode_FAILURE extraData:[self transformToStringWithTimeoutType:timeoutType] extraCode:nil extraInfo:nil];
    //          UPDeviceLogError(@"%s[%d] QCConnectDevice timeout！timeoutType:%ld", __PRETTY_FUNCTION__, __LINE__, timeoutType);
    //          if (timeOutBlock) {
    //              timeOutBlock(result);
    //          }
    //        }];
}


@end
