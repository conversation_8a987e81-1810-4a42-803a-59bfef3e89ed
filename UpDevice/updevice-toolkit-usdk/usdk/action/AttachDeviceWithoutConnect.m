//
//  AttachDeviceWithoutConnect.m
//  UPDevice
//
//  Created by 闫达 on 2021/9/28.
//  Copyright © 2021 海尔优家智能科技（北京）有限公司. All rights reserved.
//

#import "AttachDeviceWithoutConnect.h"
#import "UPDeviceLog.h"

NSString *const AttachDeviceWithoutConnect_NAME = @"AttachDeviceWithoutConnect";
NSString *const AttachDeviceWithoutConnect_KEY_DEVICE_LISTENER = @"usdk-device-listener";

@implementation AttachDeviceWithoutConnect
+ (AttachDeviceWithoutConnect *)AttachDeviceWithoutConnect:(uSDKManager *)manager DeviceManager:(uSDKDeviceManager *)deviceManager
{
    return [[AttachDeviceWithoutConnect alloc] initWith:manager DeviceManager:deviceManager];
}

- (instancetype)initWith:(uSDKManager *)manager DeviceManager:(uSDKDeviceManager *)deviceManager
{
    if (self = [super initWithName:AttachDeviceWithoutConnect_NAME Manager:manager DeviceManager:deviceManager]) {
    }
    return self;
}
- (void)execute:(NSDictionary *)params finishBlock:(void (^)(UpDeviceResult *result))finishBlock
{
    NSString *deviceId = nil;
    id<uSDKDeviceDelegate> deviceListener = nil;

    if (params != nil) {
        deviceId = [self getParam:params Key:WifiDeviceAction_KEY_DEVICE_ID Class:[NSString class]];

        deviceListener = [self getParam:params Key:AttachDeviceWithoutConnect_KEY_DEVICE_LISTENER Class:[NSObject class]];
    }

    if (deviceListener == nil || deviceId == nil) {
        UpDeviceResult *result = [[UpDeviceResult alloc] initWithErrorCode:ErrorCode_FAILURE extraData:nil extraCode:nil extraInfo:@"deviceListener或者mac为空"];
        UPDeviceLogError(@"%s[%d]执行失败! deviceListener:%@ deviceId:%@ error:deviceListener或者mac为空", __PRETTY_FUNCTION__, __LINE__, deviceListener, deviceId);
        if (finishBlock) {
            finishBlock(result);
        }
        return;
    }

    uSDKDevice *device = [self getDevice:deviceId];
    if (![device isKindOfClass:[uSDKDevice class]]) {
        NSString *msg = [NSString stringWithFormat:@"uSDK中未发现该设备对象：%@", deviceId];
        UpDeviceResult *result = [[UpDeviceResult alloc] initWithErrorCode:ErrorCode_FAILURE extraData:nil extraCode:nil extraInfo:msg];
        UPDeviceLogError(@"%s[%d]设备(%@)订阅失败! error:%@", __PRETTY_FUNCTION__, __LINE__, deviceId, msg);
        if (finishBlock) {
            finishBlock(result);
        }
        return;
    }

    device.delegate = deviceListener;
    UpDeviceResult *result = [UpDeviceResult UpDeviceResult:ErrorCode_SUCCESS extraData:nil];
    UPDeviceLogDebug(@"%s[%d]设备(%@)订阅usdk成功！", __PRETTY_FUNCTION__, __LINE__, deviceId);
    if (finishBlock) {
        finishBlock(result);
    }
}

@end
