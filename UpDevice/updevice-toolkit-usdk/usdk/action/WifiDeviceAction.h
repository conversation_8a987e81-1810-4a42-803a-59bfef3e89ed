//
//  WifiDeviceAction.h
//  UPDeviceDebugger
//
//  Created by f on 2/1/2019.
//  Copyright © 2019 海尔优家智能科技（北京）有限公司. All rights reserved.
//

#import <Foundation/Foundation.h>
#import "WifiToolkitAction.h"
#import <uSDK/uSDK.h>

NS_ASSUME_NONNULL_BEGIN

extern NSString *const WifiDeviceAction_KEY_DEVICE_ID;

@interface WifiDeviceAction : WifiToolkitAction

- (nullable uSDKDevice *)getDevice:(NSString *)deviceId;
- (NSArray<uSDKDevice *> *)createuSDKDevice:(NSArray *)deviceIds;

@end

NS_ASSUME_NONNULL_END
