//
//  GetDeviceSleepState.m
//  UPDevice
//
//  Created by 闫达 on 2021/11/2.
//  Copyright © 2021 海尔优家智能科技（北京）有限公司. All rights reserved.
//

#import "GetDeviceSleepState.h"
#import "WifiDeviceHelper.h"
#import "UPDeviceLog.h"
NSString *const GetDeviceSleepState_NAME = @"GetDeviceSleepState";
@implementation GetDeviceSleepState
+ (GetDeviceSleepState *)GetDeviceSleepState:(uSDKManager *)manager DeviceManager:(uSDKDeviceManager *)deviceManager
{
    return [[GetDeviceSleepState alloc] initWith:manager DeviceManager:deviceManager];
}

- (instancetype)initWith:(uSDKManager *)manager DeviceManager:(uSDKDeviceManager *)deviceManager
{
    if (self = [super initWithName:GetDeviceSleepState_NAME Manager:manager DeviceManager:deviceManager]) {
    }
    return self;
}

- (void)execute:(NSDictionary *)params finishBlock:(void (^)(UpDeviceResult *result))finishBlock
{
    NSString *deviceId = nil;
    if (params != nil) {
        deviceId = [self getParam:params Key:WifiDeviceAction_KEY_DEVICE_ID Class:[NSObject class]];
    }

    if (deviceId == nil) {
        UpDeviceResult *result = [UpDeviceResult UpDeviceResult:ErrorCode_FAILURE extraData:nil];
        UPDeviceLogError(@"%s[%d]device(%@)GetDeviceSleepState!error:%@", __PRETTY_FUNCTION__, __LINE__, deviceId, result.extraInfo);
        if (finishBlock) {
            finishBlock(result);
        }
        return;
    }

    uSDKDevice *device = [self getDevice:deviceId];
    UpDeviceResult *result = [UpDeviceResult UpDeviceResult:ErrorCode_SUCCESS extraData:nil];
    UpDeviceSleepState sleepState = [WifiDeviceHelper parseSleepState:device.sleepState];
    result.extraData = [NSNumber numberWithInt:(int)sleepState];
    UPDeviceLogDebug(@"%s[%d]device(%@)GetDeviceSleepState success！state:%ld", __PRETTY_FUNCTION__, __LINE__, deviceId, device.sleepState);
    if (finishBlock) {
        finishBlock(result);
    }
}

@end
