//
//  WifiToolkitAction.h
//  UPDeviceDebugger
//
//  Created by f on 2/1/2019.
//  Copyright © 2019 海尔优家智能科技（北京）有限公司. All rights reserved.
//

#import <Foundation/Foundation.h>
#import "UpDeviceAction.h"
#import <uSDK/uSDK.h>

extern NSString *_Nullable const WifiToolkitAction_KEY_TIMEOUT;
extern NSTimeInterval const TIMEOUT_DEFAULT_GROUPDEVICE;
extern NSTimeInterval const TIMEOUT_ADD_GROUPDEVICE;
NS_ASSUME_NONNULL_BEGIN

@interface WifiToolkitAction : UpDeviceAction

- (instancetype)initWithName:(NSString *)name Manager:(uSDKManager *)manager DeviceManager:(uSDKDeviceManager *)deviceManager;

- (uSDKManager *)getManager;

- (uSDKDeviceManager *)getDeviceManager;

- (int)getTimeout:(NSDictionary<NSString *, id> *)params;

@end

NS_ASSUME_NONNULL_END
