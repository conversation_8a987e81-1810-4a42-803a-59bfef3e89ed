//
//  GetDeviceNetworkLevel.m
//  UPDevice
//
//  Created by 王杰 on 2021/12/1.
//  Copyright © 2021 海尔优家智能科技（北京）有限公司. All rights reserved.
//

#import "GetDeviceNetworkLevel.h"
#import "WifiDeviceHelper.h"
#import "UPDeviceLog.h"
#import "UpDeviceNetworkLevel.h"
NSString *const GetDeviceNetworkLevel_NAME = @"GetDeviceNetworkLevel";
@implementation GetDeviceNetworkLevel

+ (GetDeviceNetworkLevel *)GetDeviceNetworkLevel:(uSDKManager *)manager DeviceManager:(uSDKDeviceManager *)deviceManager
{
    return [[self alloc] initWith:manager DeviceManager:deviceManager];
}

- (instancetype)initWith:(uSDKManager *)manager DeviceManager:(uSDKDeviceManager *)deviceManager
{
    if (self = [super initWithName:GetDeviceNetworkLevel_NAME Manager:manager DeviceManager:deviceManager]) {
    }
    return self;
}

- (void)execute:(NSDictionary *)params finishBlock:(void (^)(UpDeviceResult *result))finishBlock
{
    NSString *deviceId = nil;
    if (params != nil) {
        deviceId = [self getParam:params Key:WifiDeviceAction_KEY_DEVICE_ID Class:[NSObject class]];
    }

    if (deviceId == nil) {
        UpDeviceResult *result = [UpDeviceResult UpDeviceResult:ErrorCode_FAILURE extraData:nil];
        UPDeviceLogError(@"%s[%d]device(%@)GetDeviceNetworkLevel!error:%@", __PRETTY_FUNCTION__, __LINE__, deviceId, result.extraInfo);
        if (finishBlock) {
            finishBlock(result);
        }
        return;
    }

    uSDKDevice *device = [self getDevice:deviceId];
    UpDeviceResult *result = [UpDeviceResult UpDeviceResult:ErrorCode_SUCCESS extraData:nil];
    UpDeviceNetworkLevel networkLevel = [WifiDeviceHelper parseNetworkLevel:device.qualityLevel];

    result.extraData = [NSNumber numberWithInt:(int)networkLevel];
    UPDeviceLogDebug(@"%s[%d]device(%@)GetDeviceNetworkLevel success！state:%ld", __PRETTY_FUNCTION__, __LINE__, deviceId, device.qualityLevel);
    if (finishBlock) {
        finishBlock(result);
    }
}


@end
