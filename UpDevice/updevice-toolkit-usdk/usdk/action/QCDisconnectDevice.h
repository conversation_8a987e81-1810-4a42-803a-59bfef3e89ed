//
//  QCDisconnectDevice.h
//  UPDevice
//
//  Created by 王杰 on 2022/1/4.
//  Copyright © 2022 海尔优家智能科技（北京）有限公司. All rights reserved.
//

#import "WifiDeviceAction.h"

NS_ASSUME_NONNULL_BEGIN

extern NSString *const QCDisconnectDevice_NAME;
@interface QCDisconnectDevice : WifiDeviceAction
+ (QCDisconnectDevice *)QCDisconnectDevice:(uSDKManager *)manager DeviceManager:(uSDKDeviceManager *)deviceManager;
@end

NS_ASSUME_NONNULL_END
