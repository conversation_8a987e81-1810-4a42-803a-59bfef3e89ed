//
//  GetuSDKDeviceWrapper.m
//  UPDevice
//
//  Created by <PERSON><PERSON><PERSON>   on 2025/4/9.
//  Copyright © 2025 海尔优家智能科技（北京）有限公司. All rights reserved.
//

#import "GetuSDKDeviceWrapper.h"
#import "WifiDeviceHelper.h"
#import "UPDeviceLog.h"
#import "uSDKDeviceWrapper.h"

NSString *const GetuSDKDeviceWrapper_NAME = @"GetuSDKDeviceWrapper_NAME";

@implementation GetuSDKDeviceWrapper
+ (GetuSDKDeviceWrapper *)getuSDKDeviceWrapper:(uSDKManager *)manager DeviceManager:(uSDKDeviceManager *)deviceManager
{
    return [[GetuSDKDeviceWrapper alloc] initWith:manager DeviceManager:deviceManager];
}

- (instancetype)initWith:(uSDKManager *)manager DeviceManager:(uSDKDeviceManager *)deviceManager
{
    if (self = [super initWithName:GetuSDKDeviceWrapper_NAME Manager:manager DeviceManager:deviceManager]) {
    }
    return self;
}

- (void)execute:(NSDictionary *)params finishBlock:(void (^)(UpDeviceResult *result))finishBlock
{
    NSString *deviceId = nil;
    if (params != nil) {
        deviceId = [self getParam:params Key:WifiDeviceAction_KEY_DEVICE_ID Class:[NSObject class]];
    }

    if (deviceId == nil) {
        UpDeviceResult *result = [UpDeviceResult UpDeviceResult:ErrorCode_FAILURE extraData:nil];
        UPDeviceLogError(@"%s[%d]device(%@) GetuSDKDeviceWrapper!error:%@", __PRETTY_FUNCTION__, __LINE__, deviceId, result.extraInfo);
        if (finishBlock) {
            finishBlock(result);
        }
        return;
    }

    uSDKDevice *device = [self getDevice:deviceId];
    if (!device) {
        UpDeviceResult *result = [UpDeviceResult UpDeviceResult:ErrorCode_FAILURE extraData:nil];
        UPDeviceLogError(@"%s[%d]devcie(%@)GetuSDKDeviceWrapper fail,no usdkDevice!error:%@", __PRETTY_FUNCTION__, __LINE__, deviceId, result.extraInfo);
        if (finishBlock) {
            finishBlock(result);
        }
        return;
    }
    UpDeviceResult *result = [UpDeviceResult UpDeviceResult:ErrorCode_SUCCESS extraData:nil];

    uSDKDeviceWrapper *wrapper = [[uSDKDeviceWrapper alloc] init];
    wrapper.offlineCause = [WifiDeviceHelper parseDeviceOfflineCause:device.activeOfflineCause];
    wrapper.offlineDays = device.offlineDays;
    wrapper.onlyConfigState = [WifiDeviceHelper parseOnlyConfigState:device.onlyConfigState];
    wrapper.connection = [WifiDeviceHelper parseDeviceConnection:device.state];
    wrapper.controlState = [WifiDeviceHelper parseDeviceControlState:device.controlState];
    wrapper.faultInformationCode = device.faultInfo.stateCode;
    wrapper.realOnlineStatus = [WifiDeviceHelper paresOnlineState:device.onlineState];
    wrapper.realOnlineStateV2 = [WifiDeviceHelper paresOnlineStateV2:device.onlineStateV2];
    wrapper.networkLevel = [WifiDeviceHelper parseNetworkLevel:device.qualityLevel];
    wrapper.bleState = [WifiDeviceHelper parseBleState:device.bleState];
    wrapper.sleepState = [WifiDeviceHelper parseSleepState:device.sleepState];
    NSArray<uSDKSubDevice *> *subDevList = [device subDeviceList];
    if (subDevList != nil) {
        wrapper.subDevInfoList = [self getSubDeviceList:subDevList];
    }
    NSDictionary<NSString *, uSDKDeviceAttribute *> *attrMap = [device attributeDict];
    if (attrMap != nil) {
        @synchronized(attrMap)
        {
            wrapper.attributeList = [WifiDeviceHelper parseDeviceAttributeList:attrMap.allValues];
        }
    }
    NSArray<uSDKDeviceAlarm *> *alarmList = [device alarmList];
    if (alarmList != nil) {
        @synchronized(alarmList)
        {
            wrapper.cautionList = [WifiDeviceHelper parseDeviceCautionList:alarmList];
        }
    }
    result.extraData = wrapper;
    UPDeviceLogDebug(@"%s[%d]device(%@)GetuSDKDeviceWrapper success！wrapper:%@", __PRETTY_FUNCTION__, __LINE__, deviceId, wrapper);
    if (finishBlock) {
        finishBlock(result);
    }
}

- (NSArray<id<UpDeviceBaseInfo>> *)getSubDeviceList:(NSArray<uSDKSubDevice *> *)subDevList
{
    NSMutableArray<id<UpDeviceBaseInfo>> *baseInfoList = [NSMutableArray array];
    for (uSDKDevice *device in subDevList) {
        DeviceBaseInfo *baseInfo = [WifiDeviceHelper createDeviceBaseInfo:device];
        if (baseInfo) {
            [baseInfoList addObject:baseInfo];
        }
    }
    return [NSArray arrayWithArray:baseInfoList];
}
@end
