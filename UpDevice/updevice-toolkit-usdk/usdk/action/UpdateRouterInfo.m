//
//  UpdateRouterInfo.m
//  UPDevice
//
//  Created by MAC on 2021/9/30.
//  Copyright © 2021 海尔优家智能科技（北京）有限公司. All rights reserved.
//

#import "UpdateRouterInfo.h"
#import "UPDeviceLog.h"
#import "WifiDeviceHelper.h"
#import <uSDK/uSDKRouterInfo.h>

NSString *const UpdateRouterInfo_NAME = @"UpdateRouterInfo";
NSString *const UPDATE_SSID_LISTENER = @"update-ssid-listener";
NSString *const UPDATE_SSID_SSID = @"ssid";
NSString *const UPDATE_SSID_PASSWORD = @"password";
NSString *const UPDATE_SSID_BSSID = @"bssid";
NSString *const UPDATE_SSID_TIMEOUT = @"timeout";

@implementation UpdateRouterInfo

+ (UpdateRouterInfo *)UpdateRouterInfo:(uSDKManager *)manager DeviceManager:(uSDKDeviceManager *)deviceManager
{
    return [[UpdateRouterInfo alloc] initWith:manager DeviceManager:deviceManager];
}

- (instancetype)initWith:(uSDKManager *)manager DeviceManager:(uSDKDeviceManager *)deviceManager
{
    if (self = [super initWithName:UpdateRouterInfo_NAME Manager:manager DeviceManager:deviceManager]) {
    }
    return self;
}

- (void)execute:(NSDictionary *)params finishBlock:(void (^)(UpDeviceResult *result))finishBlock
{
    NSString *deviceId = nil;
    void (^progressBlock)(UpDeviceResult *result);
    NSString *ssid = nil;
    NSString *password = nil;
    NSString *bssid = nil;
    NSNumber *timeout = nil;
    if (params != nil) {
        deviceId = [self getParam:params Key:WifiDeviceAction_KEY_DEVICE_ID Class:[NSObject class]];
        progressBlock = [self getParam:params Key:UPDATE_SSID_LISTENER Class:[NSObject class]];
        ssid = [self getParam:params Key:UPDATE_SSID_SSID Class:[NSObject class]];
        password = [self getParam:params Key:UPDATE_SSID_PASSWORD Class:[NSObject class]];
        bssid = [self getParam:params Key:UPDATE_SSID_BSSID Class:[NSObject class]];
        timeout = [self getParam:params Key:UPDATE_SSID_TIMEOUT Class:[NSNumber class]];
    }

    if (deviceId == nil) {
        UpDeviceResult *result = [UpDeviceResult UpDeviceResult:ErrorCode_FAILURE extraData:nil];
        UPDeviceLogError(@"%s[%d]devcie(%@) UpdateRouterInfo fail!error:%@", __PRETTY_FUNCTION__, __LINE__, deviceId, result.extraInfo);
        if (finishBlock) {
            finishBlock(result);
        }
        return;
    }

    uSDKDevice *device = [self getDevice:deviceId];
    if (!device) {
        UpDeviceResult *result = [UpDeviceResult UpDeviceResult:ErrorCode_FAILURE extraData:nil];
        UPDeviceLogError(@"%s[%d]devcie(%@)get UpdateRouterInfo fail,no usdkDevice!error:%@", __PRETTY_FUNCTION__, __LINE__, deviceId, result.extraInfo);
        if (finishBlock) {
            finishBlock(result);
        }
        return;
    }
    uSDKRouterInfo *info = [uSDKRouterInfo new];
    info.SSID = ssid;
    info.password = password;
    info.BSSID = bssid;
    info.timeoutInterval = 30;
    if (timeout && timeout.intValue > 0) {
        info.timeoutInterval = timeout.intValue;
    }
    info.traceNodeCS = [uTraceNode new];
    [device updateRouterInfo:info
        progressNotify:^(uSDKRouterInfoUpdateProgress updateProgress) {
          UPDeviceLogDebug(@"%s[%d]devcie(%@) UpdateRouterInfo progress！updateProgress:%lu", __PRETTY_FUNCTION__, __LINE__, deviceId, updateProgress);
          NSString *progress = [self transformUpdateProgressToString:updateProgress];
          UpDeviceResult *result = [UpDeviceResult UpDeviceResult:ErrorCode_SUCCESS extraData:progress];
          if (progressBlock) {
              progressBlock(result);
          }
        }
        completionHandler:^(NSError *error) {
          UpDeviceResult *result;
          if (!error) {
              NSDictionary *dataDic = @{
                  @"deviceId" : device.deviceID ?: @"",
                  @"deviceName" : device.deviceName ?: @"",
                  @"typeId" : device.uplusID ?: @""
              };
              result = [UpDeviceResult UpDeviceResult:ErrorCode_SUCCESS extraData:dataDic];
              UPDeviceLogDebug(@"%s[%d]devcie(%@) UpdateRouterInfo success！", __PRETTY_FUNCTION__, __LINE__, deviceId);
          }
          else {
              result = [[UpDeviceResult alloc] initWithErrorCode:ErrorCode_FAILURE extraData:nil extraCode:[NSString stringWithFormat:@"%ld", error.code] extraInfo:error.description];
              UPDeviceLogError(@"%s[%d]devcie(%@) UpdateRouterInfo fail！error:%@", __PRETTY_FUNCTION__, __LINE__, deviceId, error.description);
          }
          if (finishBlock) {
              finishBlock(result);
          }
        }];
}

- (NSString *)transformUpdateProgressToString:(uSDKRouterInfoUpdateProgress)updateProgress
{
    if (updateProgress == uSDKRouterInfoUpdateProgressConnecting) {
        return @"connecting";
    }
    else if (updateProgress == uSDKRouterInfoUpdateProgressUpdating) {
        return @"updating";
    }

    return @"";
}

@end
