//
//  IsBound.h
//  UPDevice
//
//  Created by gump on 20/9/2019.
//  Copyright © 2019 海尔优家智能科技（北京）有限公司. All rights reserved.
//

#import "WifiDeviceAction.h"
#import <uSDK/uSDK.h>

extern NSString *const IsBound_NAME;

/**
 * description: 标识设备是否被绑定了
 */

@interface IsBound : WifiDeviceAction

+ (IsBound *)IsBound:(uSDKManager *)manager DeviceManager:(uSDKDeviceManager *)deviceManager;

- (instancetype)initWith:(uSDKManager *)manager Devi<PERSON><PERSON>anager:(uSDKDeviceManager *)deviceManager;

@end
