//
//  InFocus.h
//  UPDevice
//
//  Created by gump on 7/2/2020.
//  Copyright © 2020 海尔优家智能科技（北京）有限公司. All rights reserved.
//

#import "WifiDeviceAction.h"
#import <uSDK/uSDK.h>

extern NSString *const InFocus_NAME;

/**
 * description: 标记设备进入焦点（详情页）
 */

@interface InFocus : WifiDeviceAction

+ (InFocus *)InFocus:(uSDKManager *)manager DeviceManager:(uSDKDeviceManager *)deviceManager;

- (instancetype)initWith:(uSDKManager *)manager DeviceManager:(uSDKDeviceManager *)deviceManager;

@end
