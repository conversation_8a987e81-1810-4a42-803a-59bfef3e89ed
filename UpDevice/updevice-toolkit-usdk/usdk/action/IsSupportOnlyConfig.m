//
//  IsSupportOnlyConfig.m
//  UPDevice
//
//  Created by lichen on 2025/4/18.
//  Copyright © 2025 海尔优家智能科技（北京）有限公司. All rights reserved.
//

#import "IsSupportOnlyConfig.h"
#import "WifiDeviceHelper.h"
#import "UPDeviceLog.h"

NSString *const IsSupportOnlyConfig_NAME = @"IsSupportOnlyConfig";

@implementation IsSupportOnlyConfig

+ (IsSupportOnlyConfig *)IsSupportOnlyConfig:(uSDKManager *)manager DeviceManager:(uSDKDeviceManager *)deviceManager
{
    return [[IsSupportOnlyConfig alloc] initWith:manager DeviceManager:deviceManager];
}

- (instancetype)initWith:(uSDKManager *)manager DeviceManager:(uSDKDeviceManager *)deviceManager
{
    if (self = [super initWithName:IsSupportOnlyConfig_NAME Manager:manager DeviceManager:deviceManager]) {
    }
    return self;
}

- (void)execute:(NSDictionary *)params finishBlock:(void (^)(UpDeviceResult *result))finishBlock
{
    NSString *deviceId = nil;
    if (params != nil) {
        deviceId = [self getParam:params Key:WifiDeviceAction_KEY_DEVICE_ID Class:[NSObject class]];
    }

    if (deviceId == nil) {
        UpDeviceResult *result = [UpDeviceResult UpDeviceResult:ErrorCode_FAILURE extraData:nil];
        UPDeviceLogError(@"%s[%d]device(%@)IsSupportOnlyConfig!error:%@", __PRETTY_FUNCTION__, __LINE__, deviceId, result.extraInfo);
        if (finishBlock) {
            finishBlock(result);
        }
        return;
    }

    uSDKDevice *device = [self getDevice:deviceId];
    if (!device) {
        UpDeviceResult *result = [UpDeviceResult UpDeviceResult:ErrorCode_FAILURE extraData:nil];
        UPDeviceLogError(@"%s[%d]devcie(%@)IsSupportOnlyConfig fail,no usdkDevice!error:%@", __PRETTY_FUNCTION__, __LINE__, deviceId, result.extraInfo);
        if (finishBlock) {
            finishBlock(result);
        }
        return;
    }

    BOOL isSupportOnlyConfig = device.isSupportOnlyConfig;
    UpDeviceResult *result = [UpDeviceResult UpDeviceResult:ErrorCode_SUCCESS extraData:[NSNumber numberWithBool:isSupportOnlyConfig]];

    UPDeviceLogDebug(@"%s[%d]device(%@)IsSupportOnlyConfig success！IsSupportOnlyConfig:%i", __PRETTY_FUNCTION__, __LINE__, deviceId, isSupportOnlyConfig);
    if (finishBlock) {
        finishBlock(result);
    }
}

@end
