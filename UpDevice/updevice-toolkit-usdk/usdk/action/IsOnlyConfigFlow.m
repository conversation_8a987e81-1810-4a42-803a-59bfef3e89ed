//
//  IsOnlyConfigFlow.m
//  UPDevice
//
//  Created by lichen on 2025/4/18.
//  Copyright © 2025 海尔优家智能科技（北京）有限公司. All rights reserved.
//

#import "IsOnlyConfigFlow.h"
#import "WifiDeviceHelper.h"
#import "UPDeviceLog.h"

NSString *const IsOnlyConfigFlow_NAME = @"IsOnlyConfigFlow";

@implementation IsOnlyConfigFlow

+ (IsOnlyConfigFlow *)IsOnlyConfigFlow:(uSDKManager *)manager DeviceManager:(uSDKDeviceManager *)deviceManager
{
    return [[IsOnlyConfigFlow alloc] initWith:manager DeviceManager:deviceManager];
}

- (instancetype)initWith:(uSDKManager *)manager DeviceManager:(uSDKDeviceManager *)deviceManager
{
    if (self = [super initWithName:IsOnlyConfigFlow_NAME Manager:manager DeviceManager:deviceManager]) {
    }
    return self;
}

- (void)execute:(NSDictionary *)params finishBlock:(void (^)(UpDeviceResult *result))finishBlock
{
    NSString *deviceId = nil;
    if (params != nil) {
        deviceId = [self getParam:params Key:WifiDeviceAction_KEY_DEVICE_ID Class:[NSObject class]];
    }

    if (deviceId == nil) {
        UpDeviceResult *result = [UpDeviceResult UpDeviceResult:ErrorCode_FAILURE extraData:nil];
        UPDeviceLogError(@"%s[%d]device(%@)IsOnlyConfigFlow!error:%@", __PRETTY_FUNCTION__, __LINE__, deviceId, result.extraInfo);
        if (finishBlock) {
            finishBlock(result);
        }
        return;
    }

    uSDKDevice *device = [self getDevice:deviceId];
    if (!device) {
        UpDeviceResult *result = [UpDeviceResult UpDeviceResult:ErrorCode_FAILURE extraData:nil];
        UPDeviceLogError(@"%s[%d]devcie(%@)IsOnlyConfigFlow fail,no usdkDevice!error:%@", __PRETTY_FUNCTION__, __LINE__, deviceId, result.extraInfo);
        if (finishBlock) {
            finishBlock(result);
        }
        return;
    }

    BOOL isOnlyConfigFlow = device.isOnlyConfigFlow;
    UpDeviceResult *result = [UpDeviceResult UpDeviceResult:ErrorCode_SUCCESS extraData:[NSNumber numberWithBool:isOnlyConfigFlow]];

    UPDeviceLogDebug(@"%s[%d]device(%@)IsOnlyConfigFlow success！IsOnlyConfigFlow:%i", __PRETTY_FUNCTION__, __LINE__, deviceId, isOnlyConfigFlow);
    if (finishBlock) {
        finishBlock(result);
    }
}


@end
