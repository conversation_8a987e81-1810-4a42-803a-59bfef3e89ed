//
//  WifiDeviceToolkitImpl.m
//  UPDeviceDebugger
//
//  Created by f on 2/1/2019.
//  Copyright © 2019 海尔优家智能科技（北京）有限公司. All rights reserved.
//

#import "WifiDeviceToolkitImpl.h"
#import "WifiToolkitAction.h"
#import "UpDeviceExecutable.h"
#import "AttachToolkit.h"
#import "DetachToolkit.h"
#import "GetDeviceList.h"
#import "AttachDevice.h"
#import "DetachDevice.h"
#import "GetDeviceInfo.h"
#import "GetDeviceConnection.h"
#import "GetSubDevList.h"
#import "GetDeviceAttributes.h"
#import "GetDeviceAttribute.h"
#import "GetDeviceCautions.h"
#import "ExecuteCommand.h"
#import "GetBindInfo.h"
#import "ConnectRemoteDevices.h"
#import "DisconnectRemoteDevices.h"
#import "WifiDeviceHelper.h"
#import "UpDeviceCarrier.h"
#import "MessageListener.h"
#import "GatewayMessageListener.h"
#import "GetDeviceConnection.h"
#import "GetDeviceAttribute.h"
#import "UpDeviceBaseInfo.h"
#import "UPDeviceLog.h"
#import "UpDeviceInjection.h"
#import "AttachResource.h"
#import "DetachResource.h"
#import "StartBoardFOTA.h"
#import "CheckBoardFOTAInfo.h"
#import "FetchBoardFOTAStatus.h"
#import "DeviceFOTAStatusInfo.h"
#import "StartModuleUpdate.h"
#import "DeviceBLEHistoryInfo.h"
#import "FetchBLEHistoryData.h"
#import "CancelFetchBLEHistoryData.h"
#import "IsModuleNeedOta.h"
#import "IsBound.h"
#import "RefresUsdkDeviceList.h"
#import "SmartLinkSoftwareVersion.h"
#import "GetSubDevListBySubDevice.h"
#import "InFocus.h"
#import "OutFocus.h"
#import "GetNetworkQuality.h"
#import <LogicEngine/LECommonFuncs.h>
#import "GetNetType.h"
#import "FetchGroupableDeviceList.h"
#import "CreateDeviceGroup.h"
#import "AddDevicesToGroup.h"
#import "RemoveDevicesFromGroup.h"
#import "DeleteDeviceGroup.h"
#import "GetGroupMemberList.h"
#import "AttachDecodeResource.h"
#import "IsGroup.h"
#import "StartWashFota.h"
#import "UpDeviceControlState.h"
#import "GetControlState.h"
#import "GetFaultInformationCode.h"
#import "AttachDeviceWithoutConnect.h"
#import "DetachDeviceWithoutConnect.h"
#import "BindDeviceWithoutWifi.h"
#import "UpdateRouterInfo.h"
#import "GetConfigRouterInfo.h"
#import "UpDeviceTracker.h"
#import "ConnectDevice.h"
#import "DisconnectDevice.h"
#import "UpDeviceOnlineStatus.h"
#import "GetDeviceOnlineStatus.h"
#import "GetDeviceSleepState.h"
#import "GetDeviceOnlineStateV2.h"
#import "GetDeviceNetworkLevel.h"
#import "QCConnectDevice.h"
#import "QCDisconnectDevice.h"
#import "GetDeviceWifiLocalState.h"
#import "GetDeviceBleState.h"
#import "UpDeviceHelper.h"
#import "UpDeviceOfflineCause.h"
#import "GetOfflineCause.h"
#import "GetOfflineDays.h"
#import "GetOnlyConfigState.h"
#import "ExecuteCommandWithResult.h"
#import "GetuSDKDeviceWrapper.h"
#import "IsSupportOnlyConfig.h"
#import "IsOnlyConfigFlow.h"
#import "GetDeviceWifiOnlineState.h"

static NSString *const FOTA_TRACE = @"MB17697";

@interface WifiDeviceToolkitImpl () <uSDKCommonLogReDirectProtocol> {
    uSDKManager *_manager;
    uSDKDeviceManager *_deviceManager;
    NSMutableDictionary<NSString *, id<UpDeviceExecutable>> *_executableMap;
    id<UpDeviceToolkitListener> _toolkitListenerRef;
    id<UpDeviceDetectListener> _detectListenerRef;
    BOOL _toolkitAttached;
    UpDeviceCarrier *_reportListenerCarrier;
    UpDeviceCarrier *_messageListenerCarrier;
    UpDeviceCarrier *_gatewayListenerCarrier;

    NSString *_supportProtocol;
    NSString *_toolkitVersion;
    UpDeviceToolkitState _toolkitState;
    id<UpDeviceTracker> _deviceTracker;
}

@property (nonatomic, copy) NSString *appID;
@property (nonatomic, copy) NSString *appKey;
@property (nonatomic, copy) NSString *secretKey;
@property (nonatomic, assign) UPDeviceuSDKIDCArea area;
@property (nonatomic, assign) UPDeviceuSDKFeatures features;
@property (nonatomic, assign) BOOL enableBLEControllableSearch;
@property (nonatomic, assign) BOOL enableWifiSearch;
@end

@implementation WifiDeviceToolkitImpl

+ (WifiDeviceToolkitImpl *)WifiDeviceToolkitImpl:(uSDKManager *)manager DeviceManager:(uSDKDeviceManager *)deviceManager
{
    return [[WifiDeviceToolkitImpl alloc] initWith:manager DeviceManager:deviceManager];
}

- (instancetype)init
{
    if (self = [super init]) {
        _executableMap = [NSMutableDictionary dictionary];
        _reportListenerCarrier = [[UpDeviceCarrier alloc] initWithUniversalKey:nil];
        _messageListenerCarrier = [[UpDeviceCarrier alloc] init];
        _gatewayListenerCarrier = [[UpDeviceCarrier alloc] init];

        _supportProtocol = WifiDeviceToolkit_PROTOCOL;
        _toolkitVersion = [uSDKManager defaultManager].uSDKVersion;
        _toolkitState = [WifiDeviceHelper parseState:[_manager state]];
    }

    return self;
}

- (instancetype)initWith:(uSDKManager *)manager DeviceManager:(uSDKDeviceManager *)deviceManager
{
    if (self = [self init]) {
        _deviceManager = deviceManager;
        _manager = manager;
        [uSDKLogManager sharedLogManager].delegate = self;
        [self initActionMap];

        _enableBLEControllableSearch = NO;
        _enableWifiSearch = NO;
    }

    return self;
}

- (void)setWifiDeviceToolkitAppId:(NSString *)appID appKey:(NSString *)appKey secretKey:(NSString *)secretKey area:(UPDeviceuSDKIDCArea)area features:(UPDeviceuSDKFeatures)features
{
    if (appID == nil || appKey == nil || secretKey == nil) {
        UPDeviceLogError(@"%s[%d]setWifiDeviceToolkitAppId失败！参数错误!appID:%@,appKey:%@,secretKey:%@", __PRETTY_FUNCTION__, __LINE__, appID, appKey, secretKey);
    }
    _appID = [self checkOutNullString:appID];
    _appKey = [self checkOutNullString:appKey];
    _secretKey = [self checkOutNullString:secretKey];
    _area = area;
    _features = features;
}

- (void)setWifiDeviceToolkitWith:(BOOL)enableBLEControllableSearch enableWifiSearch:(BOOL)enableWifiSearch
{
    UPDeviceLogError(@"%s[%d]setWifiDeviceToolkitWith enableBLEControllableSearch:%d enableWifiSearch:%d", __PRETTY_FUNCTION__, __LINE__, enableBLEControllableSearch, enableWifiSearch);
    _enableBLEControllableSearch = enableBLEControllableSearch;
    _enableWifiSearch = enableWifiSearch;
}

- (void)initActionMap
{
    AttachToolkit *attachToolkit = [AttachToolkit AttachToolkit:_manager DeviceManager:_deviceManager];
    [self addAction:attachToolkit];

    DetachToolkit *detachToolkit = [DetachToolkit DetachToolkit:_manager DeviceManager:_deviceManager];
    [self addAction:detachToolkit];

    GetDeviceList *getDeviceList = [GetDeviceList GetDeviceList:_manager DeviceManager:_deviceManager];
    [self addAction:getDeviceList];

    AttachDevice *attachDevice = [AttachDevice AttachDevice:_manager DeviceManager:_deviceManager];
    [self addAction:attachDevice];

    DetachDevice *detachDevice = [DetachDevice DetachDevice:_manager DeviceManager:_deviceManager];
    [self addAction:detachDevice];

    GetDeviceConnection *getDeviceConnection = [GetDeviceConnection GetDeviceConnection:_manager DeviceManager:_deviceManager];
    [self addAction:getDeviceConnection];

    GetSubDevList *getSubDevList = [GetSubDevList GetSubDevList:_manager DeviceManager:_deviceManager];
    [self addAction:getSubDevList];

    GetDeviceAttributes *getDeviceAttributes = [GetDeviceAttributes GetDeviceAttributes:_manager DeviceManager:_deviceManager];
    [self addAction:getDeviceAttributes];

    GetDeviceAttribute *getDeviceAttribute = [GetDeviceAttribute GetDeviceAttribute:_manager DeviceManager:_deviceManager];
    [self addAction:getDeviceAttribute];

    GetDeviceCautions *getDeviceCautions = [GetDeviceCautions GetDeviceCautions:_manager DeviceManager:_deviceManager];
    [self addAction:getDeviceCautions];

    ExecuteCommand *executeCommand = [ExecuteCommand ExecuteCommand:_manager DeviceManager:_deviceManager];
    [self addAction:executeCommand];

    GetBindInfo *getBindInfo = [GetBindInfo GetBindInfo:_manager DeviceManager:_deviceManager];
    [self addAction:getBindInfo];

    ConnectRemoteDevices *connectRemoteDevices = [ConnectRemoteDevices ConnectRemoteDevices:_manager DeviceManager:_deviceManager];
    [self addAction:connectRemoteDevices];

    DisconnectRemoteDevices *disconnectRemoteDevices = [DisconnectRemoteDevices DisconnectRemoteDevices:_manager DeviceManager:_deviceManager];
    [self addAction:disconnectRemoteDevices];
    AttachResource *attachResource = [AttachResource AttachResource:_manager DeviceManager:_deviceManager];
    [self addAction:attachResource];

    DetachResource *detachResource = [DetachResource DetachResource:_manager DeviceManager:_deviceManager];
    [self addAction:detachResource];

    StartBoardFOTA *startBoardFOTA = [StartBoardFOTA StartBoardFOTA:_manager DeviceManager:_deviceManager];
    [self addAction:startBoardFOTA];

    CheckBoardFOTAInfo *checkBoardFOTAInfo = [CheckBoardFOTAInfo CheckBoardFOTAInfo:_manager DeviceManager:_deviceManager];
    [self addAction:checkBoardFOTAInfo];

    FetchBoardFOTAStatus *fetchBoardFOTAStatus = [FetchBoardFOTAStatus FetchBoardFOTAStatus:_manager DeviceManager:_deviceManager];
    [self addAction:fetchBoardFOTAStatus];

    StartModuleUpdate *startModuleUpdate = [StartModuleUpdate StartModuleUpdate:_manager DeviceManager:_deviceManager];
    [self addAction:startModuleUpdate];

    FetchBLEHistoryData *fetchBLEHistoryData = [FetchBLEHistoryData FetchBLEHistoryData:_manager DeviceManager:_deviceManager];
    [self addAction:fetchBLEHistoryData];

    CancelFetchBLEHistoryData *cancelFetchBLEHistoryData = [CancelFetchBLEHistoryData CancelFetchBLEHistoryData:_manager DeviceManager:_deviceManager];
    [self addAction:cancelFetchBLEHistoryData];

    IsModuleNeedOta *isModuleNeedOta = [IsModuleNeedOta IsModuleNeedOta:_manager DeviceManager:_deviceManager];
    [self addAction:isModuleNeedOta];

    IsBound *isBound = [IsBound IsBound:_manager DeviceManager:_deviceManager];
    [self addAction:isBound];

    RefresUsdkDeviceList *refresUsdkDeviceList = [RefresUsdkDeviceList RefresUsdkDeviceList:_manager DeviceManager:_deviceManager];
    [self addAction:refresUsdkDeviceList];

    GetDeviceInfo *getDeviceInfo = [GetDeviceInfo GetDeviceInfo:_manager DeviceManager:_deviceManager];
    [self addAction:getDeviceInfo];

    SmartLinkSoftwareVersion *smartLinkSoftwareVersion = [SmartLinkSoftwareVersion SmartLinkSoftwareVersion:_manager DeviceManager:_deviceManager];
    [self addAction:smartLinkSoftwareVersion];

    GetSubDevListBySubDevice *getSubDevListBySubDevice = [GetSubDevListBySubDevice GetSubDevListBySubDevice:_manager DeviceManager:_deviceManager];
    [self addAction:getSubDevListBySubDevice];

    InFocus *inFocus = [InFocus InFocus:_manager DeviceManager:_deviceManager];
    [self addAction:inFocus];

    OutFocus *outFocus = [OutFocus OutFocus:_manager DeviceManager:_deviceManager];
    [self addAction:outFocus];

    GetNetworkQuality *getNetworkQuality = [GetNetworkQuality GetNetworkQuality:_manager DeviceManager:_deviceManager];
    [self addAction:getNetworkQuality];

    GetNetType *getNetType = [GetNetType getNetType:_manager DeviceManager:_deviceManager];
    [self addAction:getNetType];

    FetchGroupableDeviceList *fetchGroupableDeviceList = [FetchGroupableDeviceList FetchGroupableDeviceList:_manager DeviceManager:_deviceManager];
    [self addAction:fetchGroupableDeviceList];

    CreateDeviceGroup *createDeviceGroup = [CreateDeviceGroup CreateDeviceGroup:_manager DeviceManager:_deviceManager];
    [self addAction:createDeviceGroup];

    AddDevicesToGroup *addDevicesToGroup = [AddDevicesToGroup AddDevicesToGroup:_manager DeviceManager:_deviceManager];
    [self addAction:addDevicesToGroup];

    RemoveDevicesFromGroup *removeDevices = [RemoveDevicesFromGroup RemoveDevicesFromGroup:_manager DeviceManager:_deviceManager];
    [self addAction:removeDevices];

    DeleteDeviceGroup *deleteDeviceGroup = [DeleteDeviceGroup DeleteDeviceGroup:_manager DeviceManager:_deviceManager];
    [self addAction:deleteDeviceGroup];

    GetGroupMemberList *getGroupMembers = [GetGroupMemberList GetGroupMemberList:_manager DeviceManager:_deviceManager];
    [self addAction:getGroupMembers];

    AttachDecodeResource *decodeResource = [AttachDecodeResource AttachDecodeResource:_manager DeviceManager:_deviceManager];
    [self addAction:decodeResource];

    IsGroup *isGroup = [IsGroup IsGroup:_manager DeviceManager:_deviceManager];
    [self addAction:isGroup];

    StartWashFota *startWashFota = [StartWashFota StartWashFota:_manager DeviceManager:_deviceManager];
    [self addAction:startWashFota];

    GetControlState *getControlState = [GetControlState GetControlState:_manager DeviceManager:_deviceManager];
    [self addAction:getControlState];

    GetFaultInformationCode *getFaultInformationCode = [GetFaultInformationCode GetFaultInformationCode:_manager DeviceManager:_deviceManager];
    [self addAction:getFaultInformationCode];

    AttachDeviceWithoutConnect *attachWithoutConnect = [AttachDeviceWithoutConnect AttachDeviceWithoutConnect:_manager DeviceManager:_deviceManager];
    [self addAction:attachWithoutConnect];

    DetachDeviceWithoutConnect *detachWithoutConnect = [DetachDeviceWithoutConnect DetachDeviceWithoutConnect:_manager DeviceManager:_deviceManager];
    [self addAction:detachWithoutConnect];

    BindDeviceWithoutWifi *bindDeviceWithoutWifi = [BindDeviceWithoutWifi BindDeviceWithoutWifi:_manager DeviceManager:_deviceManager];
    [self addAction:bindDeviceWithoutWifi];

    UpdateRouterInfo *updateRouterInfo = [UpdateRouterInfo UpdateRouterInfo:_manager DeviceManager:_deviceManager];
    [self addAction:updateRouterInfo];

    GetConfigRouterInfo *getConfigRouterInfo = [GetConfigRouterInfo GetConfigRouterInfo:_manager DeviceManager:_deviceManager];
    [self addAction:getConfigRouterInfo];

    ConnectDevice *connectDevice = [ConnectDevice ConnectDevice:_manager DeviceManager:_deviceManager];
    [self addAction:connectDevice];

    DisconnectDevice *disconnectDevice = [DisconnectDevice DisconnectDevice:_manager DeviceManager:_deviceManager];
    [self addAction:disconnectDevice];

    GetDeviceOnlineStatus *getDeviceOnlineStatus = [GetDeviceOnlineStatus GetDeviceOnlineStatus:_manager DeviceManager:_deviceManager];
    [self addAction:getDeviceOnlineStatus];

    GetDeviceSleepState *getDeviceSleepState = [GetDeviceSleepState GetDeviceSleepState:_manager DeviceManager:_deviceManager];
    [self addAction:getDeviceSleepState];

    GetDeviceOnlineStateV2 *getDeviceOnlineStateV2 = [GetDeviceOnlineStateV2 GetDeviceOnlineStatus:_manager DeviceManager:_deviceManager];
    [self addAction:getDeviceOnlineStateV2];

    GetDeviceWifiOnlineState *getDeviceWifiOnlineState = [GetDeviceWifiOnlineState GetDeviceWifiOnlineState:_manager DeviceManager:_deviceManager];
    [self addAction:getDeviceWifiOnlineState];

    GetDeviceNetworkLevel *getDeviceNetworkLevel = [GetDeviceNetworkLevel GetDeviceNetworkLevel:_manager DeviceManager:_deviceManager];
    [self addAction:getDeviceNetworkLevel];

    QCConnectDevice *qcConnectDevice = [QCConnectDevice QCConnectDevice:_manager DeviceManager:_deviceManager];
    [self addAction:qcConnectDevice];

    QCDisconnectDevice *qcDisconnectDevice = [QCDisconnectDevice QCDisconnectDevice:_manager DeviceManager:_deviceManager];
    [self addAction:qcDisconnectDevice];

    GetDeviceWifiLocalState *getDeviceWifiLocalState = [GetDeviceWifiLocalState GetDeviceWifiLocalState:_manager DeviceManager:_deviceManager];
    [self addAction:getDeviceWifiLocalState];

    GetDeviceBleState *getDeviceBleState = [GetDeviceBleState GetDeviceBleState:_manager DeviceManager:_deviceManager];
    [self addAction:getDeviceBleState];

    GetOfflineCause *getOfflineCause = [GetOfflineCause getDeviceOfflineCause:_manager DeviceManager:_deviceManager];
    [self addAction:getOfflineCause];

    GetOfflineDays *getOfflineDays = [GetOfflineDays getDeviceOfflineDays:_manager DeviceManager:_deviceManager];
    [self addAction:getOfflineDays];

    GetOnlyConfigState *getOnlyConfigState = [GetOnlyConfigState getDeviceOnlyConfigState:_manager DeviceManager:_deviceManager];
    [self addAction:getOnlyConfigState];

    IsSupportOnlyConfig *isSupportOnlyConfig = [IsSupportOnlyConfig IsSupportOnlyConfig:_manager DeviceManager:_deviceManager];
    [self addAction:isSupportOnlyConfig];

    IsOnlyConfigFlow *isOnlyConfigFlow = [IsOnlyConfigFlow IsOnlyConfigFlow:_manager DeviceManager:_deviceManager];
    [self addAction:isOnlyConfigFlow];

    ExecuteCommandWithResult *executeCommandWithResult = [ExecuteCommandWithResult ExecuteCommandWithResult:_manager DeviceManager:_deviceManager];
    [self addAction:executeCommandWithResult];

    GetuSDKDeviceWrapper *usdkDeviceWrapper = [GetuSDKDeviceWrapper getuSDKDeviceWrapper:_manager DeviceManager:_deviceManager];
    [self addAction:usdkDeviceWrapper];
}

- (void)addAction:(WifiToolkitAction *)action
{
    if (action) {
        [_executableMap setObject:action forKey:action.name];
    }
}

- (id<UpDeviceExecutable>)findByAction:(NSString *)action
{
    id<UpDeviceExecutable> executable = [_executableMap objectForKey:action];
    return executable;
}

- (NSString *)supportProtocol
{
    return _supportProtocol;
}

- (NSString *)toolkitVersion
{
    return _toolkitVersion;
}

- (UpDeviceToolkitState)toolkitState
{
    return _toolkitState;
}

- (NSString *)getClientId
{
    return [[uSDKManager defaultManager] getClientID];
}

- (void)setSupportProtocol:(NSString *)protocol
{
    _supportProtocol = protocol;
}

- (void)setToolkitVersion:(NSString *)version
{
    _toolkitVersion = version;
}

- (void)setToolkitState:(UpDeviceToolkitState)state
{
    _toolkitState = state;
}


- (NSString *)getSupportProtocol
{
    return _supportProtocol;
}

- (NSString *)getToolkitVersion
{
    return _toolkitVersion;
}

- (UpDeviceToolkitState)getToolkitState
{
    return _toolkitState;
}

- (void)attachToolkit:(id<UpDeviceToolkitListener>)deviceToolkitListener detectListener:(id<UpDeviceDetectListener>)deviceDetectListener finishBlock:(void (^)(UpDeviceResult *result))finishBlock
{
    if (deviceToolkitListener != nil) {
        _toolkitListenerRef = deviceToolkitListener;
    }

    if (deviceDetectListener != nil) {
        _detectListenerRef = deviceDetectListener;
    }

    if (_toolkitAttached) {
        UpDeviceResult *result = [[UpDeviceResult alloc] initWithErrorCode:ErrorCode_SUCCESS extraData:nil extraCode:@"WifiDeviceToolkit has been already attached." extraInfo:nil];
        UPDeviceLogWarning(@"%s[%d]WifiDeviceToolkit has been already attached！", __PRETTY_FUNCTION__, __LINE__);
        if (finishBlock) {
            finishBlock(result);
        }
    }
    else {
        NSMutableDictionary<NSString *, NSObject *> *params = [NSMutableDictionary dictionary];
        [params setObject:self forKey:AttachToolkit_KEY_MANAGER_LISTENER];
        [params setObject:self forKey:AttachToolkit_KEY_DEVICE_MANAGER_LISTENER];
        if (LECommon_isValidString(self.appID)) {
            [params setObject:self.appID forKey:AttachToolkit_APP_ID];
        }
        if (LECommon_isValidString(self.appKey)) {
            [params setObject:self.appKey forKey:AttachToolkit_APP_KEY];
        }
        if (LECommon_isValidString(self.secretKey)) {
            [params setObject:self.secretKey forKey:AttachToolkit_SECRET_KEY];
        }
        [params setObject:[NSNumber numberWithInteger:self.area] forKey:AttachToolkit_AREA_KEY];
        [params setObject:[NSNumber numberWithUnsignedInteger:self.features] forKey:AttachToolkit_FEATURES_KEY];
        [params setObject:[NSNumber numberWithBool:self.enableBLEControllableSearch] forKey:AttachToolkit_ENABLE_BLE_KEY];
        [params setObject:[NSNumber numberWithBool:self.enableWifiSearch] forKey:AttachToolkit_ENABLE_WIFI_KEY];

        [self execute:AttachToolkit_NAME
                 params:params
            finishBlock:^(UpDeviceResult *result) {
              if ([result isSuccessful]) {
                  self->_toolkitAttached = YES;
                  UPDeviceLogDebug(@"%s[%d]执行attachToolkit成功！", __PRETTY_FUNCTION__, __LINE__);
              }
              else {
                  UPDeviceLogError(@"%s[%d]执行attachToolkit失败！error:%@", __PRETTY_FUNCTION__, __LINE__, result.extraInfo);
              }
              if (finishBlock) {
                  finishBlock(result);
              }
            }];
    }
}

- (void)detachToolkit:(void (^)(UpDeviceResult *result))finishBlock
{
    _toolkitListenerRef = nil;
    _detectListenerRef = nil;
    if (_toolkitAttached) {
        [self execute:DetachToolkit_NAME
                 params:nil
            finishBlock:^(UpDeviceResult *result) {
              if ([result isSuccessful]) {
                  self->_toolkitAttached = NO;
                  UPDeviceLogDebug(@"%s[%d]执行detachToolkit成功！", __PRETTY_FUNCTION__, __LINE__);
              }
              else {
                  UPDeviceLogError(@"%s[%d]执行detachToolkit失败！error:%@", __PRETTY_FUNCTION__, __LINE__, result.extraInfo);
              }
              if (finishBlock) {
                  finishBlock(result);
              }
            }];
    }
    else {
        UpDeviceResult *result = [[UpDeviceResult alloc] initWithErrorCode:ErrorCode_SUCCESS extraData:nil extraCode:@"WifiDeviceToolkit has been already detached." extraInfo:nil];
        UPDeviceLogWarning(@"%s[%d]WifiDeviceToolkit has been already attached！", __PRETTY_FUNCTION__, __LINE__);
        if (finishBlock) {
            finishBlock(result);
        }
    }
}
- (void)attachResource:(NSString *)deviceId resourceName:(NSString *)resourceName finishBlock:(void (^)(UpDeviceResult *result))finishBlock
{

    NSMutableDictionary<NSString *, NSObject *> *params = [NSMutableDictionary dictionary];
    [params setObject:deviceId ?: @"" forKey:WifiDeviceAction_KEY_DEVICE_ID];
    [params setObject:resourceName ?: @"" forKey:KEY_RESOURCE_NAME];
    [self execute:AttachResource_NAME params:params finishBlock:finishBlock];
}

- (void)attachResourceWithDecode:(NSString *)deviceId resourceName:(NSString *)resourceName finishBlock:(void (^)(UpDeviceResult *result))finishBlock
{
    NSMutableDictionary<NSString *, NSObject *> *params = [NSMutableDictionary dictionary];
    [params setObject:deviceId ?: @"" forKey:WifiDeviceAction_KEY_DEVICE_ID];
    [params setObject:resourceName ?: @"" forKey:KEY_RESOURCE_NAME];
    [self execute:AttachDecodeResource_NAME params:params finishBlock:finishBlock];
}

- (void)detachResource:(NSString *)deviceId resourceName:(NSString *)resourceName finishBlock:(void (^)(UpDeviceResult *result))finishBlock
{
    NSMutableDictionary<NSString *, NSObject *> *params = [NSMutableDictionary dictionary];
    [params setObject:deviceId ?: @"" forKey:WifiDeviceAction_KEY_DEVICE_ID];
    [params setObject:resourceName ?: @"" forKey:KEY_RESOURCE_NAME];
    [self execute:DetachResource_NAME params:params finishBlock:finishBlock];
}
- (void)disconnectRemoteDevices:(void (^)(UpDeviceResult *result))finishBlock
{
    [self execute:DisconnectRemoteDevices_NAME
             params:nil
        finishBlock:^(UpDeviceResult *result) {
          if (finishBlock) {
              finishBlock(result);
          }
        }];
}

- (void)getDeviceBaseInfoList:(void (^)(UpDeviceResult *result))finishBlock
{
    [self execute:GetDeviceList_NAME
             params:nil
        finishBlock:^(UpDeviceResult *result) {
          if (finishBlock) {
              finishBlock(result);
          }
        }];
}

- (void)attachDevice:(NSString *)deviceId reportListener:(id<UpDeviceReportListener>)listener finishBlock:(void (^)(UpDeviceResult *result))finishBlock
{
    if (deviceId != nil && deviceId.length > 0 && listener != nil) {
        [_reportListenerCarrier put:deviceId Target:listener];
    }
    NSMutableDictionary<NSString *, NSObject *> *params = [self paramsOfDeviceId:deviceId];
    [params setObject:self forKey:AttachDevice_KEY_DEVICE_LISTENER];
    [self execute:AttachDevice_NAME
             params:params
        finishBlock:^(UpDeviceResult *result) {
          if (finishBlock) {
              finishBlock(result);
          }
        }];
}

- (void)detachDevice:(NSString *)deviceId finishBlock:(void (^)(UpDeviceResult *result))finishBlock
{
    if (deviceId != nil && deviceId.length > 0) {
        [_reportListenerCarrier clear:deviceId];
    }
    NSMutableDictionary<NSString *, NSObject *> *params = [self paramsOfDeviceId:deviceId];
    [self execute:DetachDevice_NAME
             params:params
        finishBlock:^(UpDeviceResult *result) {
          if (finishBlock) {
              finishBlock(result);
          }
        }];
}

- (void)getDeviceBaseInfo:(NSString *)deviceId finishBlock:(void (^)(UpDeviceResult *result))finishBlock
{
    NSMutableDictionary<NSString *, NSObject *> *params = [self paramsOfDeviceId:deviceId];
    [self execute:GetDeviceInfo_NAME
             params:params
        finishBlock:^(UpDeviceResult *result) {
          if (finishBlock) {
              finishBlock(result);
          }
        }];
}

- (void)getDeviceConnection:(NSString *)deviceId finishBlock:(void (^)(UpDeviceResult *result))finishBlock
{
    NSMutableDictionary<NSString *, NSObject *> *params = [self paramsOfDeviceId:deviceId];
    [self execute:GetDeviceConnection_NAME
             params:params
        finishBlock:^(UpDeviceResult *result) {
          if (finishBlock) {
              finishBlock(result);
          }
        }];
}

- (void)getDeviceControlState:(NSString *)deviceId finishBlock:(void (^)(UpDeviceResult *))finishBlock
{
    NSMutableDictionary<NSString *, NSObject *> *params = [self paramsOfDeviceId:deviceId];
    [self execute:GetControlState_NAME
             params:params
        finishBlock:^(UpDeviceResult *result) {
          if (finishBlock) {
              finishBlock(result);
          }
        }];
}

- (void)getDeviceFaultInformationCode:(NSString *)deviceId finishBlock:(void (^)(UpDeviceResult *))finishBlock
{
    NSMutableDictionary<NSString *, NSObject *> *params = [self paramsOfDeviceId:deviceId];
    [self execute:GetFaultInformationCode_NAME
             params:params
        finishBlock:^(UpDeviceResult *result) {
          if (finishBlock) {
              finishBlock(result);
          }
        }];
}

- (void)getDeviceOnlineStatus:(NSString *)deviceId finishBlock:(void (^)(UpDeviceResult *))finishBlock
{
    NSMutableDictionary<NSString *, NSObject *> *params = [self paramsOfDeviceId:deviceId];
    [self execute:GetDeviceOnlineStatus_NAME
             params:params
        finishBlock:^(UpDeviceResult *result) {
          if (finishBlock) {
              finishBlock(result);
          }
        }];
}

- (void)getDeviceOnlineStateV2:(NSString *)deviceId finishBlock:(void (^)(UpDeviceResult *))finishBlock
{
    NSMutableDictionary<NSString *, NSObject *> *params = [self paramsOfDeviceId:deviceId];

    [self execute:GetDeviceOnlineStateV2_NAME
             params:params
        finishBlock:^(UpDeviceResult *result) {
          if (finishBlock) {
              finishBlock(result);
          }
        }];
}

- (UpDeviceRealOnline)getDeviceWifiOnlineState:(NSString *)deviceId
{
    NSMutableDictionary<NSString *, NSObject *> *params = [self paramsOfDeviceId:deviceId];

    __block UpDeviceRealOnline wifiOnline = UpDeviceRealOnline_OFFLINE;
    [self execute:GetDeviceWifiOnlineState_NAME
             params:params
        finishBlock:^(UpDeviceResult *result) {
          if (result.isSuccessful) {
              wifiOnline = [result.extraData integerValue];
          }
          else {
              UPLogError(kUPDevicePrefix, @"%s[%d]device(%@)wifiOnlineState fail! error:%@", __PRETTY_FUNCTION__, __LINE__, deviceId, result.extraInfo);
          }
        }];

    return wifiOnline;
}

- (void)getDeviceSleepState:(NSString *)deviceId finishBlock:(void (^)(UpDeviceResult *))finishBlock
{
    NSMutableDictionary<NSString *, NSObject *> *params = [self paramsOfDeviceId:deviceId];
    [self execute:GetDeviceSleepState_NAME
             params:params
        finishBlock:^(UpDeviceResult *result) {
          if (finishBlock) {
              finishBlock(result);
          }
        }];
}

- (void)getDeviceNetWorkLevel:(NSString *)deviceId finishBlock:(void (^)(UpDeviceResult *))finishBlock
{
    NSMutableDictionary<NSString *, NSObject *> *params = [self paramsOfDeviceId:deviceId];
    [self execute:GetDeviceNetworkLevel_NAME
             params:params
        finishBlock:^(UpDeviceResult *result) {
          if (finishBlock) {
              finishBlock(result);
          }
        }];
}

- (void)getDeviceWifiLocalState:(NSString *)deviceId finishBlock:(void (^)(UpDeviceResult *))finishBlock
{
    NSMutableDictionary<NSString *, NSObject *> *params = [self paramsOfDeviceId:deviceId];
    [self execute:GetDeviceWifiLocalState_NAME
             params:params
        finishBlock:^(UpDeviceResult *result) {
          if (finishBlock) {
              finishBlock(result);
          }
        }];
}

- (void)getDeviceOfflineCause:(NSString *)deviceId finishBlock:(void (^)(UpDeviceResult *))finishBlock
{
    NSMutableDictionary<NSString *, NSObject *> *params = [self paramsOfDeviceId:deviceId];
    [self execute:GetOfflineCause_NAME
             params:params
        finishBlock:^(UpDeviceResult *result) {
          if (finishBlock) {
              finishBlock(result);
          }
        }];
}

- (void)getDeviceOfflineDays:(NSString *)deviceId finishBlock:(void (^)(UpDeviceResult *))finishBlock
{
    NSMutableDictionary<NSString *, NSObject *> *params = [self paramsOfDeviceId:deviceId];
    [self execute:GetOfflineDays_NAME
             params:params
        finishBlock:^(UpDeviceResult *result) {
          if (finishBlock) {
              finishBlock(result);
          }
        }];
}

- (void)getDeviceOnlyConfigState:(NSString *)deviceId finishBlock:(void (^)(UpDeviceResult *))finishBlock
{
    NSMutableDictionary<NSString *, NSObject *> *params = [self paramsOfDeviceId:deviceId];
    [self execute:GetOnlyConfigState_NAME
             params:params
        finishBlock:^(UpDeviceResult *result) {
          if (finishBlock) {
              finishBlock(result);
          }
        }];
}

- (BOOL)isSupportOnlyConfig:(NSString *)deviceId
{
    NSMutableDictionary<NSString *, NSObject *> *params = [self paramsOfDeviceId:deviceId];

    __block BOOL isSupportOnlyConfig;
    [self execute:IsSupportOnlyConfig_NAME
             params:params
        finishBlock:^(UpDeviceResult *result) {
          if (result.isSuccessful) {
              isSupportOnlyConfig = [result.extraData boolValue];
          }
          else {
              isSupportOnlyConfig = NO;
          }
        }];

    return isSupportOnlyConfig;
}

- (BOOL)isOnlyConfigFlow:(NSString *)deviceId
{
    NSMutableDictionary<NSString *, NSObject *> *params = [self paramsOfDeviceId:deviceId];

    __block BOOL isOnlyConfigFlow;
    [self execute:IsOnlyConfigFlow_NAME
             params:params
        finishBlock:^(UpDeviceResult *result) {
          if (result.isSuccessful) {
              isOnlyConfigFlow = [result.extraData boolValue];
          }
          else {
              isOnlyConfigFlow = NO;
          }
        }];

    return isOnlyConfigFlow;
}

- (void)getSubDevBaseInfoList:(NSString *)deviceId finishBlock:(void (^)(UpDeviceResult *))finishBlock
{
    NSMutableDictionary<NSString *, NSObject *> *params = [self paramsOfDeviceId:deviceId];
    [self execute:GetSubDevList_NAME
             params:params
        finishBlock:^(UpDeviceResult *result) {
          if (finishBlock) {
              finishBlock(result);
          }
        }];
}


- (void)getDeviceAttributeList:(NSString *)deviceId finishBlock:(void (^)(UpDeviceResult *result))finishBlock
{
    NSMutableDictionary<NSString *, NSObject *> *params = [self paramsOfDeviceId:deviceId];
    [self execute:GetDeviceAttributes_NAME
             params:params
        finishBlock:^(UpDeviceResult *result) {
          if (finishBlock) {
              finishBlock(result);
          }
        }];
}

- (void)getDeviceCautionList:(NSString *)deviceId finishBlock:(void (^)(UpDeviceResult *result))finishBlock
{
    NSMutableDictionary<NSString *, NSObject *> *params = [self paramsOfDeviceId:deviceId];
    [self execute:GetDeviceCautions_NAME
             params:params
        finishBlock:^(UpDeviceResult *result) {
          if (finishBlock) {
              finishBlock(result);
          }
        }];
}

- (void)getDeviceAttributeByName:(NSString *)name deviceId:(NSString *)deviceId timeout:(NSTimeInterval)timeout finishBlock:(void (^)(UpDeviceResult *result))finishBlock
{
    if (name == nil || deviceId == nil) {
        UPDeviceLogError(@"%s[%d]getDeviceAttributeByName失败！参数错误! name:%@,deviceId:%@", __PRETTY_FUNCTION__, __LINE__, name, deviceId);
    }
    NSMutableDictionary<NSString *, NSObject *> *params = [self paramsOfDeviceId:deviceId];
    [params setObject:[self checkOutNullString:name] forKey:GetDeviceAttribute_KEY_ATTR_NAME];
    [params setObject:@(timeout) forKey:WifiToolkitAction_KEY_TIMEOUT];
    [self execute:GetDeviceCautions_NAME
             params:params
        finishBlock:^(UpDeviceResult *result) {
          if (finishBlock) {
              finishBlock(result);
          }
        }];
}

- (void)executeDeviceCommand:(NSString *)deviceId command:(id<UpDeviceCommand>)command timeout:(NSTimeInterval)timeout finishBlock:(void (^)(UpDeviceResult *result))finishBlock
{
    NSMutableDictionary<NSString *, NSObject *> *params = [self paramsOfDeviceId:deviceId];
    [params setObject:command forKey:ExecuteCommand_KEY_DEVICE_COMMAND];
    [params setObject:@(timeout) forKey:WifiToolkitAction_KEY_TIMEOUT];
    [self execute:ExecuteCommand_NAME
             params:params
        finishBlock:^(UpDeviceResult *result) {
          if (finishBlock) {
              finishBlock(result);
          }
        }];
}

- (void)executeCommandWithResult:(NSString *)deviceId command:(id<UpDeviceCommand>)command timeout:(NSTimeInterval)timeout finishBlock:(void (^)(UpDeviceResult *result))finishBlock
{
    NSMutableDictionary<NSString *, NSObject *> *params = [self paramsOfDeviceId:deviceId];
    [params setObject:command forKey:ExecuteCommand_KEY_DEVICE_COMMAND];
    [params setObject:@(timeout) forKey:WifiToolkitAction_KEY_TIMEOUT];
    [self execute:ExecuteCommandWithResult_NAME
             params:params
        finishBlock:^(UpDeviceResult *result) {
          if (finishBlock) {
              finishBlock(result);
          }
        }];
}

- (void)connectRemoteDevices:(NSMutableArray<id<UpDeviceBaseInfo>> *)baseInfoList params:(NSMutableDictionary *)params finishBlock:(void (^)(UpDeviceResult *))finishBlock
{

    if (params == nil) {
        params = [NSMutableDictionary dictionary];
    }
    [self execute:ConnectRemoteDevices_NAME
             params:params
        finishBlock:^(UpDeviceResult *result) {
          if (finishBlock) {
              finishBlock(result);
          }
        }];
}

- (void)bindDeviceWithoutWifi:(NSString *)deviceId progressBlock:(void (^)(UpDeviceResult *))progressBlock finishBlock:(void (^)(UpDeviceResult *))finishBlock
{
    NSMutableDictionary<NSString *, NSObject *> *params = [self paramsOfDeviceId:deviceId];
    if (progressBlock) {
        [params setObject:progressBlock forKey:BIND_WITHOUT_WIFI_LISTENER];
    }
    [self execute:BindDeviceWithoutWifi_NAME
             params:params
        finishBlock:^(UpDeviceResult *result) {
          if (finishBlock) {
              finishBlock(result);
          }
        }];
}

- (void)updateRouterInfo:(NSString *)deviceId routerInfo:(NSDictionary *)info progressBlock:(void (^)(UpDeviceResult *result))progressBlock finishBlock:(void (^)(UpDeviceResult *result))finishBlock
{
    NSMutableDictionary<NSString *, NSObject *> *params = [self paramsOfDeviceId:deviceId];
    if ([info isKindOfClass:NSDictionary.class]) {
        [params addEntriesFromDictionary:info];
    }
    if (progressBlock) {
        [params setObject:progressBlock forKey:UPDATE_SSID_LISTENER];
    }
    [self execute:UpdateRouterInfo_NAME
             params:params
        finishBlock:^(UpDeviceResult *result) {
          if (finishBlock) {
              finishBlock(result);
          }
        }];
}

- (void)getConfigRouterInfo:(void (^)(UpDeviceResult *result))finishBlock
{
    [self execute:GetConfigRouterInfo_NAME
             params:nil
        finishBlock:^(UpDeviceResult *result) {
          if (finishBlock) {
              finishBlock(result);
          }
        }];
}

- (void)connectDevice:(NSString *)deviceId finishBlock:(void (^)(UpDeviceResult *))finishBlock
{
    NSMutableDictionary<NSString *, NSObject *> *params = [self paramsOfDeviceId:deviceId];
    [self execute:ConnectDevice_NAME
             params:params
        finishBlock:^(UpDeviceResult *result) {
          if (finishBlock) {
              finishBlock(result);
          }
        }];
}

- (void)disConnectDevice:(NSString *)deviceId finishBlock:(void (^)(UpDeviceResult *))finishBlock
{
    NSMutableDictionary<NSString *, NSObject *> *params = [self paramsOfDeviceId:deviceId];
    [self execute:DisconnectDevice_NAME
             params:params
        finishBlock:^(UpDeviceResult *result) {
          if (finishBlock) {
              finishBlock(result);
          }
        }];
}

- (void)QCConnectDevice:(NSString *)deviceId finishBlock:(void (^)(UpDeviceResult *))finishBlock
{
    NSMutableDictionary<NSString *, NSObject *> *params = [self paramsOfDeviceId:deviceId];
    [self execute:QCConnectDevice_NAME
             params:params
        finishBlock:^(UpDeviceResult *result) {
          if (finishBlock) {
              finishBlock(result);
          }
        }];
}

- (void)QCDisConnectDevice:(NSString *)deviceId finishBlock:(void (^)(UpDeviceResult *))finishBlock
{
    NSMutableDictionary<NSString *, NSObject *> *params = [self paramsOfDeviceId:deviceId];
    [self execute:QCDisconnectDevice_NAME
             params:params
        finishBlock:^(UpDeviceResult *result) {
          if (finishBlock) {
              finishBlock(result);
          }
        }];
}

- (void)getDeviceBleState:(NSString *)deviceId finishBlock:(void (^)(UpDeviceResult *))finishBlock
{
    NSMutableDictionary<NSString *, NSObject *> *params = [self paramsOfDeviceId:deviceId];
    [self execute:GetDeviceBleState_NAME
             params:params
        finishBlock:^(UpDeviceResult *result) {
          if (finishBlock) {
              finishBlock(result);
          }
        }];
}


- (void)setTracker:(id<UpDeviceTracker>)tracker
{
    _deviceTracker = tracker;
}

- (void)execute:(NSString *)action params:(NSDictionary *)params finishBlock:(void (^)(UpDeviceResult *))finishBlock
{
    [[self findByAction:action] execute:action params:params finishBlock:finishBlock];
}

#pragma mark - uSDKManagerDelegate

- (void)uSDKManager:(uSDKManager *)sdkManager sessionException:(NSString *)token
{
    if (!token) {
        UPDeviceLogWarning(@"%s[%d]token为空！", __PRETTY_FUNCTION__, __LINE__);
        return;
    }
    NSArray<id<MessageListener>> *messageListenerList = [_messageListenerCarrier get];
    for (id<MessageListener> listener in messageListenerList) {
        [listener onSessionException:token];
    }
}

- (void)uSDKManager:(uSDKManager *)sdkManager businessMessage:(NSString *)businessMessage
{
    if (!businessMessage) {
        UPDeviceLogWarning(@"%s[%d]businessMessage为空！", __PRETTY_FUNCTION__, __LINE__);
        return;
    }

    NSArray<id<MessageListener>> *messageListenerList = [_messageListenerCarrier get];
    for (id<MessageListener> listener in messageListenerList) {
        [listener onBusinessMessage:businessMessage];
    }
}

#pragma mark - uSDKLogManagerDelegate

- (void)logReDirectWithTag:(NSString *)tag level:(uSDKLogLevelConst)level msg:(NSString *)msg
{
    switch (level) {
        case USDK_LOG_ERROR:
            UPLogError(@"uSDK", @"%@", msg);
            break;
        case USDK_LOG_INFO:
            UPLogInfo(@"uSDK", @"%@", msg);
            break;
        case USDK_LOG_DEBUG:
            UPLogDebug(@"uSDK", @"%@", msg);
            break;
        case USDK_LOG_WARNING:
            UPLogWarning(@"uSDK", @"%@", msg);
            break;

        default:
            UPLogInfo(@"uSDK", @"%@", msg);
            break;
    }
}

#pragma mark - uSDKDeviceManagerDelegate

- (void)deviceManager:(uSDKDeviceManager *)deviceManager didAddDevices:(NSArray<uSDKDevice *> *)devices
{
    if (!devices) {
        UPDeviceLogWarning(@"%s[%d]addDevices为空！", __PRETTY_FUNCTION__, __LINE__);
        return;
    }
    NSArray<id<UpDeviceBaseInfo>> *baseInfoList = [WifiDeviceHelper createDeviceBaseInfoList:devices];
    id<UpDeviceDetectListener> listener = _detectListenerRef;
    if (listener) {
        [listener onFind:baseInfoList];
    }
    NSArray *familyPriorityPrepareQueue = [[UpDeviceInjection getInstance].deviceManager curFamilyPriorityPrepareQueue];
    if (familyPriorityPrepareQueue.count > 0) {
        UPDeviceLogInfo(@"%s[%d]familyPriorityPrepareQueue %@", __PRETTY_FUNCTION__, __LINE__, familyPriorityPrepareQueue);
        [devices enumerateObjectsUsingBlock:^(uSDKDevice *_Nonnull usdkDevice, NSUInteger idx, BOOL *_Nonnull stop) {
          NSString *uniqueId = [UpDeviceHelper genUniqueId:[self getSupportProtocol] DeviceId:usdkDevice.deviceID];
          if ([familyPriorityPrepareQueue containsObject:uniqueId]) {
              id<UpDevice> upDevice = [[UpDeviceInjection getInstance].deviceManager getDevice:usdkDevice.deviceID];
              if (upDevice && upDevice.getState == UpDeviceState_RELEASED) {
                  UPDeviceLogInfo(@"%s[%d]familyPriorityPrepareQueue uSDK find device and not attached %@", __PRETTY_FUNCTION__, __LINE__, upDevice.getInfo.deviceId);
                  [[UpDeviceInjection getInstance].deviceManager moveDeviceToQueueHeadPure:usdkDevice.deviceID];
              }
          }
        }];
    }

    NSArray *priorityPrepareQueue = [[UpDeviceInjection getInstance].deviceManager getPriorityPrepareQueue];
    if (priorityPrepareQueue.count > 0) {
        UPDeviceLogInfo(@"%s[%d]priorityPrepareQueue %@", __PRETTY_FUNCTION__, __LINE__, priorityPrepareQueue);
        [devices enumerateObjectsUsingBlock:^(uSDKDevice *_Nonnull usdkDevice, NSUInteger idx, BOOL *_Nonnull stop) {
          NSString *uniqueId = [UpDeviceHelper genUniqueId:[self getSupportProtocol] DeviceId:usdkDevice.deviceID];
          if ([priorityPrepareQueue containsObject:uniqueId]) {
              id<UpDevice> upDevice = [[UpDeviceInjection getInstance].deviceManager getDevice:usdkDevice.deviceID];
              if (upDevice && upDevice.getState == UpDeviceState_RELEASED) {
                  UPDeviceLogInfo(@"%s[%d]uSDK find device and not attached %@", __PRETTY_FUNCTION__, __LINE__, upDevice.getInfo.deviceId);
                  [[UpDeviceInjection getInstance].deviceManager moveDeviceToQueueHead:usdkDevice.deviceID];
              }
          }
        }];
    }
}

- (void)deviceManager:(uSDKDeviceManager *)deviceManager didRemoveDevices:(NSArray<uSDKDevice *> *)devices
{
    if (!devices) {
        UPDeviceLogWarning(@"%s[%d]removeDevices为空！", __PRETTY_FUNCTION__, __LINE__);
        return;
    }
    NSArray<id<UpDeviceBaseInfo>> *baseInfoList = [WifiDeviceHelper createDeviceBaseInfoList:devices];
    id<UpDeviceDetectListener> listener = _detectListenerRef;
    if (listener) {
        [listener onLose:baseInfoList];
    }
}

- (void)deviceManager:(uSDKDeviceManager *)deviceManager didBindDevice:(NSString *)deviceID
{
    if (!deviceID) {
        UPDeviceLogWarning(@"%s[%d]deviceID参数为空！", __PRETTY_FUNCTION__, __LINE__);
        return;
    }
    NSArray<id<GatewayMessageListener>> *listenerList = [_gatewayListenerCarrier get];
    for (id<GatewayMessageListener> listener in listenerList) {
        [listener onDeviceBound:deviceID];
    }
    id<UpDeviceDetectListener> listener = _detectListenerRef;
    if (listener) {
        [listener onDeviceListChanged];
    }
}

- (void)deviceManager:(uSDKDeviceManager *)deviceManager didUnbindDevice:(NSString *)deviceID
{
    if (!deviceID) {
        UPDeviceLogWarning(@"%s[%d]deviceID参数为空！", __PRETTY_FUNCTION__, __LINE__);
        return;
    }
    NSArray<id<GatewayMessageListener>> *listenerList = [_gatewayListenerCarrier get];
    for (id<GatewayMessageListener> listener in listenerList) {
        [listener onDeviceUnbound:deviceID];
    }
    id<UpDeviceDetectListener> listener = _detectListenerRef;
    if (listener) {
        [listener onDeviceListChanged];
    }
}

- (void)deviceManager:(uSDKDeviceManager *)deviceManager didUpdateCloudState:(uSDKCloudConnectionState)state error:(NSError *)offlineReason
{
    GatewayConnection gatewayConnection = [WifiDeviceHelper parseGatewayConnection:state];
    UPDeviceLogInfo(@"%s[%d]UpdateCloudState %ld！", __PRETTY_FUNCTION__, __LINE__, (long)state);
    NSArray<id<GatewayMessageListener>> *listenerList = [_gatewayListenerCarrier get];
    for (id<GatewayMessageListener> listener in listenerList) {
        [listener onGatewayConnectionChange:gatewayConnection];
    }
}

#pragma mark - uSDKDeviceDelegate

- (void)deviceDidUpdateBaseInfo:(uSDKDevice *)device
{
    if (!device) {
        UPDeviceLogWarning(@"%s[%d]device为空！", __PRETTY_FUNCTION__, __LINE__);
        return;
    }
    id<UpDeviceBaseInfo> baseInfo = [WifiDeviceHelper createDeviceBaseInfo:device];
    UpDeviceControlState controlState = [WifiDeviceHelper parseDeviceControlState:device.controlState];
    UPDeviceLogDebug(@"[%s][%d]devcie controlState ,typeId and bleState report.mac:%@,controlState:%ld ,typeId:%@", __PRETTY_FUNCTION__, __LINE__, device.deviceID, device.controlState, device.uplusID);
    NSArray<id<UpDeviceReportListener>> *listeners = [_reportListenerCarrier get:device.deviceID];
    if (listeners) {
        for (id<UpDeviceReportListener> listener in listeners) {
            [listener onControlStateChange:baseInfo controlState:controlState];
            [listener onDeviceInfoChange:baseInfo];
        }
    }
}

- (void)device:(uSDKDevice *)device didUpdateState:(uSDKDeviceState)state error:(NSError *)error
{
    if (!device) {
        UPDeviceLogWarning(@"%s[%d]device为空！", __PRETTY_FUNCTION__, __LINE__);
        return;
    }
    UPDeviceLogDebug(@"[%s][%d]mac:%@,state:%ld,error:%@", __PRETTY_FUNCTION__, __LINE__, device.deviceID, state, error);
    id<UpDeviceBaseInfo> baseInfo = [WifiDeviceHelper createDeviceBaseInfo:device];
    UpDeviceConnection connection = [WifiDeviceHelper parseDeviceConnection:state];
    NSArray<id<UpDeviceReportListener>> *listeners = [_reportListenerCarrier get:device.deviceID];
    if (listeners) {
        for (id<UpDeviceReportListener> listener in listeners) {
            [listener onConnectionChange:baseInfo connection:connection];
        }
    }
    [self traceIot:device code:@"00000" devst:[self deviceState:state]];
}

- (void)device:(uSDKDevice *)device didUpdateFaultInformation:(uSDKFaultInformation *)faultInformation
{
    if (!device) {
        UPDeviceLogWarning(@"%s[%d]device is empty！", __PRETTY_FUNCTION__, __LINE__);
        return;
    }
    if (![faultInformation isKindOfClass:uSDKFaultInformation.class]) {
        UPDeviceLogWarning(@"%s[%d]faultInformation class error！", __PRETTY_FUNCTION__, __LINE__);
        return;
    }
    UPDeviceLogDebug(@"[%s][%d]devcie faultInformation report.mac:%@,code:%ld", __PRETTY_FUNCTION__, __LINE__, device.deviceID, faultInformation.stateCode);
    id<UpDeviceBaseInfo> baseInfo = [WifiDeviceHelper createDeviceBaseInfo:device];
    NSArray<id<UpDeviceReportListener>> *listeners = [_reportListenerCarrier get:device.deviceID];
    if (listeners) {
        for (id<UpDeviceReportListener> listener in listeners) {
            [listener onFaultInformationCodeChange:baseInfo code:faultInformation.stateCode];
        }
    }
}

- (void)device:(uSDKDevice *)device didUpdateOnlineState:(uSDKDeviceOnlineState)state
{
    if (!device) {
        UPDeviceLogWarning(@"%s[%d]device is empty！", __PRETTY_FUNCTION__, __LINE__);
        return;
    }
    UPDeviceLogDebug(@"[%s][%d]mac:%@,onlineStatus:%ld", __PRETTY_FUNCTION__, __LINE__, device.deviceID, state);
    id<UpDeviceBaseInfo> baseInfo = [WifiDeviceHelper createDeviceBaseInfo:device];
    UpDeviceRealOnline onlineState = [WifiDeviceHelper paresOnlineState:state];
    NSArray<id<UpDeviceReportListener>> *listeners = [_reportListenerCarrier get:device.deviceID];
    if (listeners) {
        for (id<UpDeviceReportListener> listener in listeners) {
            [listener onRealOnlineChange:baseInfo status:onlineState];
        }
    }
}

- (void)device:(uSDKDevice *)device didUpdateOnlineStateV2:(uSDKDeviceOnlineStateV2)onlineStateV2
{
    if (!device) {
        UPDeviceLogWarning(@"%s[%d]device is empty！", __PRETTY_FUNCTION__, __LINE__);
        return;
    }
    UPDeviceLogDebug(@"[%s][%d]mac:%@,onlineStateV2:%lu", __PRETTY_FUNCTION__, __LINE__, device.deviceID, onlineStateV2);
    id<UpDeviceBaseInfo> baseInfo = [WifiDeviceHelper createDeviceBaseInfo:device];
    UpDeviceRealOnlineV2 onlineState = [WifiDeviceHelper paresOnlineStateV2:onlineStateV2];
    NSArray<id<UpDeviceReportListener>> *listeners = [_reportListenerCarrier get:device.deviceID];
    if (listeners) {
        for (id<UpDeviceReportListener> listener in listeners) {
            [listener onRealOnlineChangeV2:baseInfo status:onlineState];
        }
    }
}

- (void)device:(uSDKDevice *)device didUpdateValueForAttributes:(NSArray<uSDKDeviceAttribute *> *)attributes
{
    if (attributes.count == 0) {
        UPDeviceLogWarning(@"%s[%d]attributes为空！", __PRETTY_FUNCTION__, __LINE__);
        return;
    }
    UPDeviceLogDebug(@"[%s][%d]mac:%@,attributes:%@", __PRETTY_FUNCTION__, __LINE__, device.deviceID, attributes);
    id<UpDeviceBaseInfo> baseInfo = [WifiDeviceHelper createDeviceBaseInfo:device];
    NSArray<id<UpDeviceAttribute>> *attributeList = [WifiDeviceHelper parseDeviceAttributeList:attributes];
    NSArray<id<UpDeviceReportListener>> *listeners = [_reportListenerCarrier get:device.deviceID];
    if (listeners) {
        for (id<UpDeviceReportListener> listener in listeners) {
            [listener onAttributesChange:baseInfo attributeList:attributeList];
        }
    }
}

- (void)device:(uSDKDevice *)device didReceiveAlarms:(NSArray<uSDKDeviceAlarm *> *)alarms
{
    if (alarms.count == 0) {
        UPDeviceLogWarning(@"%s[%d]alarms为空！", __PRETTY_FUNCTION__, __LINE__);
        return;
    }
    UPDeviceLogDebug(@"[%s][%d]mac:%@,alarms:%@", __PRETTY_FUNCTION__, __LINE__, device.deviceID, alarms);
    id<UpDeviceBaseInfo> baseInfo = [WifiDeviceHelper createDeviceBaseInfo:device];
    NSArray<id<UpDeviceCaution>> *cautionList = [WifiDeviceHelper parseDeviceCautionList:alarms];
    NSArray<id<UpDeviceReportListener>> *listeners = [_reportListenerCarrier get:device.deviceID];
    if (listeners) {
        for (id<UpDeviceReportListener> listener in listeners) {
            [listener onDeviceCaution:baseInfo cautionList:cautionList];
        }
    }
}

- (void)device:(uSDKDevice *)device didAddSubDevices:(NSArray<uSDKSubDevice *> *)subDevices
{
    if (!device) {
        UPDeviceLogWarning(@"%s[%d]device为空！", __PRETTY_FUNCTION__, __LINE__);
        return;
    }
    UPDeviceLogDebug(@"[%s][%d]mac:%@,didAddSubDevices:%@", __PRETTY_FUNCTION__, __LINE__, device.deviceID, subDevices);
    id<UpDeviceBaseInfo> baseInfo = [WifiDeviceHelper createDeviceBaseInfo:device];
    NSArray<id<UpDeviceBaseInfo>> *subDevInfoList = [WifiDeviceHelper createDeviceBaseInfoList:subDevices];
    NSArray<id<UpDeviceReportListener>> *listeners = [_reportListenerCarrier get:device.deviceID];
    if (listeners) {
        for (id<UpDeviceReportListener> listener in listeners) {
            [listener onSubDevListChange:baseInfo subDevInfoList:subDevInfoList];
        }
    }
}
- (void)device:(uSDKDevice *)device didReceiveResource:(NSString *)resource data:(NSData *)data
{
    if (!device || !resource) {
        UPDeviceLogWarning(@"%s[%d]device或者resource为空！", __PRETTY_FUNCTION__, __LINE__);
        return;
    }
    UPDeviceLogDebug(@"[%s][%d]mac:%@,didReceiveResource resource:%@ data:%@", __PRETTY_FUNCTION__, __LINE__, device.deviceID, resource, data);
    id<UpDeviceBaseInfo> baseInfo = [WifiDeviceHelper createDeviceBaseInfo:device];
    NSArray<id<UpDeviceReportListener>> *listeners = [_reportListenerCarrier get:device.deviceID];
    if (listeners) {
        for (id<UpDeviceReportListener> listener in listeners) {
            [listener onDeviceReceive:baseInfo name:resource data:data];
        }
    }
}
- (void)device:(uSDKDevice *)device didReceiveDecodeResource:(NSString *)resource data:(NSString *)JSONData
{
    if (!device || !resource) {
        UPDeviceLogWarning(@"%s[%d]device或者resource为空！", __PRETTY_FUNCTION__, __LINE__);
        return;
    }
    UPDeviceLogDebug(@"[%s][%d]mac:%@,didReceiveDecodeResource resource:%@ data:%@", __PRETTY_FUNCTION__, __LINE__, device.deviceID, resource, JSONData);
    id<UpDeviceBaseInfo> baseInfo = [WifiDeviceHelper createDeviceBaseInfo:device];
    NSArray<id<UpDeviceReportListener>> *listeners = [_reportListenerCarrier get:device.deviceID];
    if (listeners) {
        for (id<UpDeviceReportListener> listener in listeners) {
            [listener onDeviceReceive:baseInfo name:resource data:JSONData];
        }
    }
}
- (void)device:(uSDKDevice *)device didUpdateBoardFOTAStatus:(uSDKFOTAStatusInfo *)FOTAStatusInfo
{
    UPDeviceLogDebug(@"[%s][%d]mac:%@,didUpdateBoardFOTAStatus FOTAStatusInfo:%@", __PRETTY_FUNCTION__, __LINE__, device.deviceID, FOTAStatusInfo);
    id<UpDeviceBaseInfo> baseInfo = [WifiDeviceHelper createDeviceBaseInfo:device];
    DeviceFOTAStatusInfo *info = [DeviceFOTAStatusInfo DeviceFOTAStatusInfo:FOTAStatusInfo.upgradeStatus upgradeErrInfo:FOTAStatusInfo.upgradeErrorInfo];
    NSArray<id<UpDeviceReportListener>> *listeners = [_reportListenerCarrier get:device.deviceID];
    if (listeners) {
        for (id<UpDeviceReportListener> listener in listeners) {
            [listener onDeviceUpdateBoardFOTAStatus:baseInfo FOTAStatusInfo:info];
        }
    }
}

- (void)device:(uSDKDevice *)device didReceiveBLEHistoryData:(NSData *)data currentCount:(NSUInteger)currentCount totalCount:(NSUInteger)totalCount
{
    UPDeviceLogDebug(@"[%s][%d]mac:%@,didReceiveBLEHistoryData", __PRETTY_FUNCTION__, __LINE__, device.deviceID);
    id<UpDeviceBaseInfo> baseInfo = [WifiDeviceHelper createDeviceBaseInfo:device];
    DeviceBLEHistoryInfo *info = [DeviceBLEHistoryInfo DeviceBLEHistoryInfo:data currentCount:currentCount totalCount:totalCount];
    NSArray<id<UpDeviceReportListener>> *listeners = [_reportListenerCarrier get:device.deviceID];
    if (listeners) {
        for (id<UpDeviceReportListener> listener in listeners) {
            [listener onDeviceReceiveBLEHistoryData:baseInfo BLEHistoryInfo:info];
        }
    }
}

- (void)device:(uSDKDevice *)device didReceiveBLERealTimeData:(NSData *)data
{
    UPDeviceLogDebug(@"[%s][%d]mac:%@,didReceiveBLERealTimeData", __PRETTY_FUNCTION__, __LINE__, device.deviceID);
    id<UpDeviceBaseInfo> baseInfo = [WifiDeviceHelper createDeviceBaseInfo:device];
    NSArray<id<UpDeviceReportListener>> *listeners = [_reportListenerCarrier get:device.deviceID];
    if (listeners) {
        for (id<UpDeviceReportListener> listener in listeners) {
            [listener onDeviceReceiveBLERealTimeData:baseInfo data:data];
        }
    }
}

- (void)device:(uSDKDevice *)device didUpdateSleepState:(uSDKDeviceSleepState)sleepState
{
    if (!(sleepState == uSDKDeviceSleepStateUnsleeping ||
          sleepState == uSDKDeviceSleepStateSleeping ||
          sleepState == uSDKDeviceSleepStateWakingUp ||
          sleepState == uSDKDeviceSleepStateLowPowerMode) ||
        device == nil) {
        UPDeviceLogError(@"[%s][%d]mac:%@,error sleepState:%ld", __PRETTY_FUNCTION__, __LINE__, device.deviceID, sleepState);
        return;
    }
    UPDeviceLogDebug(@"[%s][%d]mac:%@,didUpdateSleepState:%ld", __PRETTY_FUNCTION__, __LINE__, device.deviceID, sleepState);
    NSArray<id<UpDeviceReportListener>> *listeners = [_reportListenerCarrier get:device.deviceID];
    id<UpDeviceBaseInfo> baseInfo = [WifiDeviceHelper createDeviceBaseInfo:device];
    UpDeviceSleepState state = [WifiDeviceHelper parseSleepState:sleepState];
    if (listeners) {
        for (id<UpDeviceReportListener> listener in listeners) {
            [listener onSleepStateChange:baseInfo state:state];
        }
    }
}

- (void)device:(uSDKDevice *)device didUpdateNetQualityLevel:(uSDKDeviceNetQualityLevel)qualityLevel
{
    if (device == nil) {
        UPDeviceLogError(@"[%s][%d]mac:%@,error qualityLevel:%ld", __PRETTY_FUNCTION__, __LINE__, device.deviceID, qualityLevel);
        return;
    }
    UPDeviceLogDebug(@"[%s][%d]mac:%@,qualityLevel:%ld", __PRETTY_FUNCTION__, __LINE__, device.deviceID, qualityLevel);
    NSArray<id<UpDeviceReportListener>> *listeners = [_reportListenerCarrier get:device.deviceID];
    id<UpDeviceBaseInfo> baseInfo = [WifiDeviceHelper createDeviceBaseInfo:device];
    UpDeviceNetworkLevel level = [WifiDeviceHelper parseNetworkLevel:qualityLevel];
    if (listeners) {
        for (id<UpDeviceReportListener> listener in listeners) {
            [listener onNetworkLevelChange:baseInfo level:level];
        }
    }
}

- (void)device:(uSDKDevice *)device onQCConnectTimeout:(uSDKQCConnectTimeoutType)timeoutType
{
    if (device == nil) {
        UPDeviceLogError(@"[%s][%d]mac:%@,error onQCConnectTimeout:%lu", __PRETTY_FUNCTION__, __LINE__, device.deviceID, timeoutType);
        return;
    }
    UPDeviceLogDebug(@"[%s][%d]mac:%@,onQCConnectTimeout:%ld", __PRETTY_FUNCTION__, __LINE__, device.deviceID, timeoutType);
    NSArray<id<UpDeviceReportListener>> *listeners = [_reportListenerCarrier get:device.deviceID];
    id<UpDeviceBaseInfo> baseInfo = [WifiDeviceHelper createDeviceBaseInfo:device];
    if (listeners) {
        for (id<UpDeviceReportListener> listener in listeners) {
            [listener onQCConnectTimeout:baseInfo timeoutType:[WifiDeviceHelper parseQCConnectTimeoutType:timeoutType]];
        }
    }
}

- (void)device:(uSDKDevice *)device didUpdateActiveOfflineCause:(uSDKDeviceActiveOfflineCause)activeOfflineCause
{
    if (device == nil) {
        UPDeviceLogError(@"[%s][%d]error:device is null,UpdateActiveOfflineCause:%ld", __PRETTY_FUNCTION__, __LINE__, activeOfflineCause);
        return;
    }
    UPDeviceLogDebug(@"[%s][%d]mac:%@,usdk report offlineCause:%ld", __PRETTY_FUNCTION__, __LINE__, device.deviceID, activeOfflineCause);
    UpDeviceOfflineCause offlineCause = [WifiDeviceHelper parseDeviceOfflineCause:activeOfflineCause];
    NSArray<id<UpDeviceReportListener>> *listeners = [_reportListenerCarrier get:device.deviceID];
    id<UpDeviceBaseInfo> baseInfo = [WifiDeviceHelper createDeviceBaseInfo:device];
    if (listeners) {
        for (id<UpDeviceReportListener> listener in listeners) {
            [listener onOfflineCauseChange:baseInfo offlineCause:offlineCause];
        }
    }
}

- (void)device:(uSDKDevice *)device didUpdateOfflineDays:(NSInteger)offlineDays
{
    if (device == nil) {
        UPDeviceLogError(@"[%s][%d]error:device is null,didUpdateOfflineDays:%ld", __PRETTY_FUNCTION__, __LINE__, offlineDays);
        return;
    }
    UPDeviceLogDebug(@"[%s][%d]mac:%@,usdk report offlineDays:%ld", __PRETTY_FUNCTION__, __LINE__, device.deviceID, offlineDays);
    NSArray<id<UpDeviceReportListener>> *listeners = [_reportListenerCarrier get:device.deviceID];
    id<UpDeviceBaseInfo> baseInfo = [WifiDeviceHelper createDeviceBaseInfo:device];
    if (listeners) {
        for (id<UpDeviceReportListener> listener in listeners) {
            [listener onOfflineDaysChange:baseInfo offlineDays:offlineDays];
        }
    }
}

- (void)device:(uSDKDevice *)device didUpdateOnlyConfigState:(uSDKDeviceOnlyConfigState)onlyConfigState
{
    if (device == nil) {
        UPDeviceLogError(@"[%s][%d]error:device is null,didUpdateOnlyConfigState:%ld", __PRETTY_FUNCTION__, __LINE__, onlyConfigState);
        return;
    }
    UPDeviceLogDebug(@"[%s][%d]mac:%@,usdk report onlyConfigState:%ld", __PRETTY_FUNCTION__, __LINE__, device.deviceID, onlyConfigState);
    NSArray<id<UpDeviceReportListener>> *listeners = [_reportListenerCarrier get:device.deviceID];
    id<UpDeviceBaseInfo> baseInfo = [WifiDeviceHelper createDeviceBaseInfo:device];
    if (listeners) {
        for (id<UpDeviceReportListener> listener in listeners) {
            [listener onOnlyConfigStateChange:baseInfo onlyConfigState:[WifiDeviceHelper parseOnlyConfigState:onlyConfigState]];
        }
    }
}

- (void)device:(uSDKDevice *_Nonnull)device didUpdateBleState:(uSDKDeviceState)state error:(NSError *)error
{
    if (![device isKindOfClass:uSDKDevice.class]) {
        UPDeviceLogWarning(@"%s[%d]device is not uSDKDevice.class！", __PRETTY_FUNCTION__, __LINE__);
        return;
    }
    id<UpDeviceBaseInfo> baseInfo = [WifiDeviceHelper createDeviceBaseInfo:device];
    UpDeviceConnection bleState = [WifiDeviceHelper parseBleState:device.bleState];
    UPDeviceLogDebug(@"[%s][%d]devcie bleState report.mac:%@,bleState:%ld", __PRETTY_FUNCTION__, __LINE__, device.deviceID, device.bleState);
    NSArray<id<UpDeviceReportListener>> *listeners = [_reportListenerCarrier get:device.deviceID];
    if (listeners) {
        for (id<UpDeviceReportListener> listener in listeners) {
            if ([listener respondsToSelector:@selector(onBleStateChange:state:)]) {
                [listener onBleStateChange:baseInfo state:bleState];
            }
        }
    }
}


#pragma mark - private

- (NSMutableDictionary<NSString *, NSObject *> *)paramsOfDeviceId:(NSString *)deviceId
{
    if (deviceId == nil) {
        UPDeviceLogError(@"%s[%d]paramsOfDeviceId失败，deviceId参数为空！", __PRETTY_FUNCTION__, __LINE__);
    }
    NSMutableDictionary<NSString *, NSObject *> *params = [NSMutableDictionary dictionary];
    if (deviceId) {
        [params setObject:deviceId forKey:WifiDeviceAction_KEY_DEVICE_ID];
    }
    return params;
}

- (NSString *)checkOutNullString:(NSString *)string
{
    if ([string isKindOfClass:[NSString class]] && string != nil) {
        return string;
    }

    return @"";
}
- (void)traceIot:(uSDKDevice *)device code:(NSString *)code devst:(NSInteger)devst;
{
    uTrace *trace = [uTrace createDITrace];
    uTraceNodeDI *traceNode = [uTraceNodeDI new];
    traceNode.bId = @"common";
    traceNode.sys = @"APP";
    traceNode.subSys = @"updevice";
    traceNode.bName = @"onlinestatuschange";
    traceNode.dId = device.deviceID;
    traceNode.code = code;
    NSMutableDictionary *argsDict = [NSMutableDictionary dictionary];
    argsDict[@"devst"] = [NSString stringWithFormat:@"%ld", (long)devst];
    argsDict[@"mac"] = device.deviceID;
    traceNode.args = argsDict;
    NSError *error;
    [trace addTraceNodeDI:traceNode error:&error];
    if (error) {
        UPDeviceLogError(@"traceIot！onlinestatuschange埋点添加失败：error:%@", error);
    }
}
- (NSInteger)deviceState:(uSDKDeviceState)state
{
    switch (state) {
        case uSDKDeviceStateUnconnect:
            return 0;
            break;
        case uSDKDeviceStateConnecting:
            return 1;
            break;
        case uSDKDeviceStateConnected:
            return 2;
            break;
        case uSDKDeviceStateOffline:
            return 3;
            break;
        case uSDKDeviceStateReady:
            return 4;
            break;

        default:
            break;
    }
}
#pragma mark - FOTA
- (void)checkBoardFOTAInfoSuccess:(NSString *)deviceId finishBlock:(void (^)(UpDeviceResult *))finishBlock
{
    NSMutableDictionary<NSString *, NSObject *> *params = [self paramsOfDeviceId:deviceId];
    [self execute:CheckBoardFOTAInfo_NAME
             params:params
        finishBlock:^(UpDeviceResult *result) {
          if (finishBlock) {
              finishBlock(result);
          }
        }];
}


- (void)fetchBoardFOTAStatusSuccess:(NSString *)deviceId finishBlock:(void (^)(UpDeviceResult *))finishBlock
{
    NSMutableDictionary<NSString *, NSObject *> *params = [self paramsOfDeviceId:deviceId];
    [self execute:FetchBoardFOTAStatus_NAME
             params:params
        finishBlock:^(UpDeviceResult *result) {
          if (finishBlock) {
              finishBlock(result);
          }
        }];
}


- (void)startBoardFOTA:(NSString *)deviceId finishBlock:(void (^)(UpDeviceResult *))finishBlock
{
    if (_deviceTracker != nil) {
        [_deviceTracker trace:FOTA_TRACE deviceId:deviceId];
    }
    NSMutableDictionary<NSString *, NSObject *> *params = [self paramsOfDeviceId:deviceId];
    [self execute:StartBoardFOTA_NAME
             params:params
        finishBlock:^(UpDeviceResult *result) {
          if (finishBlock) {
              finishBlock(result);
          }
        }];
}


- (void)startModuleUpdateSuccess:(NSString *)deviceId finishBlock:(void (^)(UpDeviceResult *))finishBlock
{
    NSMutableDictionary<NSString *, NSObject *> *params = [self paramsOfDeviceId:deviceId];
    [self execute:StartModuleUpdate_NAME
             params:params
        finishBlock:^(UpDeviceResult *result) {
          if (finishBlock) {
              finishBlock(result);
          }
        }];
}

- (BOOL)isModuleNeedOta:(NSString *)deviceId
{
    NSMutableDictionary<NSString *, NSObject *> *params = [self paramsOfDeviceId:deviceId];
    __block UpDeviceResult *blockResult;
    dispatch_group_t group = dispatch_group_create();
    dispatch_group_enter(group);
    [self execute:IsModuleNeedOta_NAME
             params:params
        finishBlock:^(UpDeviceResult *result) {
          blockResult = result;
          dispatch_group_leave(group);
        }];
    dispatch_group_wait(group, DISPATCH_TIME_FOREVER);
    if ([blockResult isSuccessful]) {
        if ([blockResult.extraData isKindOfClass:[NSNumber class]]) {
            NSNumber *isNeed = blockResult.extraData;
            return isNeed.boolValue;
        }
        return NO;
    }
    else {
        return NO;
    }
}

- (NSString *)getSmartLinkSoftwareVersion:(NSString *)deviceId
{
    NSMutableDictionary<NSString *, NSObject *> *params = [self paramsOfDeviceId:deviceId];
    __block UpDeviceResult *blockResult;
    dispatch_group_t group = dispatch_group_create();
    dispatch_group_enter(group);
    [self execute:SmartLinkSoftwareVersion_NAME
             params:params
        finishBlock:^(UpDeviceResult *result) {
          blockResult = result;
          dispatch_group_leave(group);
        }];
    dispatch_group_wait(group, DISPATCH_TIME_FOREVER);
    if ([blockResult isSuccessful]) {
        if ([blockResult.extraData isKindOfClass:[NSString class]]) {
            return blockResult.extraData;
        }
        return @"";
    }
    else {
        return @"";
    }
}

- (void)startFOTAWithDeviceFOTA:(NSString *)deviceId traceId:(NSString *)traceId firmwareId:(NSString *)firmwareId finishBlock:(void (^)(UpDeviceResult *))finishBlock
{
    if (_deviceTracker != nil) {
        [_deviceTracker trace:FOTA_TRACE deviceId:deviceId];
    }
    NSMutableDictionary<NSString *, NSObject *> *params = [self paramsOfDeviceId:deviceId];
    [params setObject:traceId ?: @"" forKey:KEY_TRACE_ID];
    [params setObject:firmwareId ?: @"" forKey:KEY_FIRM_WARE_ID];
    [self execute:StartWashFota_NAME
             params:params
        finishBlock:^(UpDeviceResult *result) {
          if (finishBlock) {
              finishBlock(result);
          }
        }];
}


- (void)attachDeviceWithoutConnect:(NSString *)deviceId reportListener:(id<UpDeviceReportListener>)listener finishBlock:(void (^)(UpDeviceResult *))finishBlock
{
    if (deviceId != nil && deviceId.length > 0 && listener != nil) {
        [_reportListenerCarrier put:deviceId Target:listener];
    }
    NSMutableDictionary<NSString *, NSObject *> *params = [self paramsOfDeviceId:deviceId];
    [params setObject:self forKey:AttachDeviceWithoutConnect_KEY_DEVICE_LISTENER];
    [self execute:AttachDeviceWithoutConnect_NAME
             params:params
        finishBlock:^(UpDeviceResult *result) {
          if (finishBlock) {
              finishBlock(result);
          }
        }];
}


- (void)detachDeviceWithoutConnect:(NSString *)deviceId finishBlock:(void (^)(UpDeviceResult *))finishBlock
{
    if (deviceId != nil && deviceId.length > 0) {
        [_reportListenerCarrier clear:deviceId];
    }
    NSMutableDictionary<NSString *, NSObject *> *params = [self paramsOfDeviceId:deviceId];
    [self execute:DetachDeviceWithoutConnect_NAME
             params:params
        finishBlock:^(UpDeviceResult *result) {
          if (finishBlock) {
              finishBlock(result);
          }
        }];
}
#pragma mark - subDevice

- (BOOL)isBound:(NSString *)deviceId
{
    NSMutableDictionary<NSString *, NSObject *> *params = [self paramsOfDeviceId:deviceId];
    __block UpDeviceResult *blockResult;
    dispatch_group_t group = dispatch_group_create();
    dispatch_group_enter(group);
    [self execute:IsBound_NAME
             params:params
        finishBlock:^(UpDeviceResult *result) {
          blockResult = result;
          dispatch_group_leave(group);
        }];
    dispatch_group_wait(group, DISPATCH_TIME_FOREVER);
    if ([blockResult isSuccessful]) {
        if ([blockResult.extraData isKindOfClass:[NSNumber class]]) {
            NSNumber *isNeed = blockResult.extraData;
            return isNeed.boolValue;
        }
        return NO;
    }
    else {
        return NO;
    }
}

- (void)getDeviceBindInfo:(NSString *)deviceId finishBlock:(void (^)(UpDeviceResult *result))finishBlock
{
    NSMutableDictionary<NSString *, NSObject *> *params = [self paramsOfDeviceId:deviceId];
    id<UpDeviceBroker> broker = [[UpDeviceInjection getInstance].deviceManager getBroker];
    NSMutableDictionary<NSString *, id> *gateway = [broker gatewayParams];
    if (gateway) {
        [params addEntriesFromDictionary:gateway];
    }
    [self execute:GetBindInfo_NAME
             params:params
        finishBlock:^(UpDeviceResult *result) {
          if (finishBlock) {
              finishBlock(result);
          }
        }];
}

- (void)refreshUsdkDeviceList:(void (^)(UpDeviceResult *result))finishBlock
{
    [self execute:RefresUsdkDeviceList_NAME
             params:nil
        finishBlock:^(UpDeviceResult *result) {
          if (finishBlock) {
              finishBlock(result);
          }
        }];
}

- (void)getSubDevListBySubDev:(NSString *)deviceId finishBlock:(void (^)(UpDeviceResult *result))finishBlock
{
    NSMutableDictionary<NSString *, NSObject *> *params = [self paramsOfDeviceId:deviceId];
    [self execute:GetSubDevListBySubDevice_NAME
             params:params
        finishBlock:^(UpDeviceResult *result) {
          if (finishBlock) {
              finishBlock(result);
          }
        }];
}

#pragma mark - BLE
- (void)fetchBLEHistoryData:(NSString *)deviceId finishBlock:(void (^)(UpDeviceResult *))finishBlock
{
    NSMutableDictionary<NSString *, NSObject *> *params = [self paramsOfDeviceId:deviceId];
    [self execute:FetchBLEHistoryData_NAME
             params:params
        finishBlock:^(UpDeviceResult *result) {
          if (finishBlock) {
              finishBlock(result);
          }
        }];
}

- (void)cancelFetchBLEHistoryData:(NSString *)deviceId finishBlock:(void (^)(UpDeviceResult *))finishBlock
{
    NSMutableDictionary<NSString *, NSObject *> *params = [self paramsOfDeviceId:deviceId];
    [self execute:CancelFetchBLEHistoryData_NAME
             params:params
        finishBlock:^(UpDeviceResult *result) {
          if (finishBlock) {
              finishBlock(result);
          }
        }];
}

#pragma mark - UpDeviceFocus
- (BOOL)inFocus:(NSString *)deviceId
{
    NSMutableDictionary<NSString *, NSObject *> *params = [self paramsOfDeviceId:deviceId];
    __block UpDeviceResult *blockResult;
    dispatch_group_t group = dispatch_group_create();
    dispatch_group_enter(group);
    [self execute:InFocus_NAME
             params:params
        finishBlock:^(UpDeviceResult *result) {
          blockResult = result;
          dispatch_group_leave(group);
        }];
    dispatch_group_wait(group, DISPATCH_TIME_FOREVER);
    if ([blockResult isSuccessful]) {
        if ([blockResult.extraData isKindOfClass:[NSNumber class]]) {
            NSNumber *inFocus = blockResult.extraData;
            return inFocus.boolValue;
        }
        return NO;
    }
    else {
        return NO;
    }
}

- (BOOL)outFocus:(NSString *)deviceId
{
    NSMutableDictionary<NSString *, NSObject *> *params = [self paramsOfDeviceId:deviceId];
    __block UpDeviceResult *blockResult;
    dispatch_group_t group = dispatch_group_create();
    dispatch_group_enter(group);
    [self execute:OutFocus_NAME
             params:params
        finishBlock:^(UpDeviceResult *result) {
          blockResult = result;
          dispatch_group_leave(group);
        }];
    dispatch_group_wait(group, DISPATCH_TIME_FOREVER);
    if ([blockResult isSuccessful]) {
        if ([blockResult.extraData isKindOfClass:[NSNumber class]]) {
            NSNumber *outFocus = blockResult.extraData;
            return outFocus.boolValue;
        }
        return NO;
    }
    else {
        return NO;
    }
}

#pragma mark - UpDeviceNetWorkQuality
- (void)getNetworkQuality:(NSString *)deviceId finishBlock:(void (^)(UpDeviceResult *result))finishBlock
{
    NSMutableDictionary<NSString *, NSObject *> *params = [self paramsOfDeviceId:deviceId];
    [self execute:GetNetworkQuality_NAME
             params:params
        finishBlock:^(UpDeviceResult *result) {
          if (finishBlock) {
              finishBlock(result);
          }
        }];
}
#pragma mark - UPDeviceNetType
- (DeviceNetType)getNetType:(NSString *)deviceId
{
    NSMutableDictionary<NSString *, NSObject *> *params = [self paramsOfDeviceId:deviceId];
    __block UpDeviceResult *blockResult;
    [self execute:GetNetType_NAME
             params:params
        finishBlock:^(UpDeviceResult *result) {
          blockResult = result;
        }];
    if ([blockResult isSuccessful]) {
        if ([blockResult.extraData isKindOfClass:[NSNumber class]]) {
            NSNumber *netType = blockResult.extraData;
            return netType.integerValue;
        }
    }
    return -1;
}

#pragma mark - UpDeviceGroupOptDeleagte

- (void)fetchGroupableDeviceList:(NSString *)deviceId finishBlock:(void (^)(UpDeviceResult *result))finishBlock
{
    NSMutableDictionary<NSString *, NSObject *> *params = [self paramsOfDeviceId:deviceId];
    [self execute:FetchGroupableDeviceList_NAME
             params:params
        finishBlock:^(UpDeviceResult *result) {
          if (finishBlock) {
              finishBlock(result);
          }
        }];
}

- (void)createDeviceGroup:(NSString *)deviceId finishBlock:(void (^)(UpDeviceResult *result))finishBlock
{
    NSMutableDictionary<NSString *, NSObject *> *params = [self paramsOfDeviceId:deviceId];
    [self execute:CreateDeviceGroup_NAME
             params:params
        finishBlock:^(UpDeviceResult *result) {
          if (finishBlock) {
              finishBlock(result);
          }
        }];
}

- (void)addDevicesToGroup:(NSString *)deviceId deviceIds:(NSArray<NSString *> *)deviceIds finishBlock:(void (^)(UpDeviceResult *result))finishBlock
{
    NSMutableDictionary<NSString *, NSObject *> *params = [self paramsOfDeviceId:deviceId];
    if (deviceIds.count > 0) {
        [params setObject:deviceIds forKey:KEY_DEVICEIDS_NAME];
    }
    [self execute:AddDevicesToGroup_NAME
             params:params
        finishBlock:^(UpDeviceResult *result) {
          if (finishBlock) {
              finishBlock(result);
          }
        }];
}

- (void)removeDevicesFromGroup:(NSString *)deviceId deviceIds:(NSArray<NSString *> *)deviceIds finishBlock:(void (^)(UpDeviceResult *result))finishBlock
{
    NSMutableDictionary<NSString *, NSObject *> *params = [self paramsOfDeviceId:deviceId];
    if (deviceIds.count > 0) {
        [params setObject:deviceIds forKey:KEY_DEVICEIDS_NAME];
    }
    [self execute:RemoveDevicesFromGroup_NAME
             params:params
        finishBlock:^(UpDeviceResult *result) {
          if (finishBlock) {
              finishBlock(result);
          }
        }];
}

- (void)deleteDeviceGroup:(NSString *)deviceId finishBlock:(void (^)(UpDeviceResult *result))finishBlock
{
    NSMutableDictionary<NSString *, NSObject *> *params = [self paramsOfDeviceId:deviceId];
    [self execute:DeleteDeviceGroup_NAME
             params:params
        finishBlock:^(UpDeviceResult *result) {
          if (finishBlock) {
              finishBlock(result);
          }
        }];
}

- (void)getGroupMemberList:(NSString *)deviceId finishBlock:(void (^)(UpDeviceResult *result))finishBlock
{
    NSMutableDictionary<NSString *, NSObject *> *params = [self paramsOfDeviceId:deviceId];
    [self execute:GetGroupMemberList_NAME
             params:params
        finishBlock:^(UpDeviceResult *result) {
          if (finishBlock) {
              finishBlock(result);
          }
        }];
}

- (BOOL)isGroup:(NSString *)deviceId
{
    NSMutableDictionary<NSString *, NSObject *> *params = [self paramsOfDeviceId:deviceId];
    __block UpDeviceResult *blockResult;
    dispatch_group_t group = dispatch_group_create();
    dispatch_group_enter(group);
    [self execute:IsGroup_NAME
             params:params
        finishBlock:^(UpDeviceResult *result) {
          blockResult = result;
          dispatch_group_leave(group);
        }];
    dispatch_group_wait(group, DISPATCH_TIME_FOREVER);
    if ([blockResult isSuccessful]) {
        if ([blockResult.extraData isKindOfClass:[NSNumber class]]) {
            NSNumber *isNeed = blockResult.extraData;
            return isNeed.boolValue;
        }
        return NO;
    }
    else {
        return NO;
    }
}

- (uSDKDeviceWrapper *)getuSDKDeviceWrapper:(NSString *)deviceId
{
    NSMutableDictionary<NSString *, NSObject *> *params = [self paramsOfDeviceId:deviceId];
    __block uSDKDeviceWrapper *wrapper = nil;
    [self execute:GetuSDKDeviceWrapper_NAME
             params:params
        finishBlock:^(UpDeviceResult *result) {
          if (result.isSuccessful) {
              wrapper = (uSDKDeviceWrapper *)result.extraData;
          }
        }];
    return wrapper;
}

@end
