//
//  WifiDeviceHelper.m
//  UPDeviceDebugger
//
//  Created by f on 2/1/2019.
//  Copyright © 2019 海尔优家智能科技（北京）有限公司. All rights reserved.
//

#import "WifiDeviceHelper.h"
#import "WifiDeviceType.h"
#import "WifiDeviceToolkit.h"
#import "UPDeviceLog.h"

static NSMutableDictionary *uSDKDeviceInfoDic;

@implementation WifiDeviceHelper

+ (NSString *)formatError:(uSDKErrorConst)error
{
    return [NSString stringWithFormat:@"uSDKErrorConst = %d", (int)error];
}

+ (UpDeviceToolkitState)parseState:(uSDKState)state
{
    UpDeviceToolkitState tookitStatus;
    switch (state) {
        case uSDKStateStarting:
            tookitStatus = STARTING;
            break;

        case uSDKStateStarted:
            tookitStatus = STARTED;
            break;

        default:
            tookitStatus = STOPPED;
            break;
    }

    return tookitStatus;
}

+ (UpDeviceRealOnline)paresOnlineState:(uSDKDeviceOnlineState)state
{
    UpDeviceRealOnline onlineState = UpDeviceRealOnline_ONLINE;
    if (state == uSDKDeviceOnlineStateOffline) {
        onlineState = UpDeviceRealOnline_OFFLINE;
    }
    return onlineState;
}

+ (UpDeviceRealOnlineV2)paresOnlineStateV2:(uSDKDeviceOnlineStateV2)state
{
    UpDeviceRealOnlineV2 onlineStateV2 = UpDeviceRealOnlineV2_ONLINE_READY;
    if (state == uSDKDeviceOnlineStateV2Offline) {
        onlineStateV2 = UpDeviceRealOnlineV2_OFFLINE;
    }
    else if (state == uSDKDeviceOnlineStateV2OnlineNotReady) {
        onlineStateV2 = UpDeviceRealOnlineV2_ONLINE_NOT_READY;
    }

    return onlineStateV2;
}

+ (UpDeviceOnlyConfigState)parseOnlyConfigState:
    (uSDKDeviceOnlyConfigState)state
{
    switch (state) {
        case uSDKDeviceOnlyConfigStateNearConfigurable:
            return UpDeviceOnlyConfigStateNearConfigurable;
        case uSDKDeviceOnlyConfigStateConfigurable:
            return UpDeviceOnlyConfigStateConfigurable;
        default:
            return UpDeviceOnlyConfigStateUnConfigurable;
    }
}

+ (UpDeviceSleepState)parseSleepState:(uSDKDeviceSleepState)state
{
    UpDeviceSleepState sleepState = UpDeviceSleepStateUnsleeping;
    switch (state) {
        case uSDKDeviceSleepStateUnsleeping:
            sleepState = UpDeviceSleepStateUnsleeping;
            break;

        case uSDKDeviceSleepStateSleeping:
            sleepState = UpDeviceSleepStateSleeping;
            break;

        case uSDKDeviceSleepStateWakingUp:
            sleepState = UpDeviceSleepStateWakingUp;
            break;

        case uSDKDeviceSleepStateLowPowerMode:
            sleepState = UpDeviceSleepStateLowPower;
        default:
            break;
    }
    return sleepState;
}

+ (UpDeviceNetworkLevel)parseNetworkLevel:(uSDKDeviceNetQualityLevel)level
{
    UpDeviceNetworkLevel networkLevel;
    switch (level) {
        case uSDKDeviceNetQualityLevelUnKnown:
            networkLevel = UpDeviceNetworkLevelUnKnown;
            break;
        case uSDKDeviceNetQualityLevelExcellent:
            networkLevel = UpDeviceNetworkLevelExcellent;
            break;
        case uSDKDeviceNetQualityLevelGood:
            networkLevel = UpDeviceNetworkLevelGood;
            break;
        case uSDKDeviceNetQualityLevelQualified:
            networkLevel = UpDeviceNetworkLevelQualified;
            break;
        case uSDKDeviceNetQualityLevelPoor:
            networkLevel = UpDeviceNetworkLevelPoor;
            break;

        default:
            networkLevel = UpDeviceNetworkLevelUnKnown;
            break;
    }
    return networkLevel;
}

+ (UpDeviceResultErrorCode)parseErrorCode:(uSDKErrorConst)error
{
    UpDeviceResultErrorCode errorCode;

    switch (error) {
        case RET_USDK_OK:
            errorCode = ErrorCode_SUCCESS;
            break;

        case ERR_USDK_TIMEOUT:
            errorCode = ErrorCode_TIMEOUT;
            break;

        case ERR_USDK_INVALID_PARAM:
            errorCode = ErrorCode_INVALID;
            break;

        default:
            errorCode = ErrorCode_FAILURE;
            break;
    }

    return errorCode;
}

+ (UpStringResult *)parseError:(uSDKErrorConst)error
{
    UpDeviceResultErrorCode errorCode = [self parseErrorCode:error];
    NSString *extraData = [self formatError:error];
    UpStringResult *stringResult =
        [[UpStringResult alloc] initWithErrorCode:errorCode
                                        extraData:extraData
                                        extraCode:nil
                                        extraInfo:nil];
    return stringResult;
}

+ (GatewayConnection)parseGatewayConnection:
    (uSDKCloudConnectionState)connection
{
    GatewayConnection gatwayConn;

    switch (connection) {
        case uSDKCloudConnectionStateConnecting:
            gatwayConn = CONNECTING;
            break;

        case uSDKCloudConnectionStateConnected:
            gatwayConn = CONNECTED;
            break;

        case uSDKCloudConnectionStateConnectFailed:
            gatwayConn = CONNECT_FAILED;
            break;

        default:
            gatwayConn = DISCONNECTED;
            break;
    }

    return gatwayConn;
}

+ (UpDeviceConnection)parseDeviceConnection:(uSDKDeviceState)connection
{
    UpDeviceConnection deviceConn;

    switch (connection) {
        case uSDKDeviceStateUnconnect:
            deviceConn = UpDeviceConnection_DISCONNECTED;
            break;

        case uSDKDeviceStateConnecting:
            deviceConn = UpDeviceConnection_CONNECTING;
            break;

        case uSDKDeviceStateConnected:
            deviceConn = UpDeviceConnection_CONNECTED;
            break;

        case uSDKDeviceStateReady:
            deviceConn = UpDeviceConnection_READY;
            break;

        default:
            deviceConn = UpDeviceConnection_OFFLINE;
            break;
    }

    return deviceConn;
}

+ (UpDeviceControlState)parseDeviceControlState:(uSDKDeviceControlState)state
{
    UpDeviceControlState controlState;

    switch (state) {
        case uSDKDeviceControlStateNone:
            controlState = UpDeviceControlState_None;
            break;

        case uSDKDeviceControlStateControllable:
            controlState = UpDeviceControlState_Controllable;
            break;

        case uSDKDeviceControlStateNearControllable:
            controlState = UpDeviceControlState_NearControllable;
            break;

        case uSDKDeviceControlStateControllableAndAuthorized:
            controlState = UpDeviceControlState_ControllableAndAuthorized;
            break;

        default:
            controlState = UpDeviceControlState_None;
            break;
    }

    return controlState;
}

+ (WifiDeviceNetworkType)parseNetworkType:(uSDKDeviceNetTypeConst)networkType
{
    WifiDeviceNetworkType deviceNetworkType;

    switch (networkType) {
        case NET_TYPE_REMOTE:
            deviceNetworkType = WifiDeviceNetworkTypeRemote;
            break;

        case NET_TYPE_LOCAL:
            deviceNetworkType = WifiDeviceNetworkTypeLocal;
            break;

        default:
            deviceNetworkType = WifiDeviceNetworkTypeOneSelf;
            break;
    }

    return deviceNetworkType;
}

+ (UpDeviceQCConnectTimeoutType)parseQCConnectTimeoutType:
    (uSDKQCConnectTimeoutType)timeoutType
{
    UpDeviceQCConnectTimeoutType deviceTimeoutType;

    switch (timeoutType) {
        case uSDKQCConnectTimeoutTypeConnect:
            deviceTimeoutType = UpDeviceQCConnectTimeoutTypeConnect;
            break;

        case uSDKQCConnectTimeoutTypeAuthorize:
            deviceTimeoutType = UpDeviceQCConnectTimeoutTypeAuthorize;
            break;

        default:
            deviceTimeoutType = UpDeviceQCConnectTimeoutTypeConnect;
            break;
    }

    return deviceTimeoutType;
}

+ (DeviceBaseInfo *)createDeviceBaseInfo:(uSDKDevice *)device
{
    if (device == nil) {
        UPDeviceLogWarning(@"%s[%d]device参数为空！", __PRETTY_FUNCTION__,
                           __LINE__);
        return nil;
    }

    DeviceBaseInfo *deviceBaseInfo;

    NSString *deviceId = device.deviceID;
    NSString *typeName =
        [WifiDeviceType convertuSDKDeviceTypeIntoString:device.type];
    NSString *parentId = nil;
    NSString *subDevNo = nil;
    if ([device isKindOfClass:[uSDKSubDevice class]]) {
        uSDKSubDevice *subDevice = (uSDKSubDevice *)device;
        if (subDevice.parentDevice) {
            parentId = subDevice.parentDevice.deviceID;
            subDevNo = subDevice.subId;
        }
    }
    NSString *Protocol = WifiDeviceToolkit_PROTOCOL;
    NSString *TypeId = device.uplusID;
    NSString *model = nil;
    NSString *proNo = device.productCode ?: nil;
    NSString *typeCode = nil;

    // 未完成
    deviceBaseInfo = [DeviceBaseInfo DeviceBaseInfo:Protocol
                                           DeviceId:deviceId
                                             TypeId:TypeId
                                           TypeName:typeName
                                           typeCode:typeCode
                                              Model:model
                                             ProdNo:proNo
                                           ParentId:parentId
                                           SubDevNo:subDevNo];

    return deviceBaseInfo;
}

+ (nullable DeviceBaseInfo *)createDeviceBaseInfoBy:
    (uSDKDeviceInfo *)deviceInfo
{
    if (deviceInfo == nil) {
        UPDeviceLogWarning(@"%s[%d]deviceInfo参数为空！", __PRETTY_FUNCTION__,
                           __LINE__);
        return nil;
    }

    DeviceBaseInfo *deviceBaseInfo;
    NSString *deviceId = deviceInfo.deviceID;
    NSString *typeName =
        [WifiDeviceType convertuSDKDeviceTypeIntoString:deviceInfo.type];
    NSString *parentId = nil;
    NSString *subDevNo = nil;
    NSString *Protocol = WifiDeviceToolkit_PROTOCOL;
    NSString *TypeId = deviceInfo.uplusID;
    NSString *model = @"empty";
    NSString *proNo = deviceInfo.productCode;
    NSString *typeCode = nil;

    deviceBaseInfo = [DeviceBaseInfo DeviceBaseInfo:Protocol
                                           DeviceId:deviceId
                                             TypeId:TypeId
                                           TypeName:typeName
                                           typeCode:typeCode
                                              Model:model
                                             ProdNo:proNo
                                           ParentId:parentId
                                           SubDevNo:subDevNo];

    return deviceBaseInfo;
}

+ (NSArray<id<UpDeviceBaseInfo>> *)createDeviceBaseInfoList:
    (NSArray<uSDKDevice *> *)deviceList
{
    NSMutableArray *result = [NSMutableArray array];

    for (uSDKDevice *device in deviceList) {
        DeviceBaseInfo *baseInfo = [self createDeviceBaseInfo:device];
        if (baseInfo) {
            [result addObject:baseInfo];
        }
    }

    return result;
}
+ (nullable DeviceCaution *)parseDeviceCaution:(uSDKDeviceAlarm *)alarm
{

    if (alarm == nil || alarm.name == nil) {
        UPDeviceLogWarning(@"%s[%d]alarm参数或者alarm.name为空！",
                           __PRETTY_FUNCTION__, __LINE__);
        return nil;
    }
    DeviceCaution *deviceCaution =
        [[DeviceCaution alloc] initWithName:alarm.name
                                      Value:alarm.value
                                       Time:alarm.alarmTimestamp];

    return deviceCaution;
}

+ (NSArray<id<UpDeviceCaution>> *)parseDeviceCautionList:
    (NSArray<uSDKDeviceAlarm *> *)alarmList
{
    NSMutableArray *result = [NSMutableArray array];
    for (uSDKDeviceAlarm *alarm in alarmList) {
        DeviceCaution *deviceCaution = [self parseDeviceCaution:alarm];
        if (deviceCaution) {
            [result addObject:deviceCaution];
        }
    }
    return result;
}

+ (nullable DeviceAttribute *)parseDeviceAttribute:
    (uSDKDeviceAttribute *)attribute
{
    if (attribute == nil || attribute.attrName == nil) {
        UPDeviceLogWarning(@"%s[%d]attribute参数或者attribute.attrName为空！",
                           __PRETTY_FUNCTION__, __LINE__);
        return nil;
    }

    DeviceAttribute *deviceAttr = [[DeviceAttribute alloc] init];
    deviceAttr.name = attribute.attrName;
    deviceAttr.value = attribute.attrValue;

    return deviceAttr;
}

+ (NSArray<id<UpDeviceAttribute>> *)parseDeviceAttributeList:
    (NSArray<uSDKDeviceAttribute *> *)attributes
{
    NSMutableArray *result = [NSMutableArray array];
    for (uSDKDeviceAttribute *attr in attributes) {
        DeviceAttribute *deviceAttr = [self parseDeviceAttribute:attr];
        if (deviceAttr) {
            [result addObject:deviceAttr];
        }
    }
    return result;
}

+ (UpDeviceConnection)parseWifiLocalState:(uSDKDeviceState)state
{
    UpDeviceConnection localState = UpDeviceConnection_OFFLINE;
    switch (state) {
        case uSDKDeviceStateUnconnect:
            localState = UpDeviceConnection_DISCONNECTED;
            break;
        case uSDKDeviceStateOffline:
            localState = UpDeviceConnection_OFFLINE;
            break;
        case uSDKDeviceStateConnecting:
            localState = UpDeviceConnection_CONNECTING;
            break;
        case uSDKDeviceStateConnected:
            localState = UpDeviceConnection_CONNECTED;
            break;
        case uSDKDeviceStateReady:
            localState = UpDeviceConnection_READY;
            break;
    }
    return localState;
}

+ (UpDeviceConnection)parseBleState:(uSDKDeviceState)state
{
    UpDeviceConnection bleState = UpDeviceConnection_OFFLINE;
    switch (state) {
        case uSDKDeviceStateUnconnect:
            bleState = UpDeviceConnection_DISCONNECTED;
            break;
        case uSDKDeviceStateOffline:
            bleState = UpDeviceConnection_OFFLINE;
            break;
        case uSDKDeviceStateConnecting:
            bleState = UpDeviceConnection_CONNECTING;
            break;
        case uSDKDeviceStateConnected:
            bleState = UpDeviceConnection_CONNECTED;
            break;
        case uSDKDeviceStateReady:
            bleState = UpDeviceConnection_READY;
            break;
    }
    return bleState;
}

+ (uSDKArgument *)createArgument:(NSString *)name Value:(NSString *)value
{
    if (name == nil) {
        UPDeviceLogWarning(@"%s[%d]name参数为空！", __PRETTY_FUNCTION__, __LINE__);
        return nil;
    }

    uSDKArgument *argument = [[uSDKArgument alloc] init];
    argument.name = name;
    argument.value = value;

    return argument;
}

+ (NSArray<uSDKArgument *> *)createArgumentList:
    (NSDictionary<NSString *, NSString *> *)attributeMap
{
    NSMutableArray *result = [NSMutableArray array];
    for (NSString *key in attributeMap.allKeys) {
        uSDKArgument *argument = [self createArgument:key Value:attributeMap[key]];
        if (argument) {
            [result addObject:argument];
        }
    }
    return result;
}

+ (nullable uSDKDeviceInfo *)getUSDKDeviceInfo:(NSString *)deviceId
{
    if (![deviceId isKindOfClass:NSString.class]) {
        return nil;
    }
    return uSDKDeviceInfoDic[deviceId];
}

+ (void)saveUSDKDeviceInfo:(uSDKDeviceInfo *)info
{
    if (!uSDKDeviceInfoDic) {
        uSDKDeviceInfoDic = [NSMutableDictionary new];
    }
    if (info) {
        [uSDKDeviceInfoDic setObject:info
                              forKey:info.deviceID ? info.deviceID : @""];
    }
}

+ (UpDeviceOfflineCause)parseDeviceOfflineCause:
    (uSDKDeviceActiveOfflineCause)offlineCause
{
    UpDeviceOfflineCause cause = UpDeviceOfflineCause_None;
    switch (offlineCause) {
        case uSDKDeviceActiveOfflineCauseNormal:
            cause = UpDeviceOfflineCause_Normal;
            break;
        case uSDKDeviceActiveOfflineCauseLowPowerMode:
            cause = UpDeviceOfflineCause_LowPower;
            break;
        case uSDKDeviceActiveOfflineCauseCloseWIFI:
            cause = UpDeviceOfflineCause_WIFI_Closed;
            break;
        default:
            break;
    }
    return cause;
}
@end
