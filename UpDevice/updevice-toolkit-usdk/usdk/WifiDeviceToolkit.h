//
//  WifiDeviceToolkit.h
//  UPDeviceDebugger
//
//  Created by f on 2/1/2019.
//  Copyright © 2019 海尔优家智能科技（北京）有限公司. All rights reserved.
//

#import <Foundation/Foundation.h>
#import "UpDeviceToolkit.h"
NS_ASSUME_NONNULL_BEGIN

#define TIMEOUT_MIN 5
#define TIMEOUT_MAX 60
#define TIMEOUT_DEF 15

#define WifiDeviceToolkit_PROTOCOL @"haier-usdk"
#define WifiDeviceToolkit_KEY_HOST @"usdk-host"
#define WifiDeviceToolkit_KEY_PORT @"usdk-port"
#define WifiDeviceToolkit_KEY_ACCESS_TOKEN @"usdk-access-token"
#define WifiDeviceToolkit_KEY_USERID @"usdk-access-userId"
#define WifiDeviceToolkit_GATEWAY_HOST_RELEASE @"gw.haier.net"
#define WifiDeviceToolkit_GATEWAY_PORT_RELEASE 56811
#define WifiDeviceToolkit_GATEWAY_HOST_DEBUG @"usermg.uopendev.haier.net"
#define WifiDeviceToolkit_GATEWAY_PORT_DEBUG 56821
/**
 *    特性定义
 */
typedef NS_OPTIONS(NSUInteger, UPDeviceuSDKFeatures) {
    /**
     *    不开启任何特性
     */
    UPDeviceuSDKFeatureNone = 0,
    /**
     *    默认，开启全部特性
     */
    UPDeviceuSDKFeatureDefault = NSUIntegerMax,
    /**
     *  链式跟踪特性
     *
     * @deprecated 5.4.1
     * @warning 该特性已不允许设置，国内版本将自动启动链式跟踪功能，国外版本没有链式跟踪功能
     */
    UPDeviceuSDKFeatureTrace = (1UL << 0),
    /**
     *    DNS解析特性
     * @deprecated 5.4.1
     * @warning 该功能uSDK已删除
     */
    UPDeviceuSDKFeatureDNSParse = (1UL << 1),
    /**
     *    HTTPDNS解析特性
     */
    UPDeviceuSDKFeatureHTTPDNSParse = (1UL << 2),
    /**
     *  网络环境
     * @deprecated 5.4.1
     * @warning 该特性已不允许设置，国内版本将自动启动链式跟踪功能，国外版本没有链式跟踪功能
     */
    UPDeviceuSDKFeatureNetEnv = (1UL << 3),

};
/**
 uSDK的数据中心
 
 - uSDKIDCAreaChina: 国内数据中心
 - uSDKIDCAreaAMERICA: 美国数据中心
 - uSDKIDCAreaEUROPE: 欧洲数据中心
 - uSDKIDCAreaSoutheastAsia: 东南亚数据中心
 */
typedef NS_ENUM(NSUInteger, UPDeviceuSDKIDCArea) {
    UPDeviceuSDKIDCAreaChina,
    UPDeviceuSDKIDCAreaAMERICA,
    UPDeviceuSDKIDCAreaEUROPE,
    UPDeviceuSDKIDCAreaSoutheastAsia,
};

@protocol WifiDeviceToolkit <UpDeviceToolkit>

- (void)setWifiDeviceToolkitAppId:(NSString *)appID appKey:(NSString *)appKey secretKey:(NSString *)secretKey area:(UPDeviceuSDKIDCArea)area features:(UPDeviceuSDKFeatures)features;
- (void)setWifiDeviceToolkitWith:(BOOL)enableBLEControllableSearch enableWifiSearch:(BOOL)enableWifiSearch;
- (NSString *)getClientId;
@end

NS_ASSUME_NONNULL_END
