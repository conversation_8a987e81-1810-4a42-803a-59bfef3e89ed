//
//  WifiDeviceType.h
//  UPDeviceDebugger
//
//  Created by f on 2/1/2019.
//  Copyright © 2019 海尔优家智能科技（北京）有限公司. All rights reserved.
//

#import <Foundation/Foundation.h>
#import <uSDK/uSDK.h>

NS_ASSUME_NONNULL_BEGIN

extern NSString *const UPDevice_UnKnownTypeName;

@interface WifiDeviceType : NSObject

+ (NSString *)convertuSDKDeviceTypeIntoString:(uSDKDeviceTypeConst)type;

- (instancetype)initWithType:(uSDKDeviceTypeConst)type;

- (int)getTypeId;

- (NSString *)getTypeName;

- (uSDKDeviceTypeConst)getValue;

@end

NS_ASSUME_NONNULL_END
