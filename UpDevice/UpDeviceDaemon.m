//
//  UpDeviceDaemon.m
//  UPDevice
//
//  Created by gump on 8/1/2019.
//  Copyright © 2019 海尔优家智能科技（北京）有限公司. All rights reserved.
//

#import "UpDeviceDaemon.h"
#import "UPDeviceLog.h"
#import "UDSafeMutableArray.h"
#import "UDSafeMutableDictionary.h"
#import "UpDeviceDaemonGIO.h"

NSTimeInterval const UpDeviceDaemonSendMessageDelay = 1.0;

@interface UpDeviceDaemon () <UpDeviceListener>

@property (nonatomic, strong) UDSafeMutableDictionary<NSString *, id<UpDevice>> *deviceMap;
@property (nonatomic, strong) UDSafeMutableDictionary<NSString *, id<UpDevice>> *deviceScannerMap;
@property (nonatomic, strong) UDSafeMutableDictionary<NSString *, id<UpDevice>> *deviceReleaseMap;
@property (nonatomic, strong) UDSafeMutableDictionary<NSString *, id<UpDevice>> *deviceQuickBindReleaseMap;
@property (nonatomic, strong) UDSafeMutableDictionary<NSString *, NSString *> *disposables;
@property (nonatomic, strong) UDSafeMutableDictionary<NSString *, NSString *> *quickBindDisposables;

@property (nonatomic, strong) UpDeviceCarrier *subDevCarrier;
@property (nonatomic, strong) UDSafeMutableArray<NSString *> *prepareQueue;
@property (nonatomic, strong) UDSafeMutableArray<NSString *> *releaseQueue;
@property (nonatomic, strong) UDSafeMutableArray<NSString *> *prepareQuickBindQueue;
@property (nonatomic, strong) UDSafeMutableArray<NSString *> *releaseQuickBindQueue;
@property (nonatomic, strong) dispatch_queue_t queue;
@property (nonatomic, strong) dispatch_queue_t quickBindQueue;
@property (atomic, assign) BOOL isRunning;
@property (atomic, assign) BOOL isReleasingDevice;
@property (atomic, assign) BOOL isQuickBindPreparingDevice;
@property (atomic, assign) BOOL isQuickBindReleasingDevice;
@property (atomic, strong) NSOperationQueue *queueAttach;
@property (atomic, strong) NSOperationQueue *queueConfig;
@property (atomic, strong) UpDeviceDaemonGIO *daemonGIO;

@property (nonatomic, strong) UDSafeMutableArray<NSString *> *priorityPrepareQueue;
//当前家庭准备队列
@property (nonatomic, strong) UDSafeMutableArray<NSString *> *curFamilyPrepareQueue;

// 当前家庭的设备数量
@property (nonatomic, assign) NSInteger curFamilyDeviceCount;

@end

@implementation UpDeviceDaemon
- (instancetype)init
{
    self = [super init];

    if (self) {
        self.deviceMap = [UDSafeMutableDictionary new];
        self.deviceScannerMap = [UDSafeMutableDictionary new];
        self.deviceReleaseMap = [UDSafeMutableDictionary new];
        self.deviceQuickBindReleaseMap = [UDSafeMutableDictionary new];
        self.disposables = [UDSafeMutableDictionary new];
        self.quickBindDisposables = [UDSafeMutableDictionary new];
        NSString *unKey = nil;
        self.subDevCarrier = [UpDeviceCarrier UpDeviceCarrier:unKey];
        self.prepareQueue = [UDSafeMutableArray new];
        self.releaseQueue = [UDSafeMutableArray new];
        self.prepareQuickBindQueue = [UDSafeMutableArray new];
        self.releaseQuickBindQueue = [UDSafeMutableArray new];
        self.isRunning = YES;
        self.priorityPrepareQueue = [UDSafeMutableArray new];
        self.curFamilyPrepareQueue = [UDSafeMutableArray new];
        self.queueAttach = [[NSOperationQueue alloc] init];
        self.queueAttach.maxConcurrentOperationCount = 1;
        self.queueConfig = [[NSOperationQueue alloc] init];
        self.queueConfig.maxConcurrentOperationCount = 1;
        self.daemonGIO = [[UpDeviceDaemonGIO alloc] init];
    }

    return self;
}

- (void)addConfigOperation:(NSBlockOperation *)operation
{
    [self.queueConfig addOperation:operation];
}

- (dispatch_queue_t)quickBindQueue
{
    if (!_quickBindQueue) {
        _quickBindQueue = dispatch_queue_create("DeviceDaemonQuickBindThread#", DISPATCH_QUEUE_CONCURRENT);
    }
    return _quickBindQueue;
}

- (void)start
{
    if (!self.queue) {
        self.queue = dispatch_queue_create("DeviceDaemonThread#", DISPATCH_QUEUE_SERIAL);
    }
    self.isRunning = YES;
    [self sendPrepareMessage];
    UPDeviceLogDebug(@"%s[%d]UpDeviceDaemon has already started.", __PRETTY_FUNCTION__, __LINE__);
}

- (void)stop
{
    self.queue = nil;
    self.isRunning = NO;
    UPDeviceLogDebug(@"%s[%d]UpDeviceDaemon has already stopped.", __PRETTY_FUNCTION__, __LINE__);
}
- (void)put:(NSString *)uniqueId device:(id<UpDevice>)device
{
    if (uniqueId == nil || device == nil) {
        UPDeviceLogWarning(@"%s[%d]设备put时参数为空，无法加入准备(prepare)队列！uniqueId:%@,device:%@", __PRETTY_FUNCTION__, __LINE__, uniqueId, device);
        return;
    }
    id<UpDevice> removed = self.deviceMap[uniqueId];
    [self.deviceMap setObject:device forKey:uniqueId];
    [self addToPrepare:uniqueId];
    if (removed != nil && removed != device) {
        [self addToRelease:removed.uniqueId];
    }
}

- (id<UpDevice>)get:(NSString *)uniqueId
{
    // 主设备
    id<UpDevice> device = _deviceMap[uniqueId];
    NSString *subDevId = [self obtainSubDevNo:uniqueId];
    //    UPDeviceLogDebug(@"getUniqueId = %@,subDevId = %@", uniqueId, subDevId);
    if (subDevId.length > 0 && !device) {
        // 子设备
        device = [self obtainSubDevice:uniqueId subDevId:subDevId];
        [self put:uniqueId device:device];
    }
    if (!device) {
        device = _deviceScannerMap[uniqueId];
    }
    return device;
}

- (id<UpDevice>)getControlDevice:(NSString *)uniqueId
{
    // 主设备
    id<UpDevice> device = _deviceMap[uniqueId];
    NSString *subDevId = [self obtainSubDevNo:uniqueId];
    //    UPDeviceLogDebug(@"getUniqueId = %@,subDevId = %@", uniqueId, subDevId);
    if (subDevId.length > 0 && !device) {
        // 子设备
        device = [self obtainSubDevice:uniqueId subDevId:subDevId];
        [self put:uniqueId device:device];
    }
    return device;
}

- (NSString *)obtainSubDevNo:(NSString *)uniqueId
{
    NSString *patternStr = @"-";
    NSArray *array = [uniqueId componentsSeparatedByString:patternStr];
    if (array.count == 3) {
        return array.lastObject;
    }
    else {
        return nil;
    }
}
- (id<UpDevice>)obtainSubDevice:(NSString *)uniqueId subDevId:(NSString *)subDevId
{
    NSString *patternStr = @"_";
    NSArray *array = [uniqueId componentsSeparatedByString:patternStr];
    NSString *parentUniqueId = [NSString stringWithFormat:@"%@", array.firstObject];
    NSArray *subDevList = [_subDevCarrier get:parentUniqueId];
    id<UpDevice> device = nil;
    for (id<UpDevice> subDev in subDevList) {
        if ([subDevId isEqualToString:subDev.uniqueId]) {
            device = subDev;
            break;
        }
    }
    return device;
}
- (id<UpDevice>)remove:(NSString *)uniqueId
{
    id<UpDevice> removed = self.deviceMap[uniqueId];
    if (removed != nil) {
        [self.deviceMap removeObjectForKey:uniqueId];
        [self.deviceReleaseMap setObject:removed forKey:uniqueId];
        [self addToRelease:removed.uniqueId];
    }
    return removed;
}

- (NSMutableArray<id<UpDevice>> *)list:(id<UpDeviceFilter>)filter
{
    NSMutableArray<id<UpDevice>> *listArray = [NSMutableArray array];
    if (filter == nil) {
        [listArray addObjectsFromArray:self.deviceMap.allValues];
    }
    else {
        for (id<UpDevice> device in self.deviceMap.allValues) {
            if ([filter accept:device]) {
                [listArray addObject:device];
            }
        }
        for (id<UpDevice> device in self.deviceScannerMap.allValues) {
            if ([filter accept:device]) {
                [listArray addObject:device];
            }
        }
    }
    UPDeviceLogDebug(@"%s[%d]deviceArray: getdevicelist = %ld", __PRETTY_FUNCTION__, __LINE__, listArray.count);
    return listArray;
}

- (NSString *)genSubUniqueId:(id<UpDevice>)parent subDev:(id<UpDevice>)subDev
{
    return [NSString stringWithFormat:@"%@(%@)", parent.uniqueId, subDev.uniqueId];
}

- (NSString *)tryParseParentId:(NSString *)uniqueId
{
    NSString *parentId = uniqueId;
    NSString *patternStr = @"(.+?)(?=\\()";
    NSRange range = [uniqueId rangeOfString:patternStr options:NSRegularExpressionSearch];
    if (range.location != NSNotFound) {
        parentId = [uniqueId substringWithRange:range];
    }
    return parentId;
}

- (NSString *)tryParseSubDevId:(NSString *)uniqueId
{
    NSString *subDevNo = nil;
    NSString *patternStr = @"(?<=\\()(.+?)(?=\\))";
    NSRange range = [uniqueId rangeOfString:patternStr options:NSRegularExpressionSearch];
    if (range.location != NSNotFound) {
        subDevNo = [uniqueId substringWithRange:range];
    }
    return subDevNo;
}

- (void)addToPrepare:(NSString *)uniqueId
{
    [self dequeueRelease:uniqueId];
    [self queuePrepare:uniqueId];
    [self sendPrepareMessage];
}
- (void)addToRelease:(NSString *)uniqueId
{
    [self dequeuePrepare:uniqueId];
    [self queueRelease:uniqueId];
    [self sendReleaseMessageWithShortDelay:YES];
}
- (void)queuePrepare:(NSString *)uniqueId
{
    if (uniqueId == nil) {
        return;
    }

    NSArray *quArray = [NSArray arrayWithArray:self.prepareQueue.array];
    if ([quArray containsObject:uniqueId]) {
        return;
    }
    [self.prepareQueue addObject:uniqueId];
    UPDeviceLogDebug(@"%s[%d]设备加入准备(prepare)队列。uniqueId：%@", __PRETTY_FUNCTION__, __LINE__, uniqueId);
}
- (void)dequeuePrepare:(NSString *)uniqueId
{
    if (uniqueId == nil) {
        return;
    }

    [self.prepareQueue removeObject:uniqueId];
}

- (void)insertQueuePrepare:(NSString *)uniqueId
{
    if (uniqueId == nil) {
        return;
    }

    NSArray *quArray = [NSArray arrayWithArray:self.prepareQueue.array];
    if ([quArray containsObject:uniqueId]) {
        return;
    }

    NSInteger offset = self.curFamilyDeviceCount + 3;

    if (quArray.count > offset) {
        [self.prepareQueue insertObject:uniqueId atIndex:offset - 1];
    }
    else {
        [self.prepareQueue addObject:uniqueId];
    }
    UPDeviceLogDebug(@"%s[%d]设备加入准备(prepare)队列。uniqueId：%@", __PRETTY_FUNCTION__, __LINE__, uniqueId);
}

- (void)dequeuePriorityPrepare:(NSString *)uniqueId
{
    if (uniqueId == nil) {
        return;
    }

    if ([self.priorityPrepareQueue containsObject:uniqueId]) {
        [self.priorityPrepareQueue removeObject:uniqueId];
    }
}

- (void)dequeueCurFamilyPriorityPrepareQueue:(NSString *)uniqueId
{
    if (uniqueId == nil) {
        return;
    }

    if ([self.curFamilyPrepareQueue containsObject:uniqueId]) {
        [self.curFamilyPrepareQueue removeObject:uniqueId];
    }
}

- (void)queueRelease:(NSString *)uniqueId
{
    if (uniqueId == nil) {
        return;
    }

    [self.releaseQueue addObject:uniqueId];
    UPDeviceLogDebug(@"%s[%d]设备加入释放(release)队列。uniqueId：%@", __PRETTY_FUNCTION__, __LINE__, uniqueId);
}
- (void)dequeueRelease:(NSString *)uniqueId
{
    if (uniqueId == nil) {
        return;
    }

    [self.releaseQueue removeObject:uniqueId];
}
- (void)putDisposable:(NSString *)uniqueId disposable:(id)disposable
{
    if (!uniqueId || !disposable) {
        return;
    }

    [self.disposables setObject:disposable forKey:uniqueId];
}
- (void)removeDisposable:(NSString *)uniqueId
{
    if (!uniqueId) {
        return;
    }

    [self.disposables removeObjectForKey:uniqueId];
}

- (void)sendPrepareMessage
{
    if (!self.isRunning || self.prepareQueue.count == 0) {
        return;
    }

    if (self.queueAttach.operationCount < 5) {
        NSInvocationOperation *attachOp = [[NSInvocationOperation alloc] initWithTarget:self selector:@selector(checkAndAttach) object:nil];
        [self.queueAttach addOperation:attachOp];
    }
}

- (void)sendReleaseMessageWithShortDelay:(BOOL)shortDelay
{
    if (!self.isRunning || self.isReleasingDevice || self.releaseQueue.count == 0) {
        return;
    }
    dispatch_async(dispatch_get_global_queue(DISPATCH_QUEUE_PRIORITY_DEFAULT, 0), ^{
      [NSThread sleepForTimeInterval:0.1]; //线程沉睡时间不管失败还是成功都是0.1s
      [self checkAndDetach];
    });
    self.isReleasingDevice = YES;
}

- (void)onDeviceReport:(NSInteger)event device:(id)device
{
    if (event != EVENT_SUB_DEV_LIST_CHANGE) {
        return;
    }
    [self handleSubDevList:(id<UpDevice>)device];
}

- (void)moveDeviceToQueueHead:(NSString *)uniqueId
{
    if (uniqueId == nil) {
        UPDeviceLogError(@"%s[%d]uniqueId is empty!", __PRETTY_FUNCTION__, __LINE__);
        return;
    }

    NSArray *quArray = [NSArray arrayWithArray:self.prepareQueue.array];
    if (![quArray containsObject:uniqueId]) {
        return;
    }

    NSUInteger index = [self.prepareQueue indexOfObject:uniqueId];
    [self.prepareQueue removeObject:uniqueId];
    [self.prepareQueue insertObject:uniqueId atIndex:0];
    if (![self.priorityPrepareQueue containsObject:uniqueId]) {
        [self.priorityPrepareQueue addObject:uniqueId];
    }
    UPDeviceLogDebug(@"%s[%d]move device queue from index  %lu to 0！uniqueId:%@", __PRETTY_FUNCTION__, __LINE__, index, uniqueId);
    [self sendPrepareMessage];
}

- (void)moveDeviceToQueueHeadPure:(NSString *)uniqueId
{
    if (uniqueId == nil) {
        UPDeviceLogError(@"%s[%d]uniqueId is empty!", __PRETTY_FUNCTION__, __LINE__);
        return;
    }

    NSArray *quArray = [NSArray arrayWithArray:self.prepareQueue.array];
    if (![quArray containsObject:uniqueId]) {
        return;
    }

    [self.prepareQueue removeObject:uniqueId];
    [self.prepareQueue insertObject:uniqueId atIndex:0];
}

- (void)moveDevicesToQueueHead:(NSArray<NSString *> *)uniqueIds
{
    if (uniqueIds == nil) {
        UPDeviceLogError(@"%s[%d]uniqueIds is empty!", __PRETTY_FUNCTION__, __LINE__);
        return;
    }

    [uniqueIds enumerateObjectsUsingBlock:^(NSString *_Nonnull uniqueId, NSUInteger idx, BOOL *_Nonnull stop) {
      if ([self.prepareQueue containsObject:uniqueId] && idx < self.prepareQueue.count) {
          NSUInteger queueIdx = [self.prepareQueue indexOfObject:uniqueId];
          [self.prepareQueue removeObject:uniqueId];
          [self.prepareQueue insertObject:uniqueId atIndex:0];
          if (![self.priorityPrepareQueue containsObject:uniqueId]) {
              [self.priorityPrepareQueue addObject:uniqueId];
          }
          UPDeviceLogDebug(@"%s[%d]move device queue from index %lu to %lu！uniqueId:%@", __PRETTY_FUNCTION__, __LINE__, queueIdx, idx, uniqueId);
      }
    }];
    [self sendPrepareMessage];
}

- (NSArray<NSString *> *)getPriorityPrepareQueue
{
    return self.priorityPrepareQueue.array;
}

- (NSArray<NSString *> *)getPrepareQueue
{
    return self.prepareQueue.array;
}

#pragma mark 子设备对象的准备和释放
- (void)handleSubDevList:(id<UpDevice>)parent
{
    NSArray *newSubDevList = [parent getSubDevList];
    NSString *parentUniqueId = parent.uniqueId;
    NSMutableArray *newUniqueIds = [NSMutableArray array];
    for (id<UpDevice> subDev in newSubDevList) {
        [_subDevCarrier put:parentUniqueId Target:subDev];
        [newUniqueIds addObject:subDev.uniqueId];
        [self addToPrepare:[self genSubUniqueId:parent subDev:subDev]];
    }
    NSArray *oldSubDevList = [_subDevCarrier get:parent.uniqueId];
    for (id<UpDevice> subDev in oldSubDevList) {
        if (![newUniqueIds containsObject:subDev.uniqueId]) {
            [self addToRelease:[self genSubUniqueId:parent subDev:subDev]];
        }
    }
}
- (void)checkAndAttach
{
    [self.daemonGIO devicePrepareBegin];
    NSString *uniqueId = [self.prepareQueue firstObject];
    id<UpDevice> device = [self findById:uniqueId deviceDic:self.deviceMap];
    [self dequeuePrepare:uniqueId];

    if (device) {
        if (device.getState == UpDeviceState_RELEASED) {
            [self.daemonGIO countPrepareTimes];
            __weak typeof(self) weakSelf = self;
            [device prepare:^(UpDeviceResult *result) {
              BOOL success = [result isSuccessful];
              if (success) {
                  UPDeviceLogDebug(@"%s[%d]设备准备(prepare)成功！uniqueId:%@", __PRETTY_FUNCTION__, __LINE__, uniqueId);

                  [device attach:weakSelf];

                  [weakSelf dequeuePriorityPrepare:uniqueId];
                  [weakSelf dequeueCurFamilyPriorityPrepareQueue:uniqueId];
                  [weakSelf.daemonGIO devicePrepareEnd:device.getInfo.deviceId];
                  [weakSelf sendPrepareMessage];
              }
              else {
                  UPDeviceLogError(@"%s[%d]设备准备(prepare)失败，重新加入Prepare队列！ uniqueId:%@ error:%@", __PRETTY_FUNCTION__, __LINE__, uniqueId, result.extraInfo);
                  [NSThread sleepForTimeInterval:0.1];
                  [weakSelf insertQueuePrepare:uniqueId];
                  // 失败失去了优先级
                  [weakSelf dequeuePriorityPrepare:uniqueId];
                  [weakSelf dequeueCurFamilyPriorityPrepareQueue:uniqueId];
                  [weakSelf sendPrepareMessage];
              }
            }];
        }
        else if (device.getState == UpDeviceState_PREPARING) {
            UPDeviceLogError(@"%s[%d]device.getState is PREPARING，重新加入Prepare队列！ uniqueId:%@", __PRETTY_FUNCTION__, __LINE__, uniqueId);
            [NSThread sleepForTimeInterval:0.1];
            [self queuePrepare:uniqueId];
            [self sendPrepareMessage];
        }
        else if (device.getState == UpDeviceState_PREPARED) {

            [self dequeuePrepare:uniqueId];
            [self dequeuePriorityPrepare:uniqueId];
            [self dequeueCurFamilyPriorityPrepareQueue:uniqueId];
            [device attach:self];
            [self sendPrepareMessage];
            UPDeviceLogDebug(@"%s[%d]设备准备(prepare)成功！uniqueId:%@", __PRETTY_FUNCTION__, __LINE__, uniqueId);
        }
    }
    else {
        [self sendPrepareMessage];
        UPDeviceLogDebug(@"%s[%d]no device.will not send message. uniqueId:%@  count:%ld", __PRETTY_FUNCTION__, __LINE__, uniqueId, self.prepareQueue.count);
    }
}

- (void)checkAndDetach
{
    dispatch_async(self.queue, ^{
      NSString *uniqueId = [self.releaseQueue firstObject];
      if (!uniqueId) {
          self.isReleasingDevice = NO;
          [self sendReleaseMessageWithShortDelay:NO];
          return;
      }
      [self.releaseQueue removeObject:uniqueId];
      NSString *disposable = [self.disposables objectForKey:uniqueId];
      if (!disposable) {
          id<UpDevice> device = [self findById:uniqueId deviceDic:self.deviceReleaseMap];
          if (device) {
              [device release:^(UpDeviceResult *result) {
                [self removeDisposable:uniqueId];
                BOOL success = [result isSuccessful];
                if (success) {
                    UPDeviceLogDebug(@"%s[%d]设备释放(release)成功，取消设备状态订阅！ uniqueId:%@", __PRETTY_FUNCTION__, __LINE__, uniqueId);
                    [device detachAll];
                    [self.deviceReleaseMap removeObjectForKey:uniqueId];
                }
                else {
                    UPDeviceLogError(@"%s[%d]设备释放(release)失败！重新加入释放(release)队列！uniqueId:%@ error:%@", __PRETTY_FUNCTION__, __LINE__, uniqueId, result.extraInfo);
                    [self queueRelease:uniqueId];
                }
                self.isReleasingDevice = NO;
                [self sendReleaseMessageWithShortDelay:success];
              }];
              [self putDisposable:uniqueId disposable:@""];
          }
          else {
              UPDeviceLogInfo(@"%s[%d]%@ 未找到对应设备对象，无法释放(release)", __PRETTY_FUNCTION__, __LINE__, uniqueId);
              self.isReleasingDevice = NO;
              [self sendReleaseMessageWithShortDelay:NO];
          }
      }
      else {
          self.isReleasingDevice = NO;
          [self sendReleaseMessageWithShortDelay:NO];
      }
    });
}

- (id<UpDevice>)findById:(NSString *)uniqueId deviceDic:(UDSafeMutableDictionary *)deviceDic
{
    id<UpDevice> device = nil;
    NSString *subDevId = [self tryParseSubDevId:uniqueId];
    if (subDevId == nil) {
        // 主设备
        device = deviceDic[uniqueId];
    }
    else {
        // 子设备
        NSString *parentId = [self tryParseParentId:uniqueId];
        NSArray *subDevList = [_subDevCarrier get:parentId];
        for (id<UpDevice> subDev in subDevList) {
            if ([subDevId isEqualToString:subDev.uniqueId]) {
                device = subDev;
                break;
            }
        }
    }
    return device;
}

- (void)clearPrepareQueue
{
    [self.prepareQueue removeAllObjects];
    [self.priorityPrepareQueue removeAllObjects];
}

- (void)setCurFamilyPriorityPrepareQueue:(NSArray<NSString *> *)deviceIds
{
    [self.curFamilyPrepareQueue removeAllObjects];
    for (NSString *devcieid in deviceIds) {
        [self.curFamilyPrepareQueue addObject:devcieid ?: @""];
    }
    self.curFamilyDeviceCount = self.curFamilyPrepareQueue.count;
}

- (NSArray<NSString *> *)curFamilyPriorityPrepareQueue
{
    return self.curFamilyPrepareQueue.array;
}

#pragma mark - quickbind3.0

- (BOOL)isScannerDevice:(NSString *)uniqueId
{
    if (!uniqueId) {
        return NO;
    }
    id<UpDevice> device = self.deviceScannerMap[uniqueId];
    if (device) {
        return YES;
    }

    return NO;
}

- (id<UpDevice>)getScannerDevice:(NSString *)uniqueId
{
    if (!uniqueId) {
        return nil;
    }
    id<UpDevice> device = self.deviceScannerMap[uniqueId];
    return device;
}

- (void)putScannerDevice:(NSString *)uniqueId device:(id<UpDevice>)device
{
    if (uniqueId == nil || device == nil) {
        UPDeviceLogWarning(@"%s[%d]设备put时参数为空，无法准备！uniqueId:%@,device:%@", __PRETTY_FUNCTION__, __LINE__, uniqueId, device);
        return;
    }
    id<UpDevice> removed = self.deviceScannerMap[uniqueId];
    __weak typeof(self) weakSelf = self;
    [self.deviceScannerMap setObject:device forKey:uniqueId];
    if (device != nil) {
        [device prepareWithoutConnect:^(UpDeviceResult *result) {
          if (result.isSuccessful) {
              UPLogDebug(kUPDevicePrefix, @"%s[%d]快连设备prepare成功！", __PRETTY_FUNCTION__, __LINE__);
          }
          else {
              UPLogError(kUPDevicePrefix, @"%s[%d]快连设备prepare失败！error:%@", __PRETTY_FUNCTION__, __LINE__, result.extraInfo);
              [weakSelf addQuickBindToPrepare:uniqueId];
              if (removed != nil && removed != device) {
                  [weakSelf addQuickBindToRelease:removed.uniqueId];
              }
          }
        }];
    }
}

- (nullable id<UpDevice>)removeScannerDevice:(NSString *)uniqueId
{
    if (uniqueId == nil || uniqueId.length == 0) {
        UPDeviceLogWarning(@"%s[%d]快连设备put时参数为空,移除失败！uniqueId:%@", __PRETTY_FUNCTION__, __LINE__, uniqueId);
        return nil;
    }
    id<UpDevice> removed = self.deviceScannerMap[uniqueId];
    if (removed != nil) {
        [self.deviceScannerMap removeObjectForKey:uniqueId];
        [removed releaseWithoutConnect:^(UpDeviceResult *result) {
          if (result.isSuccessful) {
              UPLogDebug(kUPDevicePrefix, @"%s[%d]快连设备release成功！", __PRETTY_FUNCTION__, __LINE__);
          }
          else {
              UPLogError(kUPDevicePrefix, @"%s[%d]快连设备release失败！error:%@", __PRETTY_FUNCTION__, __LINE__, result.extraInfo);
          }
        }];
        [self.deviceQuickBindReleaseMap setObject:removed forKey:uniqueId];
        [self addQuickBindToRelease:removed.uniqueId];
    }
    return removed;
}

- (void)addQuickBindToPrepare:(NSString *)uniqueId
{
    [self dequeueQuickBindRelease:uniqueId];
    [self queueQuickBindPrepare:uniqueId];
    [self sendQuickBindPrepareMessageWithShortDelay:YES];
}

- (void)addQuickBindToRelease:(NSString *)uniqueId
{
    [self dequeueQuickBindPrepare:uniqueId];
    [self queueQuickBindRelease:uniqueId];
    [self sendQuickBindReleaseMessageWithShortDelay:YES];
}

- (void)dequeueQuickBindRelease:(NSString *)uniqueId
{
    if (uniqueId == nil) {
        return;
    }

    [self.releaseQuickBindQueue removeObject:uniqueId];
}

- (void)queueQuickBindPrepare:(NSString *)uniqueId
{
    if (uniqueId == nil) {
        return;
    }

    NSArray *quArray = [NSArray arrayWithArray:self.prepareQuickBindQueue.array];
    if ([quArray containsObject:uniqueId]) {
        return;
    }
    [self.prepareQuickBindQueue addObject:uniqueId];
    UPDeviceLogDebug(@"%s[%d]快连设备加入准备(prepare)队列。uniqueId：%@", __PRETTY_FUNCTION__, __LINE__, uniqueId);
}

- (void)dequeueQuickBindPrepare:(NSString *)uniqueId
{
    if (uniqueId == nil) {
        return;
    }

    [self.prepareQuickBindQueue removeObject:uniqueId];
}

- (void)queueQuickBindRelease:(NSString *)uniqueId
{
    if (uniqueId == nil) {
        return;
    }

    [self.releaseQuickBindQueue addObject:uniqueId];
    UPDeviceLogDebug(@"%s[%d]快连设备加入释放(release)队列。uniqueId：%@", __PRETTY_FUNCTION__, __LINE__, uniqueId);
}

- (void)sendQuickBindPrepareMessageWithShortDelay:(BOOL)shortDelay
{
    if (self.isQuickBindPreparingDevice || self.prepareQuickBindQueue.count == 0) {
        return;
    }

    dispatch_async(dispatch_get_global_queue(DISPATCH_QUEUE_PRIORITY_DEFAULT, 0), ^{
      [NSThread sleepForTimeInterval:0.1];
      [self quickBindCheckAndAttach];
    });
    self.isQuickBindPreparingDevice = YES;
}

- (void)sendQuickBindReleaseMessageWithShortDelay:(BOOL)shortDelay
{
    if (self.isQuickBindReleasingDevice || self.releaseQuickBindQueue.count == 0) {
        return;
    }
    dispatch_async(dispatch_get_global_queue(DISPATCH_QUEUE_PRIORITY_DEFAULT, 0), ^{
      [NSThread sleepForTimeInterval:0.1]; //线程沉睡时间不管失败还是成功都是0.1s
      [self quickBindCheckAndDetach];
    });
    self.isQuickBindReleasingDevice = YES;
}

- (void)quickBindCheckAndAttach
{
    dispatch_async(self.quickBindQueue, ^{
      NSString *uniqueId = [self.prepareQuickBindQueue firstObject];
      id<UpDevice> device = [self findById:uniqueId deviceDic:self.deviceScannerMap];
      if (device) {
          [self dequeueQuickBindPrepare:uniqueId];
          [device prepareWithoutConnect:^(UpDeviceResult *result) {
            if (result.isSuccessful) {
                UPLogDebug(kUPDevicePrefix, @"%s[%d]快连设备prepare成功！", __PRETTY_FUNCTION__, __LINE__);
                [self dequeueQuickBindPrepare:uniqueId];
            }
            else {
                [self queueQuickBindPrepare:uniqueId];
            }
          }];
          self.isQuickBindPreparingDevice = NO;
          [self sendQuickBindPrepareMessageWithShortDelay:YES];
      }
      else {
          self.isQuickBindPreparingDevice = NO;
          UPDeviceLogDebug(@"%s[%d]no device.will not send message. uniqueId:%@  count:%ld", __PRETTY_FUNCTION__, __LINE__, uniqueId, self.prepareQueue.count);
      }
    });
}

- (void)quickBindCheckAndDetach
{
    dispatch_async(self.quickBindQueue, ^{
      NSString *uniqueId = [self.releaseQuickBindQueue firstObject];
      if (!uniqueId) {
          self.isQuickBindReleasingDevice = NO;
          [self sendQuickBindReleaseMessageWithShortDelay:NO];
          return;
      }
      [self.releaseQuickBindQueue removeObject:uniqueId];
      NSString *disposable = [self.quickBindDisposables objectForKey:uniqueId];
      if (!disposable) {
          id<UpDevice> device = [self findById:uniqueId deviceDic:self.deviceQuickBindReleaseMap];
          if (device) {
              [device releaseWithoutConnect:^(UpDeviceResult *result) {
                [self removeQuickBindDisposable:uniqueId];
                BOOL success = [result isSuccessful];
                if (success) {
                    UPDeviceLogDebug(@"%s[%d]快连设备释放(release)成功，取消设备状态订阅！ uniqueId:%@", __PRETTY_FUNCTION__, __LINE__, uniqueId);
                    [self.deviceQuickBindReleaseMap removeObjectForKey:uniqueId];
                }
                else {
                    UPDeviceLogError(@"%s[%d]快连设备释放(release)失败！重新加入释放(release)队列！uniqueId:%@ error:%@", __PRETTY_FUNCTION__, __LINE__, uniqueId, result.extraInfo);
                    [self queueQuickBindRelease:uniqueId];
                }
                self.isQuickBindReleasingDevice = NO;
                [self sendQuickBindReleaseMessageWithShortDelay:success];
              }];
              [self putQuickBindDisposable:uniqueId disposable:@""];
          }
          else {
              UPDeviceLogInfo(@"%s[%d]%@ 未找到对应设备对象，无法释放(release)", __PRETTY_FUNCTION__, __LINE__, uniqueId);
              self.isQuickBindReleasingDevice = NO;
              [self sendQuickBindReleaseMessageWithShortDelay:NO];
          }
      }
      else {
          self.isQuickBindReleasingDevice = NO;
          [self sendQuickBindReleaseMessageWithShortDelay:NO];
      }
    });
}

- (void)putQuickBindDisposable:(NSString *)uniqueId disposable:(id)disposable
{
    if (!uniqueId || !disposable) {
        return;
    }

    [self.quickBindDisposables setObject:disposable forKey:uniqueId];
}
- (void)removeQuickBindDisposable:(NSString *)uniqueId
{
    if (!uniqueId) {
        return;
    }

    [self.quickBindDisposables removeObjectForKey:uniqueId];
}

- (void)beginGIO:(NSArray<id<UpDeviceInfo>> *)devices
{
    [self.daemonGIO beginPrepare:devices];
}

- (void)GIOCountActiveDevice:(NSString *)deviceId
{
    [self.daemonGIO countActiveDevice:deviceId];
}

- (void)GIOCountAttachTime
{
    [self.daemonGIO countAttachTime];
}

- (void)GIOCountFindConfigFileTime
{
    [self.daemonGIO countFindConfigFileTime];
}

- (void)GIOCountParseConfigFileTime
{
    [self.daemonGIO countParseConfigFileTime];
}

@end
