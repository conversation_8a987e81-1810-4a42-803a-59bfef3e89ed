//
//  UpPresenceInfo.h
//  UPDevice
//
//  Created by lichen on 2025/4/17.
//  Copyright © 2025 海尔优家智能科技（北京）有限公司. All rights reserved.
//

#import <Foundation/Foundation.h>
#import "UpPopupTipInfo.h"

NS_ASSUME_NONNULL_BEGIN

/// 设备安全级别
typedef NS_ENUM(NSInteger, UpPresenceCode) {

    // 离线提示+无重连
    UpPresenceStateOfflineNormal = 9000,

    // 离线提示+wifi设置绑定
    UpPresenceStateOfflineWifiBind = 9001,

    // 离线提示+配网引导绑定
    UpPresenceStateOfflineGuideBind = 9002,

    // 离线提示+离线页面蓝牙监听
    UpPresenceStateOfflineBleMonitor = 9003,

    // 部分可控提示+wifi设置绑定
    UpPresenceStateBleOnlineWifiBind = 8001,

    // 部分可控提示+配网引导绑定
    UpPresenceStateBleOnlineGuideBind = 8002,

    // 部分可控提示+直接跳转wifi设置
    UpPresenceStateBleOnlineWifiSetting = 8004,
};

@interface UpPresenceInfo : NSObject

/// 连接离线数据码
@property (nonatomic, assign) UpPresenceCode presenceCode;

/// 离线弹框提示
@property (nonatomic, strong) UpPopupTipInfo *popupTipInfo;

@end

NS_ASSUME_NONNULL_END
