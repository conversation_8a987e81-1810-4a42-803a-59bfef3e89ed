// !$*UTF8*$!
{
	archiveVersion = 1;
	classes = {
	};
	objectVersion = 54;
	objects = {

/* Begin PBXBuildFile section */
		041D69E12703031D00833105 /* UpDeviceControlState.h in Headers */ = {isa = PBXBuildFile; fileRef = 041D69E0270302EB00833105 /* UpDeviceControlState.h */; settings = {ATTRIBUTES = (Public, ); }; };
		041D69E82704137800833105 /* GetControlState.m in Sources */ = {isa = PBXBuildFile; fileRef = 041D69E62704137800833105 /* GetControlState.m */; };
		041D69E92704137800833105 /* GetControlState.h in Headers */ = {isa = PBXBuildFile; fileRef = 041D69E72704137800833105 /* GetControlState.h */; };
		041D69EC27042ADF00833105 /* GetFaultInformationCode.h in Headers */ = {isa = PBXBuildFile; fileRef = 041D69EA27042ADF00833105 /* GetFaultInformationCode.h */; };
		041D69ED27042ADF00833105 /* GetFaultInformationCode.m in Sources */ = {isa = PBXBuildFile; fileRef = 041D69EB27042ADF00833105 /* GetFaultInformationCode.m */; };
		041D69F02704545800833105 /* BindDeviceWithoutWifi.h in Headers */ = {isa = PBXBuildFile; fileRef = 041D69EE2704545800833105 /* BindDeviceWithoutWifi.h */; };
		041D69F12704545800833105 /* BindDeviceWithoutWifi.m in Sources */ = {isa = PBXBuildFile; fileRef = 041D69EF2704545800833105 /* BindDeviceWithoutWifi.m */; };
		041D69F42705240D00833105 /* UpdateRouterInfo.h in Headers */ = {isa = PBXBuildFile; fileRef = 041D69F22705240D00833105 /* UpdateRouterInfo.h */; };
		041D69F52705240D00833105 /* UpdateRouterInfo.m in Sources */ = {isa = PBXBuildFile; fileRef = 041D69F32705240D00833105 /* UpdateRouterInfo.m */; };
		041D69F827054A8300833105 /* GetConfigRouterInfo.h in Headers */ = {isa = PBXBuildFile; fileRef = 041D69F627054A8300833105 /* GetConfigRouterInfo.h */; };
		041D69F927054A8300833105 /* GetConfigRouterInfo.m in Sources */ = {isa = PBXBuildFile; fileRef = 041D69F727054A8300833105 /* GetConfigRouterInfo.m */; };
		04C3CDC02716EE5500F951CB /* ConnectDevice.h in Headers */ = {isa = PBXBuildFile; fileRef = 04C3CDBE2716EE5500F951CB /* ConnectDevice.h */; };
		04C3CDC12716EE5500F951CB /* ConnectDevice.m in Sources */ = {isa = PBXBuildFile; fileRef = 04C3CDBF2716EE5500F951CB /* ConnectDevice.m */; };
		04C3CDC42716F54700F951CB /* DisconnectDevice.h in Headers */ = {isa = PBXBuildFile; fileRef = 04C3CDC22716F54700F951CB /* DisconnectDevice.h */; };
		04C3CDC52716F54700F951CB /* DisconnectDevice.m in Sources */ = {isa = PBXBuildFile; fileRef = 04C3CDC32716F54700F951CB /* DisconnectDevice.m */; };
		04E3E60427182E6B00D1DDBE /* UpDeviceOnlineStatus.h in Headers */ = {isa = PBXBuildFile; fileRef = 04E3E60327182E6B00D1DDBE /* UpDeviceOnlineStatus.h */; settings = {ATTRIBUTES = (Public, ); }; };
		04F426DA2A3954290068FCC7 /* UpDeviceDaemonGIO.h in Headers */ = {isa = PBXBuildFile; fileRef = 04F426D82A3954290068FCC7 /* UpDeviceDaemonGIO.h */; };
		04F426DB2A3954290068FCC7 /* UpDeviceDaemonGIO.m in Sources */ = {isa = PBXBuildFile; fileRef = 04F426D92A3954290068FCC7 /* UpDeviceDaemonGIO.m */; };
		086A596B6C6F3D4EA7B23177 /* libPods-UPDeviceTests.a in Frameworks */ = {isa = PBXBuildFile; fileRef = 5A394E3B1310D11A99A55CD4 /* libPods-UPDeviceTests.a */; };
		221C2A2425C14D3D00D68224 /* FakeDelegateFactory.m in Sources */ = {isa = PBXBuildFile; fileRef = 221C2A2325C14D3D00D68224 /* FakeDelegateFactory.m */; };
		2232840B25BEA9A4001CBE68 /* CustomFilterProvider.m in Sources */ = {isa = PBXBuildFile; fileRef = 2232840A25BEA9A4001CBE68 /* CustomFilterProvider.m */; };
		2232841F25C01B74001CBE68 /* FakeUpDeviceBase.m in Sources */ = {isa = PBXBuildFile; fileRef = 2232841925C01B73001CBE68 /* FakeUpDeviceBase.m */; };
		2232842025C01B74001CBE68 /* FakeUpDeviceReceiver.m in Sources */ = {isa = PBXBuildFile; fileRef = 2232841B25C01B73001CBE68 /* FakeUpDeviceReceiver.m */; };
		2232842125C01B74001CBE68 /* FakeUpDeviceListener.m in Sources */ = {isa = PBXBuildFile; fileRef = 2232841E25C01B74001CBE68 /* FakeUpDeviceListener.m */; };
		22344EC3262FC0FA00FF4ACA /* FakeReportListener.m in Sources */ = {isa = PBXBuildFile; fileRef = 22344EC2262FC0FA00FF4ACA /* FakeReportListener.m */; };
		22344EC62630102600FF4ACA /* FakeDetectListener.m in Sources */ = {isa = PBXBuildFile; fileRef = 22344EC52630102600FF4ACA /* FakeDetectListener.m */; };
		22344EC92631833300FF4ACA /* FakeLEEngineListener.m in Sources */ = {isa = PBXBuildFile; fileRef = 22344EC82631833300FF4ACA /* FakeLEEngineListener.m */; };
		225B08EE25BAA53900E5E86A /* FakeDelegateListener.m in Sources */ = {isa = PBXBuildFile; fileRef = 225B08ED25BAA53900E5E86A /* FakeDelegateListener.m */; };
		22A5225B25C3EDB000E76C88 /* FakeUSDKDevice.m in Sources */ = {isa = PBXBuildFile; fileRef = 22A5225A25C3EDB000E76C88 /* FakeUSDKDevice.m */; };
		22F785CB265CEDDB00175218 /* FetchGroupableDeviceList.m in Sources */ = {isa = PBXBuildFile; fileRef = 22F785BF265CEDDA00175218 /* FetchGroupableDeviceList.m */; };
		22F785CC265CEDDB00175218 /* RemoveDevicesFromGroup.h in Headers */ = {isa = PBXBuildFile; fileRef = 22F785C0265CEDDA00175218 /* RemoveDevicesFromGroup.h */; };
		22F785CD265CEDDB00175218 /* RemoveDevicesFromGroup.m in Sources */ = {isa = PBXBuildFile; fileRef = 22F785C1265CEDDA00175218 /* RemoveDevicesFromGroup.m */; };
		22F785CE265CEDDB00175218 /* GetGroupMemberList.h in Headers */ = {isa = PBXBuildFile; fileRef = 22F785C2265CEDDA00175218 /* GetGroupMemberList.h */; };
		22F785CF265CEDDB00175218 /* CreateDeviceGroup.m in Sources */ = {isa = PBXBuildFile; fileRef = 22F785C3265CEDDB00175218 /* CreateDeviceGroup.m */; };
		22F785D0265CEDDB00175218 /* CreateDeviceGroup.h in Headers */ = {isa = PBXBuildFile; fileRef = 22F785C4265CEDDB00175218 /* CreateDeviceGroup.h */; };
		22F785D1265CEDDB00175218 /* DeleteDeviceGroup.h in Headers */ = {isa = PBXBuildFile; fileRef = 22F785C5265CEDDB00175218 /* DeleteDeviceGroup.h */; };
		22F785D2265CEDDB00175218 /* DeleteDeviceGroup.m in Sources */ = {isa = PBXBuildFile; fileRef = 22F785C6265CEDDB00175218 /* DeleteDeviceGroup.m */; };
		22F785D3265CEDDB00175218 /* AddDevicesToGroup.m in Sources */ = {isa = PBXBuildFile; fileRef = 22F785C7265CEDDB00175218 /* AddDevicesToGroup.m */; };
		22F785D4265CEDDB00175218 /* GetGroupMemberList.m in Sources */ = {isa = PBXBuildFile; fileRef = 22F785C8265CEDDB00175218 /* GetGroupMemberList.m */; };
		22F785D5265CEDDB00175218 /* FetchGroupableDeviceList.h in Headers */ = {isa = PBXBuildFile; fileRef = 22F785C9265CEDDB00175218 /* FetchGroupableDeviceList.h */; };
		22F785D6265CEDDB00175218 /* AddDevicesToGroup.h in Headers */ = {isa = PBXBuildFile; fileRef = 22F785CA265CEDDB00175218 /* AddDevicesToGroup.h */; };
		22F785E4265CF88B00175218 /* DeviceManagerSteps.m in Sources */ = {isa = PBXBuildFile; fileRef = 22F785D9265CF88B00175218 /* DeviceManagerSteps.m */; };
		22F785E5265CF88B00175218 /* WashDeviceSteps.m in Sources */ = {isa = PBXBuildFile; fileRef = 22F785DB265CF88B00175218 /* WashDeviceSteps.m */; };
		22F785E6265CF88B00175218 /* WifiDeviceToolkitSteps.m in Sources */ = {isa = PBXBuildFile; fileRef = 22F785DE265CF88B00175218 /* WifiDeviceToolkitSteps.m */; };
		22F785E7265CF88B00175218 /* InitializationSteps.m in Sources */ = {isa = PBXBuildFile; fileRef = 22F785DF265CF88B00175218 /* InitializationSteps.m */; };
		22F785E8265CF88B00175218 /* EngineDeviceSteps.m in Sources */ = {isa = PBXBuildFile; fileRef = 22F785E1265CF88B00175218 /* EngineDeviceSteps.m */; };
		22F785E9265CF88B00175218 /* DeviceBaseSteps.m in Sources */ = {isa = PBXBuildFile; fileRef = 22F785E3265CF88B00175218 /* DeviceBaseSteps.m */; };
		22F785EC265CF89C00175218 /* StepsUtils.m in Sources */ = {isa = PBXBuildFile; fileRef = 22F785EA265CF89C00175218 /* StepsUtils.m */; };
		22F785ED265D00C400175218 /* GetNetType.h in Headers */ = {isa = PBXBuildFile; fileRef = 333B1ABF26170B6700A389D9 /* GetNetType.h */; settings = {ATTRIBUTES = (Public, ); }; };
		22F785EE265D00D000175218 /* GetNetType.m in Sources */ = {isa = PBXBuildFile; fileRef = 333B1ABE26170B6700A389D9 /* GetNetType.m */; };
		22F785F1265D022D00175218 /* FakeDelegateBase.m in Sources */ = {isa = PBXBuildFile; fileRef = 22F785F0265D022D00175218 /* FakeDelegateBase.m */; };
		22F785FC265DE20F00175218 /* UpAttachResourceDelegate.h in Headers */ = {isa = PBXBuildFile; fileRef = 22F785FB265DE20F00175218 /* UpAttachResourceDelegate.h */; };
		22F785FF265DE40500175218 /* AttachDecodeResource.m in Sources */ = {isa = PBXBuildFile; fileRef = 22F785FD265DE40500175218 /* AttachDecodeResource.m */; };
		22F78600265DE40500175218 /* AttachDecodeResource.h in Headers */ = {isa = PBXBuildFile; fileRef = 22F785FE265DE40500175218 /* AttachDecodeResource.h */; };
		22F78605265E1E2500175218 /* IsGroup.m in Sources */ = {isa = PBXBuildFile; fileRef = 22F78603265E1E2500175218 /* IsGroup.m */; };
		22F78606265E1E2500175218 /* IsGroup.h in Headers */ = {isa = PBXBuildFile; fileRef = 22F78604265E1E2500175218 /* IsGroup.h */; };
		334AFCEB266F408F003F443B /* StartWashFota.h in Headers */ = {isa = PBXBuildFile; fileRef = 334AFCE9266F408F003F443B /* StartWashFota.h */; };
		334AFCEC266F408F003F443B /* StartWashFota.m in Sources */ = {isa = PBXBuildFile; fileRef = 334AFCEA266F408F003F443B /* StartWashFota.m */; };
		3382E04D26E6087B009576F5 /* GetDeviceOnlineStatus.h in Headers */ = {isa = PBXBuildFile; fileRef = 3382E04B26E6087B009576F5 /* GetDeviceOnlineStatus.h */; };
		3382E04E26E6087B009576F5 /* GetDeviceOnlineStatus.m in Sources */ = {isa = PBXBuildFile; fileRef = 3382E04C26E6087B009576F5 /* GetDeviceOnlineStatus.m */; };
		33ED73B6269FFFA500D4C7A8 /* features in Resources */ = {isa = PBXBuildFile; fileRef = 33ED73B5269FFFA500D4C7A8 /* features */; };
		33FDA61226285A3E0099964E /* UpCompatEngineDevice.h in Headers */ = {isa = PBXBuildFile; fileRef = 33FDA61026285A3E0099964E /* UpCompatEngineDevice.h */; settings = {ATTRIBUTES = (Public, ); }; };
		33FDA61326285A3E0099964E /* UpCompatEngineDevice.m in Sources */ = {isa = PBXBuildFile; fileRef = 33FDA61126285A3E0099964E /* UpCompatEngineDevice.m */; };
		33FDA62026285A690099964E /* UpWashDevice.m in Sources */ = {isa = PBXBuildFile; fileRef = 33FDA61826285A690099964E /* UpWashDevice.m */; };
		33FDA62126285A690099964E /* UpWashAdapterApi.h in Headers */ = {isa = PBXBuildFile; fileRef = 33FDA61926285A690099964E /* UpWashAdapterApi.h */; settings = {ATTRIBUTES = (Public, ); }; };
		33FDA62226285A690099964E /* UpWashModel.m in Sources */ = {isa = PBXBuildFile; fileRef = 33FDA61A26285A690099964E /* UpWashModel.m */; };
		33FDA62326285A690099964E /* UpWashDevice.h in Headers */ = {isa = PBXBuildFile; fileRef = 33FDA61B26285A690099964E /* UpWashDevice.h */; settings = {ATTRIBUTES = (Public, ); }; };
		33FDA62526285A690099964E /* UpWashAdapterApi.m in Sources */ = {isa = PBXBuildFile; fileRef = 33FDA61D26285A690099964E /* UpWashAdapterApi.m */; };
		33FDA62626285A690099964E /* UpWashModel.h in Headers */ = {isa = PBXBuildFile; fileRef = 33FDA61E26285A690099964E /* UpWashModel.h */; settings = {ATTRIBUTES = (Public, ); }; };
		33FDA768262D619B0099964E /* UpVoiceBoxDevice.h in Headers */ = {isa = PBXBuildFile; fileRef = 33FDA766262D619B0099964E /* UpVoiceBoxDevice.h */; settings = {ATTRIBUTES = (Public, ); }; };
		33FDA769262D619B0099964E /* UpVoiceBoxDevice.m in Sources */ = {isa = PBXBuildFile; fileRef = 33FDA767262D619B0099964E /* UpVoiceBoxDevice.m */; };
		33FDA77A262ECA0E0099964E /* UpWashDataResponseParser.h in Headers */ = {isa = PBXBuildFile; fileRef = 33FDA778262ECA0E0099964E /* UpWashDataResponseParser.h */; settings = {ATTRIBUTES = (Public, ); }; };
		33FDA77B262ECA0E0099964E /* UpWashDataResponseParser.m in Sources */ = {isa = PBXBuildFile; fileRef = 33FDA779262ECA0E0099964E /* UpWashDataResponseParser.m */; };
		40E0AC4925148816007DBC42 /* UDSafeMutableArray.m in Sources */ = {isa = PBXBuildFile; fileRef = 40E0AC4525148816007DBC42 /* UDSafeMutableArray.m */; };
		40E0AC4A25148816007DBC42 /* UDSafeMutableDictionary.h in Headers */ = {isa = PBXBuildFile; fileRef = 40E0AC4625148816007DBC42 /* UDSafeMutableDictionary.h */; settings = {ATTRIBUTES = (Public, ); }; };
		40E0AC4B25148816007DBC42 /* UDSafeMutableDictionary.m in Sources */ = {isa = PBXBuildFile; fileRef = 40E0AC4725148816007DBC42 /* UDSafeMutableDictionary.m */; };
		40E0AC4C25148816007DBC42 /* UDSafeMutableArray.h in Headers */ = {isa = PBXBuildFile; fileRef = 40E0AC4825148816007DBC42 /* UDSafeMutableArray.h */; settings = {ATTRIBUTES = (Public, ); }; };
		40E0AC6225148BD8007DBC42 /* Info.plist in Resources */ = {isa = PBXBuildFile; fileRef = 40E0AC6125148BD8007DBC42 /* Info.plist */; };
		4AC22BEA2754B1B200BC7418 /* GetDeviceOnlineStateV2.h in Headers */ = {isa = PBXBuildFile; fileRef = 4AC22BE82754B1B200BC7418 /* GetDeviceOnlineStateV2.h */; };
		4AC22BEB2754B1B200BC7418 /* GetDeviceOnlineStateV2.m in Sources */ = {isa = PBXBuildFile; fileRef = 4AC22BE92754B1B200BC7418 /* GetDeviceOnlineStateV2.m */; };
		4AC22C072754CBAE00BC7418 /* UpDeviceOnlineStateV2.h in Headers */ = {isa = PBXBuildFile; fileRef = 4AC22C062754CBAE00BC7418 /* UpDeviceOnlineStateV2.h */; };
		4AFC2856275F4788008F4DDB /* GetDeviceWifiLocalState.h in Headers */ = {isa = PBXBuildFile; fileRef = 4AFC2854275F4787008F4DDB /* GetDeviceWifiLocalState.h */; };
		4AFC2857275F4788008F4DDB /* GetDeviceWifiLocalState.m in Sources */ = {isa = PBXBuildFile; fileRef = 4AFC2855275F4788008F4DDB /* GetDeviceWifiLocalState.m */; };
		4F22A5802AFE52F700269406 /* GetOfflineCause.h in Headers */ = {isa = PBXBuildFile; fileRef = 4F22A57E2AFE52F700269406 /* GetOfflineCause.h */; };
		4F22A5812AFE52F700269406 /* GetOfflineCause.m in Sources */ = {isa = PBXBuildFile; fileRef = 4F22A57F2AFE52F700269406 /* GetOfflineCause.m */; };
		4F5ED4E02C72F15500119B62 /* UpNonNetworkDeviceFactory.h in Headers */ = {isa = PBXBuildFile; fileRef = 4F5ED4DC2C72F15500119B62 /* UpNonNetworkDeviceFactory.h */; };
		4F5ED4E12C72F15500119B62 /* UPNonNetworkDevice.h in Headers */ = {isa = PBXBuildFile; fileRef = 4F5ED4DD2C72F15500119B62 /* UPNonNetworkDevice.h */; };
		4F5ED4E22C72F15500119B62 /* UPNonNetworkDevice.m in Sources */ = {isa = PBXBuildFile; fileRef = 4F5ED4DE2C72F15500119B62 /* UPNonNetworkDevice.m */; };
		4F5ED4E32C72F15500119B62 /* UpNonNetworkDeviceFactory.m in Sources */ = {isa = PBXBuildFile; fileRef = 4F5ED4DF2C72F15500119B62 /* UpNonNetworkDeviceFactory.m */; };
		4F6869912B883B4B00943C1A /* ExecuteCommandWithResult.h in Headers */ = {isa = PBXBuildFile; fileRef = 4F68698F2B883B4B00943C1A /* ExecuteCommandWithResult.h */; };
		4F6869922B883B4B00943C1A /* ExecuteCommandWithResult.m in Sources */ = {isa = PBXBuildFile; fileRef = 4F6869902B883B4B00943C1A /* ExecuteCommandWithResult.m */; };
		4F7EB0912D9524AC007694FE /* UpDeviceCardInfo.h in Headers */ = {isa = PBXBuildFile; fileRef = 4F7EB08F2D9524AC007694FE /* UpDeviceCardInfo.h */; };
		4F7EB0922D9524AC007694FE /* UpDeviceCardInfo.m in Sources */ = {isa = PBXBuildFile; fileRef = 4F7EB0902D9524AC007694FE /* UpDeviceCardInfo.m */; };
		4F9B18792D7E8B6200C62B67 /* UPAggregateDevice.h in Headers */ = {isa = PBXBuildFile; fileRef = 4F9B18772D7E8B6200C62B67 /* UPAggregateDevice.h */; };
		4F9B187A2D7E8B6200C62B67 /* UPAggregateDevice.m in Sources */ = {isa = PBXBuildFile; fileRef = 4F9B18782D7E8B6200C62B67 /* UPAggregateDevice.m */; };
		4F9B187D2D7E8CA200C62B67 /* UPAggregateDeviceFactory.h in Headers */ = {isa = PBXBuildFile; fileRef = 4F9B187B2D7E8CA200C62B67 /* UPAggregateDeviceFactory.h */; };
		4F9B187E2D7E8CA200C62B67 /* UPAggregateDeviceFactory.m in Sources */ = {isa = PBXBuildFile; fileRef = 4F9B187C2D7E8CA200C62B67 /* UPAggregateDeviceFactory.m */; };
		54A04299AC185BCD88CF75A6 /* libPods-UPDevice.a in Frameworks */ = {isa = PBXBuildFile; fileRef = 6A009374069A3438B0417DEB /* libPods-UPDevice.a */; };
		69989D04D2CC7EB9FCA54A2A /* libPods-UPDeviceDebugger.a in Frameworks */ = {isa = PBXBuildFile; fileRef = 6487A8411EC9F1313E98D8F1 /* libPods-UPDeviceDebugger.a */; };
		8412D69C2D7F0C02002B44F7 /* UpDeviceCacheManager.m in Sources */ = {isa = PBXBuildFile; fileRef = 8412D69B2D7F0C02002B44F7 /* UpDeviceCacheManager.m */; };
		8412D69D2D7F0C02002B44F7 /* UpDeviceCacheManager.h in Headers */ = {isa = PBXBuildFile; fileRef = 8412D69A2D7F0C02002B44F7 /* UpDeviceCacheManager.h */; };
		8447614C2D9A7AFF00AE8BE4 /* UpDeviceCardManager.h in Headers */ = {isa = PBXBuildFile; fileRef = 8447614A2D9A7AFF00AE8BE4 /* UpDeviceCardManager.h */; };
		8447614D2D9A7AFF00AE8BE4 /* UpDeviceCardManager.m in Sources */ = {isa = PBXBuildFile; fileRef = 8447614B2D9A7AFF00AE8BE4 /* UpDeviceCardManager.m */; };
		8457E18B2D8AC31B007EBE53 /* UpDeviceGroupOptDeleagte.h in Headers */ = {isa = PBXBuildFile; fileRef = 8457E1852D8AC31B007EBE53 /* UpDeviceGroupOptDeleagte.h */; };
		8457E18C2D8AC31B007EBE53 /* UpDeviceReportMonitor.h in Headers */ = {isa = PBXBuildFile; fileRef = 8457E1862D8AC31B007EBE53 /* UpDeviceReportMonitor.h */; };
		8457E18E2D8AC31B007EBE53 /* UpDeviceReportMonitor.m in Sources */ = {isa = PBXBuildFile; fileRef = 8457E1872D8AC31B007EBE53 /* UpDeviceReportMonitor.m */; };
		8466DE9F2CCF32A500DFDA87 /* GetOnlyConfigState.m in Sources */ = {isa = PBXBuildFile; fileRef = 8466DE9D2CCF32A500DFDA87 /* GetOnlyConfigState.m */; };
		8466DEA02CCF32A500DFDA87 /* GetOnlyConfigState.h in Headers */ = {isa = PBXBuildFile; fileRef = 8466DE9E2CCF32A500DFDA87 /* GetOnlyConfigState.h */; };
		8466DEA22CCF32F200DFDA87 /* UpDeviceOnlyConfigState.h in Headers */ = {isa = PBXBuildFile; fileRef = 8466DEA12CCF32F200DFDA87 /* UpDeviceOnlyConfigState.h */; };
		84AEE78C2DA65221008FF4C7 /* uSDKDeviceWrapper.h in Headers */ = {isa = PBXBuildFile; fileRef = 84AEE78A2DA65221008FF4C7 /* uSDKDeviceWrapper.h */; };
		84AEE78D2DA65221008FF4C7 /* uSDKDeviceWrapper.m in Sources */ = {isa = PBXBuildFile; fileRef = 84AEE78B2DA65221008FF4C7 /* uSDKDeviceWrapper.m */; };
		84AEE7902DA668C0008FF4C7 /* GetuSDKDeviceWrapper.m in Sources */ = {isa = PBXBuildFile; fileRef = 84AEE78F2DA668C0008FF4C7 /* GetuSDKDeviceWrapper.m */; };
		84AEE7912DA668C0008FF4C7 /* GetuSDKDeviceWrapper.h in Headers */ = {isa = PBXBuildFile; fileRef = 84AEE78E2DA668C0008FF4C7 /* GetuSDKDeviceWrapper.h */; };
		84C30C682BF44F9B00754263 /* GetOfflineDays.h in Headers */ = {isa = PBXBuildFile; fileRef = 84C30C662BF44F9B00754263 /* GetOfflineDays.h */; };
		84C30C692BF44F9B00754263 /* GetOfflineDays.m in Sources */ = {isa = PBXBuildFile; fileRef = 84C30C672BF44F9B00754263 /* GetOfflineDays.m */; };
		966049242A2DC39F00138B09 /* UpEngineDevice+ResourceConfigSource.h in Headers */ = {isa = PBXBuildFile; fileRef = 966049222A2DC39E00138B09 /* UpEngineDevice+ResourceConfigSource.h */; };
		966049252A2DC39F00138B09 /* UpEngineDevice+ResourceConfigSource.m in Sources */ = {isa = PBXBuildFile; fileRef = 966049232A2DC39E00138B09 /* UpEngineDevice+ResourceConfigSource.m */; };
		9B98655ABC783C3C463E125F /* libPods-updevice_api.a in Frameworks */ = {isa = PBXBuildFile; fileRef = FEF68A3D049F1359CACD4BF1 /* libPods-updevice_api.a */; };
		A01559DF280821DE00723EDC /* UpDeviceObjectCommonHelper.h in Headers */ = {isa = PBXBuildFile; fileRef = A01559DD280821DE00723EDC /* UpDeviceObjectCommonHelper.h */; };
		A01559E0280821DE00723EDC /* UpDeviceObjectCommonHelper.m in Sources */ = {isa = PBXBuildFile; fileRef = A01559DE280821DE00723EDC /* UpDeviceObjectCommonHelper.m */; };
		A0277540298A39090014920F /* UpWashDeviceManager.h in Headers */ = {isa = PBXBuildFile; fileRef = A027753E298A39090014920F /* UpWashDeviceManager.h */; settings = {ATTRIBUTES = (Public, ); }; };
		A0277541298A39090014920F /* UpWashDeviceManager.m in Sources */ = {isa = PBXBuildFile; fileRef = A027753F298A39090014920F /* UpWashDeviceManager.m */; };
		A0277544298A3D760014920F /* WashDeviceManagerSteps.m in Sources */ = {isa = PBXBuildFile; fileRef = A0277543298A3D760014920F /* WashDeviceManagerSteps.m */; };
		A0277547298A3DC80014920F /* FakeWashdapterApi.m in Sources */ = {isa = PBXBuildFile; fileRef = A0277546298A3DC80014920F /* FakeWashdapterApi.m */; };
		A073AE38278423CB00ED90BB /* QCConnectDevice.h in Headers */ = {isa = PBXBuildFile; fileRef = A073AE36278423CB00ED90BB /* QCConnectDevice.h */; };
		A073AE39278423CB00ED90BB /* QCConnectDevice.m in Sources */ = {isa = PBXBuildFile; fileRef = A073AE37278423CB00ED90BB /* QCConnectDevice.m */; };
		A073AE3C2784240400ED90BB /* QCDisconnectDevice.h in Headers */ = {isa = PBXBuildFile; fileRef = A073AE3A2784240400ED90BB /* QCDisconnectDevice.h */; };
		A073AE3D2784240400ED90BB /* QCDisconnectDevice.m in Sources */ = {isa = PBXBuildFile; fileRef = A073AE3B2784240400ED90BB /* QCDisconnectDevice.m */; };
		A0C41BD02949D8C700A877F9 /* UpDeviceGetDeviceResourceTool.h in Headers */ = {isa = PBXBuildFile; fileRef = A0C41BCE2949D8C700A877F9 /* UpDeviceGetDeviceResourceTool.h */; };
		A0C41BD12949D8C700A877F9 /* UpDeviceGetDeviceResourceTool.m in Sources */ = {isa = PBXBuildFile; fileRef = A0C41BCF2949D8C700A877F9 /* UpDeviceGetDeviceResourceTool.m */; };
		A0C41BD4294AF23200A877F9 /* UpDeviceResourceManager.h in Headers */ = {isa = PBXBuildFile; fileRef = A0C41BD2294AF23200A877F9 /* UpDeviceResourceManager.h */; settings = {ATTRIBUTES = (Public, ); }; };
		A0C41BD5294AF23200A877F9 /* UpDeviceResourceManager.m in Sources */ = {isa = PBXBuildFile; fileRef = A0C41BD3294AF23200A877F9 /* UpDeviceResourceManager.m */; };
		A0C41BDE294B297600A877F9 /* UpDeviceStringCommonHelper.h in Headers */ = {isa = PBXBuildFile; fileRef = A0C41BDC294B297600A877F9 /* UpDeviceStringCommonHelper.h */; };
		A0C41BDF294B297600A877F9 /* UpDeviceStringCommonHelper.m in Sources */ = {isa = PBXBuildFile; fileRef = A0C41BDD294B297600A877F9 /* UpDeviceStringCommonHelper.m */; };
		A0C41BE1294C11BB00A877F9 /* UpDeviceResourceSteps.m in Sources */ = {isa = PBXBuildFile; fileRef = A0C41BE0294C11BA00A877F9 /* UpDeviceResourceSteps.m */; };
		A0CE394B27576A60003FD92C /* GetDeviceNetworkLevel.h in Headers */ = {isa = PBXBuildFile; fileRef = A0CE394927576A60003FD92C /* GetDeviceNetworkLevel.h */; };
		A0CE394C27576A60003FD92C /* GetDeviceNetworkLevel.m in Sources */ = {isa = PBXBuildFile; fileRef = A0CE394A27576A60003FD92C /* GetDeviceNetworkLevel.m */; };
		A0CE395327576C8D003FD92C /* UpDeviceNetworkLevel.h in Headers */ = {isa = PBXBuildFile; fileRef = A0CE395227576C8D003FD92C /* UpDeviceNetworkLevel.h */; settings = {ATTRIBUTES = (Public, ); }; };
		A0CFB3AD282CFBDC007E27BA /* FakeResourceManager.m in Sources */ = {isa = PBXBuildFile; fileRef = A0CFB3AC282CFBDC007E27BA /* FakeResourceManager.m */; };
		A0D3FEB727E42F1800DA145B /* UpDeviceQCConnectTimeoutType.h in Headers */ = {isa = PBXBuildFile; fileRef = A0D3FEB627E42F0800DA145B /* UpDeviceQCConnectTimeoutType.h */; settings = {ATTRIBUTES = (Public, ); }; };
		A0F0AEC8285B1A5900882A13 /* GetDeviceBleState.h in Headers */ = {isa = PBXBuildFile; fileRef = A0F0AEC6285B1A5900882A13 /* GetDeviceBleState.h */; };
		A0F0AEC9285B1A5900882A13 /* GetDeviceBleState.m in Sources */ = {isa = PBXBuildFile; fileRef = A0F0AEC7285B1A5900882A13 /* GetDeviceBleState.m */; };
		AD0A1CCC2713DB2600415B3D /* UpDeviceTracker.h in Headers */ = {isa = PBXBuildFile; fileRef = AD0A1CCB2713DB2600415B3D /* UpDeviceTracker.h */; };
		AD113C022730DB7700EDB9F1 /* GetDeviceSleepState.h in Headers */ = {isa = PBXBuildFile; fileRef = AD113C002730DB7700EDB9F1 /* GetDeviceSleepState.h */; };
		AD113C032730DB7700EDB9F1 /* GetDeviceSleepState.m in Sources */ = {isa = PBXBuildFile; fileRef = AD113C012730DB7700EDB9F1 /* GetDeviceSleepState.m */; };
		AD2772622600576300287D0C /* (null) in Headers */ = {isa = PBXBuildFile; settings = {ATTRIBUTES = (Public, ); }; };
		AD2772632600576300287D0C /* (null) in Sources */ = {isa = PBXBuildFile; };
		AD27726A2600587F00287D0C /* (null) in Headers */ = {isa = PBXBuildFile; settings = {ATTRIBUTES = (Public, ); }; };
		AD27726B2600587F00287D0C /* (null) in Sources */ = {isa = PBXBuildFile; };
		AD8DD10C2643E4B500086CBF /* UDSafeMutableDictionary.m in Sources */ = {isa = PBXBuildFile; fileRef = 40E0AC4725148816007DBC42 /* UDSafeMutableDictionary.m */; };
		AD8DD11D2643E5C500086CBF /* Info.plist in Resources */ = {isa = PBXBuildFile; fileRef = 40E0AC8A25148CD5007DBC42 /* Info.plist */; };
		AD8DD11E2643E61800086CBF /* DeviceManagerDetectListener.h in Headers */ = {isa = PBXBuildFile; fileRef = F86314CA250A039500C948B9 /* DeviceManagerDetectListener.h */; settings = {ATTRIBUTES = (Public, ); }; };
		AD8DD11F2643E61800086CBF /* DeviceManagerToolkitListener.h in Headers */ = {isa = PBXBuildFile; fileRef = F8631545250A039600C948B9 /* DeviceManagerToolkitListener.h */; settings = {ATTRIBUTES = (Public, ); }; };
		AD8DD1202643E61800086CBF /* UpDeviceDaemon.h in Headers */ = {isa = PBXBuildFile; fileRef = F86314E9250A039600C948B9 /* UpDeviceDaemon.h */; settings = {ATTRIBUTES = (Public, ); }; };
		AD8DD1212643E61800086CBF /* UpDeviceInjection.h in Headers */ = {isa = PBXBuildFile; fileRef = F86314E8250A039600C948B9 /* UpDeviceInjection.h */; settings = {ATTRIBUTES = (Public, ); }; };
		AD8DD1222643E61800086CBF /* UpDeviceManager.h in Headers */ = {isa = PBXBuildFile; fileRef = F86314EE250A039600C948B9 /* UpDeviceManager.h */; settings = {ATTRIBUTES = (Public, ); }; };
		AD8DD1232643E61800086CBF /* UpDeviceChangeCallback.h in Headers */ = {isa = PBXBuildFile; fileRef = F86314E0250A039600C948B9 /* UpDeviceChangeCallback.h */; settings = {ATTRIBUTES = (Public, ); }; };
		AD8DD1242643E61800086CBF /* UpDeviceNetworkType.h in Headers */ = {isa = PBXBuildFile; fileRef = F86314E2250A039600C948B9 /* UpDeviceNetworkType.h */; settings = {ATTRIBUTES = (Public, ); }; };
		AD8DD1252643E61800086CBF /* UpDeviceStatus.h in Headers */ = {isa = PBXBuildFile; fileRef = F86314E4250A039600C948B9 /* UpDeviceStatus.h */; settings = {ATTRIBUTES = (Public, ); }; };
		AD8DD1262643E61800086CBF /* UpDeviceType.h in Headers */ = {isa = PBXBuildFile; fileRef = F86314E3250A039600C948B9 /* UpDeviceType.h */; settings = {ATTRIBUTES = (Public, ); }; };
		AD8DD1272643E61800086CBF /* UpSubDevChangeCallback.h in Headers */ = {isa = PBXBuildFile; fileRef = F86314E1250A039600C948B9 /* UpSubDevChangeCallback.h */; settings = {ATTRIBUTES = (Public, ); }; };
		AD8DD1292643E61800086CBF /* UpCompatApi.h in Headers */ = {isa = PBXBuildFile; fileRef = F86314D6250A039600C948B9 /* UpCompatApi.h */; settings = {ATTRIBUTES = (Public, ); }; };
		AD8DD12A2643E61800086CBF /* UpCompatCallbackWrapper.h in Headers */ = {isa = PBXBuildFile; fileRef = F86314D4250A039600C948B9 /* UpCompatCallbackWrapper.h */; settings = {ATTRIBUTES = (Public, ); }; };
		AD8DD12B2643E61800086CBF /* UpCompatCommand.h in Headers */ = {isa = PBXBuildFile; fileRef = F86314DA250A039600C948B9 /* UpCompatCommand.h */; settings = {ATTRIBUTES = (Public, ); }; };
		AD8DD12C2643E61800086CBF /* UpCompatDevice.h in Headers */ = {isa = PBXBuildFile; fileRef = F86314D9250A039600C948B9 /* UpCompatDevice.h */; settings = {ATTRIBUTES = (Public, ); }; };
		AD8DD12D2643E61800086CBF /* UpCompatDeviceStore.h in Headers */ = {isa = PBXBuildFile; fileRef = F86314D0250A039500C948B9 /* UpCompatDeviceStore.h */; settings = {ATTRIBUTES = (Public, ); }; };
		AD8DD12E2643E61800086CBF /* UpDeviceDataCache.h in Headers */ = {isa = PBXBuildFile; fileRef = F86314D1250A039500C948B9 /* UpDeviceDataCache.h */; settings = {ATTRIBUTES = (Public, ); }; };
		AD8DD1312643E61800086CBF /* DummyDeviceBroker.h in Headers */ = {isa = PBXBuildFile; fileRef = F8631548250A039700C948B9 /* DummyDeviceBroker.h */; settings = {ATTRIBUTES = (Public, ); }; };
		AD8DD1322643E61800086CBF /* DummyDeviceToolkit.h in Headers */ = {isa = PBXBuildFile; fileRef = F8631547250A039700C948B9 /* DummyDeviceToolkit.h */; settings = {ATTRIBUTES = (Public, ); }; };
		AD8DD1332643E61800086CBF /* UpEngineDataSource.h in Headers */ = {isa = PBXBuildFile; fileRef = F863154D250A039700C948B9 /* UpEngineDataSource.h */; settings = {ATTRIBUTES = (Public, ); }; };
		AD8DD1342643E61800086CBF /* UpEngineDevice.h in Headers */ = {isa = PBXBuildFile; fileRef = F863154F250A039700C948B9 /* UpEngineDevice.h */; settings = {ATTRIBUTES = (Public, ); }; };
		AD8DD1352643E61900086CBF /* UpEngineDevice+ConfigSource.h in Headers */ = {isa = PBXBuildFile; fileRef = F863155A250A039700C948B9 /* UpEngineDevice+ConfigSource.h */; settings = {ATTRIBUTES = (Public, ); }; };
		AD8DD1362643E61900086CBF /* UpEngineDevice+EngineDelegate.h in Headers */ = {isa = PBXBuildFile; fileRef = F8631556250A039700C948B9 /* UpEngineDevice+EngineDelegate.h */; settings = {ATTRIBUTES = (Public, ); }; };
		AD8DD1372643E61900086CBF /* UpEngineDeviceFactory.h in Headers */ = {isa = PBXBuildFile; fileRef = F8631554250A039700C948B9 /* UpEngineDeviceFactory.h */; settings = {ATTRIBUTES = (Public, ); }; };
		AD8DD1382643E61900086CBF /* UpEngineException.h in Headers */ = {isa = PBXBuildFile; fileRef = F8631555250A039700C948B9 /* UpEngineException.h */; settings = {ATTRIBUTES = (Public, ); }; };
		AD8DD1392643E61900086CBF /* UpEngineReloadHandler.h in Headers */ = {isa = PBXBuildFile; fileRef = F863154C250A039700C948B9 /* UpEngineReloadHandler.h */; settings = {ATTRIBUTES = (Public, ); }; };
		AD8DD13A2643E61900086CBF /* UpEngineReporter.h in Headers */ = {isa = PBXBuildFile; fileRef = F8631559250A039700C948B9 /* UpEngineReporter.h */; settings = {ATTRIBUTES = (Public, ); }; };
		AD8DD13B2643E61900086CBF /* UpLogicEngineApi.h in Headers */ = {isa = PBXBuildFile; fileRef = F863154E250A039700C948B9 /* UpLogicEngineApi.h */; settings = {ATTRIBUTES = (Public, ); }; };
		AD8DD13C2643E61900086CBF /* AttachDevice.h in Headers */ = {isa = PBXBuildFile; fileRef = F8631517250A039600C948B9 /* AttachDevice.h */; settings = {ATTRIBUTES = (Public, ); }; };
		AD8DD13D2643E61900086CBF /* AttachResource.h in Headers */ = {isa = PBXBuildFile; fileRef = F8631500250A039600C948B9 /* AttachResource.h */; settings = {ATTRIBUTES = (Public, ); }; };
		AD8DD13E2643E61900086CBF /* AttachToolkit.h in Headers */ = {isa = PBXBuildFile; fileRef = F8631538250A039600C948B9 /* AttachToolkit.h */; settings = {ATTRIBUTES = (Public, ); }; };
		AD8DD13F2643E61900086CBF /* CancelFetchBLEHistoryData.h in Headers */ = {isa = PBXBuildFile; fileRef = F8631531250A039600C948B9 /* CancelFetchBLEHistoryData.h */; settings = {ATTRIBUTES = (Public, ); }; };
		AD8DD1402643E61900086CBF /* CheckBoardFOTAInfo.h in Headers */ = {isa = PBXBuildFile; fileRef = F8631518250A039600C948B9 /* CheckBoardFOTAInfo.h */; settings = {ATTRIBUTES = (Public, ); }; };
		AD8DD1412643E61900086CBF /* ConnectRemoteDevices.h in Headers */ = {isa = PBXBuildFile; fileRef = F8631537250A039600C948B9 /* ConnectRemoteDevices.h */; settings = {ATTRIBUTES = (Public, ); }; };
		AD8DD1422643E61900086CBF /* DetachDevice.h in Headers */ = {isa = PBXBuildFile; fileRef = F863151B250A039600C948B9 /* DetachDevice.h */; settings = {ATTRIBUTES = (Public, ); }; };
		AD8DD1432643E61900086CBF /* DetachResource.h in Headers */ = {isa = PBXBuildFile; fileRef = F8631519250A039600C948B9 /* DetachResource.h */; settings = {ATTRIBUTES = (Public, ); }; };
		AD8DD1442643E61900086CBF /* DetachToolkit.h in Headers */ = {isa = PBXBuildFile; fileRef = F8631527250A039600C948B9 /* DetachToolkit.h */; settings = {ATTRIBUTES = (Public, ); }; };
		AD8DD1452643E61900086CBF /* DisconnectRemoteDevices.h in Headers */ = {isa = PBXBuildFile; fileRef = F863152C250A039600C948B9 /* DisconnectRemoteDevices.h */; settings = {ATTRIBUTES = (Public, ); }; };
		AD8DD1462643E61900086CBF /* ExecuteCommand.h in Headers */ = {isa = PBXBuildFile; fileRef = F8631508250A039600C948B9 /* ExecuteCommand.h */; settings = {ATTRIBUTES = (Public, ); }; };
		AD8DD1472643E61900086CBF /* FetchBLEHistoryData.h in Headers */ = {isa = PBXBuildFile; fileRef = F8631536250A039600C948B9 /* FetchBLEHistoryData.h */; settings = {ATTRIBUTES = (Public, ); }; };
		AD8DD1482643E61900086CBF /* FetchBoardFOTAStatus.h in Headers */ = {isa = PBXBuildFile; fileRef = F8631515250A039600C948B9 /* FetchBoardFOTAStatus.h */; settings = {ATTRIBUTES = (Public, ); }; };
		AD8DD1492643E61900086CBF /* GetBindInfo.h in Headers */ = {isa = PBXBuildFile; fileRef = F8631524250A039600C948B9 /* GetBindInfo.h */; settings = {ATTRIBUTES = (Public, ); }; };
		AD8DD14A2643E61900086CBF /* GetDeviceAttribute.h in Headers */ = {isa = PBXBuildFile; fileRef = F863151F250A039600C948B9 /* GetDeviceAttribute.h */; settings = {ATTRIBUTES = (Public, ); }; };
		AD8DD14B2643E61900086CBF /* GetDeviceAttributes.h in Headers */ = {isa = PBXBuildFile; fileRef = F8631529250A039600C948B9 /* GetDeviceAttributes.h */; settings = {ATTRIBUTES = (Public, ); }; };
		AD8DD14C2643E61900086CBF /* GetDeviceCautions.h in Headers */ = {isa = PBXBuildFile; fileRef = F863152A250A039600C948B9 /* GetDeviceCautions.h */; settings = {ATTRIBUTES = (Public, ); }; };
		AD8DD14D2643E61900086CBF /* GetDeviceConnection.h in Headers */ = {isa = PBXBuildFile; fileRef = F8631533250A039600C948B9 /* GetDeviceConnection.h */; settings = {ATTRIBUTES = (Public, ); }; };
		AD8DD14E2643E61900086CBF /* GetDeviceInfo.h in Headers */ = {isa = PBXBuildFile; fileRef = F863152F250A039600C948B9 /* GetDeviceInfo.h */; settings = {ATTRIBUTES = (Public, ); }; };
		AD8DD14F2643E61900086CBF /* GetDeviceList.h in Headers */ = {isa = PBXBuildFile; fileRef = F8631520250A039600C948B9 /* GetDeviceList.h */; settings = {ATTRIBUTES = (Public, ); }; };
		AD8DD1502643E61900086CBF /* GetNetworkQuality.h in Headers */ = {isa = PBXBuildFile; fileRef = F8631510250A039600C948B9 /* GetNetworkQuality.h */; settings = {ATTRIBUTES = (Public, ); }; };
		AD8DD1512643E61900086CBF /* GetSubDevList.h in Headers */ = {isa = PBXBuildFile; fileRef = F8631522250A039600C948B9 /* GetSubDevList.h */; settings = {ATTRIBUTES = (Public, ); }; };
		AD8DD1522643E61900086CBF /* GetSubDevListBySubDevice.h in Headers */ = {isa = PBXBuildFile; fileRef = F863151A250A039600C948B9 /* GetSubDevListBySubDevice.h */; settings = {ATTRIBUTES = (Public, ); }; };
		AD8DD1532643E61900086CBF /* InFocus.h in Headers */ = {isa = PBXBuildFile; fileRef = F8631513250A039600C948B9 /* InFocus.h */; settings = {ATTRIBUTES = (Public, ); }; };
		AD8DD1542643E61900086CBF /* IsBound.h in Headers */ = {isa = PBXBuildFile; fileRef = F8631521250A039600C948B9 /* IsBound.h */; settings = {ATTRIBUTES = (Public, ); }; };
		AD8DD1562643E61900086CBF /* IsModuleNeedOta.h in Headers */ = {isa = PBXBuildFile; fileRef = F86314FE250A039600C948B9 /* IsModuleNeedOta.h */; settings = {ATTRIBUTES = (Public, ); }; };
		AD8DD1572643E61900086CBF /* OutFocus.h in Headers */ = {isa = PBXBuildFile; fileRef = F8631509250A039600C948B9 /* OutFocus.h */; settings = {ATTRIBUTES = (Public, ); }; };
		AD8DD1582643E61900086CBF /* RefresUsdkDeviceList.h in Headers */ = {isa = PBXBuildFile; fileRef = F8631534250A039600C948B9 /* RefresUsdkDeviceList.h */; settings = {ATTRIBUTES = (Public, ); }; };
		AD8DD1592643E61900086CBF /* SmartLinkSoftwareVersion.h in Headers */ = {isa = PBXBuildFile; fileRef = F8631505250A039600C948B9 /* SmartLinkSoftwareVersion.h */; settings = {ATTRIBUTES = (Public, ); }; };
		AD8DD15A2643E61900086CBF /* StartBoardFOTA.h in Headers */ = {isa = PBXBuildFile; fileRef = F8631530250A039600C948B9 /* StartBoardFOTA.h */; settings = {ATTRIBUTES = (Public, ); }; };
		AD8DD15B2643E61900086CBF /* StartModuleUpdate.h in Headers */ = {isa = PBXBuildFile; fileRef = F8631535250A039600C948B9 /* StartModuleUpdate.h */; settings = {ATTRIBUTES = (Public, ); }; };
		AD8DD15C2643E61900086CBF /* WifiDeviceAction.h in Headers */ = {isa = PBXBuildFile; fileRef = F863153E250A039600C948B9 /* WifiDeviceAction.h */; settings = {ATTRIBUTES = (Public, ); }; };
		AD8DD15D2643E61900086CBF /* WifiToolkitAction.h in Headers */ = {isa = PBXBuildFile; fileRef = F8631532250A039600C948B9 /* WifiToolkitAction.h */; settings = {ATTRIBUTES = (Public, ); }; };
		AD8DD15E2643E61900086CBF /* GatewayConnection.h in Headers */ = {isa = PBXBuildFile; fileRef = F86314F4250A039600C948B9 /* GatewayConnection.h */; settings = {ATTRIBUTES = (Public, ); }; };
		AD8DD15F2643E61900086CBF /* GatewayMessageListener.h in Headers */ = {isa = PBXBuildFile; fileRef = F86314F9250A039600C948B9 /* GatewayMessageListener.h */; settings = {ATTRIBUTES = (Public, ); }; };
		AD8DD1602643E61900086CBF /* MessageListener.h in Headers */ = {isa = PBXBuildFile; fileRef = F8631543250A039600C948B9 /* MessageListener.h */; settings = {ATTRIBUTES = (Public, ); }; };
		AD8DD1612643E61900086CBF /* WifiDeviceBaseInfo.h in Headers */ = {isa = PBXBuildFile; fileRef = F86314FB250A039600C948B9 /* WifiDeviceBaseInfo.h */; settings = {ATTRIBUTES = (Public, ); }; };
		AD8DD1622643E61900086CBF /* WifiDeviceHelper.h in Headers */ = {isa = PBXBuildFile; fileRef = F8631540250A039600C948B9 /* WifiDeviceHelper.h */; settings = {ATTRIBUTES = (Public, ); }; };
		AD8DD1632643E61900086CBF /* WifiDeviceNetworkType.h in Headers */ = {isa = PBXBuildFile; fileRef = F86314F7250A039600C948B9 /* WifiDeviceNetworkType.h */; settings = {ATTRIBUTES = (Public, ); }; };
		AD8DD1642643E61900086CBF /* WifiDeviceToolkit.h in Headers */ = {isa = PBXBuildFile; fileRef = F86314FA250A039600C948B9 /* WifiDeviceToolkit.h */; settings = {ATTRIBUTES = (Public, ); }; };
		AD8DD1652643E61900086CBF /* WifiDeviceToolkitImpl.h in Headers */ = {isa = PBXBuildFile; fileRef = F8631544250A039600C948B9 /* WifiDeviceToolkitImpl.h */; settings = {ATTRIBUTES = (Public, ); }; };
		AD8DD1662643E61900086CBF /* WifiDeviceType.h in Headers */ = {isa = PBXBuildFile; fileRef = F8631541250A039600C948B9 /* WifiDeviceType.h */; settings = {ATTRIBUTES = (Public, ); }; };
		AD8DD1672643E61900086CBF /* UpDeviceDataSourceWrapper.h in Headers */ = {isa = PBXBuildFile; fileRef = F86314F0250A039600C948B9 /* UpDeviceDataSourceWrapper.h */; settings = {ATTRIBUTES = (Public, ); }; };
		AD8DD1682643E67900086CBF /* DeviceManagerDetectListener.m in Sources */ = {isa = PBXBuildFile; fileRef = F86314CD250A039500C948B9 /* DeviceManagerDetectListener.m */; };
		AD8DD1692643E67900086CBF /* DeviceManagerToolkitListener.m in Sources */ = {isa = PBXBuildFile; fileRef = F86314CB250A039500C948B9 /* DeviceManagerToolkitListener.m */; };
		AD8DD16A2643E67900086CBF /* UpDeviceDaemon.m in Sources */ = {isa = PBXBuildFile; fileRef = F86314CC250A039500C948B9 /* UpDeviceDaemon.m */; };
		AD8DD16B2643E67900086CBF /* UpDeviceInjection.m in Sources */ = {isa = PBXBuildFile; fileRef = F86314EC250A039600C948B9 /* UpDeviceInjection.m */; };
		AD8DD16C2643E67900086CBF /* UpDeviceManager.m in Sources */ = {isa = PBXBuildFile; fileRef = F86314ED250A039600C948B9 /* UpDeviceManager.m */; };
		AD8DD16D2643E67900086CBF /* UpDeviceNetworkType.m in Sources */ = {isa = PBXBuildFile; fileRef = F86314DE250A039600C948B9 /* UpDeviceNetworkType.m */; };
		AD8DD16E2643E67900086CBF /* UpDeviceType.m in Sources */ = {isa = PBXBuildFile; fileRef = F86314DF250A039600C948B9 /* UpDeviceType.m */; };
		AD8DD16F2643E67900086CBF /* (null) in Sources */ = {isa = PBXBuildFile; };
		AD8DD1702643E67900086CBF /* UpCompatCallbackWrapper.m in Sources */ = {isa = PBXBuildFile; fileRef = F86314E6250A039600C948B9 /* UpCompatCallbackWrapper.m */; };
		AD8DD1712643E67900086CBF /* UpCompatCommand.m in Sources */ = {isa = PBXBuildFile; fileRef = F86314D3250A039500C948B9 /* UpCompatCommand.m */; };
		AD8DD1722643E67900086CBF /* UpCompatDevice.m in Sources */ = {isa = PBXBuildFile; fileRef = F86314CF250A039500C948B9 /* UpCompatDevice.m */; };
		AD8DD1732643E67900086CBF /* UpCompatDeviceStore.m in Sources */ = {isa = PBXBuildFile; fileRef = F86314D8250A039600C948B9 /* UpCompatDeviceStore.m */; };
		AD8DD1742643E67900086CBF /* UpDeviceDataCache.m in Sources */ = {isa = PBXBuildFile; fileRef = F86314DC250A039600C948B9 /* UpDeviceDataCache.m */; };
		AD8DD1772643E67900086CBF /* DummyDeviceBroker.m in Sources */ = {isa = PBXBuildFile; fileRef = F863154A250A039700C948B9 /* DummyDeviceBroker.m */; };
		AD8DD1782643E67900086CBF /* DummyDeviceToolkit.m in Sources */ = {isa = PBXBuildFile; fileRef = F8631549250A039700C948B9 /* DummyDeviceToolkit.m */; };
		AD8DD1792643E67900086CBF /* UpEngineDataSource.m in Sources */ = {isa = PBXBuildFile; fileRef = F8631558250A039700C948B9 /* UpEngineDataSource.m */; };
		AD8DD17A2643E67900086CBF /* UpEngineDevice.m in Sources */ = {isa = PBXBuildFile; fileRef = F8631557250A039700C948B9 /* UpEngineDevice.m */; };
		AD8DD17B2643E67900086CBF /* UpEngineDevice+ConfigSource.m in Sources */ = {isa = PBXBuildFile; fileRef = F8631552250A039700C948B9 /* UpEngineDevice+ConfigSource.m */; };
		AD8DD17C2643E67900086CBF /* UpEngineDevice+EngineDelegate.m in Sources */ = {isa = PBXBuildFile; fileRef = F8631550250A039700C948B9 /* UpEngineDevice+EngineDelegate.m */; };
		AD8DD17D2643E67900086CBF /* UpEngineDeviceFactory.m in Sources */ = {isa = PBXBuildFile; fileRef = F863155B250A039700C948B9 /* UpEngineDeviceFactory.m */; };
		AD8DD17E2643E67900086CBF /* UpEngineException.m in Sources */ = {isa = PBXBuildFile; fileRef = F8631551250A039700C948B9 /* UpEngineException.m */; };
		AD8DD17F2643E67900086CBF /* UpEngineReporter.m in Sources */ = {isa = PBXBuildFile; fileRef = F8631553250A039700C948B9 /* UpEngineReporter.m */; };
		AD8DD1802643E67900086CBF /* AttachDevice.m in Sources */ = {isa = PBXBuildFile; fileRef = F863153F250A039600C948B9 /* AttachDevice.m */; };
		AD8DD1812643E67900086CBF /* AttachResource.m in Sources */ = {isa = PBXBuildFile; fileRef = F8631523250A039600C948B9 /* AttachResource.m */; };
		AD8DD1822643E67900086CBF /* AttachToolkit.m in Sources */ = {isa = PBXBuildFile; fileRef = F863151C250A039600C948B9 /* AttachToolkit.m */; };
		AD8DD1832643E67900086CBF /* CancelFetchBLEHistoryData.m in Sources */ = {isa = PBXBuildFile; fileRef = F863150F250A039600C948B9 /* CancelFetchBLEHistoryData.m */; };
		AD8DD1842643E67900086CBF /* CheckBoardFOTAInfo.m in Sources */ = {isa = PBXBuildFile; fileRef = F863153C250A039600C948B9 /* CheckBoardFOTAInfo.m */; };
		AD8DD1852643E67900086CBF /* ConnectRemoteDevices.m in Sources */ = {isa = PBXBuildFile; fileRef = F863151D250A039600C948B9 /* ConnectRemoteDevices.m */; };
		AD8DD1862643E67900086CBF /* DetachDevice.m in Sources */ = {isa = PBXBuildFile; fileRef = F8631539250A039600C948B9 /* DetachDevice.m */; };
		AD8DD1872643E67900086CBF /* DetachResource.m in Sources */ = {isa = PBXBuildFile; fileRef = F863153A250A039600C948B9 /* DetachResource.m */; };
		AD8DD1882643E67900086CBF /* DetachToolkit.m in Sources */ = {isa = PBXBuildFile; fileRef = F863150A250A039600C948B9 /* DetachToolkit.m */; };
		AD8DD1892643E67900086CBF /* DisconnectRemoteDevices.m in Sources */ = {isa = PBXBuildFile; fileRef = F8631514250A039600C948B9 /* DisconnectRemoteDevices.m */; };
		AD8DD18A2643E67900086CBF /* ExecuteCommand.m in Sources */ = {isa = PBXBuildFile; fileRef = F8631528250A039600C948B9 /* ExecuteCommand.m */; };
		AD8DD18B2643E67900086CBF /* FetchBLEHistoryData.m in Sources */ = {isa = PBXBuildFile; fileRef = F863151E250A039600C948B9 /* FetchBLEHistoryData.m */; };
		AD8DD18C2643E67900086CBF /* FetchBoardFOTAStatus.m in Sources */ = {isa = PBXBuildFile; fileRef = F863153D250A039600C948B9 /* FetchBoardFOTAStatus.m */; };
		AD8DD18D2643E67900086CBF /* GetBindInfo.m in Sources */ = {isa = PBXBuildFile; fileRef = F86314FF250A039600C948B9 /* GetBindInfo.m */; };
		AD8DD18E2643E67900086CBF /* GetDeviceAttribute.m in Sources */ = {isa = PBXBuildFile; fileRef = F8631504250A039600C948B9 /* GetDeviceAttribute.m */; };
		AD8DD18F2643E67900086CBF /* GetDeviceAttributes.m in Sources */ = {isa = PBXBuildFile; fileRef = F8631506250A039600C948B9 /* GetDeviceAttributes.m */; };
		AD8DD1902643E67A00086CBF /* GetDeviceCautions.m in Sources */ = {isa = PBXBuildFile; fileRef = F8631507250A039600C948B9 /* GetDeviceCautions.m */; };
		AD8DD1912643E67A00086CBF /* GetDeviceConnection.m in Sources */ = {isa = PBXBuildFile; fileRef = F863150D250A039600C948B9 /* GetDeviceConnection.m */; };
		AD8DD1922643E67A00086CBF /* GetDeviceInfo.m in Sources */ = {isa = PBXBuildFile; fileRef = F8631512250A039600C948B9 /* GetDeviceInfo.m */; };
		AD8DD1932643E67A00086CBF /* GetDeviceList.m in Sources */ = {isa = PBXBuildFile; fileRef = F8631503250A039600C948B9 /* GetDeviceList.m */; };
		AD8DD1942643E67A00086CBF /* GetNetworkQuality.m in Sources */ = {isa = PBXBuildFile; fileRef = F863152E250A039600C948B9 /* GetNetworkQuality.m */; };
		AD8DD1952643E67A00086CBF /* GetSubDevList.m in Sources */ = {isa = PBXBuildFile; fileRef = F8631501250A039600C948B9 /* GetSubDevList.m */; };
		AD8DD1962643E67A00086CBF /* GetSubDevListBySubDevice.m in Sources */ = {isa = PBXBuildFile; fileRef = F863153B250A039600C948B9 /* GetSubDevListBySubDevice.m */; };
		AD8DD1972643E67A00086CBF /* InFocus.m in Sources */ = {isa = PBXBuildFile; fileRef = F863152D250A039600C948B9 /* InFocus.m */; };
		AD8DD1982643E67A00086CBF /* IsBound.m in Sources */ = {isa = PBXBuildFile; fileRef = F8631502250A039600C948B9 /* IsBound.m */; };
		AD8DD19A2643E67A00086CBF /* IsModuleNeedOta.m in Sources */ = {isa = PBXBuildFile; fileRef = F8631525250A039600C948B9 /* IsModuleNeedOta.m */; };
		AD8DD19B2643E67A00086CBF /* OutFocus.m in Sources */ = {isa = PBXBuildFile; fileRef = F8631526250A039600C948B9 /* OutFocus.m */; };
		AD8DD19C2643E67A00086CBF /* RefresUsdkDeviceList.m in Sources */ = {isa = PBXBuildFile; fileRef = F863150C250A039600C948B9 /* RefresUsdkDeviceList.m */; };
		AD8DD19D2643E67A00086CBF /* SmartLinkSoftwareVersion.m in Sources */ = {isa = PBXBuildFile; fileRef = F863152B250A039600C948B9 /* SmartLinkSoftwareVersion.m */; };
		AD8DD19E2643E67A00086CBF /* StartBoardFOTA.m in Sources */ = {isa = PBXBuildFile; fileRef = F8631511250A039600C948B9 /* StartBoardFOTA.m */; };
		AD8DD19F2643E67A00086CBF /* StartModuleUpdate.m in Sources */ = {isa = PBXBuildFile; fileRef = F863150B250A039600C948B9 /* StartModuleUpdate.m */; };
		AD8DD1A02643E67A00086CBF /* WifiDeviceAction.m in Sources */ = {isa = PBXBuildFile; fileRef = F8631516250A039600C948B9 /* WifiDeviceAction.m */; };
		AD8DD1A12643E67A00086CBF /* WifiToolkitAction.m in Sources */ = {isa = PBXBuildFile; fileRef = F863150E250A039600C948B9 /* WifiToolkitAction.m */; };
		AD8DD1A22643E67A00086CBF /* WifiDeviceHelper.m in Sources */ = {isa = PBXBuildFile; fileRef = F86314F6250A039600C948B9 /* WifiDeviceHelper.m */; };
		AD8DD1A32643E67A00086CBF /* WifiDeviceToolkitImpl.m in Sources */ = {isa = PBXBuildFile; fileRef = F86314FC250A039600C948B9 /* WifiDeviceToolkitImpl.m */; };
		AD8DD1A42643E67A00086CBF /* WifiDeviceType.m in Sources */ = {isa = PBXBuildFile; fileRef = F86314F5250A039600C948B9 /* WifiDeviceType.m */; };
		AD8DD1A52643E67A00086CBF /* UpDeviceDataSourceWrapper.m in Sources */ = {isa = PBXBuildFile; fileRef = F86314F1250A039600C948B9 /* UpDeviceDataSourceWrapper.m */; };
		AD8DD1A62643E68D00086CBF /* libUPDeviceUtil.a in Frameworks */ = {isa = PBXBuildFile; fileRef = AD8DD0FF2643E48600086CBF /* libUPDeviceUtil.a */; };
		AD8DD1A72643E69500086CBF /* libUPDevice.a in Frameworks */ = {isa = PBXBuildFile; fileRef = AD8DD1122643E5A300086CBF /* libUPDevice.a */; };
		AD8DD1A82643E69B00086CBF /* libUPDevice.a in Frameworks */ = {isa = PBXBuildFile; fileRef = AD8DD1122643E5A300086CBF /* libUPDevice.a */; };
		AD98A5A6265B773300EDAEA3 /* libupdevice_api.a in Frameworks */ = {isa = PBXBuildFile; fileRef = AD98A500265B74C700EDAEA3 /* libupdevice_api.a */; };
		AD98ABDD265B8B7B00EDAEA3 /* DefaultNotification.h in Headers */ = {isa = PBXBuildFile; fileRef = AD98AB64265B8B7A00EDAEA3 /* DefaultNotification.h */; settings = {ATTRIBUTES = (Public, ); }; };
		AD98ABDE265B8B7B00EDAEA3 /* DefaultBrokerHolder.m in Sources */ = {isa = PBXBuildFile; fileRef = AD98AB65265B8B7A00EDAEA3 /* DefaultBrokerHolder.m */; };
		AD98ABDF265B8B7B00EDAEA3 /* DefaultReportListener.h in Headers */ = {isa = PBXBuildFile; fileRef = AD98AB66265B8B7A00EDAEA3 /* DefaultReportListener.h */; settings = {ATTRIBUTES = (Public, ); }; };
		AD98ABE0265B8B7B00EDAEA3 /* DefaultDeviceBroker.m in Sources */ = {isa = PBXBuildFile; fileRef = AD98AB67265B8B7A00EDAEA3 /* DefaultDeviceBroker.m */; };
		AD98ABE1265B8B7B00EDAEA3 /* DefaultToolkitListener.m in Sources */ = {isa = PBXBuildFile; fileRef = AD98AB68265B8B7A00EDAEA3 /* DefaultToolkitListener.m */; };
		AD98ABE2265B8B7B00EDAEA3 /* DefaultDetectListener.h in Headers */ = {isa = PBXBuildFile; fileRef = AD98AB69265B8B7A00EDAEA3 /* DefaultDetectListener.h */; settings = {ATTRIBUTES = (Public, ); }; };
		AD98ABE3265B8B7B00EDAEA3 /* DefaultDeviceBroker.h in Headers */ = {isa = PBXBuildFile; fileRef = AD98AB6A265B8B7A00EDAEA3 /* DefaultDeviceBroker.h */; settings = {ATTRIBUTES = (Public, ); }; };
		AD98ABE4265B8B7B00EDAEA3 /* DefaultReportListener.m in Sources */ = {isa = PBXBuildFile; fileRef = AD98AB6B265B8B7A00EDAEA3 /* DefaultReportListener.m */; };
		AD98ABE5265B8B7B00EDAEA3 /* DefaultNotification.m in Sources */ = {isa = PBXBuildFile; fileRef = AD98AB6C265B8B7A00EDAEA3 /* DefaultNotification.m */; };
		AD98ABE6265B8B7B00EDAEA3 /* DefaultBrokerHolder.h in Headers */ = {isa = PBXBuildFile; fileRef = AD98AB6D265B8B7A00EDAEA3 /* DefaultBrokerHolder.h */; settings = {ATTRIBUTES = (Public, ); }; };
		AD98ABE7265B8B7B00EDAEA3 /* DefaultToolkitListener.h in Headers */ = {isa = PBXBuildFile; fileRef = AD98AB6E265B8B7A00EDAEA3 /* DefaultToolkitListener.h */; settings = {ATTRIBUTES = (Public, ); }; };
		AD98ABE8265B8B7B00EDAEA3 /* DefaultDetectListener.m in Sources */ = {isa = PBXBuildFile; fileRef = AD98AB6F265B8B7A00EDAEA3 /* DefaultDetectListener.m */; };
		AD98ABE9265B8B7B00EDAEA3 /* UpDeviceBroker.h in Headers */ = {isa = PBXBuildFile; fileRef = AD98AB70265B8B7A00EDAEA3 /* UpDeviceBroker.h */; settings = {ATTRIBUTES = (Public, ); }; };
		AD98ABEA265B8B7B00EDAEA3 /* UpDeviceBrokerHolder.h in Headers */ = {isa = PBXBuildFile; fileRef = AD98AB71265B8B7A00EDAEA3 /* UpDeviceBrokerHolder.h */; settings = {ATTRIBUTES = (Public, ); }; };
		AD98ABEB265B8B7B00EDAEA3 /* UpDeviceFilter.h in Headers */ = {isa = PBXBuildFile; fileRef = AD98AB72265B8B7A00EDAEA3 /* UpDeviceFilter.h */; settings = {ATTRIBUTES = (Public, ); }; };
		AD98ABEC265B8B7B00EDAEA3 /* UpDeviceFactory.h in Headers */ = {isa = PBXBuildFile; fileRef = AD98AB73265B8B7A00EDAEA3 /* UpDeviceFactory.h */; settings = {ATTRIBUTES = (Public, ); }; };
		AD98ABED265B8B7C00EDAEA3 /* UpDeviceCreator.h in Headers */ = {isa = PBXBuildFile; fileRef = AD98AB74265B8B7A00EDAEA3 /* UpDeviceCreator.h */; settings = {ATTRIBUTES = (Public, ); }; };
		AD98ABEE265B8B7C00EDAEA3 /* UpDeviceFilters.h in Headers */ = {isa = PBXBuildFile; fileRef = AD98AB75265B8B7A00EDAEA3 /* UpDeviceFilters.h */; settings = {ATTRIBUTES = (Public, ); }; };
		AD98ABF2265B8B7C00EDAEA3 /* UpDeviceReceiver.h in Headers */ = {isa = PBXBuildFile; fileRef = AD98AB7C265B8B7A00EDAEA3 /* UpDeviceReceiver.h */; settings = {ATTRIBUTES = (Public, ); }; };
		AD98ABF3265B8B7C00EDAEA3 /* UPDeviceNetType.h in Headers */ = {isa = PBXBuildFile; fileRef = AD98AB7D265B8B7A00EDAEA3 /* UPDeviceNetType.h */; settings = {ATTRIBUTES = (Public, ); }; };
		AD98ABF4265B8B7C00EDAEA3 /* UpCommonDevice.h in Headers */ = {isa = PBXBuildFile; fileRef = AD98AB7E265B8B7A00EDAEA3 /* UpCommonDevice.h */; settings = {ATTRIBUTES = (Public, ); }; };
		AD98ABF5265B8B7C00EDAEA3 /* UpDeviceBLE.h in Headers */ = {isa = PBXBuildFile; fileRef = AD98AB7F265B8B7A00EDAEA3 /* UpDeviceBLE.h */; settings = {ATTRIBUTES = (Public, ); }; };
		AD98ABF6265B8B7C00EDAEA3 /* UpDeviceBase.h in Headers */ = {isa = PBXBuildFile; fileRef = AD98AB80265B8B7A00EDAEA3 /* UpDeviceBase.h */; settings = {ATTRIBUTES = (Public, ); }; };
		AD98ABF7265B8B7C00EDAEA3 /* UpDeviceReporter.m in Sources */ = {isa = PBXBuildFile; fileRef = AD98AB81265B8B7A00EDAEA3 /* UpDeviceReporter.m */; };
		AD98ABF8265B8B7C00EDAEA3 /* UpDeviceSub.h in Headers */ = {isa = PBXBuildFile; fileRef = AD98AB82265B8B7A00EDAEA3 /* UpDeviceSub.h */; settings = {ATTRIBUTES = (Public, ); }; };
		AD98ABF9265B8B7C00EDAEA3 /* UpExtendDevice.h in Headers */ = {isa = PBXBuildFile; fileRef = AD98AB83265B8B7A00EDAEA3 /* UpExtendDevice.h */; settings = {ATTRIBUTES = (Public, ); }; };
		AD98ABFA265B8B7C00EDAEA3 /* UpDeviceCache.h in Headers */ = {isa = PBXBuildFile; fileRef = AD98AB84265B8B7A00EDAEA3 /* UpDeviceCache.h */; settings = {ATTRIBUTES = (Public, ); }; };
		AD98ABFB265B8B7C00EDAEA3 /* UpCommonDevice.m in Sources */ = {isa = PBXBuildFile; fileRef = AD98AB85265B8B7A00EDAEA3 /* UpCommonDevice.m */; };
		AD98ABFC265B8B7C00EDAEA3 /* UpDeviceConfigState.h in Headers */ = {isa = PBXBuildFile; fileRef = AD98AB86265B8B7A00EDAEA3 /* UpDeviceConfigState.h */; settings = {ATTRIBUTES = (Public, ); }; };
		AD98ABFD265B8B7C00EDAEA3 /* UpDeviceListener.h in Headers */ = {isa = PBXBuildFile; fileRef = AD98AB87265B8B7A00EDAEA3 /* UpDeviceListener.h */; settings = {ATTRIBUTES = (Public, ); }; };
		AD98ABFE265B8B7C00EDAEA3 /* UpDevice.h in Headers */ = {isa = PBXBuildFile; fileRef = AD98AB88265B8B7A00EDAEA3 /* UpDevice.h */; settings = {ATTRIBUTES = (Public, ); }; };
		AD98ABFF265B8B7C00EDAEA3 /* UpDeviceStore.h in Headers */ = {isa = PBXBuildFile; fileRef = AD98AB89265B8B7A00EDAEA3 /* UpDeviceStore.h */; settings = {ATTRIBUTES = (Public, ); }; };
		AD98AC00265B8B7D00EDAEA3 /* UpDeviceFocus.h in Headers */ = {isa = PBXBuildFile; fileRef = AD98AB8A265B8B7A00EDAEA3 /* UpDeviceFocus.h */; settings = {ATTRIBUTES = (Public, ); }; };
		AD98AC01265B8B7D00EDAEA3 /* UpDeviceNetWorkQuality.h in Headers */ = {isa = PBXBuildFile; fileRef = AD98AB8B265B8B7A00EDAEA3 /* UpDeviceNetWorkQuality.h */; settings = {ATTRIBUTES = (Public, ); }; };
		AD98AC02265B8B7D00EDAEA3 /* UpDeviceReporter.h in Headers */ = {isa = PBXBuildFile; fileRef = AD98AB8C265B8B7A00EDAEA3 /* UpDeviceReporter.h */; settings = {ATTRIBUTES = (Public, ); }; };
		AD98AC03265B8B7D00EDAEA3 /* UpDeviceBase.m in Sources */ = {isa = PBXBuildFile; fileRef = AD98AB8D265B8B7A00EDAEA3 /* UpDeviceBase.m */; };
		AD98AC04265B8B7D00EDAEA3 /* UpDeviceFOTA.h in Headers */ = {isa = PBXBuildFile; fileRef = AD98AB8E265B8B7A00EDAEA3 /* UpDeviceFOTA.h */; settings = {ATTRIBUTES = (Public, ); }; };
		AD98AC06265B8B7D00EDAEA3 /* UpDeviceState.h in Headers */ = {isa = PBXBuildFile; fileRef = AD98AB90265B8B7A00EDAEA3 /* UpDeviceState.h */; settings = {ATTRIBUTES = (Public, ); }; };
		AD98AC07265B8B7D00EDAEA3 /* UpDeviceCache.m in Sources */ = {isa = PBXBuildFile; fileRef = AD98AB91265B8B7A00EDAEA3 /* UpDeviceCache.m */; };
		AD98AC08265B8B7D00EDAEA3 /* UpCompositeProcessor.h in Headers */ = {isa = PBXBuildFile; fileRef = AD98AB93265B8B7A00EDAEA3 /* UpCompositeProcessor.h */; settings = {ATTRIBUTES = (Public, ); }; };
		AD98AC09265B8B7D00EDAEA3 /* UpCompositeProcessor.m in Sources */ = {isa = PBXBuildFile; fileRef = AD98AB94265B8B7A00EDAEA3 /* UpCompositeProcessor.m */; };
		AD98AC0A265B8B7D00EDAEA3 /* UpDeviceProcessor.h in Headers */ = {isa = PBXBuildFile; fileRef = AD98AB95265B8B7A00EDAEA3 /* UpDeviceProcessor.h */; settings = {ATTRIBUTES = (Public, ); }; };
		AD98AC0B265B8B7D00EDAEA3 /* UpDeviceFilters.m in Sources */ = {isa = PBXBuildFile; fileRef = AD98AB96265B8B7A00EDAEA3 /* UpDeviceFilters.m */; };
		AD98AC0C265B8B7D00EDAEA3 /* UpDeviceCreator.m in Sources */ = {isa = PBXBuildFile; fileRef = AD98AB97265B8B7A00EDAEA3 /* UpDeviceCreator.m */; };
		AD98AC0D265B8B7D00EDAEA3 /* UpDeviceCenter.h in Headers */ = {isa = PBXBuildFile; fileRef = AD98AB98265B8B7A00EDAEA3 /* UpDeviceCenter.h */; settings = {ATTRIBUTES = (Public, ); }; };
		AD98AC0E265B8B7D00EDAEA3 /* updevice_api.h in Headers */ = {isa = PBXBuildFile; fileRef = AD98AB99265B8B7A00EDAEA3 /* updevice_api.h */; settings = {ATTRIBUTES = (Public, ); }; };
		AD98AC0F265B8B7D00EDAEA3 /* DeviceBaseInfo.m in Sources */ = {isa = PBXBuildFile; fileRef = AD98AB9D265B8B7A00EDAEA3 /* DeviceBaseInfo.m */; };
		AD98AC10265B8B7D00EDAEA3 /* DeviceOTAStatusInfo.h in Headers */ = {isa = PBXBuildFile; fileRef = AD98AB9E265B8B7A00EDAEA3 /* DeviceOTAStatusInfo.h */; settings = {ATTRIBUTES = (Public, ); }; };
		AD98AC11265B8B7D00EDAEA3 /* DeviceNetWorkQualityInfo.m in Sources */ = {isa = PBXBuildFile; fileRef = AD98AB9F265B8B7A00EDAEA3 /* DeviceNetWorkQualityInfo.m */; };
		AD98AC12265B8B7D00EDAEA3 /* DeviceAttribute.m in Sources */ = {isa = PBXBuildFile; fileRef = AD98ABA0265B8B7A00EDAEA3 /* DeviceAttribute.m */; };
		AD98AC13265B8B7D00EDAEA3 /* DeviceFOTAInfo.h in Headers */ = {isa = PBXBuildFile; fileRef = AD98ABA1265B8B7A00EDAEA3 /* DeviceFOTAInfo.h */; settings = {ATTRIBUTES = (Public, ); }; };
		AD98AC14265B8B7D00EDAEA3 /* DeviceBLEHistoryInfo.h in Headers */ = {isa = PBXBuildFile; fileRef = AD98ABA2265B8B7A00EDAEA3 /* DeviceBLEHistoryInfo.h */; settings = {ATTRIBUTES = (Public, ); }; };
		AD98AC15265B8B7E00EDAEA3 /* DeviceFOTAStatusInfo.m in Sources */ = {isa = PBXBuildFile; fileRef = AD98ABA3265B8B7A00EDAEA3 /* DeviceFOTAStatusInfo.m */; };
		AD98AC16265B8B7E00EDAEA3 /* DeviceCaution.h in Headers */ = {isa = PBXBuildFile; fileRef = AD98ABA4265B8B7A00EDAEA3 /* DeviceCaution.h */; settings = {ATTRIBUTES = (Public, ); }; };
		AD98AC17265B8B7E00EDAEA3 /* DeviceCommand.m in Sources */ = {isa = PBXBuildFile; fileRef = AD98ABA5265B8B7A00EDAEA3 /* DeviceCommand.m */; };
		AD98AC18265B8B7E00EDAEA3 /* DeviceExtras.h in Headers */ = {isa = PBXBuildFile; fileRef = AD98ABA6265B8B7A00EDAEA3 /* DeviceExtras.h */; settings = {ATTRIBUTES = (Public, ); }; };
		AD98AC19265B8B7E00EDAEA3 /* DeviceInfo.h in Headers */ = {isa = PBXBuildFile; fileRef = AD98ABA7265B8B7A00EDAEA3 /* DeviceInfo.h */; settings = {ATTRIBUTES = (Public, ); }; };
		AD98AC1A265B8B7E00EDAEA3 /* DeviceBaseInfo.h in Headers */ = {isa = PBXBuildFile; fileRef = AD98ABA8265B8B7A00EDAEA3 /* DeviceBaseInfo.h */; settings = {ATTRIBUTES = (Public, ); }; };
		AD98AC1B265B8B7E00EDAEA3 /* DeviceFOTAInfo.m in Sources */ = {isa = PBXBuildFile; fileRef = AD98ABA9265B8B7A00EDAEA3 /* DeviceFOTAInfo.m */; };
		AD98AC1C265B8B7E00EDAEA3 /* DeviceNetWorkQualityInfo.h in Headers */ = {isa = PBXBuildFile; fileRef = AD98ABAA265B8B7A00EDAEA3 /* DeviceNetWorkQualityInfo.h */; settings = {ATTRIBUTES = (Public, ); }; };
		AD98AC1D265B8B7E00EDAEA3 /* DeviceAttribute.h in Headers */ = {isa = PBXBuildFile; fileRef = AD98ABAB265B8B7A00EDAEA3 /* DeviceAttribute.h */; settings = {ATTRIBUTES = (Public, ); }; };
		AD98AC1E265B8B7E00EDAEA3 /* DeviceOTAStatusInfo.m in Sources */ = {isa = PBXBuildFile; fileRef = AD98ABAC265B8B7A00EDAEA3 /* DeviceOTAStatusInfo.m */; };
		AD98AC1F265B8B7E00EDAEA3 /* DeviceFOTAStatusInfo.h in Headers */ = {isa = PBXBuildFile; fileRef = AD98ABAD265B8B7A00EDAEA3 /* DeviceFOTAStatusInfo.h */; settings = {ATTRIBUTES = (Public, ); }; };
		AD98AC20265B8B7E00EDAEA3 /* DeviceBLEHistoryInfo.m in Sources */ = {isa = PBXBuildFile; fileRef = AD98ABAE265B8B7A00EDAEA3 /* DeviceBLEHistoryInfo.m */; };
		AD98AC21265B8B7E00EDAEA3 /* DeviceInfo.m in Sources */ = {isa = PBXBuildFile; fileRef = AD98ABAF265B8B7A00EDAEA3 /* DeviceInfo.m */; };
		AD98AC22265B8B7E00EDAEA3 /* DeviceExtras.m in Sources */ = {isa = PBXBuildFile; fileRef = AD98ABB0265B8B7A00EDAEA3 /* DeviceExtras.m */; };
		AD98AC23265B8B7E00EDAEA3 /* DeviceCaution.m in Sources */ = {isa = PBXBuildFile; fileRef = AD98ABB1265B8B7A00EDAEA3 /* DeviceCaution.m */; };
		AD98AC24265B8B7E00EDAEA3 /* DeviceCommand.h in Headers */ = {isa = PBXBuildFile; fileRef = AD98ABB2265B8B7A00EDAEA3 /* DeviceCommand.h */; settings = {ATTRIBUTES = (Public, ); }; };
		AD98AC25265B8B7E00EDAEA3 /* UpDeviceAttribute.h in Headers */ = {isa = PBXBuildFile; fileRef = AD98ABB3265B8B7A00EDAEA3 /* UpDeviceAttribute.h */; settings = {ATTRIBUTES = (Public, ); }; };
		AD98AC26265B8B7E00EDAEA3 /* UpDeviceFOTAInfo.h in Headers */ = {isa = PBXBuildFile; fileRef = AD98ABB4265B8B7B00EDAEA3 /* UpDeviceFOTAInfo.h */; settings = {ATTRIBUTES = (Public, ); }; };
		AD98AC27265B8B7E00EDAEA3 /* UpDeviceOTAStatusInfo.h in Headers */ = {isa = PBXBuildFile; fileRef = AD98ABB5265B8B7B00EDAEA3 /* UpDeviceOTAStatusInfo.h */; settings = {ATTRIBUTES = (Public, ); }; };
		AD98AC28265B8B7E00EDAEA3 /* UpDeviceFOTAStatusInfo.h in Headers */ = {isa = PBXBuildFile; fileRef = AD98ABB6265B8B7B00EDAEA3 /* UpDeviceFOTAStatusInfo.h */; settings = {ATTRIBUTES = (Public, ); }; };
		AD98AC29265B8B7E00EDAEA3 /* UpDeviceCommand.h in Headers */ = {isa = PBXBuildFile; fileRef = AD98ABB7265B8B7B00EDAEA3 /* UpDeviceCommand.h */; settings = {ATTRIBUTES = (Public, ); }; };
		AD98AC2A265B8B7E00EDAEA3 /* UpDeviceInfo.h in Headers */ = {isa = PBXBuildFile; fileRef = AD98ABB8265B8B7B00EDAEA3 /* UpDeviceInfo.h */; settings = {ATTRIBUTES = (Public, ); }; };
		AD98AC2B265B8B7F00EDAEA3 /* UpDeviceExtras.h in Headers */ = {isa = PBXBuildFile; fileRef = AD98ABB9265B8B7B00EDAEA3 /* UpDeviceExtras.h */; settings = {ATTRIBUTES = (Public, ); }; };
		AD98AC2C265B8B7F00EDAEA3 /* UpDeviceBaseInfo.h in Headers */ = {isa = PBXBuildFile; fileRef = AD98ABBA265B8B7B00EDAEA3 /* UpDeviceBaseInfo.h */; settings = {ATTRIBUTES = (Public, ); }; };
		AD98AC2D265B8B7F00EDAEA3 /* UpDeviceConnection.h in Headers */ = {isa = PBXBuildFile; fileRef = AD98ABBB265B8B7B00EDAEA3 /* UpDeviceConnection.h */; settings = {ATTRIBUTES = (Public, ); }; };
		AD98AC2E265B8B7F00EDAEA3 /* UpDeviceBLEHistoryInfo.h in Headers */ = {isa = PBXBuildFile; fileRef = AD98ABBC265B8B7B00EDAEA3 /* UpDeviceBLEHistoryInfo.h */; settings = {ATTRIBUTES = (Public, ); }; };
		AD98AC2F265B8B7F00EDAEA3 /* UpDeviceNetWorkQualityInfo.h in Headers */ = {isa = PBXBuildFile; fileRef = AD98ABBD265B8B7B00EDAEA3 /* UpDeviceNetWorkQualityInfo.h */; settings = {ATTRIBUTES = (Public, ); }; };
		AD98AC30265B8B7F00EDAEA3 /* UpDeviceCaution.h in Headers */ = {isa = PBXBuildFile; fileRef = AD98ABBE265B8B7B00EDAEA3 /* UpDeviceCaution.h */; settings = {ATTRIBUTES = (Public, ); }; };
		AD98AC31265B8B7F00EDAEA3 /* UpDeviceSetMap.h in Headers */ = {isa = PBXBuildFile; fileRef = AD98ABC0265B8B7B00EDAEA3 /* UpDeviceSetMap.h */; settings = {ATTRIBUTES = (Public, ); }; };
		AD98AC32265B8B7F00EDAEA3 /* UpDeviceAction.m in Sources */ = {isa = PBXBuildFile; fileRef = AD98ABC1265B8B7B00EDAEA3 /* UpDeviceAction.m */; };
		AD98AC33265B8B7F00EDAEA3 /* UpDeviceResult.m in Sources */ = {isa = PBXBuildFile; fileRef = AD98ABC2265B8B7B00EDAEA3 /* UpDeviceResult.m */; };
		AD98AC34265B8B7F00EDAEA3 /* UpDeviceCarrier.m in Sources */ = {isa = PBXBuildFile; fileRef = AD98ABC3265B8B7B00EDAEA3 /* UpDeviceCarrier.m */; };
		AD98AC35265B8B7F00EDAEA3 /* UpDeviceExecutable.h in Headers */ = {isa = PBXBuildFile; fileRef = AD98ABC4265B8B7B00EDAEA3 /* UpDeviceExecutable.h */; settings = {ATTRIBUTES = (Public, ); }; };
		AD98AC36265B8B7F00EDAEA3 /* UpDeviceHelper.m in Sources */ = {isa = PBXBuildFile; fileRef = AD98ABC5265B8B7B00EDAEA3 /* UpDeviceHelper.m */; };
		AD98AC37265B8B7F00EDAEA3 /* UpStringResult.h in Headers */ = {isa = PBXBuildFile; fileRef = AD98ABC6265B8B7B00EDAEA3 /* UpStringResult.h */; settings = {ATTRIBUTES = (Public, ); }; };
		AD98AC38265B8B7F00EDAEA3 /* UpDeviceCarrier.h in Headers */ = {isa = PBXBuildFile; fileRef = AD98ABC7265B8B7B00EDAEA3 /* UpDeviceCarrier.h */; settings = {ATTRIBUTES = (Public, ); }; };
		AD98AC39265B8B7F00EDAEA3 /* UpDeviceResult.h in Headers */ = {isa = PBXBuildFile; fileRef = AD98ABC8265B8B7B00EDAEA3 /* UpDeviceResult.h */; settings = {ATTRIBUTES = (Public, ); }; };
		AD98AC3A265B8B7F00EDAEA3 /* UpDeviceAction.h in Headers */ = {isa = PBXBuildFile; fileRef = AD98ABC9265B8B7B00EDAEA3 /* UpDeviceAction.h */; settings = {ATTRIBUTES = (Public, ); }; };
		AD98AC3B265B8B7F00EDAEA3 /* UpStringResult.m in Sources */ = {isa = PBXBuildFile; fileRef = AD98ABCA265B8B7B00EDAEA3 /* UpStringResult.m */; };
		AD98AC3C265B8B7F00EDAEA3 /* UpDeviceHelper.h in Headers */ = {isa = PBXBuildFile; fileRef = AD98ABCB265B8B7B00EDAEA3 /* UpDeviceHelper.h */; settings = {ATTRIBUTES = (Public, ); }; };
		AD98AC3D265B8B7F00EDAEA3 /* UpDeviceException.h in Headers */ = {isa = PBXBuildFile; fileRef = AD98ABCD265B8B7B00EDAEA3 /* UpDeviceException.h */; settings = {ATTRIBUTES = (Public, ); }; };
		AD98AC3E265B8B7F00EDAEA3 /* UpDeviceException.m in Sources */ = {isa = PBXBuildFile; fileRef = AD98ABCE265B8B7B00EDAEA3 /* UpDeviceException.m */; };
		AD98AC3F265B8B7F00EDAEA3 /* UpDeviceDataSource.h in Headers */ = {isa = PBXBuildFile; fileRef = AD98ABD1265B8B7B00EDAEA3 /* UpDeviceDataSource.h */; settings = {ATTRIBUTES = (Public, ); }; };
		AD98AC40265B8B8000EDAEA3 /* UpDeviceReportListener.h in Headers */ = {isa = PBXBuildFile; fileRef = AD98ABD4265B8B7B00EDAEA3 /* UpDeviceReportListener.h */; settings = {ATTRIBUTES = (Public, ); }; };
		AD98AC41265B8B8000EDAEA3 /* UpDeviceToolkitListener.h in Headers */ = {isa = PBXBuildFile; fileRef = AD98ABD5265B8B7B00EDAEA3 /* UpDeviceToolkitListener.h */; settings = {ATTRIBUTES = (Public, ); }; };
		AD98AC42265B8B8000EDAEA3 /* UpDeviceDetectListener.h in Headers */ = {isa = PBXBuildFile; fileRef = AD98ABD6265B8B7B00EDAEA3 /* UpDeviceDetectListener.h */; settings = {ATTRIBUTES = (Public, ); }; };
		AD98AC43265B8B8000EDAEA3 /* EmptyDeviceToolkit.m in Sources */ = {isa = PBXBuildFile; fileRef = AD98ABD7265B8B7B00EDAEA3 /* EmptyDeviceToolkit.m */; };
		AD98AC44265B8B8000EDAEA3 /* UpDeviceToolkitState.h in Headers */ = {isa = PBXBuildFile; fileRef = AD98ABD8265B8B7B00EDAEA3 /* UpDeviceToolkitState.h */; settings = {ATTRIBUTES = (Public, ); }; };
		AD98AC45265B8B8000EDAEA3 /* UpDeviceToolkit.h in Headers */ = {isa = PBXBuildFile; fileRef = AD98ABD9265B8B7B00EDAEA3 /* UpDeviceToolkit.h */; settings = {ATTRIBUTES = (Public, ); }; };
		AD98AC46265B8B8000EDAEA3 /* EmptyDeviceToolkit.h in Headers */ = {isa = PBXBuildFile; fileRef = AD98ABDA265B8B7B00EDAEA3 /* EmptyDeviceToolkit.h */; settings = {ATTRIBUTES = (Public, ); }; };
		AD98AC47265B8B8000EDAEA3 /* UPDeviceLog.h in Headers */ = {isa = PBXBuildFile; fileRef = AD98ABDC265B8B7B00EDAEA3 /* UPDeviceLog.h */; settings = {ATTRIBUTES = (Public, ); }; };
		ADFEFDB62702E39400B336F0 /* AttachDeviceWithoutConnect.h in Headers */ = {isa = PBXBuildFile; fileRef = ADFEFDB42702E39400B336F0 /* AttachDeviceWithoutConnect.h */; };
		ADFEFDB72702E39400B336F0 /* AttachDeviceWithoutConnect.m in Sources */ = {isa = PBXBuildFile; fileRef = ADFEFDB52702E39400B336F0 /* AttachDeviceWithoutConnect.m */; };
		ADFEFDBA2702E3AB00B336F0 /* DetachDeviceWithoutConnect.h in Headers */ = {isa = PBXBuildFile; fileRef = ADFEFDB82702E3AB00B336F0 /* DetachDeviceWithoutConnect.h */; };
		ADFEFDBB2702E3AB00B336F0 /* DetachDeviceWithoutConnect.m in Sources */ = {isa = PBXBuildFile; fileRef = ADFEFDB92702E3AB00B336F0 /* DetachDeviceWithoutConnect.m */; };
		DB1BFCDC2DB119A100E6815A /* UpPresenceInfo.m in Sources */ = {isa = PBXBuildFile; fileRef = DB1BFCDB2DB119A100E6815A /* UpPresenceInfo.m */; };
		DB1BFCDD2DB119A100E6815A /* UpPresenceInfo.h in Headers */ = {isa = PBXBuildFile; fileRef = DB1BFCDA2DB119A100E6815A /* UpPresenceInfo.h */; };
		DB1BFCE02DB119BD00E6815A /* UpPopupTipInfo.m in Sources */ = {isa = PBXBuildFile; fileRef = DB1BFCDF2DB119BD00E6815A /* UpPopupTipInfo.m */; };
		DB1BFCE12DB119BD00E6815A /* UpPopupTipInfo.h in Headers */ = {isa = PBXBuildFile; fileRef = DB1BFCDE2DB119BD00E6815A /* UpPopupTipInfo.h */; };
		DB1BFCE42DB11A1300E6815A /* UpConnectivityPresenceHandler.m in Sources */ = {isa = PBXBuildFile; fileRef = DB1BFCE32DB11A1300E6815A /* UpConnectivityPresenceHandler.m */; };
		DB1BFCE52DB11A1300E6815A /* UpConnectivityPresenceHandler.h in Headers */ = {isa = PBXBuildFile; fileRef = DB1BFCE22DB11A1300E6815A /* UpConnectivityPresenceHandler.h */; };
		DB4F95152DB21C6300064BC5 /* GetDeviceWifiOnlineState.m in Sources */ = {isa = PBXBuildFile; fileRef = DB4F95142DB21C6300064BC5 /* GetDeviceWifiOnlineState.m */; };
		DB4F95162DB21C6300064BC5 /* GetDeviceWifiOnlineState.h in Headers */ = {isa = PBXBuildFile; fileRef = DB4F95132DB21C6300064BC5 /* GetDeviceWifiOnlineState.h */; };
		DB4F95192DB23C0C00064BC5 /* IsSupportOnlyConfig.m in Sources */ = {isa = PBXBuildFile; fileRef = DB4F95182DB23C0C00064BC5 /* IsSupportOnlyConfig.m */; };
		DB4F951A2DB23C0C00064BC5 /* IsSupportOnlyConfig.h in Headers */ = {isa = PBXBuildFile; fileRef = DB4F95172DB23C0C00064BC5 /* IsSupportOnlyConfig.h */; };
		DB4F951D2DB23CA600064BC5 /* IsOnlyConfigFlow.m in Sources */ = {isa = PBXBuildFile; fileRef = DB4F951C2DB23CA600064BC5 /* IsOnlyConfigFlow.m */; };
		DB4F951E2DB23CA600064BC5 /* IsOnlyConfigFlow.h in Headers */ = {isa = PBXBuildFile; fileRef = DB4F951B2DB23CA600064BC5 /* IsOnlyConfigFlow.h */; };
		F8313D0725E0CFFD00F8A479 /* (null) in Headers */ = {isa = PBXBuildFile; settings = {ATTRIBUTES = (Public, ); }; };
		F8313D0825E0CFFD00F8A479 /* (null) in Sources */ = {isa = PBXBuildFile; };
		F86314A5250A01E200C948B9 /* AppDelegate.m in Sources */ = {isa = PBXBuildFile; fileRef = F86314A4250A01E200C948B9 /* AppDelegate.m */; };
		F86314A8250A01E200C948B9 /* SceneDelegate.m in Sources */ = {isa = PBXBuildFile; fileRef = F86314A7250A01E200C948B9 /* SceneDelegate.m */; };
		F86314AB250A01E200C948B9 /* ViewController.m in Sources */ = {isa = PBXBuildFile; fileRef = F86314AA250A01E200C948B9 /* ViewController.m */; };
		F86314AE250A01E200C948B9 /* Main.storyboard in Resources */ = {isa = PBXBuildFile; fileRef = F86314AC250A01E200C948B9 /* Main.storyboard */; };
		F86314B0250A01E400C948B9 /* Assets.xcassets in Resources */ = {isa = PBXBuildFile; fileRef = F86314AF250A01E400C948B9 /* Assets.xcassets */; };
		F86314B3250A01E400C948B9 /* LaunchScreen.storyboard in Resources */ = {isa = PBXBuildFile; fileRef = F86314B1250A01E400C948B9 /* LaunchScreen.storyboard */; };
		F86314B6250A01E400C948B9 /* main.m in Sources */ = {isa = PBXBuildFile; fileRef = F86314B5250A01E400C948B9 /* main.m */; };
		F86314C1250A01F000C948B9 /* CucumberRunner.m in Sources */ = {isa = PBXBuildFile; fileRef = F86314C0250A01F000C948B9 /* CucumberRunner.m */; };
/* End PBXBuildFile section */

/* Begin PBXContainerItemProxy section */
		AD8DD1FC2643F5C300086CBF /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = F8631489250A018800C948B9 /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = AD8DD1112643E5A300086CBF;
			remoteInfo = UPDevice;
		};
		AD8DD1FE2643F5CF00086CBF /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = F8631489250A018800C948B9 /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = AD8DD0FE2643E48600086CBF;
			remoteInfo = UPDeviceUtil;
		};
		AD8DD2002643F5D600086CBF /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = F8631489250A018800C948B9 /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = AD8DD1112643E5A300086CBF;
			remoteInfo = UPDevice;
		};
		AD98A5F0265B7D4D00EDAEA3 /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = F8631489250A018800C948B9 /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = AD98A4FF265B74C700EDAEA3;
			remoteInfo = updevice_api;
		};
/* End PBXContainerItemProxy section */

/* Begin PBXCopyFilesBuildPhase section */
		AD8DD0FD2643E48600086CBF /* CopyFiles */ = {
			isa = PBXCopyFilesBuildPhase;
			buildActionMask = 2147483647;
			dstPath = "include/$(PRODUCT_NAME)";
			dstSubfolderSpec = 16;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		AD8DD1102643E5A300086CBF /* CopyFiles */ = {
			isa = PBXCopyFilesBuildPhase;
			buildActionMask = 2147483647;
			dstPath = "include/$(PRODUCT_NAME)";
			dstSubfolderSpec = 16;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		AD98A4FE265B74C700EDAEA3 /* CopyFiles */ = {
			isa = PBXCopyFilesBuildPhase;
			buildActionMask = 2147483647;
			dstPath = "include/$(PRODUCT_NAME)";
			dstSubfolderSpec = 16;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXCopyFilesBuildPhase section */

/* Begin PBXFileReference section */
		041D69E0270302EB00833105 /* UpDeviceControlState.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = UpDeviceControlState.h; sourceTree = "<group>"; };
		041D69E62704137800833105 /* GetControlState.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; name = GetControlState.m; path = "UpDevice/updevice-toolkit-usdk/usdk/action/GetControlState.m"; sourceTree = SOURCE_ROOT; };
		041D69E72704137800833105 /* GetControlState.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; name = GetControlState.h; path = "UpDevice/updevice-toolkit-usdk/usdk/action/GetControlState.h"; sourceTree = SOURCE_ROOT; };
		041D69EA27042ADF00833105 /* GetFaultInformationCode.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; name = GetFaultInformationCode.h; path = "UpDevice/updevice-toolkit-usdk/usdk/action/GetFaultInformationCode.h"; sourceTree = SOURCE_ROOT; };
		041D69EB27042ADF00833105 /* GetFaultInformationCode.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; name = GetFaultInformationCode.m; path = "UpDevice/updevice-toolkit-usdk/usdk/action/GetFaultInformationCode.m"; sourceTree = SOURCE_ROOT; };
		041D69EE2704545800833105 /* BindDeviceWithoutWifi.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; name = BindDeviceWithoutWifi.h; path = "UpDevice/updevice-toolkit-usdk/usdk/action/BindDeviceWithoutWifi.h"; sourceTree = SOURCE_ROOT; };
		041D69EF2704545800833105 /* BindDeviceWithoutWifi.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; name = BindDeviceWithoutWifi.m; path = "UpDevice/updevice-toolkit-usdk/usdk/action/BindDeviceWithoutWifi.m"; sourceTree = SOURCE_ROOT; };
		041D69F22705240D00833105 /* UpdateRouterInfo.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; name = UpdateRouterInfo.h; path = "UpDevice/updevice-toolkit-usdk/usdk/action/UpdateRouterInfo.h"; sourceTree = SOURCE_ROOT; };
		041D69F32705240D00833105 /* UpdateRouterInfo.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; name = UpdateRouterInfo.m; path = "UpDevice/updevice-toolkit-usdk/usdk/action/UpdateRouterInfo.m"; sourceTree = SOURCE_ROOT; };
		041D69F627054A8300833105 /* GetConfigRouterInfo.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; name = GetConfigRouterInfo.h; path = "UpDevice/updevice-toolkit-usdk/usdk/action/GetConfigRouterInfo.h"; sourceTree = SOURCE_ROOT; };
		041D69F727054A8300833105 /* GetConfigRouterInfo.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; name = GetConfigRouterInfo.m; path = "UpDevice/updevice-toolkit-usdk/usdk/action/GetConfigRouterInfo.m"; sourceTree = SOURCE_ROOT; };
		04C3CDBE2716EE5500F951CB /* ConnectDevice.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; name = ConnectDevice.h; path = "UpDevice/updevice-toolkit-usdk/usdk/action/ConnectDevice.h"; sourceTree = SOURCE_ROOT; };
		04C3CDBF2716EE5500F951CB /* ConnectDevice.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; name = ConnectDevice.m; path = "UpDevice/updevice-toolkit-usdk/usdk/action/ConnectDevice.m"; sourceTree = SOURCE_ROOT; };
		04C3CDC22716F54700F951CB /* DisconnectDevice.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; name = DisconnectDevice.h; path = "UpDevice/updevice-toolkit-usdk/usdk/action/DisconnectDevice.h"; sourceTree = SOURCE_ROOT; };
		04C3CDC32716F54700F951CB /* DisconnectDevice.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; name = DisconnectDevice.m; path = "UpDevice/updevice-toolkit-usdk/usdk/action/DisconnectDevice.m"; sourceTree = SOURCE_ROOT; };
		04E3E60327182E6B00D1DDBE /* UpDeviceOnlineStatus.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; name = UpDeviceOnlineStatus.h; path = updevice_api/updevice_common/entity/UpDeviceOnlineStatus.h; sourceTree = SOURCE_ROOT; };
		04F426D82A3954290068FCC7 /* UpDeviceDaemonGIO.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = UpDeviceDaemonGIO.h; sourceTree = "<group>"; };
		04F426D92A3954290068FCC7 /* UpDeviceDaemonGIO.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = UpDeviceDaemonGIO.m; sourceTree = "<group>"; };
		221C2A2225C14D3D00D68224 /* FakeDelegateFactory.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = FakeDelegateFactory.h; sourceTree = "<group>"; };
		221C2A2325C14D3D00D68224 /* FakeDelegateFactory.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = FakeDelegateFactory.m; sourceTree = "<group>"; };
		2232840925BEA9A4001CBE68 /* CustomFilterProvider.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = CustomFilterProvider.h; sourceTree = "<group>"; };
		2232840A25BEA9A4001CBE68 /* CustomFilterProvider.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = CustomFilterProvider.m; sourceTree = "<group>"; };
		2232841925C01B73001CBE68 /* FakeUpDeviceBase.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = FakeUpDeviceBase.m; sourceTree = "<group>"; };
		2232841A25C01B73001CBE68 /* FakeUpDeviceListener.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = FakeUpDeviceListener.h; sourceTree = "<group>"; };
		2232841B25C01B73001CBE68 /* FakeUpDeviceReceiver.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = FakeUpDeviceReceiver.m; sourceTree = "<group>"; };
		2232841C25C01B74001CBE68 /* FakeUpDeviceReceiver.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = FakeUpDeviceReceiver.h; sourceTree = "<group>"; };
		2232841D25C01B74001CBE68 /* FakeUpDeviceBase.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = FakeUpDeviceBase.h; sourceTree = "<group>"; };
		2232841E25C01B74001CBE68 /* FakeUpDeviceListener.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = FakeUpDeviceListener.m; sourceTree = "<group>"; };
		22344EC1262FC0FA00FF4ACA /* FakeReportListener.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = FakeReportListener.h; sourceTree = "<group>"; };
		22344EC2262FC0FA00FF4ACA /* FakeReportListener.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = FakeReportListener.m; sourceTree = "<group>"; };
		22344EC42630102600FF4ACA /* FakeDetectListener.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = FakeDetectListener.h; sourceTree = "<group>"; };
		22344EC52630102600FF4ACA /* FakeDetectListener.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = FakeDetectListener.m; sourceTree = "<group>"; };
		22344EC72631833300FF4ACA /* FakeLEEngineListener.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = FakeLEEngineListener.h; sourceTree = "<group>"; };
		22344EC82631833300FF4ACA /* FakeLEEngineListener.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = FakeLEEngineListener.m; sourceTree = "<group>"; };
		225B08EC25BAA53900E5E86A /* FakeDelegateListener.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = FakeDelegateListener.h; sourceTree = "<group>"; };
		225B08ED25BAA53900E5E86A /* FakeDelegateListener.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = FakeDelegateListener.m; sourceTree = "<group>"; };
		22A5225925C3EDB000E76C88 /* FakeUSDKDevice.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = FakeUSDKDevice.h; sourceTree = "<group>"; };
		22A5225A25C3EDB000E76C88 /* FakeUSDKDevice.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = FakeUSDKDevice.m; sourceTree = "<group>"; };
		22F48A7B25F7079D0007F76E /* UPDevice.podspec */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = text; path = UPDevice.podspec; sourceTree = "<group>"; xcLanguageSpecificationIdentifier = xcode.lang.ruby; };
		22F785BF265CEDDA00175218 /* FetchGroupableDeviceList.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; name = FetchGroupableDeviceList.m; path = "UpDevice/updevice-toolkit-usdk/usdk/action/FetchGroupableDeviceList.m"; sourceTree = SOURCE_ROOT; };
		22F785C0265CEDDA00175218 /* RemoveDevicesFromGroup.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; name = RemoveDevicesFromGroup.h; path = "UpDevice/updevice-toolkit-usdk/usdk/action/RemoveDevicesFromGroup.h"; sourceTree = SOURCE_ROOT; };
		22F785C1265CEDDA00175218 /* RemoveDevicesFromGroup.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; name = RemoveDevicesFromGroup.m; path = "UpDevice/updevice-toolkit-usdk/usdk/action/RemoveDevicesFromGroup.m"; sourceTree = SOURCE_ROOT; };
		22F785C2265CEDDA00175218 /* GetGroupMemberList.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; name = GetGroupMemberList.h; path = "UpDevice/updevice-toolkit-usdk/usdk/action/GetGroupMemberList.h"; sourceTree = SOURCE_ROOT; };
		22F785C3265CEDDB00175218 /* CreateDeviceGroup.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; name = CreateDeviceGroup.m; path = "UpDevice/updevice-toolkit-usdk/usdk/action/CreateDeviceGroup.m"; sourceTree = SOURCE_ROOT; };
		22F785C4265CEDDB00175218 /* CreateDeviceGroup.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; name = CreateDeviceGroup.h; path = "UpDevice/updevice-toolkit-usdk/usdk/action/CreateDeviceGroup.h"; sourceTree = SOURCE_ROOT; };
		22F785C5265CEDDB00175218 /* DeleteDeviceGroup.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; name = DeleteDeviceGroup.h; path = "UpDevice/updevice-toolkit-usdk/usdk/action/DeleteDeviceGroup.h"; sourceTree = SOURCE_ROOT; };
		22F785C6265CEDDB00175218 /* DeleteDeviceGroup.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; name = DeleteDeviceGroup.m; path = "UpDevice/updevice-toolkit-usdk/usdk/action/DeleteDeviceGroup.m"; sourceTree = SOURCE_ROOT; };
		22F785C7265CEDDB00175218 /* AddDevicesToGroup.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; name = AddDevicesToGroup.m; path = "UpDevice/updevice-toolkit-usdk/usdk/action/AddDevicesToGroup.m"; sourceTree = SOURCE_ROOT; };
		22F785C8265CEDDB00175218 /* GetGroupMemberList.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; name = GetGroupMemberList.m; path = "UpDevice/updevice-toolkit-usdk/usdk/action/GetGroupMemberList.m"; sourceTree = SOURCE_ROOT; };
		22F785C9265CEDDB00175218 /* FetchGroupableDeviceList.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; name = FetchGroupableDeviceList.h; path = "UpDevice/updevice-toolkit-usdk/usdk/action/FetchGroupableDeviceList.h"; sourceTree = SOURCE_ROOT; };
		22F785CA265CEDDB00175218 /* AddDevicesToGroup.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; name = AddDevicesToGroup.h; path = "UpDevice/updevice-toolkit-usdk/usdk/action/AddDevicesToGroup.h"; sourceTree = SOURCE_ROOT; };
		22F785D8265CF88B00175218 /* InitializationSteps.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = InitializationSteps.h; sourceTree = "<group>"; };
		22F785D9265CF88B00175218 /* DeviceManagerSteps.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = DeviceManagerSteps.m; sourceTree = "<group>"; };
		22F785DA265CF88B00175218 /* WashDeviceSteps.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = WashDeviceSteps.h; sourceTree = "<group>"; };
		22F785DB265CF88B00175218 /* WashDeviceSteps.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = WashDeviceSteps.m; sourceTree = "<group>"; };
		22F785DC265CF88B00175218 /* WifiDeviceToolkitSteps.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = WifiDeviceToolkitSteps.h; sourceTree = "<group>"; };
		22F785DD265CF88B00175218 /* EngineDeviceSteps.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = EngineDeviceSteps.h; sourceTree = "<group>"; };
		22F785DE265CF88B00175218 /* WifiDeviceToolkitSteps.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = WifiDeviceToolkitSteps.m; sourceTree = "<group>"; };
		22F785DF265CF88B00175218 /* InitializationSteps.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = InitializationSteps.m; sourceTree = "<group>"; };
		22F785E0265CF88B00175218 /* DeviceBaseSteps.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = DeviceBaseSteps.h; sourceTree = "<group>"; };
		22F785E1265CF88B00175218 /* EngineDeviceSteps.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = EngineDeviceSteps.m; sourceTree = "<group>"; };
		22F785E2265CF88B00175218 /* DeviceManagerSteps.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = DeviceManagerSteps.h; sourceTree = "<group>"; };
		22F785E3265CF88B00175218 /* DeviceBaseSteps.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = DeviceBaseSteps.m; sourceTree = "<group>"; };
		22F785EA265CF89C00175218 /* StepsUtils.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = StepsUtils.m; sourceTree = "<group>"; };
		22F785EB265CF89C00175218 /* StepsUtils.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = StepsUtils.h; sourceTree = "<group>"; };
		22F785EF265D022D00175218 /* FakeDelegateBase.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = FakeDelegateBase.h; sourceTree = "<group>"; };
		22F785F0265D022D00175218 /* FakeDelegateBase.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = FakeDelegateBase.m; sourceTree = "<group>"; };
		22F785FB265DE20F00175218 /* UpAttachResourceDelegate.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = UpAttachResourceDelegate.h; sourceTree = "<group>"; };
		22F785FD265DE40500175218 /* AttachDecodeResource.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; name = AttachDecodeResource.m; path = "UpDevice/updevice-toolkit-usdk/usdk/action/AttachDecodeResource.m"; sourceTree = SOURCE_ROOT; };
		22F785FE265DE40500175218 /* AttachDecodeResource.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; name = AttachDecodeResource.h; path = "UpDevice/updevice-toolkit-usdk/usdk/action/AttachDecodeResource.h"; sourceTree = SOURCE_ROOT; };
		22F78603265E1E2500175218 /* IsGroup.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; name = IsGroup.m; path = "UpDevice/updevice-toolkit-usdk/usdk/action/IsGroup.m"; sourceTree = SOURCE_ROOT; };
		22F78604265E1E2500175218 /* IsGroup.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; name = IsGroup.h; path = "UpDevice/updevice-toolkit-usdk/usdk/action/IsGroup.h"; sourceTree = SOURCE_ROOT; };
		333B1ABE26170B6700A389D9 /* GetNetType.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = GetNetType.m; sourceTree = "<group>"; };
		333B1ABF26170B6700A389D9 /* GetNetType.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = GetNetType.h; sourceTree = "<group>"; };
		334AFCE9266F408F003F443B /* StartWashFota.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; name = StartWashFota.h; path = "UpDevice/updevice-toolkit-usdk/usdk/action/StartWashFota.h"; sourceTree = SOURCE_ROOT; };
		334AFCEA266F408F003F443B /* StartWashFota.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; name = StartWashFota.m; path = "UpDevice/updevice-toolkit-usdk/usdk/action/StartWashFota.m"; sourceTree = SOURCE_ROOT; };
		3382E04B26E6087B009576F5 /* GetDeviceOnlineStatus.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; name = GetDeviceOnlineStatus.h; path = "UpDevice/updevice-toolkit-usdk/usdk/action/GetDeviceOnlineStatus.h"; sourceTree = SOURCE_ROOT; };
		3382E04C26E6087B009576F5 /* GetDeviceOnlineStatus.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; name = GetDeviceOnlineStatus.m; path = "UpDevice/updevice-toolkit-usdk/usdk/action/GetDeviceOnlineStatus.m"; sourceTree = SOURCE_ROOT; };
		33ED73B5269FFFA500D4C7A8 /* features */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = text; path = features; sourceTree = SOURCE_ROOT; };
		33FDA61026285A3E0099964E /* UpCompatEngineDevice.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; name = UpCompatEngineDevice.h; path = UpDevice/compat/UpCompatEngineDevice.h; sourceTree = SOURCE_ROOT; };
		33FDA61126285A3E0099964E /* UpCompatEngineDevice.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; name = UpCompatEngineDevice.m; path = UpDevice/compat/UpCompatEngineDevice.m; sourceTree = SOURCE_ROOT; };
		33FDA61826285A690099964E /* UpWashDevice.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = UpWashDevice.m; sourceTree = "<group>"; };
		33FDA61926285A690099964E /* UpWashAdapterApi.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = UpWashAdapterApi.h; sourceTree = "<group>"; };
		33FDA61A26285A690099964E /* UpWashModel.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = UpWashModel.m; sourceTree = "<group>"; };
		33FDA61B26285A690099964E /* UpWashDevice.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = UpWashDevice.h; sourceTree = "<group>"; };
		33FDA61D26285A690099964E /* UpWashAdapterApi.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = UpWashAdapterApi.m; sourceTree = "<group>"; };
		33FDA61E26285A690099964E /* UpWashModel.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = UpWashModel.h; sourceTree = "<group>"; };
		33FDA766262D619B0099964E /* UpVoiceBoxDevice.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; name = UpVoiceBoxDevice.h; path = UpDevice/voicebox/UpVoiceBoxDevice.h; sourceTree = SOURCE_ROOT; };
		33FDA767262D619B0099964E /* UpVoiceBoxDevice.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; name = UpVoiceBoxDevice.m; path = UpDevice/voicebox/UpVoiceBoxDevice.m; sourceTree = SOURCE_ROOT; };
		33FDA778262ECA0E0099964E /* UpWashDataResponseParser.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = UpWashDataResponseParser.h; sourceTree = "<group>"; };
		33FDA779262ECA0E0099964E /* UpWashDataResponseParser.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = UpWashDataResponseParser.m; sourceTree = "<group>"; };
		40E0AC4525148816007DBC42 /* UDSafeMutableArray.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = UDSafeMutableArray.m; sourceTree = "<group>"; };
		40E0AC4625148816007DBC42 /* UDSafeMutableDictionary.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = UDSafeMutableDictionary.h; sourceTree = "<group>"; };
		40E0AC4725148816007DBC42 /* UDSafeMutableDictionary.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = UDSafeMutableDictionary.m; sourceTree = "<group>"; };
		40E0AC4825148816007DBC42 /* UDSafeMutableArray.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = UDSafeMutableArray.h; sourceTree = "<group>"; };
		40E0AC6125148BD8007DBC42 /* Info.plist */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = text.plist.xml; path = Info.plist; sourceTree = "<group>"; };
		40E0AC6525148BE2007DBC42 /* Info.plist */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = text.plist.xml; path = Info.plist; sourceTree = "<group>"; };
		40E0AC8A25148CD5007DBC42 /* Info.plist */ = {isa = PBXFileReference; lastKnownFileType = text.plist; name = Info.plist; path = UpDevice/Info.plist; sourceTree = SOURCE_ROOT; };
		4AC22BE82754B1B200BC7418 /* GetDeviceOnlineStateV2.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; name = GetDeviceOnlineStateV2.h; path = "UpDevice/updevice-toolkit-usdk/usdk/action/GetDeviceOnlineStateV2.h"; sourceTree = SOURCE_ROOT; };
		4AC22BE92754B1B200BC7418 /* GetDeviceOnlineStateV2.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; name = GetDeviceOnlineStateV2.m; path = "UpDevice/updevice-toolkit-usdk/usdk/action/GetDeviceOnlineStateV2.m"; sourceTree = SOURCE_ROOT; };
		4AC22C062754CBAE00BC7418 /* UpDeviceOnlineStateV2.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = UpDeviceOnlineStateV2.h; sourceTree = "<group>"; };
		4AFC2854275F4787008F4DDB /* GetDeviceWifiLocalState.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; name = GetDeviceWifiLocalState.h; path = "UpDevice/updevice-toolkit-usdk/usdk/action/GetDeviceWifiLocalState.h"; sourceTree = SOURCE_ROOT; };
		4AFC2855275F4788008F4DDB /* GetDeviceWifiLocalState.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; name = GetDeviceWifiLocalState.m; path = "UpDevice/updevice-toolkit-usdk/usdk/action/GetDeviceWifiLocalState.m"; sourceTree = SOURCE_ROOT; };
		4F22A57D2AFDCB4500269406 /* UpDeviceOfflineCause.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = UpDeviceOfflineCause.h; sourceTree = "<group>"; };
		4F22A57E2AFE52F700269406 /* GetOfflineCause.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; name = GetOfflineCause.h; path = "UpDevice/updevice-toolkit-usdk/usdk/action/GetOfflineCause.h"; sourceTree = SOURCE_ROOT; };
		4F22A57F2AFE52F700269406 /* GetOfflineCause.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; name = GetOfflineCause.m; path = "UpDevice/updevice-toolkit-usdk/usdk/action/GetOfflineCause.m"; sourceTree = SOURCE_ROOT; };
		4F5ED4DC2C72F15500119B62 /* UpNonNetworkDeviceFactory.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = UpNonNetworkDeviceFactory.h; sourceTree = "<group>"; };
		4F5ED4DD2C72F15500119B62 /* UPNonNetworkDevice.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = UPNonNetworkDevice.h; sourceTree = "<group>"; };
		4F5ED4DE2C72F15500119B62 /* UPNonNetworkDevice.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = UPNonNetworkDevice.m; sourceTree = "<group>"; };
		4F5ED4DF2C72F15500119B62 /* UpNonNetworkDeviceFactory.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = UpNonNetworkDeviceFactory.m; sourceTree = "<group>"; };
		4F68698F2B883B4B00943C1A /* ExecuteCommandWithResult.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; name = ExecuteCommandWithResult.h; path = "UpDevice/updevice-toolkit-usdk/usdk/action/ExecuteCommandWithResult.h"; sourceTree = SOURCE_ROOT; };
		4F6869902B883B4B00943C1A /* ExecuteCommandWithResult.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; name = ExecuteCommandWithResult.m; path = "UpDevice/updevice-toolkit-usdk/usdk/action/ExecuteCommandWithResult.m"; sourceTree = SOURCE_ROOT; };
		4F7EB08F2D9524AC007694FE /* UpDeviceCardInfo.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; name = UpDeviceCardInfo.h; path = UpDevice/model/UpDeviceCardInfo.h; sourceTree = SOURCE_ROOT; };
		4F7EB0902D9524AC007694FE /* UpDeviceCardInfo.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; name = UpDeviceCardInfo.m; path = UpDevice/model/UpDeviceCardInfo.m; sourceTree = SOURCE_ROOT; };
		4F9B18772D7E8B6200C62B67 /* UPAggregateDevice.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; name = UPAggregateDevice.h; path = "UpDevice/updevice-aggregate/UPAggregateDevice.h"; sourceTree = SOURCE_ROOT; };
		4F9B18782D7E8B6200C62B67 /* UPAggregateDevice.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; name = UPAggregateDevice.m; path = "UpDevice/updevice-aggregate/UPAggregateDevice.m"; sourceTree = SOURCE_ROOT; };
		4F9B187B2D7E8CA200C62B67 /* UPAggregateDeviceFactory.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; name = UPAggregateDeviceFactory.h; path = "UpDevice/updevice-aggregate/UPAggregateDeviceFactory.h"; sourceTree = SOURCE_ROOT; };
		4F9B187C2D7E8CA200C62B67 /* UPAggregateDeviceFactory.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; name = UPAggregateDeviceFactory.m; path = "UpDevice/updevice-aggregate/UPAggregateDeviceFactory.m"; sourceTree = SOURCE_ROOT; };
		59F3630A2EB70C2F08991990 /* Pods-UPDevice.debug.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-UPDevice.debug.xcconfig"; path = "Target Support Files/Pods-UPDevice/Pods-UPDevice.debug.xcconfig"; sourceTree = "<group>"; };
		5A394E3B1310D11A99A55CD4 /* libPods-UPDeviceTests.a */ = {isa = PBXFileReference; explicitFileType = archive.ar; includeInIndex = 0; path = "libPods-UPDeviceTests.a"; sourceTree = BUILT_PRODUCTS_DIR; };
		633BDFCF19857B79B382F7E8 /* Pods-updevice_api.release.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-updevice_api.release.xcconfig"; path = "Target Support Files/Pods-updevice_api/Pods-updevice_api.release.xcconfig"; sourceTree = "<group>"; };
		6487A8411EC9F1313E98D8F1 /* libPods-UPDeviceDebugger.a */ = {isa = PBXFileReference; explicitFileType = archive.ar; includeInIndex = 0; path = "libPods-UPDeviceDebugger.a"; sourceTree = BUILT_PRODUCTS_DIR; };
		6A009374069A3438B0417DEB /* libPods-UPDevice.a */ = {isa = PBXFileReference; explicitFileType = archive.ar; includeInIndex = 0; path = "libPods-UPDevice.a"; sourceTree = BUILT_PRODUCTS_DIR; };
		8412D69A2D7F0C02002B44F7 /* UpDeviceCacheManager.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = UpDeviceCacheManager.h; sourceTree = "<group>"; };
		8412D69B2D7F0C02002B44F7 /* UpDeviceCacheManager.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = UpDeviceCacheManager.m; sourceTree = "<group>"; };
		8447614A2D9A7AFF00AE8BE4 /* UpDeviceCardManager.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; name = UpDeviceCardManager.h; path = UpDevice/UpDeviceCardManager.h; sourceTree = SOURCE_ROOT; };
		8447614B2D9A7AFF00AE8BE4 /* UpDeviceCardManager.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; name = UpDeviceCardManager.m; path = UpDevice/UpDeviceCardManager.m; sourceTree = SOURCE_ROOT; };
		8457E1852D8AC31B007EBE53 /* UpDeviceGroupOptDeleagte.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = UpDeviceGroupOptDeleagte.h; sourceTree = "<group>"; };
		8457E1862D8AC31B007EBE53 /* UpDeviceReportMonitor.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = UpDeviceReportMonitor.h; sourceTree = "<group>"; };
		8457E1872D8AC31B007EBE53 /* UpDeviceReportMonitor.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = UpDeviceReportMonitor.m; sourceTree = "<group>"; };
		8466DE9D2CCF32A500DFDA87 /* GetOnlyConfigState.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; name = GetOnlyConfigState.m; path = "UpDevice/updevice-toolkit-usdk/usdk/action/GetOnlyConfigState.m"; sourceTree = SOURCE_ROOT; };
		8466DE9E2CCF32A500DFDA87 /* GetOnlyConfigState.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; name = GetOnlyConfigState.h; path = "UpDevice/updevice-toolkit-usdk/usdk/action/GetOnlyConfigState.h"; sourceTree = SOURCE_ROOT; };
		8466DEA12CCF32F200DFDA87 /* UpDeviceOnlyConfigState.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = UpDeviceOnlyConfigState.h; sourceTree = "<group>"; };
		84AEE78A2DA65221008FF4C7 /* uSDKDeviceWrapper.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = uSDKDeviceWrapper.h; sourceTree = "<group>"; };
		84AEE78B2DA65221008FF4C7 /* uSDKDeviceWrapper.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = uSDKDeviceWrapper.m; sourceTree = "<group>"; };
		84AEE78E2DA668C0008FF4C7 /* GetuSDKDeviceWrapper.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; name = GetuSDKDeviceWrapper.h; path = "UpDevice/updevice-toolkit-usdk/usdk/action/GetuSDKDeviceWrapper.h"; sourceTree = SOURCE_ROOT; };
		84AEE78F2DA668C0008FF4C7 /* GetuSDKDeviceWrapper.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; name = GetuSDKDeviceWrapper.m; path = "UpDevice/updevice-toolkit-usdk/usdk/action/GetuSDKDeviceWrapper.m"; sourceTree = SOURCE_ROOT; };
		84C30C662BF44F9B00754263 /* GetOfflineDays.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; name = GetOfflineDays.h; path = "UpDevice/updevice-toolkit-usdk/usdk/action/GetOfflineDays.h"; sourceTree = SOURCE_ROOT; };
		84C30C672BF44F9B00754263 /* GetOfflineDays.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; name = GetOfflineDays.m; path = "UpDevice/updevice-toolkit-usdk/usdk/action/GetOfflineDays.m"; sourceTree = SOURCE_ROOT; };
		88ED39B27363FAF9CE965C37 /* Pods-UPDeviceTests.debug.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-UPDeviceTests.debug.xcconfig"; path = "Target Support Files/Pods-UPDeviceTests/Pods-UPDeviceTests.debug.xcconfig"; sourceTree = "<group>"; };
		8A6C8EB9EC9E184964C16C36 /* Pods-UPDeviceTests.release.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-UPDeviceTests.release.xcconfig"; path = "Target Support Files/Pods-UPDeviceTests/Pods-UPDeviceTests.release.xcconfig"; sourceTree = "<group>"; };
		966049222A2DC39E00138B09 /* UpEngineDevice+ResourceConfigSource.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; name = "UpEngineDevice+ResourceConfigSource.h"; path = "UpDevice/updevice-logic-engine/UpEngineDevice+ResourceConfigSource.h"; sourceTree = SOURCE_ROOT; };
		966049232A2DC39E00138B09 /* UpEngineDevice+ResourceConfigSource.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; name = "UpEngineDevice+ResourceConfigSource.m"; path = "UpDevice/updevice-logic-engine/UpEngineDevice+ResourceConfigSource.m"; sourceTree = SOURCE_ROOT; };
		A01559DD280821DE00723EDC /* UpDeviceObjectCommonHelper.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = UpDeviceObjectCommonHelper.h; sourceTree = "<group>"; };
		A01559DE280821DE00723EDC /* UpDeviceObjectCommonHelper.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = UpDeviceObjectCommonHelper.m; sourceTree = "<group>"; };
		A027753E298A39090014920F /* UpWashDeviceManager.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = UpWashDeviceManager.h; sourceTree = "<group>"; };
		A027753F298A39090014920F /* UpWashDeviceManager.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = UpWashDeviceManager.m; sourceTree = "<group>"; };
		A0277542298A3D760014920F /* WashDeviceManagerSteps.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = WashDeviceManagerSteps.h; sourceTree = "<group>"; };
		A0277543298A3D760014920F /* WashDeviceManagerSteps.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = WashDeviceManagerSteps.m; sourceTree = "<group>"; };
		A0277545298A3DC80014920F /* FakeWashdapterApi.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = FakeWashdapterApi.h; sourceTree = "<group>"; };
		A0277546298A3DC80014920F /* FakeWashdapterApi.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = FakeWashdapterApi.m; sourceTree = "<group>"; };
		A073AE36278423CB00ED90BB /* QCConnectDevice.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; name = QCConnectDevice.h; path = "UpDevice/updevice-toolkit-usdk/usdk/action/QCConnectDevice.h"; sourceTree = SOURCE_ROOT; };
		A073AE37278423CB00ED90BB /* QCConnectDevice.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; name = QCConnectDevice.m; path = "UpDevice/updevice-toolkit-usdk/usdk/action/QCConnectDevice.m"; sourceTree = SOURCE_ROOT; };
		A073AE3A2784240400ED90BB /* QCDisconnectDevice.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; name = QCDisconnectDevice.h; path = "UpDevice/updevice-toolkit-usdk/usdk/action/QCDisconnectDevice.h"; sourceTree = SOURCE_ROOT; };
		A073AE3B2784240400ED90BB /* QCDisconnectDevice.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; name = QCDisconnectDevice.m; path = "UpDevice/updevice-toolkit-usdk/usdk/action/QCDisconnectDevice.m"; sourceTree = SOURCE_ROOT; };
		A0C41BCE2949D8C700A877F9 /* UpDeviceGetDeviceResourceTool.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; name = UpDeviceGetDeviceResourceTool.h; path = UPDeviceUtil/UpDeviceGetDeviceResourceTool.h; sourceTree = SOURCE_ROOT; };
		A0C41BCF2949D8C700A877F9 /* UpDeviceGetDeviceResourceTool.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; name = UpDeviceGetDeviceResourceTool.m; path = UPDeviceUtil/UpDeviceGetDeviceResourceTool.m; sourceTree = SOURCE_ROOT; };
		A0C41BD2294AF23200A877F9 /* UpDeviceResourceManager.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; name = UpDeviceResourceManager.h; path = UpDevice/UpDeviceResourceManager.h; sourceTree = SOURCE_ROOT; };
		A0C41BD3294AF23200A877F9 /* UpDeviceResourceManager.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; name = UpDeviceResourceManager.m; path = UpDevice/UpDeviceResourceManager.m; sourceTree = SOURCE_ROOT; };
		A0C41BDC294B297600A877F9 /* UpDeviceStringCommonHelper.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = UpDeviceStringCommonHelper.h; sourceTree = "<group>"; };
		A0C41BDD294B297600A877F9 /* UpDeviceStringCommonHelper.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = UpDeviceStringCommonHelper.m; sourceTree = "<group>"; };
		A0C41BE0294C11BA00A877F9 /* UpDeviceResourceSteps.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = UpDeviceResourceSteps.m; sourceTree = "<group>"; };
		A0C41BE2294C11C400A877F9 /* UpDeviceResourceSteps.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = UpDeviceResourceSteps.h; sourceTree = "<group>"; };
		A0CE394927576A60003FD92C /* GetDeviceNetworkLevel.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; name = GetDeviceNetworkLevel.h; path = "UpDevice/updevice-toolkit-usdk/usdk/action/GetDeviceNetworkLevel.h"; sourceTree = SOURCE_ROOT; };
		A0CE394A27576A60003FD92C /* GetDeviceNetworkLevel.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; name = GetDeviceNetworkLevel.m; path = "UpDevice/updevice-toolkit-usdk/usdk/action/GetDeviceNetworkLevel.m"; sourceTree = SOURCE_ROOT; };
		A0CE395227576C8D003FD92C /* UpDeviceNetworkLevel.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = UpDeviceNetworkLevel.h; sourceTree = "<group>"; };
		A0CFB3AB282CFBDB007E27BA /* FakeResourceManager.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = FakeResourceManager.h; sourceTree = "<group>"; };
		A0CFB3AC282CFBDC007E27BA /* FakeResourceManager.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = FakeResourceManager.m; sourceTree = "<group>"; };
		A0D3FEB627E42F0800DA145B /* UpDeviceQCConnectTimeoutType.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = UpDeviceQCConnectTimeoutType.h; sourceTree = "<group>"; };
		A0F0AEC6285B1A5900882A13 /* GetDeviceBleState.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; name = GetDeviceBleState.h; path = "UpDevice/updevice-toolkit-usdk/usdk/action/GetDeviceBleState.h"; sourceTree = SOURCE_ROOT; };
		A0F0AEC7285B1A5900882A13 /* GetDeviceBleState.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; name = GetDeviceBleState.m; path = "UpDevice/updevice-toolkit-usdk/usdk/action/GetDeviceBleState.m"; sourceTree = SOURCE_ROOT; };
		AD0A1CCB2713DB2600415B3D /* UpDeviceTracker.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = UpDeviceTracker.h; sourceTree = "<group>"; };
		AD113C002730DB7700EDB9F1 /* GetDeviceSleepState.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; name = GetDeviceSleepState.h; path = "UpDevice/updevice-toolkit-usdk/usdk/action/GetDeviceSleepState.h"; sourceTree = SOURCE_ROOT; };
		AD113C012730DB7700EDB9F1 /* GetDeviceSleepState.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; name = GetDeviceSleepState.m; path = "UpDevice/updevice-toolkit-usdk/usdk/action/GetDeviceSleepState.m"; sourceTree = SOURCE_ROOT; };
		AD8DD0FF2643E48600086CBF /* libUPDeviceUtil.a */ = {isa = PBXFileReference; explicitFileType = archive.ar; includeInIndex = 0; path = libUPDeviceUtil.a; sourceTree = BUILT_PRODUCTS_DIR; };
		AD8DD1122643E5A300086CBF /* libUPDevice.a */ = {isa = PBXFileReference; explicitFileType = archive.ar; includeInIndex = 0; path = libUPDevice.a; sourceTree = BUILT_PRODUCTS_DIR; };
		AD98A500265B74C700EDAEA3 /* libupdevice_api.a */ = {isa = PBXFileReference; explicitFileType = archive.ar; includeInIndex = 0; path = libupdevice_api.a; sourceTree = BUILT_PRODUCTS_DIR; };
		AD98AB64265B8B7A00EDAEA3 /* DefaultNotification.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = DefaultNotification.h; sourceTree = "<group>"; };
		AD98AB65265B8B7A00EDAEA3 /* DefaultBrokerHolder.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = DefaultBrokerHolder.m; sourceTree = "<group>"; };
		AD98AB66265B8B7A00EDAEA3 /* DefaultReportListener.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = DefaultReportListener.h; sourceTree = "<group>"; };
		AD98AB67265B8B7A00EDAEA3 /* DefaultDeviceBroker.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = DefaultDeviceBroker.m; sourceTree = "<group>"; };
		AD98AB68265B8B7A00EDAEA3 /* DefaultToolkitListener.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = DefaultToolkitListener.m; sourceTree = "<group>"; };
		AD98AB69265B8B7A00EDAEA3 /* DefaultDetectListener.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = DefaultDetectListener.h; sourceTree = "<group>"; };
		AD98AB6A265B8B7A00EDAEA3 /* DefaultDeviceBroker.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = DefaultDeviceBroker.h; sourceTree = "<group>"; };
		AD98AB6B265B8B7A00EDAEA3 /* DefaultReportListener.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = DefaultReportListener.m; sourceTree = "<group>"; };
		AD98AB6C265B8B7A00EDAEA3 /* DefaultNotification.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = DefaultNotification.m; sourceTree = "<group>"; };
		AD98AB6D265B8B7A00EDAEA3 /* DefaultBrokerHolder.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = DefaultBrokerHolder.h; sourceTree = "<group>"; };
		AD98AB6E265B8B7A00EDAEA3 /* DefaultToolkitListener.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = DefaultToolkitListener.h; sourceTree = "<group>"; };
		AD98AB6F265B8B7A00EDAEA3 /* DefaultDetectListener.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = DefaultDetectListener.m; sourceTree = "<group>"; };
		AD98AB70265B8B7A00EDAEA3 /* UpDeviceBroker.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = UpDeviceBroker.h; sourceTree = "<group>"; };
		AD98AB71265B8B7A00EDAEA3 /* UpDeviceBrokerHolder.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = UpDeviceBrokerHolder.h; sourceTree = "<group>"; };
		AD98AB72265B8B7A00EDAEA3 /* UpDeviceFilter.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = UpDeviceFilter.h; sourceTree = "<group>"; };
		AD98AB73265B8B7A00EDAEA3 /* UpDeviceFactory.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = UpDeviceFactory.h; sourceTree = "<group>"; };
		AD98AB74265B8B7A00EDAEA3 /* UpDeviceCreator.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = UpDeviceCreator.h; sourceTree = "<group>"; };
		AD98AB75265B8B7A00EDAEA3 /* UpDeviceFilters.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = UpDeviceFilters.h; sourceTree = "<group>"; };
		AD98AB7C265B8B7A00EDAEA3 /* UpDeviceReceiver.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = UpDeviceReceiver.h; sourceTree = "<group>"; };
		AD98AB7D265B8B7A00EDAEA3 /* UPDeviceNetType.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = UPDeviceNetType.h; sourceTree = "<group>"; };
		AD98AB7E265B8B7A00EDAEA3 /* UpCommonDevice.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = UpCommonDevice.h; sourceTree = "<group>"; };
		AD98AB7F265B8B7A00EDAEA3 /* UpDeviceBLE.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = UpDeviceBLE.h; sourceTree = "<group>"; };
		AD98AB80265B8B7A00EDAEA3 /* UpDeviceBase.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = UpDeviceBase.h; sourceTree = "<group>"; };
		AD98AB81265B8B7A00EDAEA3 /* UpDeviceReporter.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = UpDeviceReporter.m; sourceTree = "<group>"; };
		AD98AB82265B8B7A00EDAEA3 /* UpDeviceSub.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = UpDeviceSub.h; sourceTree = "<group>"; };
		AD98AB83265B8B7A00EDAEA3 /* UpExtendDevice.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = UpExtendDevice.h; sourceTree = "<group>"; };
		AD98AB84265B8B7A00EDAEA3 /* UpDeviceCache.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = UpDeviceCache.h; sourceTree = "<group>"; };
		AD98AB85265B8B7A00EDAEA3 /* UpCommonDevice.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = UpCommonDevice.m; sourceTree = "<group>"; };
		AD98AB86265B8B7A00EDAEA3 /* UpDeviceConfigState.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = UpDeviceConfigState.h; sourceTree = "<group>"; };
		AD98AB87265B8B7A00EDAEA3 /* UpDeviceListener.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = UpDeviceListener.h; sourceTree = "<group>"; };
		AD98AB88265B8B7A00EDAEA3 /* UpDevice.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = UpDevice.h; sourceTree = "<group>"; };
		AD98AB89265B8B7A00EDAEA3 /* UpDeviceStore.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = UpDeviceStore.h; sourceTree = "<group>"; };
		AD98AB8A265B8B7A00EDAEA3 /* UpDeviceFocus.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = UpDeviceFocus.h; sourceTree = "<group>"; };
		AD98AB8B265B8B7A00EDAEA3 /* UpDeviceNetWorkQuality.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = UpDeviceNetWorkQuality.h; sourceTree = "<group>"; };
		AD98AB8C265B8B7A00EDAEA3 /* UpDeviceReporter.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = UpDeviceReporter.h; sourceTree = "<group>"; };
		AD98AB8D265B8B7A00EDAEA3 /* UpDeviceBase.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = UpDeviceBase.m; sourceTree = "<group>"; };
		AD98AB8E265B8B7A00EDAEA3 /* UpDeviceFOTA.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = UpDeviceFOTA.h; sourceTree = "<group>"; };
		AD98AB90265B8B7A00EDAEA3 /* UpDeviceState.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = UpDeviceState.h; sourceTree = "<group>"; };
		AD98AB91265B8B7A00EDAEA3 /* UpDeviceCache.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = UpDeviceCache.m; sourceTree = "<group>"; };
		AD98AB93265B8B7A00EDAEA3 /* UpCompositeProcessor.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = UpCompositeProcessor.h; sourceTree = "<group>"; };
		AD98AB94265B8B7A00EDAEA3 /* UpCompositeProcessor.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = UpCompositeProcessor.m; sourceTree = "<group>"; };
		AD98AB95265B8B7A00EDAEA3 /* UpDeviceProcessor.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = UpDeviceProcessor.h; sourceTree = "<group>"; };
		AD98AB96265B8B7A00EDAEA3 /* UpDeviceFilters.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = UpDeviceFilters.m; sourceTree = "<group>"; };
		AD98AB97265B8B7A00EDAEA3 /* UpDeviceCreator.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = UpDeviceCreator.m; sourceTree = "<group>"; };
		AD98AB98265B8B7A00EDAEA3 /* UpDeviceCenter.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = UpDeviceCenter.h; sourceTree = "<group>"; };
		AD98AB99265B8B7A00EDAEA3 /* updevice_api.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = updevice_api.h; sourceTree = "<group>"; };
		AD98AB9D265B8B7A00EDAEA3 /* DeviceBaseInfo.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = DeviceBaseInfo.m; sourceTree = "<group>"; };
		AD98AB9E265B8B7A00EDAEA3 /* DeviceOTAStatusInfo.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = DeviceOTAStatusInfo.h; sourceTree = "<group>"; };
		AD98AB9F265B8B7A00EDAEA3 /* DeviceNetWorkQualityInfo.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = DeviceNetWorkQualityInfo.m; sourceTree = "<group>"; };
		AD98ABA0265B8B7A00EDAEA3 /* DeviceAttribute.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = DeviceAttribute.m; sourceTree = "<group>"; };
		AD98ABA1265B8B7A00EDAEA3 /* DeviceFOTAInfo.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = DeviceFOTAInfo.h; sourceTree = "<group>"; };
		AD98ABA2265B8B7A00EDAEA3 /* DeviceBLEHistoryInfo.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = DeviceBLEHistoryInfo.h; sourceTree = "<group>"; };
		AD98ABA3265B8B7A00EDAEA3 /* DeviceFOTAStatusInfo.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = DeviceFOTAStatusInfo.m; sourceTree = "<group>"; };
		AD98ABA4265B8B7A00EDAEA3 /* DeviceCaution.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = DeviceCaution.h; sourceTree = "<group>"; };
		AD98ABA5265B8B7A00EDAEA3 /* DeviceCommand.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = DeviceCommand.m; sourceTree = "<group>"; };
		AD98ABA6265B8B7A00EDAEA3 /* DeviceExtras.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = DeviceExtras.h; sourceTree = "<group>"; };
		AD98ABA7265B8B7A00EDAEA3 /* DeviceInfo.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = DeviceInfo.h; sourceTree = "<group>"; };
		AD98ABA8265B8B7A00EDAEA3 /* DeviceBaseInfo.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = DeviceBaseInfo.h; sourceTree = "<group>"; };
		AD98ABA9265B8B7A00EDAEA3 /* DeviceFOTAInfo.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = DeviceFOTAInfo.m; sourceTree = "<group>"; };
		AD98ABAA265B8B7A00EDAEA3 /* DeviceNetWorkQualityInfo.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = DeviceNetWorkQualityInfo.h; sourceTree = "<group>"; };
		AD98ABAB265B8B7A00EDAEA3 /* DeviceAttribute.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = DeviceAttribute.h; sourceTree = "<group>"; };
		AD98ABAC265B8B7A00EDAEA3 /* DeviceOTAStatusInfo.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = DeviceOTAStatusInfo.m; sourceTree = "<group>"; };
		AD98ABAD265B8B7A00EDAEA3 /* DeviceFOTAStatusInfo.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = DeviceFOTAStatusInfo.h; sourceTree = "<group>"; };
		AD98ABAE265B8B7A00EDAEA3 /* DeviceBLEHistoryInfo.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = DeviceBLEHistoryInfo.m; sourceTree = "<group>"; };
		AD98ABAF265B8B7A00EDAEA3 /* DeviceInfo.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = DeviceInfo.m; sourceTree = "<group>"; };
		AD98ABB0265B8B7A00EDAEA3 /* DeviceExtras.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = DeviceExtras.m; sourceTree = "<group>"; };
		AD98ABB1265B8B7A00EDAEA3 /* DeviceCaution.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = DeviceCaution.m; sourceTree = "<group>"; };
		AD98ABB2265B8B7A00EDAEA3 /* DeviceCommand.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = DeviceCommand.h; sourceTree = "<group>"; };
		AD98ABB3265B8B7A00EDAEA3 /* UpDeviceAttribute.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = UpDeviceAttribute.h; sourceTree = "<group>"; };
		AD98ABB4265B8B7B00EDAEA3 /* UpDeviceFOTAInfo.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = UpDeviceFOTAInfo.h; sourceTree = "<group>"; };
		AD98ABB5265B8B7B00EDAEA3 /* UpDeviceOTAStatusInfo.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = UpDeviceOTAStatusInfo.h; sourceTree = "<group>"; };
		AD98ABB6265B8B7B00EDAEA3 /* UpDeviceFOTAStatusInfo.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = UpDeviceFOTAStatusInfo.h; sourceTree = "<group>"; };
		AD98ABB7265B8B7B00EDAEA3 /* UpDeviceCommand.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = UpDeviceCommand.h; sourceTree = "<group>"; };
		AD98ABB8265B8B7B00EDAEA3 /* UpDeviceInfo.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = UpDeviceInfo.h; sourceTree = "<group>"; };
		AD98ABB9265B8B7B00EDAEA3 /* UpDeviceExtras.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = UpDeviceExtras.h; sourceTree = "<group>"; };
		AD98ABBA265B8B7B00EDAEA3 /* UpDeviceBaseInfo.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = UpDeviceBaseInfo.h; sourceTree = "<group>"; };
		AD98ABBB265B8B7B00EDAEA3 /* UpDeviceConnection.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = UpDeviceConnection.h; sourceTree = "<group>"; };
		AD98ABBC265B8B7B00EDAEA3 /* UpDeviceBLEHistoryInfo.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = UpDeviceBLEHistoryInfo.h; sourceTree = "<group>"; };
		AD98ABBD265B8B7B00EDAEA3 /* UpDeviceNetWorkQualityInfo.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = UpDeviceNetWorkQualityInfo.h; sourceTree = "<group>"; };
		AD98ABBE265B8B7B00EDAEA3 /* UpDeviceCaution.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = UpDeviceCaution.h; sourceTree = "<group>"; };
		AD98ABC0265B8B7B00EDAEA3 /* UpDeviceSetMap.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = UpDeviceSetMap.h; sourceTree = "<group>"; };
		AD98ABC1265B8B7B00EDAEA3 /* UpDeviceAction.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = UpDeviceAction.m; sourceTree = "<group>"; };
		AD98ABC2265B8B7B00EDAEA3 /* UpDeviceResult.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = UpDeviceResult.m; sourceTree = "<group>"; };
		AD98ABC3265B8B7B00EDAEA3 /* UpDeviceCarrier.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = UpDeviceCarrier.m; sourceTree = "<group>"; };
		AD98ABC4265B8B7B00EDAEA3 /* UpDeviceExecutable.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = UpDeviceExecutable.h; sourceTree = "<group>"; };
		AD98ABC5265B8B7B00EDAEA3 /* UpDeviceHelper.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = UpDeviceHelper.m; sourceTree = "<group>"; };
		AD98ABC6265B8B7B00EDAEA3 /* UpStringResult.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = UpStringResult.h; sourceTree = "<group>"; };
		AD98ABC7265B8B7B00EDAEA3 /* UpDeviceCarrier.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = UpDeviceCarrier.h; sourceTree = "<group>"; };
		AD98ABC8265B8B7B00EDAEA3 /* UpDeviceResult.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = UpDeviceResult.h; sourceTree = "<group>"; };
		AD98ABC9265B8B7B00EDAEA3 /* UpDeviceAction.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = UpDeviceAction.h; sourceTree = "<group>"; };
		AD98ABCA265B8B7B00EDAEA3 /* UpStringResult.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = UpStringResult.m; sourceTree = "<group>"; };
		AD98ABCB265B8B7B00EDAEA3 /* UpDeviceHelper.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = UpDeviceHelper.h; sourceTree = "<group>"; };
		AD98ABCD265B8B7B00EDAEA3 /* UpDeviceException.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = UpDeviceException.h; sourceTree = "<group>"; };
		AD98ABCE265B8B7B00EDAEA3 /* UpDeviceException.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = UpDeviceException.m; sourceTree = "<group>"; };
		AD98ABD1265B8B7B00EDAEA3 /* UpDeviceDataSource.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = UpDeviceDataSource.h; sourceTree = "<group>"; };
		AD98ABD4265B8B7B00EDAEA3 /* UpDeviceReportListener.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = UpDeviceReportListener.h; sourceTree = "<group>"; };
		AD98ABD5265B8B7B00EDAEA3 /* UpDeviceToolkitListener.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = UpDeviceToolkitListener.h; sourceTree = "<group>"; };
		AD98ABD6265B8B7B00EDAEA3 /* UpDeviceDetectListener.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = UpDeviceDetectListener.h; sourceTree = "<group>"; };
		AD98ABD7265B8B7B00EDAEA3 /* EmptyDeviceToolkit.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = EmptyDeviceToolkit.m; sourceTree = "<group>"; };
		AD98ABD8265B8B7B00EDAEA3 /* UpDeviceToolkitState.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = UpDeviceToolkitState.h; sourceTree = "<group>"; };
		AD98ABD9265B8B7B00EDAEA3 /* UpDeviceToolkit.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = UpDeviceToolkit.h; sourceTree = "<group>"; };
		AD98ABDA265B8B7B00EDAEA3 /* EmptyDeviceToolkit.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = EmptyDeviceToolkit.h; sourceTree = "<group>"; };
		AD98ABDC265B8B7B00EDAEA3 /* UPDeviceLog.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = UPDeviceLog.h; sourceTree = "<group>"; };
		ADF4AAFD26242A700056F104 /* Info.plist */ = {isa = PBXFileReference; lastKnownFileType = text.plist.xml; path = Info.plist; sourceTree = "<group>"; };
		ADF4AB0926242A700056F104 /* UPDeviceUtilTests.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = UPDeviceUtilTests.m; sourceTree = "<group>"; };
		ADF4AB0B26242A700056F104 /* Info.plist */ = {isa = PBXFileReference; lastKnownFileType = text.plist.xml; path = Info.plist; sourceTree = "<group>"; };
		ADFEFDB42702E39400B336F0 /* AttachDeviceWithoutConnect.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; name = AttachDeviceWithoutConnect.h; path = "UpDevice/updevice-toolkit-usdk/usdk/action/AttachDeviceWithoutConnect.h"; sourceTree = SOURCE_ROOT; };
		ADFEFDB52702E39400B336F0 /* AttachDeviceWithoutConnect.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; name = AttachDeviceWithoutConnect.m; path = "UpDevice/updevice-toolkit-usdk/usdk/action/AttachDeviceWithoutConnect.m"; sourceTree = SOURCE_ROOT; };
		ADFEFDB82702E3AB00B336F0 /* DetachDeviceWithoutConnect.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; name = DetachDeviceWithoutConnect.h; path = "UpDevice/updevice-toolkit-usdk/usdk/action/DetachDeviceWithoutConnect.h"; sourceTree = SOURCE_ROOT; };
		ADFEFDB92702E3AB00B336F0 /* DetachDeviceWithoutConnect.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; name = DetachDeviceWithoutConnect.m; path = "UpDevice/updevice-toolkit-usdk/usdk/action/DetachDeviceWithoutConnect.m"; sourceTree = SOURCE_ROOT; };
		B80DBE2ACF90E4399EE02E5B /* Pods-updevice_api.debug.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-updevice_api.debug.xcconfig"; path = "Target Support Files/Pods-updevice_api/Pods-updevice_api.debug.xcconfig"; sourceTree = "<group>"; };
		BF52CE274AFD8556CC64E316 /* Pods-UPDevice.release.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-UPDevice.release.xcconfig"; path = "Target Support Files/Pods-UPDevice/Pods-UPDevice.release.xcconfig"; sourceTree = "<group>"; };
		CE3BE4FF448563987E285035 /* Pods-UPDeviceDebugger.debug.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-UPDeviceDebugger.debug.xcconfig"; path = "Target Support Files/Pods-UPDeviceDebugger/Pods-UPDeviceDebugger.debug.xcconfig"; sourceTree = "<group>"; };
		DB1BFCDA2DB119A100E6815A /* UpPresenceInfo.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; name = UpPresenceInfo.h; path = UpDevice/model/UpPresenceInfo.h; sourceTree = SOURCE_ROOT; };
		DB1BFCDB2DB119A100E6815A /* UpPresenceInfo.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; name = UpPresenceInfo.m; path = UpDevice/model/UpPresenceInfo.m; sourceTree = SOURCE_ROOT; };
		DB1BFCDE2DB119BD00E6815A /* UpPopupTipInfo.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; name = UpPopupTipInfo.h; path = UpDevice/model/UpPopupTipInfo.h; sourceTree = SOURCE_ROOT; };
		DB1BFCDF2DB119BD00E6815A /* UpPopupTipInfo.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; name = UpPopupTipInfo.m; path = UpDevice/model/UpPopupTipInfo.m; sourceTree = SOURCE_ROOT; };
		DB1BFCE22DB11A1300E6815A /* UpConnectivityPresenceHandler.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; name = UpConnectivityPresenceHandler.h; path = UpDevice/UpConnectivityPresenceHandler.h; sourceTree = SOURCE_ROOT; };
		DB1BFCE32DB11A1300E6815A /* UpConnectivityPresenceHandler.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; name = UpConnectivityPresenceHandler.m; path = UpDevice/UpConnectivityPresenceHandler.m; sourceTree = SOURCE_ROOT; };
		DB4F95132DB21C6300064BC5 /* GetDeviceWifiOnlineState.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; name = GetDeviceWifiOnlineState.h; path = "UpDevice/updevice-toolkit-usdk/usdk/action/GetDeviceWifiOnlineState.h"; sourceTree = SOURCE_ROOT; };
		DB4F95142DB21C6300064BC5 /* GetDeviceWifiOnlineState.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; name = GetDeviceWifiOnlineState.m; path = "UpDevice/updevice-toolkit-usdk/usdk/action/GetDeviceWifiOnlineState.m"; sourceTree = SOURCE_ROOT; };
		DB4F95172DB23C0C00064BC5 /* IsSupportOnlyConfig.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; name = IsSupportOnlyConfig.h; path = "UpDevice/updevice-toolkit-usdk/usdk/action/IsSupportOnlyConfig.h"; sourceTree = SOURCE_ROOT; };
		DB4F95182DB23C0C00064BC5 /* IsSupportOnlyConfig.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; name = IsSupportOnlyConfig.m; path = "UpDevice/updevice-toolkit-usdk/usdk/action/IsSupportOnlyConfig.m"; sourceTree = SOURCE_ROOT; };
		DB4F951B2DB23CA600064BC5 /* IsOnlyConfigFlow.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; name = IsOnlyConfigFlow.h; path = "UpDevice/updevice-toolkit-usdk/usdk/action/IsOnlyConfigFlow.h"; sourceTree = SOURCE_ROOT; };
		DB4F951C2DB23CA600064BC5 /* IsOnlyConfigFlow.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; name = IsOnlyConfigFlow.m; path = "UpDevice/updevice-toolkit-usdk/usdk/action/IsOnlyConfigFlow.m"; sourceTree = SOURCE_ROOT; };
		EE39A92C771E28C3D6036669 /* Pods-UPDeviceDebugger.release.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-UPDeviceDebugger.release.xcconfig"; path = "Target Support Files/Pods-UPDeviceDebugger/Pods-UPDeviceDebugger.release.xcconfig"; sourceTree = "<group>"; };
		F86314A1250A01E200C948B9 /* UPDeviceDebugger.app */ = {isa = PBXFileReference; explicitFileType = wrapper.application; includeInIndex = 0; path = UPDeviceDebugger.app; sourceTree = BUILT_PRODUCTS_DIR; };
		F86314A3250A01E200C948B9 /* AppDelegate.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = AppDelegate.h; sourceTree = "<group>"; };
		F86314A4250A01E200C948B9 /* AppDelegate.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = AppDelegate.m; sourceTree = "<group>"; };
		F86314A6250A01E200C948B9 /* SceneDelegate.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = SceneDelegate.h; sourceTree = "<group>"; };
		F86314A7250A01E200C948B9 /* SceneDelegate.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = SceneDelegate.m; sourceTree = "<group>"; };
		F86314A9250A01E200C948B9 /* ViewController.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = ViewController.h; sourceTree = "<group>"; };
		F86314AA250A01E200C948B9 /* ViewController.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = ViewController.m; sourceTree = "<group>"; };
		F86314AD250A01E200C948B9 /* Base */ = {isa = PBXFileReference; lastKnownFileType = file.storyboard; name = Base; path = Base.lproj/Main.storyboard; sourceTree = "<group>"; };
		F86314AF250A01E400C948B9 /* Assets.xcassets */ = {isa = PBXFileReference; lastKnownFileType = folder.assetcatalog; path = Assets.xcassets; sourceTree = "<group>"; };
		F86314B2250A01E400C948B9 /* Base */ = {isa = PBXFileReference; lastKnownFileType = file.storyboard; name = Base; path = Base.lproj/LaunchScreen.storyboard; sourceTree = "<group>"; };
		F86314B5250A01E400C948B9 /* main.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = main.m; sourceTree = "<group>"; };
		F86314BE250A01F000C948B9 /* UPDeviceTests.xctest */ = {isa = PBXFileReference; explicitFileType = wrapper.cfbundle; includeInIndex = 0; path = UPDeviceTests.xctest; sourceTree = BUILT_PRODUCTS_DIR; };
		F86314C0250A01F000C948B9 /* CucumberRunner.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = CucumberRunner.m; sourceTree = "<group>"; };
		F86314CA250A039500C948B9 /* DeviceManagerDetectListener.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = DeviceManagerDetectListener.h; sourceTree = "<group>"; };
		F86314CB250A039500C948B9 /* DeviceManagerToolkitListener.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = DeviceManagerToolkitListener.m; sourceTree = "<group>"; };
		F86314CC250A039500C948B9 /* UpDeviceDaemon.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = UpDeviceDaemon.m; sourceTree = "<group>"; };
		F86314CD250A039500C948B9 /* DeviceManagerDetectListener.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = DeviceManagerDetectListener.m; sourceTree = "<group>"; };
		F86314CF250A039500C948B9 /* UpCompatDevice.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = UpCompatDevice.m; sourceTree = "<group>"; };
		F86314D0250A039500C948B9 /* UpCompatDeviceStore.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = UpCompatDeviceStore.h; sourceTree = "<group>"; };
		F86314D1250A039500C948B9 /* UpDeviceDataCache.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = UpDeviceDataCache.h; sourceTree = "<group>"; };
		F86314D3250A039500C948B9 /* UpCompatCommand.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = UpCompatCommand.m; sourceTree = "<group>"; };
		F86314D4250A039600C948B9 /* UpCompatCallbackWrapper.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = UpCompatCallbackWrapper.h; sourceTree = "<group>"; };
		F86314D6250A039600C948B9 /* UpCompatApi.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = UpCompatApi.h; sourceTree = "<group>"; };
		F86314D8250A039600C948B9 /* UpCompatDeviceStore.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = UpCompatDeviceStore.m; sourceTree = "<group>"; };
		F86314D9250A039600C948B9 /* UpCompatDevice.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = UpCompatDevice.h; sourceTree = "<group>"; };
		F86314DA250A039600C948B9 /* UpCompatCommand.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = UpCompatCommand.h; sourceTree = "<group>"; };
		F86314DC250A039600C948B9 /* UpDeviceDataCache.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = UpDeviceDataCache.m; sourceTree = "<group>"; };
		F86314DE250A039600C948B9 /* UpDeviceNetworkType.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = UpDeviceNetworkType.m; sourceTree = "<group>"; };
		F86314DF250A039600C948B9 /* UpDeviceType.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = UpDeviceType.m; sourceTree = "<group>"; };
		F86314E0250A039600C948B9 /* UpDeviceChangeCallback.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = UpDeviceChangeCallback.h; sourceTree = "<group>"; };
		F86314E1250A039600C948B9 /* UpSubDevChangeCallback.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = UpSubDevChangeCallback.h; sourceTree = "<group>"; };
		F86314E2250A039600C948B9 /* UpDeviceNetworkType.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = UpDeviceNetworkType.h; sourceTree = "<group>"; };
		F86314E3250A039600C948B9 /* UpDeviceType.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = UpDeviceType.h; sourceTree = "<group>"; };
		F86314E4250A039600C948B9 /* UpDeviceStatus.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = UpDeviceStatus.h; sourceTree = "<group>"; };
		F86314E6250A039600C948B9 /* UpCompatCallbackWrapper.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = UpCompatCallbackWrapper.m; sourceTree = "<group>"; };
		F86314E8250A039600C948B9 /* UpDeviceInjection.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = UpDeviceInjection.h; sourceTree = "<group>"; };
		F86314E9250A039600C948B9 /* UpDeviceDaemon.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = UpDeviceDaemon.h; sourceTree = "<group>"; };
		F86314EC250A039600C948B9 /* UpDeviceInjection.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = UpDeviceInjection.m; sourceTree = "<group>"; };
		F86314ED250A039600C948B9 /* UpDeviceManager.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = UpDeviceManager.m; sourceTree = "<group>"; };
		F86314EE250A039600C948B9 /* UpDeviceManager.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = UpDeviceManager.h; sourceTree = "<group>"; };
		F86314F0250A039600C948B9 /* UpDeviceDataSourceWrapper.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = UpDeviceDataSourceWrapper.h; sourceTree = "<group>"; };
		F86314F1250A039600C948B9 /* UpDeviceDataSourceWrapper.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = UpDeviceDataSourceWrapper.m; sourceTree = "<group>"; };
		F86314F4250A039600C948B9 /* GatewayConnection.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = GatewayConnection.h; sourceTree = "<group>"; };
		F86314F5250A039600C948B9 /* WifiDeviceType.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = WifiDeviceType.m; sourceTree = "<group>"; };
		F86314F6250A039600C948B9 /* WifiDeviceHelper.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = WifiDeviceHelper.m; sourceTree = "<group>"; };
		F86314F7250A039600C948B9 /* WifiDeviceNetworkType.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = WifiDeviceNetworkType.h; sourceTree = "<group>"; };
		F86314F9250A039600C948B9 /* GatewayMessageListener.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = GatewayMessageListener.h; sourceTree = "<group>"; };
		F86314FA250A039600C948B9 /* WifiDeviceToolkit.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = WifiDeviceToolkit.h; sourceTree = "<group>"; };
		F86314FB250A039600C948B9 /* WifiDeviceBaseInfo.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = WifiDeviceBaseInfo.h; sourceTree = "<group>"; };
		F86314FC250A039600C948B9 /* WifiDeviceToolkitImpl.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = WifiDeviceToolkitImpl.m; sourceTree = "<group>"; };
		F86314FE250A039600C948B9 /* IsModuleNeedOta.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = IsModuleNeedOta.h; sourceTree = "<group>"; };
		F86314FF250A039600C948B9 /* GetBindInfo.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = GetBindInfo.m; sourceTree = "<group>"; };
		F8631500250A039600C948B9 /* AttachResource.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = AttachResource.h; sourceTree = "<group>"; };
		F8631501250A039600C948B9 /* GetSubDevList.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = GetSubDevList.m; sourceTree = "<group>"; };
		F8631502250A039600C948B9 /* IsBound.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = IsBound.m; sourceTree = "<group>"; };
		F8631503250A039600C948B9 /* GetDeviceList.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = GetDeviceList.m; sourceTree = "<group>"; };
		F8631504250A039600C948B9 /* GetDeviceAttribute.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = GetDeviceAttribute.m; sourceTree = "<group>"; };
		F8631505250A039600C948B9 /* SmartLinkSoftwareVersion.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = SmartLinkSoftwareVersion.h; sourceTree = "<group>"; };
		F8631506250A039600C948B9 /* GetDeviceAttributes.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = GetDeviceAttributes.m; sourceTree = "<group>"; };
		F8631507250A039600C948B9 /* GetDeviceCautions.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = GetDeviceCautions.m; sourceTree = "<group>"; };
		F8631508250A039600C948B9 /* ExecuteCommand.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = ExecuteCommand.h; sourceTree = "<group>"; };
		F8631509250A039600C948B9 /* OutFocus.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = OutFocus.h; sourceTree = "<group>"; };
		F863150A250A039600C948B9 /* DetachToolkit.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = DetachToolkit.m; sourceTree = "<group>"; };
		F863150B250A039600C948B9 /* StartModuleUpdate.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = StartModuleUpdate.m; sourceTree = "<group>"; };
		F863150C250A039600C948B9 /* RefresUsdkDeviceList.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = RefresUsdkDeviceList.m; sourceTree = "<group>"; };
		F863150D250A039600C948B9 /* GetDeviceConnection.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = GetDeviceConnection.m; sourceTree = "<group>"; };
		F863150E250A039600C948B9 /* WifiToolkitAction.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = WifiToolkitAction.m; sourceTree = "<group>"; };
		F863150F250A039600C948B9 /* CancelFetchBLEHistoryData.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = CancelFetchBLEHistoryData.m; sourceTree = "<group>"; };
		F8631510250A039600C948B9 /* GetNetworkQuality.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = GetNetworkQuality.h; sourceTree = "<group>"; };
		F8631511250A039600C948B9 /* StartBoardFOTA.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = StartBoardFOTA.m; sourceTree = "<group>"; };
		F8631512250A039600C948B9 /* GetDeviceInfo.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = GetDeviceInfo.m; sourceTree = "<group>"; };
		F8631513250A039600C948B9 /* InFocus.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = InFocus.h; sourceTree = "<group>"; };
		F8631514250A039600C948B9 /* DisconnectRemoteDevices.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = DisconnectRemoteDevices.m; sourceTree = "<group>"; };
		F8631515250A039600C948B9 /* FetchBoardFOTAStatus.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = FetchBoardFOTAStatus.h; sourceTree = "<group>"; };
		F8631516250A039600C948B9 /* WifiDeviceAction.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = WifiDeviceAction.m; sourceTree = "<group>"; };
		F8631517250A039600C948B9 /* AttachDevice.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = AttachDevice.h; sourceTree = "<group>"; };
		F8631518250A039600C948B9 /* CheckBoardFOTAInfo.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = CheckBoardFOTAInfo.h; sourceTree = "<group>"; };
		F8631519250A039600C948B9 /* DetachResource.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = DetachResource.h; sourceTree = "<group>"; };
		F863151A250A039600C948B9 /* GetSubDevListBySubDevice.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = GetSubDevListBySubDevice.h; sourceTree = "<group>"; };
		F863151B250A039600C948B9 /* DetachDevice.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = DetachDevice.h; sourceTree = "<group>"; };
		F863151C250A039600C948B9 /* AttachToolkit.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = AttachToolkit.m; sourceTree = "<group>"; };
		F863151D250A039600C948B9 /* ConnectRemoteDevices.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = ConnectRemoteDevices.m; sourceTree = "<group>"; };
		F863151E250A039600C948B9 /* FetchBLEHistoryData.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = FetchBLEHistoryData.m; sourceTree = "<group>"; };
		F863151F250A039600C948B9 /* GetDeviceAttribute.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = GetDeviceAttribute.h; sourceTree = "<group>"; };
		F8631520250A039600C948B9 /* GetDeviceList.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = GetDeviceList.h; sourceTree = "<group>"; };
		F8631521250A039600C948B9 /* IsBound.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = IsBound.h; sourceTree = "<group>"; };
		F8631522250A039600C948B9 /* GetSubDevList.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = GetSubDevList.h; sourceTree = "<group>"; };
		F8631523250A039600C948B9 /* AttachResource.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = AttachResource.m; sourceTree = "<group>"; };
		F8631524250A039600C948B9 /* GetBindInfo.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = GetBindInfo.h; sourceTree = "<group>"; };
		F8631525250A039600C948B9 /* IsModuleNeedOta.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = IsModuleNeedOta.m; sourceTree = "<group>"; };
		F8631526250A039600C948B9 /* OutFocus.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = OutFocus.m; sourceTree = "<group>"; };
		F8631527250A039600C948B9 /* DetachToolkit.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = DetachToolkit.h; sourceTree = "<group>"; };
		F8631528250A039600C948B9 /* ExecuteCommand.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = ExecuteCommand.m; sourceTree = "<group>"; };
		F8631529250A039600C948B9 /* GetDeviceAttributes.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = GetDeviceAttributes.h; sourceTree = "<group>"; };
		F863152A250A039600C948B9 /* GetDeviceCautions.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = GetDeviceCautions.h; sourceTree = "<group>"; };
		F863152B250A039600C948B9 /* SmartLinkSoftwareVersion.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = SmartLinkSoftwareVersion.m; sourceTree = "<group>"; };
		F863152C250A039600C948B9 /* DisconnectRemoteDevices.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = DisconnectRemoteDevices.h; sourceTree = "<group>"; };
		F863152D250A039600C948B9 /* InFocus.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = InFocus.m; sourceTree = "<group>"; };
		F863152E250A039600C948B9 /* GetNetworkQuality.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = GetNetworkQuality.m; sourceTree = "<group>"; };
		F863152F250A039600C948B9 /* GetDeviceInfo.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = GetDeviceInfo.h; sourceTree = "<group>"; };
		F8631530250A039600C948B9 /* StartBoardFOTA.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = StartBoardFOTA.h; sourceTree = "<group>"; };
		F8631531250A039600C948B9 /* CancelFetchBLEHistoryData.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = CancelFetchBLEHistoryData.h; sourceTree = "<group>"; };
		F8631532250A039600C948B9 /* WifiToolkitAction.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = WifiToolkitAction.h; sourceTree = "<group>"; };
		F8631533250A039600C948B9 /* GetDeviceConnection.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = GetDeviceConnection.h; sourceTree = "<group>"; };
		F8631534250A039600C948B9 /* RefresUsdkDeviceList.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = RefresUsdkDeviceList.h; sourceTree = "<group>"; };
		F8631535250A039600C948B9 /* StartModuleUpdate.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = StartModuleUpdate.h; sourceTree = "<group>"; };
		F8631536250A039600C948B9 /* FetchBLEHistoryData.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = FetchBLEHistoryData.h; sourceTree = "<group>"; };
		F8631537250A039600C948B9 /* ConnectRemoteDevices.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = ConnectRemoteDevices.h; sourceTree = "<group>"; };
		F8631538250A039600C948B9 /* AttachToolkit.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = AttachToolkit.h; sourceTree = "<group>"; };
		F8631539250A039600C948B9 /* DetachDevice.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = DetachDevice.m; sourceTree = "<group>"; };
		F863153A250A039600C948B9 /* DetachResource.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = DetachResource.m; sourceTree = "<group>"; };
		F863153B250A039600C948B9 /* GetSubDevListBySubDevice.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = GetSubDevListBySubDevice.m; sourceTree = "<group>"; };
		F863153C250A039600C948B9 /* CheckBoardFOTAInfo.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = CheckBoardFOTAInfo.m; sourceTree = "<group>"; };
		F863153D250A039600C948B9 /* FetchBoardFOTAStatus.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = FetchBoardFOTAStatus.m; sourceTree = "<group>"; };
		F863153E250A039600C948B9 /* WifiDeviceAction.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = WifiDeviceAction.h; sourceTree = "<group>"; };
		F863153F250A039600C948B9 /* AttachDevice.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = AttachDevice.m; sourceTree = "<group>"; };
		F8631540250A039600C948B9 /* WifiDeviceHelper.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = WifiDeviceHelper.h; sourceTree = "<group>"; };
		F8631541250A039600C948B9 /* WifiDeviceType.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = WifiDeviceType.h; sourceTree = "<group>"; };
		F8631543250A039600C948B9 /* MessageListener.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = MessageListener.h; sourceTree = "<group>"; };
		F8631544250A039600C948B9 /* WifiDeviceToolkitImpl.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = WifiDeviceToolkitImpl.h; sourceTree = "<group>"; };
		F8631545250A039600C948B9 /* DeviceManagerToolkitListener.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = DeviceManagerToolkitListener.h; sourceTree = "<group>"; };
		F8631547250A039700C948B9 /* DummyDeviceToolkit.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = DummyDeviceToolkit.h; sourceTree = "<group>"; };
		F8631548250A039700C948B9 /* DummyDeviceBroker.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = DummyDeviceBroker.h; sourceTree = "<group>"; };
		F8631549250A039700C948B9 /* DummyDeviceToolkit.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = DummyDeviceToolkit.m; sourceTree = "<group>"; };
		F863154A250A039700C948B9 /* DummyDeviceBroker.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = DummyDeviceBroker.m; sourceTree = "<group>"; };
		F863154C250A039700C948B9 /* UpEngineReloadHandler.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = UpEngineReloadHandler.h; sourceTree = "<group>"; };
		F863154D250A039700C948B9 /* UpEngineDataSource.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = UpEngineDataSource.h; sourceTree = "<group>"; };
		F863154E250A039700C948B9 /* UpLogicEngineApi.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = UpLogicEngineApi.h; sourceTree = "<group>"; };
		F863154F250A039700C948B9 /* UpEngineDevice.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = UpEngineDevice.h; sourceTree = "<group>"; };
		F8631550250A039700C948B9 /* UpEngineDevice+EngineDelegate.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = "UpEngineDevice+EngineDelegate.m"; sourceTree = "<group>"; };
		F8631551250A039700C948B9 /* UpEngineException.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = UpEngineException.m; sourceTree = "<group>"; };
		F8631552250A039700C948B9 /* UpEngineDevice+ConfigSource.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = "UpEngineDevice+ConfigSource.m"; sourceTree = "<group>"; };
		F8631553250A039700C948B9 /* UpEngineReporter.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = UpEngineReporter.m; sourceTree = "<group>"; };
		F8631554250A039700C948B9 /* UpEngineDeviceFactory.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = UpEngineDeviceFactory.h; sourceTree = "<group>"; };
		F8631555250A039700C948B9 /* UpEngineException.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = UpEngineException.h; sourceTree = "<group>"; };
		F8631556250A039700C948B9 /* UpEngineDevice+EngineDelegate.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = "UpEngineDevice+EngineDelegate.h"; sourceTree = "<group>"; };
		F8631557250A039700C948B9 /* UpEngineDevice.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = UpEngineDevice.m; sourceTree = "<group>"; };
		F8631558250A039700C948B9 /* UpEngineDataSource.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = UpEngineDataSource.m; sourceTree = "<group>"; };
		F8631559250A039700C948B9 /* UpEngineReporter.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = UpEngineReporter.h; sourceTree = "<group>"; };
		F863155A250A039700C948B9 /* UpEngineDevice+ConfigSource.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = "UpEngineDevice+ConfigSource.h"; sourceTree = "<group>"; };
		F863155B250A039700C948B9 /* UpEngineDeviceFactory.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = UpEngineDeviceFactory.m; sourceTree = "<group>"; };
		FEF68A3D049F1359CACD4BF1 /* libPods-updevice_api.a */ = {isa = PBXFileReference; explicitFileType = archive.ar; includeInIndex = 0; path = "libPods-updevice_api.a"; sourceTree = BUILT_PRODUCTS_DIR; };
/* End PBXFileReference section */

/* Begin PBXFrameworksBuildPhase section */
		AD8DD0FC2643E48600086CBF /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		AD8DD10F2643E5A300086CBF /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
				AD98A5A6265B773300EDAEA3 /* libupdevice_api.a in Frameworks */,
				AD8DD1A62643E68D00086CBF /* libUPDeviceUtil.a in Frameworks */,
				54A04299AC185BCD88CF75A6 /* libPods-UPDevice.a in Frameworks */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		AD98A4FD265B74C700EDAEA3 /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
				9B98655ABC783C3C463E125F /* libPods-updevice_api.a in Frameworks */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		F863149E250A01E200C948B9 /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
				AD8DD1A72643E69500086CBF /* libUPDevice.a in Frameworks */,
				69989D04D2CC7EB9FCA54A2A /* libPods-UPDeviceDebugger.a in Frameworks */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		F86314BB250A01F000C948B9 /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
				AD8DD1A82643E69B00086CBF /* libUPDevice.a in Frameworks */,
				086A596B6C6F3D4EA7B23177 /* libPods-UPDeviceTests.a in Frameworks */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXFrameworksBuildPhase section */

/* Begin PBXGroup section */
		04F426D72A3953BC0068FCC7 /* gio */ = {
			isa = PBXGroup;
			children = (
				04F426D82A3954290068FCC7 /* UpDeviceDaemonGIO.h */,
				04F426D92A3954290068FCC7 /* UpDeviceDaemonGIO.m */,
			);
			name = gio;
			path = UpDevice/gio;
			sourceTree = SOURCE_ROOT;
		};
		33FDA61626285A690099964E /* wash */ = {
			isa = PBXGroup;
			children = (
				33FDA61926285A690099964E /* UpWashAdapterApi.h */,
				33FDA61D26285A690099964E /* UpWashAdapterApi.m */,
				33FDA778262ECA0E0099964E /* UpWashDataResponseParser.h */,
				33FDA779262ECA0E0099964E /* UpWashDataResponseParser.m */,
				33FDA61B26285A690099964E /* UpWashDevice.h */,
				33FDA61826285A690099964E /* UpWashDevice.m */,
				33FDA61E26285A690099964E /* UpWashModel.h */,
				33FDA61A26285A690099964E /* UpWashModel.m */,
				A027753E298A39090014920F /* UpWashDeviceManager.h */,
				A027753F298A39090014920F /* UpWashDeviceManager.m */,
			);
			name = wash;
			path = UpDevice/wash;
			sourceTree = SOURCE_ROOT;
		};
		33FDA765262D615C0099964E /* voicebox */ = {
			isa = PBXGroup;
			children = (
				33FDA766262D619B0099964E /* UpVoiceBoxDevice.h */,
				33FDA767262D619B0099964E /* UpVoiceBoxDevice.m */,
			);
			path = voicebox;
			sourceTree = "<group>";
		};
		4F5ED4DB2C72F15500119B62 /* updevice-non-network */ = {
			isa = PBXGroup;
			children = (
				4F5ED4DC2C72F15500119B62 /* UpNonNetworkDeviceFactory.h */,
				4F5ED4DD2C72F15500119B62 /* UPNonNetworkDevice.h */,
				4F5ED4DE2C72F15500119B62 /* UPNonNetworkDevice.m */,
				4F5ED4DF2C72F15500119B62 /* UpNonNetworkDeviceFactory.m */,
			);
			name = "updevice-non-network";
			path = "UpDevice/updevice-non-network";
			sourceTree = SOURCE_ROOT;
		};
		4F7EB08E2D952263007694FE /* model */ = {
			isa = PBXGroup;
			children = (
				4F7EB08F2D9524AC007694FE /* UpDeviceCardInfo.h */,
				4F7EB0902D9524AC007694FE /* UpDeviceCardInfo.m */,
				DB1BFCDA2DB119A100E6815A /* UpPresenceInfo.h */,
				DB1BFCDB2DB119A100E6815A /* UpPresenceInfo.m */,
				DB1BFCDE2DB119BD00E6815A /* UpPopupTipInfo.h */,
				DB1BFCDF2DB119BD00E6815A /* UpPopupTipInfo.m */,
			);
			path = model;
			sourceTree = "<group>";
		};
		4F9B18742D7E89B100C62B67 /* updevice-aggregate */ = {
			isa = PBXGroup;
			children = (
				4F9B18772D7E8B6200C62B67 /* UPAggregateDevice.h */,
				4F9B18782D7E8B6200C62B67 /* UPAggregateDevice.m */,
				4F9B187B2D7E8CA200C62B67 /* UPAggregateDeviceFactory.h */,
				4F9B187C2D7E8CA200C62B67 /* UPAggregateDeviceFactory.m */,
			);
			path = "updevice-aggregate";
			sourceTree = "<group>";
		};
		973D230122D4C635676D224E /* Frameworks */ = {
			isa = PBXGroup;
			children = (
				6A009374069A3438B0417DEB /* libPods-UPDevice.a */,
				6487A8411EC9F1313E98D8F1 /* libPods-UPDeviceDebugger.a */,
				5A394E3B1310D11A99A55CD4 /* libPods-UPDeviceTests.a */,
				FEF68A3D049F1359CACD4BF1 /* libPods-updevice_api.a */,
			);
			name = Frameworks;
			sourceTree = "<group>";
		};
		AD98AB60265B8B7A00EDAEA3 /* updevice_api */ = {
			isa = PBXGroup;
			children = (
				AD98AB61265B8B7A00EDAEA3 /* updevice_core */,
				AD98AB99265B8B7A00EDAEA3 /* updevice_api.h */,
				AD98AB9A265B8B7A00EDAEA3 /* updevice_common */,
				AD98ABCF265B8B7B00EDAEA3 /* updevice_source */,
				AD98ABD2265B8B7B00EDAEA3 /* updevice_toolkit */,
				AD98ABDB265B8B7B00EDAEA3 /* updevice_log */,
			);
			path = updevice_api;
			sourceTree = "<group>";
		};
		AD98AB61265B8B7A00EDAEA3 /* updevice_core */ = {
			isa = PBXGroup;
			children = (
				AD98AB62265B8B7A00EDAEA3 /* broker */,
				AD98AB7B265B8B7A00EDAEA3 /* device */,
				AD98AB98265B8B7A00EDAEA3 /* UpDeviceCenter.h */,
				AD98AB74265B8B7A00EDAEA3 /* UpDeviceCreator.h */,
				AD98AB97265B8B7A00EDAEA3 /* UpDeviceCreator.m */,
				AD98AB73265B8B7A00EDAEA3 /* UpDeviceFactory.h */,
				AD98AB72265B8B7A00EDAEA3 /* UpDeviceFilter.h */,
				AD98AB75265B8B7A00EDAEA3 /* UpDeviceFilters.h */,
				AD98AB96265B8B7A00EDAEA3 /* UpDeviceFilters.m */,
			);
			path = updevice_core;
			sourceTree = "<group>";
		};
		AD98AB62265B8B7A00EDAEA3 /* broker */ = {
			isa = PBXGroup;
			children = (
				AD98AB63265B8B7A00EDAEA3 /* impl */,
				AD98AB70265B8B7A00EDAEA3 /* UpDeviceBroker.h */,
				AD98AB71265B8B7A00EDAEA3 /* UpDeviceBrokerHolder.h */,
			);
			path = broker;
			sourceTree = "<group>";
		};
		AD98AB63265B8B7A00EDAEA3 /* impl */ = {
			isa = PBXGroup;
			children = (
				AD98AB6D265B8B7A00EDAEA3 /* DefaultBrokerHolder.h */,
				AD98AB65265B8B7A00EDAEA3 /* DefaultBrokerHolder.m */,
				AD98AB69265B8B7A00EDAEA3 /* DefaultDetectListener.h */,
				AD98AB6F265B8B7A00EDAEA3 /* DefaultDetectListener.m */,
				AD98AB6A265B8B7A00EDAEA3 /* DefaultDeviceBroker.h */,
				AD98AB67265B8B7A00EDAEA3 /* DefaultDeviceBroker.m */,
				AD98AB64265B8B7A00EDAEA3 /* DefaultNotification.h */,
				AD98AB6C265B8B7A00EDAEA3 /* DefaultNotification.m */,
				AD98AB66265B8B7A00EDAEA3 /* DefaultReportListener.h */,
				AD98AB6B265B8B7A00EDAEA3 /* DefaultReportListener.m */,
				AD98AB6E265B8B7A00EDAEA3 /* DefaultToolkitListener.h */,
				AD98AB68265B8B7A00EDAEA3 /* DefaultToolkitListener.m */,
			);
			path = impl;
			sourceTree = "<group>";
		};
		AD98AB7B265B8B7A00EDAEA3 /* device */ = {
			isa = PBXGroup;
			children = (
				8457E1852D8AC31B007EBE53 /* UpDeviceGroupOptDeleagte.h */,
				8457E1862D8AC31B007EBE53 /* UpDeviceReportMonitor.h */,
				8457E1872D8AC31B007EBE53 /* UpDeviceReportMonitor.m */,
				8412D69A2D7F0C02002B44F7 /* UpDeviceCacheManager.h */,
				8412D69B2D7F0C02002B44F7 /* UpDeviceCacheManager.m */,
				AD98AB92265B8B7A00EDAEA3 /* process */,
				AD98AB7E265B8B7A00EDAEA3 /* UpCommonDevice.h */,
				AD98AB85265B8B7A00EDAEA3 /* UpCommonDevice.m */,
				AD98AB88265B8B7A00EDAEA3 /* UpDevice.h */,
				AD98AB80265B8B7A00EDAEA3 /* UpDeviceBase.h */,
				AD98AB8D265B8B7A00EDAEA3 /* UpDeviceBase.m */,
				22F785FB265DE20F00175218 /* UpAttachResourceDelegate.h */,
				AD98AB7F265B8B7A00EDAEA3 /* UpDeviceBLE.h */,
				AD98AB84265B8B7A00EDAEA3 /* UpDeviceCache.h */,
				AD98AB91265B8B7A00EDAEA3 /* UpDeviceCache.m */,
				AD98AB86265B8B7A00EDAEA3 /* UpDeviceConfigState.h */,
				AD98AB8A265B8B7A00EDAEA3 /* UpDeviceFocus.h */,
				AD98AB8E265B8B7A00EDAEA3 /* UpDeviceFOTA.h */,
				AD98AB87265B8B7A00EDAEA3 /* UpDeviceListener.h */,
				AD98AB7D265B8B7A00EDAEA3 /* UPDeviceNetType.h */,
				AD98AB8B265B8B7A00EDAEA3 /* UpDeviceNetWorkQuality.h */,
				AD98AB7C265B8B7A00EDAEA3 /* UpDeviceReceiver.h */,
				AD98AB8C265B8B7A00EDAEA3 /* UpDeviceReporter.h */,
				AD98AB81265B8B7A00EDAEA3 /* UpDeviceReporter.m */,
				AD98AB90265B8B7A00EDAEA3 /* UpDeviceState.h */,
				AD98AB89265B8B7A00EDAEA3 /* UpDeviceStore.h */,
				AD98AB82265B8B7A00EDAEA3 /* UpDeviceSub.h */,
				AD98AB83265B8B7A00EDAEA3 /* UpExtendDevice.h */,
				84AEE78A2DA65221008FF4C7 /* uSDKDeviceWrapper.h */,
				84AEE78B2DA65221008FF4C7 /* uSDKDeviceWrapper.m */,
			);
			path = device;
			sourceTree = "<group>";
		};
		AD98AB92265B8B7A00EDAEA3 /* process */ = {
			isa = PBXGroup;
			children = (
				AD98AB93265B8B7A00EDAEA3 /* UpCompositeProcessor.h */,
				AD98AB94265B8B7A00EDAEA3 /* UpCompositeProcessor.m */,
				AD98AB95265B8B7A00EDAEA3 /* UpDeviceProcessor.h */,
			);
			path = process;
			sourceTree = "<group>";
		};
		AD98AB9A265B8B7A00EDAEA3 /* updevice_common */ = {
			isa = PBXGroup;
			children = (
				AD98AB9B265B8B7A00EDAEA3 /* entity */,
				AD98ABBF265B8B7B00EDAEA3 /* common */,
				AD98ABCC265B8B7B00EDAEA3 /* exception */,
			);
			path = updevice_common;
			sourceTree = "<group>";
		};
		AD98AB9B265B8B7A00EDAEA3 /* entity */ = {
			isa = PBXGroup;
			children = (
				AD98AB9C265B8B7A00EDAEA3 /* impl */,
				8466DEA12CCF32F200DFDA87 /* UpDeviceOnlyConfigState.h */,
				AD98ABB3265B8B7A00EDAEA3 /* UpDeviceAttribute.h */,
				AD98ABBA265B8B7B00EDAEA3 /* UpDeviceBaseInfo.h */,
				4F22A57D2AFDCB4500269406 /* UpDeviceOfflineCause.h */,
				AD98ABBC265B8B7B00EDAEA3 /* UpDeviceBLEHistoryInfo.h */,
				AD98ABBE265B8B7B00EDAEA3 /* UpDeviceCaution.h */,
				AD98ABB7265B8B7B00EDAEA3 /* UpDeviceCommand.h */,
				AD98ABBB265B8B7B00EDAEA3 /* UpDeviceConnection.h */,
				AD98ABB9265B8B7B00EDAEA3 /* UpDeviceExtras.h */,
				AD98ABB4265B8B7B00EDAEA3 /* UpDeviceFOTAInfo.h */,
				AD98ABB6265B8B7B00EDAEA3 /* UpDeviceFOTAStatusInfo.h */,
				AD98ABB8265B8B7B00EDAEA3 /* UpDeviceInfo.h */,
				AD98ABBD265B8B7B00EDAEA3 /* UpDeviceNetWorkQualityInfo.h */,
				AD98ABB5265B8B7B00EDAEA3 /* UpDeviceOTAStatusInfo.h */,
				041D69E0270302EB00833105 /* UpDeviceControlState.h */,
				AD0A1CCB2713DB2600415B3D /* UpDeviceTracker.h */,
				04E3E60327182E6B00D1DDBE /* UpDeviceOnlineStatus.h */,
				4AC22C062754CBAE00BC7418 /* UpDeviceOnlineStateV2.h */,
				A0CE395227576C8D003FD92C /* UpDeviceNetworkLevel.h */,
				A0D3FEB627E42F0800DA145B /* UpDeviceQCConnectTimeoutType.h */,
			);
			path = entity;
			sourceTree = "<group>";
		};
		AD98AB9C265B8B7A00EDAEA3 /* impl */ = {
			isa = PBXGroup;
			children = (
				AD98ABAB265B8B7A00EDAEA3 /* DeviceAttribute.h */,
				AD98ABA0265B8B7A00EDAEA3 /* DeviceAttribute.m */,
				AD98ABA8265B8B7A00EDAEA3 /* DeviceBaseInfo.h */,
				AD98AB9D265B8B7A00EDAEA3 /* DeviceBaseInfo.m */,
				AD98ABA2265B8B7A00EDAEA3 /* DeviceBLEHistoryInfo.h */,
				AD98ABAE265B8B7A00EDAEA3 /* DeviceBLEHistoryInfo.m */,
				AD98ABA4265B8B7A00EDAEA3 /* DeviceCaution.h */,
				AD98ABB1265B8B7A00EDAEA3 /* DeviceCaution.m */,
				AD98ABB2265B8B7A00EDAEA3 /* DeviceCommand.h */,
				AD98ABA5265B8B7A00EDAEA3 /* DeviceCommand.m */,
				AD98ABA6265B8B7A00EDAEA3 /* DeviceExtras.h */,
				AD98ABB0265B8B7A00EDAEA3 /* DeviceExtras.m */,
				AD98ABA1265B8B7A00EDAEA3 /* DeviceFOTAInfo.h */,
				AD98ABA9265B8B7A00EDAEA3 /* DeviceFOTAInfo.m */,
				AD98ABAD265B8B7A00EDAEA3 /* DeviceFOTAStatusInfo.h */,
				AD98ABA3265B8B7A00EDAEA3 /* DeviceFOTAStatusInfo.m */,
				AD98ABA7265B8B7A00EDAEA3 /* DeviceInfo.h */,
				AD98ABAF265B8B7A00EDAEA3 /* DeviceInfo.m */,
				AD98ABAA265B8B7A00EDAEA3 /* DeviceNetWorkQualityInfo.h */,
				AD98AB9F265B8B7A00EDAEA3 /* DeviceNetWorkQualityInfo.m */,
				AD98AB9E265B8B7A00EDAEA3 /* DeviceOTAStatusInfo.h */,
				AD98ABAC265B8B7A00EDAEA3 /* DeviceOTAStatusInfo.m */,
			);
			path = impl;
			sourceTree = "<group>";
		};
		AD98ABBF265B8B7B00EDAEA3 /* common */ = {
			isa = PBXGroup;
			children = (
				AD98ABC9265B8B7B00EDAEA3 /* UpDeviceAction.h */,
				AD98ABC1265B8B7B00EDAEA3 /* UpDeviceAction.m */,
				AD98ABC7265B8B7B00EDAEA3 /* UpDeviceCarrier.h */,
				AD98ABC3265B8B7B00EDAEA3 /* UpDeviceCarrier.m */,
				AD98ABC4265B8B7B00EDAEA3 /* UpDeviceExecutable.h */,
				AD98ABCB265B8B7B00EDAEA3 /* UpDeviceHelper.h */,
				AD98ABC5265B8B7B00EDAEA3 /* UpDeviceHelper.m */,
				AD98ABC8265B8B7B00EDAEA3 /* UpDeviceResult.h */,
				AD98ABC2265B8B7B00EDAEA3 /* UpDeviceResult.m */,
				AD98ABC0265B8B7B00EDAEA3 /* UpDeviceSetMap.h */,
				AD98ABC6265B8B7B00EDAEA3 /* UpStringResult.h */,
				AD98ABCA265B8B7B00EDAEA3 /* UpStringResult.m */,
			);
			path = common;
			sourceTree = "<group>";
		};
		AD98ABCC265B8B7B00EDAEA3 /* exception */ = {
			isa = PBXGroup;
			children = (
				AD98ABCD265B8B7B00EDAEA3 /* UpDeviceException.h */,
				AD98ABCE265B8B7B00EDAEA3 /* UpDeviceException.m */,
			);
			path = exception;
			sourceTree = "<group>";
		};
		AD98ABCF265B8B7B00EDAEA3 /* updevice_source */ = {
			isa = PBXGroup;
			children = (
				AD98ABD0265B8B7B00EDAEA3 /* source */,
			);
			path = updevice_source;
			sourceTree = "<group>";
		};
		AD98ABD0265B8B7B00EDAEA3 /* source */ = {
			isa = PBXGroup;
			children = (
				AD98ABD1265B8B7B00EDAEA3 /* UpDeviceDataSource.h */,
			);
			path = source;
			sourceTree = "<group>";
		};
		AD98ABD2265B8B7B00EDAEA3 /* updevice_toolkit */ = {
			isa = PBXGroup;
			children = (
				AD98ABD3265B8B7B00EDAEA3 /* toolkit */,
			);
			path = updevice_toolkit;
			sourceTree = "<group>";
		};
		AD98ABD3265B8B7B00EDAEA3 /* toolkit */ = {
			isa = PBXGroup;
			children = (
				AD98ABDA265B8B7B00EDAEA3 /* EmptyDeviceToolkit.h */,
				AD98ABD7265B8B7B00EDAEA3 /* EmptyDeviceToolkit.m */,
				AD98ABD6265B8B7B00EDAEA3 /* UpDeviceDetectListener.h */,
				AD98ABD4265B8B7B00EDAEA3 /* UpDeviceReportListener.h */,
				AD98ABD9265B8B7B00EDAEA3 /* UpDeviceToolkit.h */,
				AD98ABD5265B8B7B00EDAEA3 /* UpDeviceToolkitListener.h */,
				AD98ABD8265B8B7B00EDAEA3 /* UpDeviceToolkitState.h */,
			);
			path = toolkit;
			sourceTree = "<group>";
		};
		AD98ABDB265B8B7B00EDAEA3 /* updevice_log */ = {
			isa = PBXGroup;
			children = (
				AD98ABDC265B8B7B00EDAEA3 /* UPDeviceLog.h */,
			);
			path = updevice_log;
			sourceTree = "<group>";
		};
		ADF4AAFB26242A700056F104 /* UPDeviceUtil */ = {
			isa = PBXGroup;
			children = (
				A0C41BCE2949D8C700A877F9 /* UpDeviceGetDeviceResourceTool.h */,
				A0C41BCF2949D8C700A877F9 /* UpDeviceGetDeviceResourceTool.m */,
				40E0AC4825148816007DBC42 /* UDSafeMutableArray.h */,
				40E0AC4525148816007DBC42 /* UDSafeMutableArray.m */,
				40E0AC4625148816007DBC42 /* UDSafeMutableDictionary.h */,
				40E0AC4725148816007DBC42 /* UDSafeMutableDictionary.m */,
				A01559DD280821DE00723EDC /* UpDeviceObjectCommonHelper.h */,
				A01559DE280821DE00723EDC /* UpDeviceObjectCommonHelper.m */,
				ADF4AAFD26242A700056F104 /* Info.plist */,
				A0C41BDC294B297600A877F9 /* UpDeviceStringCommonHelper.h */,
				A0C41BDD294B297600A877F9 /* UpDeviceStringCommonHelper.m */,
			);
			path = UPDeviceUtil;
			sourceTree = "<group>";
		};
		ADF4AB0826242A700056F104 /* UPDeviceUtilTests */ = {
			isa = PBXGroup;
			children = (
				ADF4AB0926242A700056F104 /* UPDeviceUtilTests.m */,
				ADF4AB0B26242A700056F104 /* Info.plist */,
			);
			path = UPDeviceUtilTests;
			sourceTree = "<group>";
		};
		C8EEF11D9B635CA218EE6D74 /* Pods */ = {
			isa = PBXGroup;
			children = (
				59F3630A2EB70C2F08991990 /* Pods-UPDevice.debug.xcconfig */,
				BF52CE274AFD8556CC64E316 /* Pods-UPDevice.release.xcconfig */,
				CE3BE4FF448563987E285035 /* Pods-UPDeviceDebugger.debug.xcconfig */,
				EE39A92C771E28C3D6036669 /* Pods-UPDeviceDebugger.release.xcconfig */,
				88ED39B27363FAF9CE965C37 /* Pods-UPDeviceTests.debug.xcconfig */,
				8A6C8EB9EC9E184964C16C36 /* Pods-UPDeviceTests.release.xcconfig */,
				B80DBE2ACF90E4399EE02E5B /* Pods-updevice_api.debug.xcconfig */,
				633BDFCF19857B79B382F7E8 /* Pods-updevice_api.release.xcconfig */,
			);
			path = Pods;
			sourceTree = "<group>";
		};
		F86162E325942E760053904B /* FakeDelegate */ = {
			isa = PBXGroup;
			children = (
				22F785EF265D022D00175218 /* FakeDelegateBase.h */,
				22F785F0265D022D00175218 /* FakeDelegateBase.m */,
				2232841D25C01B74001CBE68 /* FakeUpDeviceBase.h */,
				2232841925C01B73001CBE68 /* FakeUpDeviceBase.m */,
				2232841A25C01B73001CBE68 /* FakeUpDeviceListener.h */,
				2232841E25C01B74001CBE68 /* FakeUpDeviceListener.m */,
				2232841C25C01B74001CBE68 /* FakeUpDeviceReceiver.h */,
				2232841B25C01B73001CBE68 /* FakeUpDeviceReceiver.m */,
				225B08EC25BAA53900E5E86A /* FakeDelegateListener.h */,
				225B08ED25BAA53900E5E86A /* FakeDelegateListener.m */,
				2232840925BEA9A4001CBE68 /* CustomFilterProvider.h */,
				2232840A25BEA9A4001CBE68 /* CustomFilterProvider.m */,
				221C2A2225C14D3D00D68224 /* FakeDelegateFactory.h */,
				221C2A2325C14D3D00D68224 /* FakeDelegateFactory.m */,
				22A5225925C3EDB000E76C88 /* FakeUSDKDevice.h */,
				22A5225A25C3EDB000E76C88 /* FakeUSDKDevice.m */,
				22344EC1262FC0FA00FF4ACA /* FakeReportListener.h */,
				22344EC2262FC0FA00FF4ACA /* FakeReportListener.m */,
				22344EC42630102600FF4ACA /* FakeDetectListener.h */,
				22344EC52630102600FF4ACA /* FakeDetectListener.m */,
				22344EC72631833300FF4ACA /* FakeLEEngineListener.h */,
				22344EC82631833300FF4ACA /* FakeLEEngineListener.m */,
				A0CFB3AB282CFBDB007E27BA /* FakeResourceManager.h */,
				A0CFB3AC282CFBDC007E27BA /* FakeResourceManager.m */,
				A0277545298A3DC80014920F /* FakeWashdapterApi.h */,
				A0277546298A3DC80014920F /* FakeWashdapterApi.m */,
			);
			path = FakeDelegate;
			sourceTree = "<group>";
		};
		F86162E425942E760053904B /* Steps */ = {
			isa = PBXGroup;
			children = (
				22F785E0265CF88B00175218 /* DeviceBaseSteps.h */,
				22F785E3265CF88B00175218 /* DeviceBaseSteps.m */,
				22F785E2265CF88B00175218 /* DeviceManagerSteps.h */,
				22F785D9265CF88B00175218 /* DeviceManagerSteps.m */,
				22F785DD265CF88B00175218 /* EngineDeviceSteps.h */,
				22F785E1265CF88B00175218 /* EngineDeviceSteps.m */,
				22F785D8265CF88B00175218 /* InitializationSteps.h */,
				22F785DF265CF88B00175218 /* InitializationSteps.m */,
				22F785DA265CF88B00175218 /* WashDeviceSteps.h */,
				22F785DB265CF88B00175218 /* WashDeviceSteps.m */,
				22F785DC265CF88B00175218 /* WifiDeviceToolkitSteps.h */,
				22F785DE265CF88B00175218 /* WifiDeviceToolkitSteps.m */,
				A0C41BE2294C11C400A877F9 /* UpDeviceResourceSteps.h */,
				A0C41BE0294C11BA00A877F9 /* UpDeviceResourceSteps.m */,
				A0277542298A3D760014920F /* WashDeviceManagerSteps.h */,
				A0277543298A3D760014920F /* WashDeviceManagerSteps.m */,
			);
			path = Steps;
			sourceTree = "<group>";
		};
		F86162E825942EFF0053904B /* Untils */ = {
			isa = PBXGroup;
			children = (
				22F785EB265CF89C00175218 /* StepsUtils.h */,
				22F785EA265CF89C00175218 /* StepsUtils.m */,
			);
			name = Untils;
			path = UPDeviceTests/Untils;
			sourceTree = SOURCE_ROOT;
		};
		F8631488250A018800C948B9 = {
			isa = PBXGroup;
			children = (
				22F48A7B25F7079D0007F76E /* UPDevice.podspec */,
				F8631494250A018800C948B9 /* UPDevice */,
				F86314A2250A01E200C948B9 /* UPDeviceDebugger */,
				F86314BF250A01F000C948B9 /* UPDeviceTests */,
				ADF4AAFB26242A700056F104 /* UPDeviceUtil */,
				AD98AB60265B8B7A00EDAEA3 /* updevice_api */,
				ADF4AB0826242A700056F104 /* UPDeviceUtilTests */,
				F8631493250A018800C948B9 /* Products */,
				C8EEF11D9B635CA218EE6D74 /* Pods */,
				973D230122D4C635676D224E /* Frameworks */,
			);
			sourceTree = "<group>";
		};
		F8631493250A018800C948B9 /* Products */ = {
			isa = PBXGroup;
			children = (
				F86314A1250A01E200C948B9 /* UPDeviceDebugger.app */,
				F86314BE250A01F000C948B9 /* UPDeviceTests.xctest */,
				AD8DD0FF2643E48600086CBF /* libUPDeviceUtil.a */,
				AD8DD1122643E5A300086CBF /* libUPDevice.a */,
				AD98A500265B74C700EDAEA3 /* libupdevice_api.a */,
			);
			name = Products;
			sourceTree = "<group>";
		};
		F8631494250A018800C948B9 /* UPDevice */ = {
			isa = PBXGroup;
			children = (
				4F7EB08E2D952263007694FE /* model */,
				4F9B18742D7E89B100C62B67 /* updevice-aggregate */,
				4F5ED4DB2C72F15500119B62 /* updevice-non-network */,
				04F426D72A3953BC0068FCC7 /* gio */,
				33FDA765262D615C0099964E /* voicebox */,
				33FDA61626285A690099964E /* wash */,
				40E0AC8A25148CD5007DBC42 /* Info.plist */,
				F86314CA250A039500C948B9 /* DeviceManagerDetectListener.h */,
				F86314CD250A039500C948B9 /* DeviceManagerDetectListener.m */,
				F8631545250A039600C948B9 /* DeviceManagerToolkitListener.h */,
				F86314CB250A039500C948B9 /* DeviceManagerToolkitListener.m */,
				F86314E9250A039600C948B9 /* UpDeviceDaemon.h */,
				F86314CC250A039500C948B9 /* UpDeviceDaemon.m */,
				8447614A2D9A7AFF00AE8BE4 /* UpDeviceCardManager.h */,
				8447614B2D9A7AFF00AE8BE4 /* UpDeviceCardManager.m */,
				F86314E8250A039600C948B9 /* UpDeviceInjection.h */,
				F86314EC250A039600C948B9 /* UpDeviceInjection.m */,
				F86314EE250A039600C948B9 /* UpDeviceManager.h */,
				F86314ED250A039600C948B9 /* UpDeviceManager.m */,
				A0C41BD2294AF23200A877F9 /* UpDeviceResourceManager.h */,
				A0C41BD3294AF23200A877F9 /* UpDeviceResourceManager.m */,
				DB1BFCE22DB11A1300E6815A /* UpConnectivityPresenceHandler.h */,
				DB1BFCE32DB11A1300E6815A /* UpConnectivityPresenceHandler.m */,
				F86314CE250A039500C948B9 /* compat */,
				F8631546250A039700C948B9 /* dummy */,
				F863154B250A039700C948B9 /* updevice-logic-engine */,
				F86314F2250A039600C948B9 /* updevice-toolkit-usdk */,
				F86314EF250A039600C948B9 /* wrapper */,
			);
			path = UPDevice;
			sourceTree = "<group>";
		};
		F86314A2250A01E200C948B9 /* UPDeviceDebugger */ = {
			isa = PBXGroup;
			children = (
				40E0AC6125148BD8007DBC42 /* Info.plist */,
				F86314A3250A01E200C948B9 /* AppDelegate.h */,
				F86314A4250A01E200C948B9 /* AppDelegate.m */,
				F86314A6250A01E200C948B9 /* SceneDelegate.h */,
				F86314A7250A01E200C948B9 /* SceneDelegate.m */,
				F86314A9250A01E200C948B9 /* ViewController.h */,
				F86314AA250A01E200C948B9 /* ViewController.m */,
				F86314AC250A01E200C948B9 /* Main.storyboard */,
				F86314AF250A01E400C948B9 /* Assets.xcassets */,
				F86314B1250A01E400C948B9 /* LaunchScreen.storyboard */,
				F86314B5250A01E400C948B9 /* main.m */,
			);
			path = UPDeviceDebugger;
			sourceTree = "<group>";
		};
		F86314BF250A01F000C948B9 /* UPDeviceTests */ = {
			isa = PBXGroup;
			children = (
				33ED73B5269FFFA500D4C7A8 /* features */,
				F86162E825942EFF0053904B /* Untils */,
				F86162E325942E760053904B /* FakeDelegate */,
				F86162E425942E760053904B /* Steps */,
				40E0AC6525148BE2007DBC42 /* Info.plist */,
				F86314C0250A01F000C948B9 /* CucumberRunner.m */,
			);
			path = UPDeviceTests;
			sourceTree = "<group>";
		};
		F86314CE250A039500C948B9 /* compat */ = {
			isa = PBXGroup;
			children = (
				33FDA61026285A3E0099964E /* UpCompatEngineDevice.h */,
				33FDA61126285A3E0099964E /* UpCompatEngineDevice.m */,
				F86314DD250A039600C948B9 /* api */,
				F86314D6250A039600C948B9 /* UpCompatApi.h */,
				F86314D4250A039600C948B9 /* UpCompatCallbackWrapper.h */,
				F86314E6250A039600C948B9 /* UpCompatCallbackWrapper.m */,
				F86314DA250A039600C948B9 /* UpCompatCommand.h */,
				F86314D3250A039500C948B9 /* UpCompatCommand.m */,
				F86314D9250A039600C948B9 /* UpCompatDevice.h */,
				F86314CF250A039500C948B9 /* UpCompatDevice.m */,
				F86314D0250A039500C948B9 /* UpCompatDeviceStore.h */,
				F86314D8250A039600C948B9 /* UpCompatDeviceStore.m */,
				F86314D1250A039500C948B9 /* UpDeviceDataCache.h */,
				F86314DC250A039600C948B9 /* UpDeviceDataCache.m */,
			);
			path = compat;
			sourceTree = "<group>";
		};
		F86314DD250A039600C948B9 /* api */ = {
			isa = PBXGroup;
			children = (
				F86314E0250A039600C948B9 /* UpDeviceChangeCallback.h */,
				F86314E2250A039600C948B9 /* UpDeviceNetworkType.h */,
				F86314DE250A039600C948B9 /* UpDeviceNetworkType.m */,
				F86314E4250A039600C948B9 /* UpDeviceStatus.h */,
				F86314E3250A039600C948B9 /* UpDeviceType.h */,
				F86314DF250A039600C948B9 /* UpDeviceType.m */,
				F86314E1250A039600C948B9 /* UpSubDevChangeCallback.h */,
			);
			path = api;
			sourceTree = "<group>";
		};
		F86314EF250A039600C948B9 /* wrapper */ = {
			isa = PBXGroup;
			children = (
				F86314F0250A039600C948B9 /* UpDeviceDataSourceWrapper.h */,
				F86314F1250A039600C948B9 /* UpDeviceDataSourceWrapper.m */,
			);
			path = wrapper;
			sourceTree = "<group>";
		};
		F86314F2250A039600C948B9 /* updevice-toolkit-usdk */ = {
			isa = PBXGroup;
			children = (
				F86314F3250A039600C948B9 /* usdk */,
			);
			path = "updevice-toolkit-usdk";
			sourceTree = "<group>";
		};
		F86314F3250A039600C948B9 /* usdk */ = {
			isa = PBXGroup;
			children = (
				F86314FD250A039600C948B9 /* action */,
				F86314F4250A039600C948B9 /* GatewayConnection.h */,
				F86314F9250A039600C948B9 /* GatewayMessageListener.h */,
				F8631543250A039600C948B9 /* MessageListener.h */,
				F86314FB250A039600C948B9 /* WifiDeviceBaseInfo.h */,
				F8631540250A039600C948B9 /* WifiDeviceHelper.h */,
				F86314F6250A039600C948B9 /* WifiDeviceHelper.m */,
				F86314F7250A039600C948B9 /* WifiDeviceNetworkType.h */,
				F86314FA250A039600C948B9 /* WifiDeviceToolkit.h */,
				F8631544250A039600C948B9 /* WifiDeviceToolkitImpl.h */,
				F86314FC250A039600C948B9 /* WifiDeviceToolkitImpl.m */,
				F8631541250A039600C948B9 /* WifiDeviceType.h */,
				F86314F5250A039600C948B9 /* WifiDeviceType.m */,
			);
			path = usdk;
			sourceTree = "<group>";
		};
		F86314FD250A039600C948B9 /* action */ = {
			isa = PBXGroup;
			children = (
				8466DE9E2CCF32A500DFDA87 /* GetOnlyConfigState.h */,
				8466DE9D2CCF32A500DFDA87 /* GetOnlyConfigState.m */,
				DB4F95172DB23C0C00064BC5 /* IsSupportOnlyConfig.h */,
				DB4F95182DB23C0C00064BC5 /* IsSupportOnlyConfig.m */,
				DB4F951B2DB23CA600064BC5 /* IsOnlyConfigFlow.h */,
				DB4F951C2DB23CA600064BC5 /* IsOnlyConfigFlow.m */,
				041D69E72704137800833105 /* GetControlState.h */,
				041D69E62704137800833105 /* GetControlState.m */,
				333B1ABF26170B6700A389D9 /* GetNetType.h */,
				333B1ABE26170B6700A389D9 /* GetNetType.m */,
				F8631517250A039600C948B9 /* AttachDevice.h */,
				F863153F250A039600C948B9 /* AttachDevice.m */,
				F8631500250A039600C948B9 /* AttachResource.h */,
				F8631523250A039600C948B9 /* AttachResource.m */,
				22F785FE265DE40500175218 /* AttachDecodeResource.h */,
				22F785FD265DE40500175218 /* AttachDecodeResource.m */,
				F8631538250A039600C948B9 /* AttachToolkit.h */,
				F863151C250A039600C948B9 /* AttachToolkit.m */,
				F8631531250A039600C948B9 /* CancelFetchBLEHistoryData.h */,
				F863150F250A039600C948B9 /* CancelFetchBLEHistoryData.m */,
				F8631518250A039600C948B9 /* CheckBoardFOTAInfo.h */,
				F863153C250A039600C948B9 /* CheckBoardFOTAInfo.m */,
				F8631537250A039600C948B9 /* ConnectRemoteDevices.h */,
				F863151D250A039600C948B9 /* ConnectRemoteDevices.m */,
				F863151B250A039600C948B9 /* DetachDevice.h */,
				F8631539250A039600C948B9 /* DetachDevice.m */,
				F8631519250A039600C948B9 /* DetachResource.h */,
				F863153A250A039600C948B9 /* DetachResource.m */,
				F8631527250A039600C948B9 /* DetachToolkit.h */,
				F863150A250A039600C948B9 /* DetachToolkit.m */,
				22F785CA265CEDDB00175218 /* AddDevicesToGroup.h */,
				22F785C7265CEDDB00175218 /* AddDevicesToGroup.m */,
				22F785C4265CEDDB00175218 /* CreateDeviceGroup.h */,
				22F785C3265CEDDB00175218 /* CreateDeviceGroup.m */,
				22F785C5265CEDDB00175218 /* DeleteDeviceGroup.h */,
				22F785C6265CEDDB00175218 /* DeleteDeviceGroup.m */,
				22F785C9265CEDDB00175218 /* FetchGroupableDeviceList.h */,
				22F785BF265CEDDA00175218 /* FetchGroupableDeviceList.m */,
				22F785C2265CEDDA00175218 /* GetGroupMemberList.h */,
				22F785C8265CEDDB00175218 /* GetGroupMemberList.m */,
				22F78604265E1E2500175218 /* IsGroup.h */,
				22F78603265E1E2500175218 /* IsGroup.m */,
				22F785C0265CEDDA00175218 /* RemoveDevicesFromGroup.h */,
				22F785C1265CEDDA00175218 /* RemoveDevicesFromGroup.m */,
				F863152C250A039600C948B9 /* DisconnectRemoteDevices.h */,
				F8631514250A039600C948B9 /* DisconnectRemoteDevices.m */,
				F8631508250A039600C948B9 /* ExecuteCommand.h */,
				F8631528250A039600C948B9 /* ExecuteCommand.m */,
				F8631536250A039600C948B9 /* FetchBLEHistoryData.h */,
				F863151E250A039600C948B9 /* FetchBLEHistoryData.m */,
				F8631515250A039600C948B9 /* FetchBoardFOTAStatus.h */,
				F863153D250A039600C948B9 /* FetchBoardFOTAStatus.m */,
				F8631524250A039600C948B9 /* GetBindInfo.h */,
				F86314FF250A039600C948B9 /* GetBindInfo.m */,
				F863151F250A039600C948B9 /* GetDeviceAttribute.h */,
				F8631504250A039600C948B9 /* GetDeviceAttribute.m */,
				F8631529250A039600C948B9 /* GetDeviceAttributes.h */,
				F8631506250A039600C948B9 /* GetDeviceAttributes.m */,
				F863152A250A039600C948B9 /* GetDeviceCautions.h */,
				F8631507250A039600C948B9 /* GetDeviceCautions.m */,
				F8631533250A039600C948B9 /* GetDeviceConnection.h */,
				F863150D250A039600C948B9 /* GetDeviceConnection.m */,
				F863152F250A039600C948B9 /* GetDeviceInfo.h */,
				F8631512250A039600C948B9 /* GetDeviceInfo.m */,
				F8631520250A039600C948B9 /* GetDeviceList.h */,
				F8631503250A039600C948B9 /* GetDeviceList.m */,
				F8631510250A039600C948B9 /* GetNetworkQuality.h */,
				F863152E250A039600C948B9 /* GetNetworkQuality.m */,
				F8631522250A039600C948B9 /* GetSubDevList.h */,
				F8631501250A039600C948B9 /* GetSubDevList.m */,
				F863151A250A039600C948B9 /* GetSubDevListBySubDevice.h */,
				F863153B250A039600C948B9 /* GetSubDevListBySubDevice.m */,
				F8631513250A039600C948B9 /* InFocus.h */,
				F863152D250A039600C948B9 /* InFocus.m */,
				F8631521250A039600C948B9 /* IsBound.h */,
				F8631502250A039600C948B9 /* IsBound.m */,
				F86314FE250A039600C948B9 /* IsModuleNeedOta.h */,
				F8631525250A039600C948B9 /* IsModuleNeedOta.m */,
				F8631509250A039600C948B9 /* OutFocus.h */,
				F8631526250A039600C948B9 /* OutFocus.m */,
				F8631534250A039600C948B9 /* RefresUsdkDeviceList.h */,
				F863150C250A039600C948B9 /* RefresUsdkDeviceList.m */,
				F8631505250A039600C948B9 /* SmartLinkSoftwareVersion.h */,
				F863152B250A039600C948B9 /* SmartLinkSoftwareVersion.m */,
				F8631530250A039600C948B9 /* StartBoardFOTA.h */,
				F8631511250A039600C948B9 /* StartBoardFOTA.m */,
				F8631535250A039600C948B9 /* StartModuleUpdate.h */,
				F863150B250A039600C948B9 /* StartModuleUpdate.m */,
				F863153E250A039600C948B9 /* WifiDeviceAction.h */,
				F8631516250A039600C948B9 /* WifiDeviceAction.m */,
				F8631532250A039600C948B9 /* WifiToolkitAction.h */,
				F863150E250A039600C948B9 /* WifiToolkitAction.m */,
				334AFCE9266F408F003F443B /* StartWashFota.h */,
				334AFCEA266F408F003F443B /* StartWashFota.m */,
				041D69EA27042ADF00833105 /* GetFaultInformationCode.h */,
				041D69EB27042ADF00833105 /* GetFaultInformationCode.m */,
				ADFEFDB42702E39400B336F0 /* AttachDeviceWithoutConnect.h */,
				ADFEFDB52702E39400B336F0 /* AttachDeviceWithoutConnect.m */,
				ADFEFDB82702E3AB00B336F0 /* DetachDeviceWithoutConnect.h */,
				ADFEFDB92702E3AB00B336F0 /* DetachDeviceWithoutConnect.m */,
				041D69EE2704545800833105 /* BindDeviceWithoutWifi.h */,
				041D69EF2704545800833105 /* BindDeviceWithoutWifi.m */,
				041D69F22705240D00833105 /* UpdateRouterInfo.h */,
				041D69F32705240D00833105 /* UpdateRouterInfo.m */,
				041D69F627054A8300833105 /* GetConfigRouterInfo.h */,
				041D69F727054A8300833105 /* GetConfigRouterInfo.m */,
				04C3CDBE2716EE5500F951CB /* ConnectDevice.h */,
				04C3CDBF2716EE5500F951CB /* ConnectDevice.m */,
				04C3CDC22716F54700F951CB /* DisconnectDevice.h */,
				04C3CDC32716F54700F951CB /* DisconnectDevice.m */,
				3382E04B26E6087B009576F5 /* GetDeviceOnlineStatus.h */,
				3382E04C26E6087B009576F5 /* GetDeviceOnlineStatus.m */,
				4AC22BE82754B1B200BC7418 /* GetDeviceOnlineStateV2.h */,
				4AC22BE92754B1B200BC7418 /* GetDeviceOnlineStateV2.m */,
				DB4F95132DB21C6300064BC5 /* GetDeviceWifiOnlineState.h */,
				DB4F95142DB21C6300064BC5 /* GetDeviceWifiOnlineState.m */,
				AD113C002730DB7700EDB9F1 /* GetDeviceSleepState.h */,
				AD113C012730DB7700EDB9F1 /* GetDeviceSleepState.m */,
				A0CE394927576A60003FD92C /* GetDeviceNetworkLevel.h */,
				A0CE394A27576A60003FD92C /* GetDeviceNetworkLevel.m */,
				A073AE36278423CB00ED90BB /* QCConnectDevice.h */,
				A073AE37278423CB00ED90BB /* QCConnectDevice.m */,
				A073AE3A2784240400ED90BB /* QCDisconnectDevice.h */,
				A073AE3B2784240400ED90BB /* QCDisconnectDevice.m */,
				4AFC2854275F4787008F4DDB /* GetDeviceWifiLocalState.h */,
				4AFC2855275F4788008F4DDB /* GetDeviceWifiLocalState.m */,
				A0F0AEC6285B1A5900882A13 /* GetDeviceBleState.h */,
				A0F0AEC7285B1A5900882A13 /* GetDeviceBleState.m */,
				4F22A57E2AFE52F700269406 /* GetOfflineCause.h */,
				4F22A57F2AFE52F700269406 /* GetOfflineCause.m */,
				84C30C662BF44F9B00754263 /* GetOfflineDays.h */,
				84C30C672BF44F9B00754263 /* GetOfflineDays.m */,
				4F68698F2B883B4B00943C1A /* ExecuteCommandWithResult.h */,
				4F6869902B883B4B00943C1A /* ExecuteCommandWithResult.m */,
				84AEE78E2DA668C0008FF4C7 /* GetuSDKDeviceWrapper.h */,
				84AEE78F2DA668C0008FF4C7 /* GetuSDKDeviceWrapper.m */,
			);
			path = action;
			sourceTree = "<group>";
		};
		F8631546250A039700C948B9 /* dummy */ = {
			isa = PBXGroup;
			children = (
				F8631548250A039700C948B9 /* DummyDeviceBroker.h */,
				F863154A250A039700C948B9 /* DummyDeviceBroker.m */,
				F8631547250A039700C948B9 /* DummyDeviceToolkit.h */,
				F8631549250A039700C948B9 /* DummyDeviceToolkit.m */,
			);
			path = dummy;
			sourceTree = "<group>";
		};
		F863154B250A039700C948B9 /* updevice-logic-engine */ = {
			isa = PBXGroup;
			children = (
				966049222A2DC39E00138B09 /* UpEngineDevice+ResourceConfigSource.h */,
				966049232A2DC39E00138B09 /* UpEngineDevice+ResourceConfigSource.m */,
				F863154D250A039700C948B9 /* UpEngineDataSource.h */,
				F8631558250A039700C948B9 /* UpEngineDataSource.m */,
				F863154F250A039700C948B9 /* UpEngineDevice.h */,
				F8631557250A039700C948B9 /* UpEngineDevice.m */,
				F863155A250A039700C948B9 /* UpEngineDevice+ConfigSource.h */,
				F8631552250A039700C948B9 /* UpEngineDevice+ConfigSource.m */,
				F8631556250A039700C948B9 /* UpEngineDevice+EngineDelegate.h */,
				F8631550250A039700C948B9 /* UpEngineDevice+EngineDelegate.m */,
				F8631554250A039700C948B9 /* UpEngineDeviceFactory.h */,
				F863155B250A039700C948B9 /* UpEngineDeviceFactory.m */,
				F8631555250A039700C948B9 /* UpEngineException.h */,
				F8631551250A039700C948B9 /* UpEngineException.m */,
				F863154C250A039700C948B9 /* UpEngineReloadHandler.h */,
				F8631559250A039700C948B9 /* UpEngineReporter.h */,
				F8631553250A039700C948B9 /* UpEngineReporter.m */,
				F863154E250A039700C948B9 /* UpLogicEngineApi.h */,
			);
			path = "updevice-logic-engine";
			sourceTree = "<group>";
		};
/* End PBXGroup section */

/* Begin PBXHeadersBuildPhase section */
		AD8DD1082643E49D00086CBF /* Headers */ = {
			isa = PBXHeadersBuildPhase;
			buildActionMask = 2147483647;
			files = (
				A01559DF280821DE00723EDC /* UpDeviceObjectCommonHelper.h in Headers */,
				A0C41BDE294B297600A877F9 /* UpDeviceStringCommonHelper.h in Headers */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		AD8DD11C2643E5BB00086CBF /* Headers */ = {
			isa = PBXHeadersBuildPhase;
			buildActionMask = 2147483647;
			files = (
				334AFCEB266F408F003F443B /* StartWashFota.h in Headers */,
				AD8DD11E2643E61800086CBF /* DeviceManagerDetectListener.h in Headers */,
				22F78606265E1E2500175218 /* IsGroup.h in Headers */,
				3382E04D26E6087B009576F5 /* GetDeviceOnlineStatus.h in Headers */,
				AD8DD11F2643E61800086CBF /* DeviceManagerToolkitListener.h in Headers */,
				A073AE38278423CB00ED90BB /* QCConnectDevice.h in Headers */,
				DB4F951A2DB23C0C00064BC5 /* IsSupportOnlyConfig.h in Headers */,
				AD8DD1202643E61800086CBF /* UpDeviceDaemon.h in Headers */,
				AD8DD1212643E61800086CBF /* UpDeviceInjection.h in Headers */,
				AD8DD1222643E61800086CBF /* UpDeviceManager.h in Headers */,
				AD8DD1232643E61800086CBF /* UpDeviceChangeCallback.h in Headers */,
				AD8DD1242643E61800086CBF /* UpDeviceNetworkType.h in Headers */,
				22F785ED265D00C400175218 /* GetNetType.h in Headers */,
				DB4F951E2DB23CA600064BC5 /* IsOnlyConfigFlow.h in Headers */,
				AD8DD1252643E61800086CBF /* UpDeviceStatus.h in Headers */,
				AD8DD1262643E61800086CBF /* UpDeviceType.h in Headers */,
				AD8DD1272643E61800086CBF /* UpSubDevChangeCallback.h in Headers */,
				AD8DD1292643E61800086CBF /* UpCompatApi.h in Headers */,
				AD8DD12A2643E61800086CBF /* UpCompatCallbackWrapper.h in Headers */,
				AD8DD12B2643E61800086CBF /* UpCompatCommand.h in Headers */,
				22F785CC265CEDDB00175218 /* RemoveDevicesFromGroup.h in Headers */,
				AD8DD12C2643E61800086CBF /* UpCompatDevice.h in Headers */,
				AD8DD12D2643E61800086CBF /* UpCompatDeviceStore.h in Headers */,
				AD8DD12E2643E61800086CBF /* UpDeviceDataCache.h in Headers */,
				4F7EB0912D9524AC007694FE /* UpDeviceCardInfo.h in Headers */,
				AD8DD1312643E61800086CBF /* DummyDeviceBroker.h in Headers */,
				AD8DD1322643E61800086CBF /* DummyDeviceToolkit.h in Headers */,
				84AEE7912DA668C0008FF4C7 /* GetuSDKDeviceWrapper.h in Headers */,
				AD8DD1332643E61800086CBF /* UpEngineDataSource.h in Headers */,
				AD8DD1342643E61800086CBF /* UpEngineDevice.h in Headers */,
				AD8DD1352643E61900086CBF /* UpEngineDevice+ConfigSource.h in Headers */,
				AD8DD1362643E61900086CBF /* UpEngineDevice+EngineDelegate.h in Headers */,
				4F5ED4E12C72F15500119B62 /* UPNonNetworkDevice.h in Headers */,
				AD8DD1372643E61900086CBF /* UpEngineDeviceFactory.h in Headers */,
				DB1BFCE12DB119BD00E6815A /* UpPopupTipInfo.h in Headers */,
				22F78600265DE40500175218 /* AttachDecodeResource.h in Headers */,
				ADFEFDB62702E39400B336F0 /* AttachDeviceWithoutConnect.h in Headers */,
				A0F0AEC8285B1A5900882A13 /* GetDeviceBleState.h in Headers */,
				041D69F827054A8300833105 /* GetConfigRouterInfo.h in Headers */,
				AD8DD1382643E61900086CBF /* UpEngineException.h in Headers */,
				04C3CDC02716EE5500F951CB /* ConnectDevice.h in Headers */,
				A0C41BD4294AF23200A877F9 /* UpDeviceResourceManager.h in Headers */,
				AD8DD1392643E61900086CBF /* UpEngineReloadHandler.h in Headers */,
				04C3CDC42716F54700F951CB /* DisconnectDevice.h in Headers */,
				AD8DD13A2643E61900086CBF /* UpEngineReporter.h in Headers */,
				AD8DD13B2643E61900086CBF /* UpLogicEngineApi.h in Headers */,
				AD8DD13C2643E61900086CBF /* AttachDevice.h in Headers */,
				AD8DD13D2643E61900086CBF /* AttachResource.h in Headers */,
				AD8DD13E2643E61900086CBF /* AttachToolkit.h in Headers */,
				AD8DD13F2643E61900086CBF /* CancelFetchBLEHistoryData.h in Headers */,
				AD8DD1402643E61900086CBF /* CheckBoardFOTAInfo.h in Headers */,
				AD8DD1412643E61900086CBF /* ConnectRemoteDevices.h in Headers */,
				AD8DD1422643E61900086CBF /* DetachDevice.h in Headers */,
				AD8DD1432643E61900086CBF /* DetachResource.h in Headers */,
				84C30C682BF44F9B00754263 /* GetOfflineDays.h in Headers */,
				A0277540298A39090014920F /* UpWashDeviceManager.h in Headers */,
				AD8DD1442643E61900086CBF /* DetachToolkit.h in Headers */,
				AD8DD1452643E61900086CBF /* DisconnectRemoteDevices.h in Headers */,
				AD8DD1462643E61900086CBF /* ExecuteCommand.h in Headers */,
				966049242A2DC39F00138B09 /* UpEngineDevice+ResourceConfigSource.h in Headers */,
				AD8DD1472643E61900086CBF /* FetchBLEHistoryData.h in Headers */,
				4AC22C072754CBAE00BC7418 /* UpDeviceOnlineStateV2.h in Headers */,
				22F785D0265CEDDB00175218 /* CreateDeviceGroup.h in Headers */,
				4F9B187D2D7E8CA200C62B67 /* UPAggregateDeviceFactory.h in Headers */,
				8466DEA02CCF32A500DFDA87 /* GetOnlyConfigState.h in Headers */,
				AD8DD1482643E61900086CBF /* FetchBoardFOTAStatus.h in Headers */,
				A0CE394B27576A60003FD92C /* GetDeviceNetworkLevel.h in Headers */,
				AD8DD1492643E61900086CBF /* GetBindInfo.h in Headers */,
				AD8DD14A2643E61900086CBF /* GetDeviceAttribute.h in Headers */,
				A073AE3C2784240400ED90BB /* QCDisconnectDevice.h in Headers */,
				AD8DD14B2643E61900086CBF /* GetDeviceAttributes.h in Headers */,
				AD8DD14C2643E61900086CBF /* GetDeviceCautions.h in Headers */,
				AD8DD14D2643E61900086CBF /* GetDeviceConnection.h in Headers */,
				4AC22BEA2754B1B200BC7418 /* GetDeviceOnlineStateV2.h in Headers */,
				041D69F42705240D00833105 /* UpdateRouterInfo.h in Headers */,
				AD8DD14E2643E61900086CBF /* GetDeviceInfo.h in Headers */,
				ADFEFDBA2702E3AB00B336F0 /* DetachDeviceWithoutConnect.h in Headers */,
				AD8DD14F2643E61900086CBF /* GetDeviceList.h in Headers */,
				DB1BFCE52DB11A1300E6815A /* UpConnectivityPresenceHandler.h in Headers */,
				AD8DD1502643E61900086CBF /* GetNetworkQuality.h in Headers */,
				AD8DD1512643E61900086CBF /* GetSubDevList.h in Headers */,
				4AFC2856275F4788008F4DDB /* GetDeviceWifiLocalState.h in Headers */,
				AD8DD1522643E61900086CBF /* GetSubDevListBySubDevice.h in Headers */,
				AD8DD1532643E61900086CBF /* InFocus.h in Headers */,
				AD8DD1542643E61900086CBF /* IsBound.h in Headers */,
				4F22A5802AFE52F700269406 /* GetOfflineCause.h in Headers */,
				22F785D5265CEDDB00175218 /* FetchGroupableDeviceList.h in Headers */,
				AD8DD1562643E61900086CBF /* IsModuleNeedOta.h in Headers */,
				AD8DD1572643E61900086CBF /* OutFocus.h in Headers */,
				22F785CE265CEDDB00175218 /* GetGroupMemberList.h in Headers */,
				041D69EC27042ADF00833105 /* GetFaultInformationCode.h in Headers */,
				AD8DD1582643E61900086CBF /* RefresUsdkDeviceList.h in Headers */,
				AD8DD1592643E61900086CBF /* SmartLinkSoftwareVersion.h in Headers */,
				AD8DD15A2643E61900086CBF /* StartBoardFOTA.h in Headers */,
				AD8DD15B2643E61900086CBF /* StartModuleUpdate.h in Headers */,
				041D69F02704545800833105 /* BindDeviceWithoutWifi.h in Headers */,
				22F785D1265CEDDB00175218 /* DeleteDeviceGroup.h in Headers */,
				AD8DD15C2643E61900086CBF /* WifiDeviceAction.h in Headers */,
				A0C41BD02949D8C700A877F9 /* UpDeviceGetDeviceResourceTool.h in Headers */,
				AD8DD15D2643E61900086CBF /* WifiToolkitAction.h in Headers */,
				22F785D6265CEDDB00175218 /* AddDevicesToGroup.h in Headers */,
				4F5ED4E02C72F15500119B62 /* UpNonNetworkDeviceFactory.h in Headers */,
				041D69E92704137800833105 /* GetControlState.h in Headers */,
				04F426DA2A3954290068FCC7 /* UpDeviceDaemonGIO.h in Headers */,
				DB1BFCDD2DB119A100E6815A /* UpPresenceInfo.h in Headers */,
				AD8DD15E2643E61900086CBF /* GatewayConnection.h in Headers */,
				AD8DD15F2643E61900086CBF /* GatewayMessageListener.h in Headers */,
				4F9B18792D7E8B6200C62B67 /* UPAggregateDevice.h in Headers */,
				AD8DD1602643E61900086CBF /* MessageListener.h in Headers */,
				DB4F95162DB21C6300064BC5 /* GetDeviceWifiOnlineState.h in Headers */,
				AD113C022730DB7700EDB9F1 /* GetDeviceSleepState.h in Headers */,
				AD8DD1612643E61900086CBF /* WifiDeviceBaseInfo.h in Headers */,
				4F6869912B883B4B00943C1A /* ExecuteCommandWithResult.h in Headers */,
				8447614C2D9A7AFF00AE8BE4 /* UpDeviceCardManager.h in Headers */,
				AD8DD1622643E61900086CBF /* WifiDeviceHelper.h in Headers */,
				AD8DD1632643E61900086CBF /* WifiDeviceNetworkType.h in Headers */,
				AD8DD1642643E61900086CBF /* WifiDeviceToolkit.h in Headers */,
				AD8DD1652643E61900086CBF /* WifiDeviceToolkitImpl.h in Headers */,
				AD8DD1662643E61900086CBF /* WifiDeviceType.h in Headers */,
				AD8DD1672643E61900086CBF /* UpDeviceDataSourceWrapper.h in Headers */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		AD98A5A8265B791300EDAEA3 /* Headers */ = {
			isa = PBXHeadersBuildPhase;
			buildActionMask = 2147483647;
			files = (
				AD98ABF4265B8B7C00EDAEA3 /* UpCommonDevice.h in Headers */,
				AD98ABF2265B8B7C00EDAEA3 /* UpDeviceReceiver.h in Headers */,
				AD98AC28265B8B7E00EDAEA3 /* UpDeviceFOTAStatusInfo.h in Headers */,
				AD98AC1F265B8B7E00EDAEA3 /* DeviceFOTAStatusInfo.h in Headers */,
				AD98ABE2265B8B7B00EDAEA3 /* DefaultDetectListener.h in Headers */,
				AD98AC10265B8B7D00EDAEA3 /* DeviceOTAStatusInfo.h in Headers */,
				AD98AC24265B8B7E00EDAEA3 /* DeviceCommand.h in Headers */,
				AD98AC2C265B8B7F00EDAEA3 /* UpDeviceBaseInfo.h in Headers */,
				AD0A1CCC2713DB2600415B3D /* UpDeviceTracker.h in Headers */,
				AD98ABE7265B8B7B00EDAEA3 /* DefaultToolkitListener.h in Headers */,
				AD98ABFC265B8B7C00EDAEA3 /* UpDeviceConfigState.h in Headers */,
				A0CE395327576C8D003FD92C /* UpDeviceNetworkLevel.h in Headers */,
				AD98ABFA265B8B7C00EDAEA3 /* UpDeviceCache.h in Headers */,
				AD98AC45265B8B8000EDAEA3 /* UpDeviceToolkit.h in Headers */,
				AD98AC42265B8B8000EDAEA3 /* UpDeviceDetectListener.h in Headers */,
				AD98AC3D265B8B7F00EDAEA3 /* UpDeviceException.h in Headers */,
				AD98AC13265B8B7D00EDAEA3 /* DeviceFOTAInfo.h in Headers */,
				AD98AC47265B8B8000EDAEA3 /* UPDeviceLog.h in Headers */,
				AD98AC00265B8B7D00EDAEA3 /* UpDeviceFocus.h in Headers */,
				AD98AC02265B8B7D00EDAEA3 /* UpDeviceReporter.h in Headers */,
				AD98AC1A265B8B7E00EDAEA3 /* DeviceBaseInfo.h in Headers */,
				AD98AC2F265B8B7F00EDAEA3 /* UpDeviceNetWorkQualityInfo.h in Headers */,
				AD98AC0E265B8B7D00EDAEA3 /* updevice_api.h in Headers */,
				AD98AC40265B8B8000EDAEA3 /* UpDeviceReportListener.h in Headers */,
				84AEE78C2DA65221008FF4C7 /* uSDKDeviceWrapper.h in Headers */,
				AD98ABF3265B8B7C00EDAEA3 /* UPDeviceNetType.h in Headers */,
				AD98ABF9265B8B7C00EDAEA3 /* UpExtendDevice.h in Headers */,
				AD98AC44265B8B8000EDAEA3 /* UpDeviceToolkitState.h in Headers */,
				AD98AC25265B8B7E00EDAEA3 /* UpDeviceAttribute.h in Headers */,
				AD98AC08265B8B7D00EDAEA3 /* UpCompositeProcessor.h in Headers */,
				AD98AC27265B8B7E00EDAEA3 /* UpDeviceOTAStatusInfo.h in Headers */,
				AD98ABEB265B8B7B00EDAEA3 /* UpDeviceFilter.h in Headers */,
				AD98AC1D265B8B7E00EDAEA3 /* DeviceAttribute.h in Headers */,
				AD98ABFD265B8B7C00EDAEA3 /* UpDeviceListener.h in Headers */,
				AD98ABE3265B8B7B00EDAEA3 /* DefaultDeviceBroker.h in Headers */,
				AD98ABDF265B8B7B00EDAEA3 /* DefaultReportListener.h in Headers */,
				AD98AC3C265B8B7F00EDAEA3 /* UpDeviceHelper.h in Headers */,
				AD98ABEA265B8B7B00EDAEA3 /* UpDeviceBrokerHolder.h in Headers */,
				AD98AC0A265B8B7D00EDAEA3 /* UpDeviceProcessor.h in Headers */,
				AD98AC38265B8B7F00EDAEA3 /* UpDeviceCarrier.h in Headers */,
				AD98ABFF265B8B7C00EDAEA3 /* UpDeviceStore.h in Headers */,
				AD98AC26265B8B7E00EDAEA3 /* UpDeviceFOTAInfo.h in Headers */,
				8457E18B2D8AC31B007EBE53 /* UpDeviceGroupOptDeleagte.h in Headers */,
				8457E18C2D8AC31B007EBE53 /* UpDeviceReportMonitor.h in Headers */,
				AD98AC2A265B8B7E00EDAEA3 /* UpDeviceInfo.h in Headers */,
				AD98AC46265B8B8000EDAEA3 /* EmptyDeviceToolkit.h in Headers */,
				AD98ABF6265B8B7C00EDAEA3 /* UpDeviceBase.h in Headers */,
				AD98ABDD265B8B7B00EDAEA3 /* DefaultNotification.h in Headers */,
				AD98ABE9265B8B7B00EDAEA3 /* UpDeviceBroker.h in Headers */,
				AD98AC31265B8B7F00EDAEA3 /* UpDeviceSetMap.h in Headers */,
				AD98AC1C265B8B7E00EDAEA3 /* DeviceNetWorkQualityInfo.h in Headers */,
				AD98AC3F265B8B7F00EDAEA3 /* UpDeviceDataSource.h in Headers */,
				AD98AC19265B8B7E00EDAEA3 /* DeviceInfo.h in Headers */,
				AD98ABF8265B8B7C00EDAEA3 /* UpDeviceSub.h in Headers */,
				AD98AC2D265B8B7F00EDAEA3 /* UpDeviceConnection.h in Headers */,
				AD98AC3A265B8B7F00EDAEA3 /* UpDeviceAction.h in Headers */,
				AD98ABFE265B8B7C00EDAEA3 /* UpDevice.h in Headers */,
				AD98AC37265B8B7F00EDAEA3 /* UpStringResult.h in Headers */,
				AD98AC04265B8B7D00EDAEA3 /* UpDeviceFOTA.h in Headers */,
				AD98AC01265B8B7D00EDAEA3 /* UpDeviceNetWorkQuality.h in Headers */,
				AD98ABF5265B8B7C00EDAEA3 /* UpDeviceBLE.h in Headers */,
				AD98ABED265B8B7C00EDAEA3 /* UpDeviceCreator.h in Headers */,
				AD98AC30265B8B7F00EDAEA3 /* UpDeviceCaution.h in Headers */,
				AD98AC16265B8B7E00EDAEA3 /* DeviceCaution.h in Headers */,
				AD98AC06265B8B7D00EDAEA3 /* UpDeviceState.h in Headers */,
				041D69E12703031D00833105 /* UpDeviceControlState.h in Headers */,
				AD98AC35265B8B7F00EDAEA3 /* UpDeviceExecutable.h in Headers */,
				AD98ABEE265B8B7C00EDAEA3 /* UpDeviceFilters.h in Headers */,
				AD98AC0D265B8B7D00EDAEA3 /* UpDeviceCenter.h in Headers */,
				AD98AC2E265B8B7F00EDAEA3 /* UpDeviceBLEHistoryInfo.h in Headers */,
				AD98AC2B265B8B7F00EDAEA3 /* UpDeviceExtras.h in Headers */,
				AD98AC39265B8B7F00EDAEA3 /* UpDeviceResult.h in Headers */,
				AD98AC29265B8B7E00EDAEA3 /* UpDeviceCommand.h in Headers */,
				AD98ABEC265B8B7B00EDAEA3 /* UpDeviceFactory.h in Headers */,
				AD98AC14265B8B7D00EDAEA3 /* DeviceBLEHistoryInfo.h in Headers */,
				AD98AC18265B8B7E00EDAEA3 /* DeviceExtras.h in Headers */,
				8466DEA22CCF32F200DFDA87 /* UpDeviceOnlyConfigState.h in Headers */,
				A0D3FEB727E42F1800DA145B /* UpDeviceQCConnectTimeoutType.h in Headers */,
				AD98AC41265B8B8000EDAEA3 /* UpDeviceToolkitListener.h in Headers */,
				AD98ABE6265B8B7B00EDAEA3 /* DefaultBrokerHolder.h in Headers */,
				AD2772622600576300287D0C /* (null) in Headers */,
				AD27726A2600587F00287D0C /* (null) in Headers */,
				33FDA77A262ECA0E0099964E /* UpWashDataResponseParser.h in Headers */,
				40E0AC4C25148816007DBC42 /* UDSafeMutableArray.h in Headers */,
				F8313D0725E0CFFD00F8A479 /* (null) in Headers */,
				33FDA62326285A690099964E /* UpWashDevice.h in Headers */,
				04E3E60427182E6B00D1DDBE /* UpDeviceOnlineStatus.h in Headers */,
				40E0AC4A25148816007DBC42 /* UDSafeMutableDictionary.h in Headers */,
				33FDA62126285A690099964E /* UpWashAdapterApi.h in Headers */,
				33FDA768262D619B0099964E /* UpVoiceBoxDevice.h in Headers */,
				22F785FC265DE20F00175218 /* UpAttachResourceDelegate.h in Headers */,
				33FDA62626285A690099964E /* UpWashModel.h in Headers */,
				8412D69D2D7F0C02002B44F7 /* UpDeviceCacheManager.h in Headers */,
				33FDA61226285A3E0099964E /* UpCompatEngineDevice.h in Headers */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXHeadersBuildPhase section */

/* Begin PBXNativeTarget section */
		AD8DD0FE2643E48600086CBF /* UPDeviceUtil */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = AD8DD1052643E48700086CBF /* Build configuration list for PBXNativeTarget "UPDeviceUtil" */;
			buildPhases = (
				AD8DD1082643E49D00086CBF /* Headers */,
				AD8DD0FB2643E48600086CBF /* Sources */,
				AD8DD0FC2643E48600086CBF /* Frameworks */,
				AD8DD0FD2643E48600086CBF /* CopyFiles */,
			);
			buildRules = (
			);
			dependencies = (
			);
			name = UPDeviceUtil;
			productName = UPDeviceUtil;
			productReference = AD8DD0FF2643E48600086CBF /* libUPDeviceUtil.a */;
			productType = "com.apple.product-type.library.static";
		};
		AD8DD1112643E5A300086CBF /* UPDevice */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = AD8DD1182643E5A300086CBF /* Build configuration list for PBXNativeTarget "UPDevice" */;
			buildPhases = (
				6BD9CCAF378D1607A4A8EF33 /* [CP] Check Pods Manifest.lock */,
				AD8DD11C2643E5BB00086CBF /* Headers */,
				AD8DD10E2643E5A300086CBF /* Sources */,
				AD8DD10F2643E5A300086CBF /* Frameworks */,
				AD8DD1102643E5A300086CBF /* CopyFiles */,
				AD8DD11B2643E5B800086CBF /* Resources */,
			);
			buildRules = (
			);
			dependencies = (
				AD98A5F1265B7D4D00EDAEA3 /* PBXTargetDependency */,
				AD8DD1FF2643F5CF00086CBF /* PBXTargetDependency */,
			);
			name = UPDevice;
			productName = UPDevice;
			productReference = AD8DD1122643E5A300086CBF /* libUPDevice.a */;
			productType = "com.apple.product-type.library.static";
		};
		AD98A4FF265B74C700EDAEA3 /* updevice_api */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = AD98A508265B74C700EDAEA3 /* Build configuration list for PBXNativeTarget "updevice_api" */;
			buildPhases = (
				F50B7043B008E370C7D48DAD /* [CP] Check Pods Manifest.lock */,
				AD98A5A8265B791300EDAEA3 /* Headers */,
				AD98A4FC265B74C700EDAEA3 /* Sources */,
				AD98A4FD265B74C700EDAEA3 /* Frameworks */,
				AD98A4FE265B74C700EDAEA3 /* CopyFiles */,
			);
			buildRules = (
			);
			dependencies = (
			);
			name = updevice_api;
			productName = updevice_api;
			productReference = AD98A500265B74C700EDAEA3 /* libupdevice_api.a */;
			productType = "com.apple.product-type.library.static";
		};
		F86314A0250A01E200C948B9 /* UPDeviceDebugger */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = F86314B7250A01E400C948B9 /* Build configuration list for PBXNativeTarget "UPDeviceDebugger" */;
			buildPhases = (
				CCE216F9466FFCF9D9FCC1C9 /* [CP] Check Pods Manifest.lock */,
				F863149D250A01E200C948B9 /* Sources */,
				F863149E250A01E200C948B9 /* Frameworks */,
				F863149F250A01E200C948B9 /* Resources */,
				71D147171D3B23C6A5544830 /* [CP] Copy Pods Resources */,
			);
			buildRules = (
			);
			dependencies = (
				AD8DD1FD2643F5C300086CBF /* PBXTargetDependency */,
			);
			name = UPDeviceDebugger;
			productName = UPDeviceDebugger;
			productReference = F86314A1250A01E200C948B9 /* UPDeviceDebugger.app */;
			productType = "com.apple.product-type.application";
		};
		F86314BD250A01F000C948B9 /* UPDeviceTests */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = F86314C6250A01F000C948B9 /* Build configuration list for PBXNativeTarget "UPDeviceTests" */;
			buildPhases = (
				CBA85099FF69CB49FE5EC98C /* [CP] Check Pods Manifest.lock */,
				F86314BA250A01F000C948B9 /* Sources */,
				F86314BB250A01F000C948B9 /* Frameworks */,
				F86314BC250A01F000C948B9 /* Resources */,
				AD9B9934CC407B72F7F44495 /* [CP] Copy Pods Resources */,
			);
			buildRules = (
			);
			dependencies = (
				AD8DD2012643F5D600086CBF /* PBXTargetDependency */,
			);
			name = UPDeviceTests;
			productName = UPDeviceTests;
			productReference = F86314BE250A01F000C948B9 /* UPDeviceTests.xctest */;
			productType = "com.apple.product-type.bundle.unit-test";
		};
/* End PBXNativeTarget section */

/* Begin PBXProject section */
		F8631489250A018800C948B9 /* Project object */ = {
			isa = PBXProject;
			attributes = {
				DefaultBuildSystemTypeForWorkspace = Original;
				LastUpgradeCheck = 1160;
				ORGANIZATIONNAME = "海尔优家智能科技（北京）有限公司";
				TargetAttributes = {
					AD8DD0FE2643E48600086CBF = {
						CreatedOnToolsVersion = 12.3;
					};
					AD8DD1112643E5A300086CBF = {
						CreatedOnToolsVersion = 12.3;
					};
					AD98A4FF265B74C700EDAEA3 = {
						CreatedOnToolsVersion = 12.3;
					};
					F86314A0250A01E200C948B9 = {
						CreatedOnToolsVersion = 11.6;
					};
					F86314BD250A01F000C948B9 = {
						CreatedOnToolsVersion = 11.6;
					};
				};
			};
			buildConfigurationList = F863148C250A018800C948B9 /* Build configuration list for PBXProject "UPDevice" */;
			compatibilityVersion = "Xcode 9.3";
			developmentRegion = en;
			hasScannedForEncodings = 0;
			knownRegions = (
				en,
				Base,
			);
			mainGroup = F8631488250A018800C948B9;
			productRefGroup = F8631493250A018800C948B9 /* Products */;
			projectDirPath = "";
			projectRoot = "";
			targets = (
				F86314A0250A01E200C948B9 /* UPDeviceDebugger */,
				F86314BD250A01F000C948B9 /* UPDeviceTests */,
				AD8DD0FE2643E48600086CBF /* UPDeviceUtil */,
				AD8DD1112643E5A300086CBF /* UPDevice */,
				AD98A4FF265B74C700EDAEA3 /* updevice_api */,
			);
		};
/* End PBXProject section */

/* Begin PBXResourcesBuildPhase section */
		AD8DD11B2643E5B800086CBF /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				AD8DD11D2643E5C500086CBF /* Info.plist in Resources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		F863149F250A01E200C948B9 /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				40E0AC6225148BD8007DBC42 /* Info.plist in Resources */,
				F86314B3250A01E400C948B9 /* LaunchScreen.storyboard in Resources */,
				F86314B0250A01E400C948B9 /* Assets.xcassets in Resources */,
				F86314AE250A01E200C948B9 /* Main.storyboard in Resources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		F86314BC250A01F000C948B9 /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				33ED73B6269FFFA500D4C7A8 /* features in Resources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXResourcesBuildPhase section */

/* Begin PBXShellScriptBuildPhase section */
		6BD9CCAF378D1607A4A8EF33 /* [CP] Check Pods Manifest.lock */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			inputFileListPaths = (
			);
			inputPaths = (
				"${PODS_PODFILE_DIR_PATH}/Podfile.lock",
				"${PODS_ROOT}/Manifest.lock",
			);
			name = "[CP] Check Pods Manifest.lock";
			outputFileListPaths = (
			);
			outputPaths = (
				"$(DERIVED_FILE_DIR)/Pods-UPDevice-checkManifestLockResult.txt",
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "diff \"${PODS_PODFILE_DIR_PATH}/Podfile.lock\" \"${PODS_ROOT}/Manifest.lock\" > /dev/null\nif [ $? != 0 ] ; then\n    # print error to STDERR\n    echo \"error: The sandbox is not in sync with the Podfile.lock. Run 'pod install' or update your CocoaPods installation.\" >&2\n    exit 1\nfi\n# This output is used by Xcode 'outputs' to avoid re-running this script phase.\necho \"SUCCESS\" > \"${SCRIPT_OUTPUT_FILE_0}\"\n";
			showEnvVarsInLog = 0;
		};
		71D147171D3B23C6A5544830 /* [CP] Copy Pods Resources */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			inputFileListPaths = (
				"${PODS_ROOT}/Target Support Files/Pods-UPDeviceDebugger/Pods-UPDeviceDebugger-resources-${CONFIGURATION}-input-files.xcfilelist",
			);
			name = "[CP] Copy Pods Resources";
			outputFileListPaths = (
				"${PODS_ROOT}/Target Support Files/Pods-UPDeviceDebugger/Pods-UPDeviceDebugger-resources-${CONFIGURATION}-output-files.xcfilelist",
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "\"${PODS_ROOT}/Target Support Files/Pods-UPDeviceDebugger/Pods-UPDeviceDebugger-resources.sh\"\n";
			showEnvVarsInLog = 0;
		};
		AD9B9934CC407B72F7F44495 /* [CP] Copy Pods Resources */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			inputFileListPaths = (
				"${PODS_ROOT}/Target Support Files/Pods-UPDeviceTests/Pods-UPDeviceTests-resources-${CONFIGURATION}-input-files.xcfilelist",
			);
			name = "[CP] Copy Pods Resources";
			outputFileListPaths = (
				"${PODS_ROOT}/Target Support Files/Pods-UPDeviceTests/Pods-UPDeviceTests-resources-${CONFIGURATION}-output-files.xcfilelist",
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "\"${PODS_ROOT}/Target Support Files/Pods-UPDeviceTests/Pods-UPDeviceTests-resources.sh\"\n";
			showEnvVarsInLog = 0;
		};
		CBA85099FF69CB49FE5EC98C /* [CP] Check Pods Manifest.lock */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			inputFileListPaths = (
			);
			inputPaths = (
				"${PODS_PODFILE_DIR_PATH}/Podfile.lock",
				"${PODS_ROOT}/Manifest.lock",
			);
			name = "[CP] Check Pods Manifest.lock";
			outputFileListPaths = (
			);
			outputPaths = (
				"$(DERIVED_FILE_DIR)/Pods-UPDeviceTests-checkManifestLockResult.txt",
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "diff \"${PODS_PODFILE_DIR_PATH}/Podfile.lock\" \"${PODS_ROOT}/Manifest.lock\" > /dev/null\nif [ $? != 0 ] ; then\n    # print error to STDERR\n    echo \"error: The sandbox is not in sync with the Podfile.lock. Run 'pod install' or update your CocoaPods installation.\" >&2\n    exit 1\nfi\n# This output is used by Xcode 'outputs' to avoid re-running this script phase.\necho \"SUCCESS\" > \"${SCRIPT_OUTPUT_FILE_0}\"\n";
			showEnvVarsInLog = 0;
		};
		CCE216F9466FFCF9D9FCC1C9 /* [CP] Check Pods Manifest.lock */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			inputFileListPaths = (
			);
			inputPaths = (
				"${PODS_PODFILE_DIR_PATH}/Podfile.lock",
				"${PODS_ROOT}/Manifest.lock",
			);
			name = "[CP] Check Pods Manifest.lock";
			outputFileListPaths = (
			);
			outputPaths = (
				"$(DERIVED_FILE_DIR)/Pods-UPDeviceDebugger-checkManifestLockResult.txt",
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "diff \"${PODS_PODFILE_DIR_PATH}/Podfile.lock\" \"${PODS_ROOT}/Manifest.lock\" > /dev/null\nif [ $? != 0 ] ; then\n    # print error to STDERR\n    echo \"error: The sandbox is not in sync with the Podfile.lock. Run 'pod install' or update your CocoaPods installation.\" >&2\n    exit 1\nfi\n# This output is used by Xcode 'outputs' to avoid re-running this script phase.\necho \"SUCCESS\" > \"${SCRIPT_OUTPUT_FILE_0}\"\n";
			showEnvVarsInLog = 0;
		};
		F50B7043B008E370C7D48DAD /* [CP] Check Pods Manifest.lock */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			inputFileListPaths = (
			);
			inputPaths = (
				"${PODS_PODFILE_DIR_PATH}/Podfile.lock",
				"${PODS_ROOT}/Manifest.lock",
			);
			name = "[CP] Check Pods Manifest.lock";
			outputFileListPaths = (
			);
			outputPaths = (
				"$(DERIVED_FILE_DIR)/Pods-updevice_api-checkManifestLockResult.txt",
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "diff \"${PODS_PODFILE_DIR_PATH}/Podfile.lock\" \"${PODS_ROOT}/Manifest.lock\" > /dev/null\nif [ $? != 0 ] ; then\n    # print error to STDERR\n    echo \"error: The sandbox is not in sync with the Podfile.lock. Run 'pod install' or update your CocoaPods installation.\" >&2\n    exit 1\nfi\n# This output is used by Xcode 'outputs' to avoid re-running this script phase.\necho \"SUCCESS\" > \"${SCRIPT_OUTPUT_FILE_0}\"\n";
			showEnvVarsInLog = 0;
		};
/* End PBXShellScriptBuildPhase section */

/* Begin PBXSourcesBuildPhase section */
		AD8DD0FB2643E48600086CBF /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				A0C41BDF294B297600A877F9 /* UpDeviceStringCommonHelper.m in Sources */,
				A01559E0280821DE00723EDC /* UpDeviceObjectCommonHelper.m in Sources */,
				AD8DD10C2643E4B500086CBF /* UDSafeMutableDictionary.m in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		AD8DD10E2643E5A300086CBF /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				DB1BFCDC2DB119A100E6815A /* UpPresenceInfo.m in Sources */,
				AD8DD1682643E67900086CBF /* DeviceManagerDetectListener.m in Sources */,
				AD8DD1692643E67900086CBF /* DeviceManagerToolkitListener.m in Sources */,
				041D69F12704545800833105 /* BindDeviceWithoutWifi.m in Sources */,
				04C3CDC52716F54700F951CB /* DisconnectDevice.m in Sources */,
				DB4F951D2DB23CA600064BC5 /* IsOnlyConfigFlow.m in Sources */,
				AD8DD16A2643E67900086CBF /* UpDeviceDaemon.m in Sources */,
				AD8DD16B2643E67900086CBF /* UpDeviceInjection.m in Sources */,
				A0C41BD12949D8C700A877F9 /* UpDeviceGetDeviceResourceTool.m in Sources */,
				22F785FF265DE40500175218 /* AttachDecodeResource.m in Sources */,
				22F785D3265CEDDB00175218 /* AddDevicesToGroup.m in Sources */,
				AD8DD16C2643E67900086CBF /* UpDeviceManager.m in Sources */,
				A0277541298A39090014920F /* UpWashDeviceManager.m in Sources */,
				22F785CD265CEDDB00175218 /* RemoveDevicesFromGroup.m in Sources */,
				AD8DD16D2643E67900086CBF /* UpDeviceNetworkType.m in Sources */,
				DB4F95152DB21C6300064BC5 /* GetDeviceWifiOnlineState.m in Sources */,
				AD8DD16E2643E67900086CBF /* UpDeviceType.m in Sources */,
				AD8DD16F2643E67900086CBF /* (null) in Sources */,
				DB1BFCE42DB11A1300E6815A /* UpConnectivityPresenceHandler.m in Sources */,
				AD8DD1702643E67900086CBF /* UpCompatCallbackWrapper.m in Sources */,
				AD8DD1712643E67900086CBF /* UpCompatCommand.m in Sources */,
				AD8DD1722643E67900086CBF /* UpCompatDevice.m in Sources */,
				ADFEFDB72702E39400B336F0 /* AttachDeviceWithoutConnect.m in Sources */,
				AD8DD1732643E67900086CBF /* UpCompatDeviceStore.m in Sources */,
				AD8DD1742643E67900086CBF /* UpDeviceDataCache.m in Sources */,
				AD8DD1772643E67900086CBF /* DummyDeviceBroker.m in Sources */,
				84AEE7902DA668C0008FF4C7 /* GetuSDKDeviceWrapper.m in Sources */,
				AD8DD1782643E67900086CBF /* DummyDeviceToolkit.m in Sources */,
				DB4F95192DB23C0C00064BC5 /* IsSupportOnlyConfig.m in Sources */,
				4F5ED4E22C72F15500119B62 /* UPNonNetworkDevice.m in Sources */,
				AD8DD1792643E67900086CBF /* UpEngineDataSource.m in Sources */,
				AD8DD17A2643E67900086CBF /* UpEngineDevice.m in Sources */,
				AD8DD17B2643E67900086CBF /* UpEngineDevice+ConfigSource.m in Sources */,
				DB1BFCE02DB119BD00E6815A /* UpPopupTipInfo.m in Sources */,
				041D69F927054A8300833105 /* GetConfigRouterInfo.m in Sources */,
				AD8DD17C2643E67900086CBF /* UpEngineDevice+EngineDelegate.m in Sources */,
				AD8DD17D2643E67900086CBF /* UpEngineDeviceFactory.m in Sources */,
				04F426DB2A3954290068FCC7 /* UpDeviceDaemonGIO.m in Sources */,
				22F785D4265CEDDB00175218 /* GetGroupMemberList.m in Sources */,
				AD8DD17E2643E67900086CBF /* UpEngineException.m in Sources */,
				4F22A5812AFE52F700269406 /* GetOfflineCause.m in Sources */,
				4F9B187A2D7E8B6200C62B67 /* UPAggregateDevice.m in Sources */,
				84C30C692BF44F9B00754263 /* GetOfflineDays.m in Sources */,
				334AFCEC266F408F003F443B /* StartWashFota.m in Sources */,
				AD8DD17F2643E67900086CBF /* UpEngineReporter.m in Sources */,
				AD8DD1802643E67900086CBF /* AttachDevice.m in Sources */,
				04C3CDC12716EE5500F951CB /* ConnectDevice.m in Sources */,
				A073AE3D2784240400ED90BB /* QCDisconnectDevice.m in Sources */,
				4F9B187E2D7E8CA200C62B67 /* UPAggregateDeviceFactory.m in Sources */,
				22F785EE265D00D000175218 /* GetNetType.m in Sources */,
				A0C41BD5294AF23200A877F9 /* UpDeviceResourceManager.m in Sources */,
				AD8DD1812643E67900086CBF /* AttachResource.m in Sources */,
				AD8DD1822643E67900086CBF /* AttachToolkit.m in Sources */,
				AD8DD1832643E67900086CBF /* CancelFetchBLEHistoryData.m in Sources */,
				041D69ED27042ADF00833105 /* GetFaultInformationCode.m in Sources */,
				AD8DD1842643E67900086CBF /* CheckBoardFOTAInfo.m in Sources */,
				AD8DD1852643E67900086CBF /* ConnectRemoteDevices.m in Sources */,
				041D69E82704137800833105 /* GetControlState.m in Sources */,
				AD8DD1862643E67900086CBF /* DetachDevice.m in Sources */,
				AD8DD1872643E67900086CBF /* DetachResource.m in Sources */,
				A0F0AEC9285B1A5900882A13 /* GetDeviceBleState.m in Sources */,
				AD8DD1882643E67900086CBF /* DetachToolkit.m in Sources */,
				AD8DD1892643E67900086CBF /* DisconnectRemoteDevices.m in Sources */,
				AD8DD18A2643E67900086CBF /* ExecuteCommand.m in Sources */,
				AD8DD18B2643E67900086CBF /* FetchBLEHistoryData.m in Sources */,
				4F5ED4E32C72F15500119B62 /* UpNonNetworkDeviceFactory.m in Sources */,
				AD8DD18C2643E67900086CBF /* FetchBoardFOTAStatus.m in Sources */,
				041D69F52705240D00833105 /* UpdateRouterInfo.m in Sources */,
				8447614D2D9A7AFF00AE8BE4 /* UpDeviceCardManager.m in Sources */,
				AD8DD18D2643E67900086CBF /* GetBindInfo.m in Sources */,
				AD8DD18E2643E67900086CBF /* GetDeviceAttribute.m in Sources */,
				AD8DD18F2643E67900086CBF /* GetDeviceAttributes.m in Sources */,
				AD8DD1902643E67A00086CBF /* GetDeviceCautions.m in Sources */,
				8466DE9F2CCF32A500DFDA87 /* GetOnlyConfigState.m in Sources */,
				AD8DD1912643E67A00086CBF /* GetDeviceConnection.m in Sources */,
				AD8DD1922643E67A00086CBF /* GetDeviceInfo.m in Sources */,
				AD8DD1932643E67A00086CBF /* GetDeviceList.m in Sources */,
				AD8DD1942643E67A00086CBF /* GetNetworkQuality.m in Sources */,
				A073AE39278423CB00ED90BB /* QCConnectDevice.m in Sources */,
				4AFC2857275F4788008F4DDB /* GetDeviceWifiLocalState.m in Sources */,
				AD8DD1952643E67A00086CBF /* GetSubDevList.m in Sources */,
				AD8DD1962643E67A00086CBF /* GetSubDevListBySubDevice.m in Sources */,
				A0CE394C27576A60003FD92C /* GetDeviceNetworkLevel.m in Sources */,
				966049252A2DC39F00138B09 /* UpEngineDevice+ResourceConfigSource.m in Sources */,
				AD8DD1972643E67A00086CBF /* InFocus.m in Sources */,
				AD8DD1982643E67A00086CBF /* IsBound.m in Sources */,
				AD8DD19A2643E67A00086CBF /* IsModuleNeedOta.m in Sources */,
				4F7EB0922D9524AC007694FE /* UpDeviceCardInfo.m in Sources */,
				AD8DD19B2643E67A00086CBF /* OutFocus.m in Sources */,
				3382E04E26E6087B009576F5 /* GetDeviceOnlineStatus.m in Sources */,
				AD8DD19C2643E67A00086CBF /* RefresUsdkDeviceList.m in Sources */,
				AD8DD19D2643E67A00086CBF /* SmartLinkSoftwareVersion.m in Sources */,
				AD8DD19E2643E67A00086CBF /* StartBoardFOTA.m in Sources */,
				AD8DD19F2643E67A00086CBF /* StartModuleUpdate.m in Sources */,
				ADFEFDBB2702E3AB00B336F0 /* DetachDeviceWithoutConnect.m in Sources */,
				AD8DD1A02643E67A00086CBF /* WifiDeviceAction.m in Sources */,
				AD8DD1A12643E67A00086CBF /* WifiToolkitAction.m in Sources */,
				22F785CF265CEDDB00175218 /* CreateDeviceGroup.m in Sources */,
				22F78605265E1E2500175218 /* IsGroup.m in Sources */,
				4F6869922B883B4B00943C1A /* ExecuteCommandWithResult.m in Sources */,
				AD8DD1A22643E67A00086CBF /* WifiDeviceHelper.m in Sources */,
				AD113C032730DB7700EDB9F1 /* GetDeviceSleepState.m in Sources */,
				22F785CB265CEDDB00175218 /* FetchGroupableDeviceList.m in Sources */,
				22F785D2265CEDDB00175218 /* DeleteDeviceGroup.m in Sources */,
				AD8DD1A32643E67A00086CBF /* WifiDeviceToolkitImpl.m in Sources */,
				AD8DD1A42643E67A00086CBF /* WifiDeviceType.m in Sources */,
				AD8DD1A52643E67A00086CBF /* UpDeviceDataSourceWrapper.m in Sources */,
				4AC22BEB2754B1B200BC7418 /* GetDeviceOnlineStateV2.m in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		AD98A4FC265B74C700EDAEA3 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				AD98ABE8265B8B7B00EDAEA3 /* DefaultDetectListener.m in Sources */,
				AD98AC0C265B8B7D00EDAEA3 /* UpDeviceCreator.m in Sources */,
				AD98AC09265B8B7D00EDAEA3 /* UpCompositeProcessor.m in Sources */,
				AD98ABE0265B8B7B00EDAEA3 /* DefaultDeviceBroker.m in Sources */,
				AD98AC11265B8B7D00EDAEA3 /* DeviceNetWorkQualityInfo.m in Sources */,
				AD98ABE4265B8B7B00EDAEA3 /* DefaultReportListener.m in Sources */,
				8457E18E2D8AC31B007EBE53 /* UpDeviceReportMonitor.m in Sources */,
				AD98AC0B265B8B7D00EDAEA3 /* UpDeviceFilters.m in Sources */,
				AD98AC03265B8B7D00EDAEA3 /* UpDeviceBase.m in Sources */,
				AD98AC36265B8B7F00EDAEA3 /* UpDeviceHelper.m in Sources */,
				AD98AC22265B8B7E00EDAEA3 /* DeviceExtras.m in Sources */,
				AD98AC20265B8B7E00EDAEA3 /* DeviceBLEHistoryInfo.m in Sources */,
				AD98AC12265B8B7D00EDAEA3 /* DeviceAttribute.m in Sources */,
				AD98AC1B265B8B7E00EDAEA3 /* DeviceFOTAInfo.m in Sources */,
				AD98AC23265B8B7E00EDAEA3 /* DeviceCaution.m in Sources */,
				AD98AC21265B8B7E00EDAEA3 /* DeviceInfo.m in Sources */,
				AD98AC15265B8B7E00EDAEA3 /* DeviceFOTAStatusInfo.m in Sources */,
				AD98AC07265B8B7D00EDAEA3 /* UpDeviceCache.m in Sources */,
				AD98AC33265B8B7F00EDAEA3 /* UpDeviceResult.m in Sources */,
				AD98AC0F265B8B7D00EDAEA3 /* DeviceBaseInfo.m in Sources */,
				AD98ABFB265B8B7C00EDAEA3 /* UpCommonDevice.m in Sources */,
				AD98AC43265B8B8000EDAEA3 /* EmptyDeviceToolkit.m in Sources */,
				AD98ABDE265B8B7B00EDAEA3 /* DefaultBrokerHolder.m in Sources */,
				AD98AC1E265B8B7E00EDAEA3 /* DeviceOTAStatusInfo.m in Sources */,
				AD98AC34265B8B7F00EDAEA3 /* UpDeviceCarrier.m in Sources */,
				AD98ABE1265B8B7B00EDAEA3 /* DefaultToolkitListener.m in Sources */,
				AD98AC3E265B8B7F00EDAEA3 /* UpDeviceException.m in Sources */,
				AD98ABF7265B8B7C00EDAEA3 /* UpDeviceReporter.m in Sources */,
				AD98ABE5265B8B7B00EDAEA3 /* DefaultNotification.m in Sources */,
				AD98AC3B265B8B7F00EDAEA3 /* UpStringResult.m in Sources */,
				AD98AC17265B8B7E00EDAEA3 /* DeviceCommand.m in Sources */,
				AD98AC32265B8B7F00EDAEA3 /* UpDeviceAction.m in Sources */,
				33FDA62026285A690099964E /* UpWashDevice.m in Sources */,
				33FDA77B262ECA0E0099964E /* UpWashDataResponseParser.m in Sources */,
				84AEE78D2DA65221008FF4C7 /* uSDKDeviceWrapper.m in Sources */,
				33FDA62226285A690099964E /* UpWashModel.m in Sources */,
				33FDA769262D619B0099964E /* UpVoiceBoxDevice.m in Sources */,
				33FDA62526285A690099964E /* UpWashAdapterApi.m in Sources */,
				F8313D0825E0CFFD00F8A479 /* (null) in Sources */,
				40E0AC4925148816007DBC42 /* UDSafeMutableArray.m in Sources */,
				33FDA61326285A3E0099964E /* UpCompatEngineDevice.m in Sources */,
				40E0AC4B25148816007DBC42 /* UDSafeMutableDictionary.m in Sources */,
				8412D69C2D7F0C02002B44F7 /* UpDeviceCacheManager.m in Sources */,
				AD27726B2600587F00287D0C /* (null) in Sources */,
				AD2772632600576300287D0C /* (null) in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		F863149D250A01E200C948B9 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				F86314AB250A01E200C948B9 /* ViewController.m in Sources */,
				F86314A5250A01E200C948B9 /* AppDelegate.m in Sources */,
				F86314B6250A01E400C948B9 /* main.m in Sources */,
				F86314A8250A01E200C948B9 /* SceneDelegate.m in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		F86314BA250A01F000C948B9 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				22F785E9265CF88B00175218 /* DeviceBaseSteps.m in Sources */,
				221C2A2425C14D3D00D68224 /* FakeDelegateFactory.m in Sources */,
				A0CFB3AD282CFBDC007E27BA /* FakeResourceManager.m in Sources */,
				22344EC92631833300FF4ACA /* FakeLEEngineListener.m in Sources */,
				22F785E7265CF88B00175218 /* InitializationSteps.m in Sources */,
				A0277547298A3DC80014920F /* FakeWashdapterApi.m in Sources */,
				22F785E8265CF88B00175218 /* EngineDeviceSteps.m in Sources */,
				2232840B25BEA9A4001CBE68 /* CustomFilterProvider.m in Sources */,
				22344EC62630102600FF4ACA /* FakeDetectListener.m in Sources */,
				A0C41BE1294C11BB00A877F9 /* UpDeviceResourceSteps.m in Sources */,
				F86314C1250A01F000C948B9 /* CucumberRunner.m in Sources */,
				22F785E4265CF88B00175218 /* DeviceManagerSteps.m in Sources */,
				22F785EC265CF89C00175218 /* StepsUtils.m in Sources */,
				225B08EE25BAA53900E5E86A /* FakeDelegateListener.m in Sources */,
				22F785F1265D022D00175218 /* FakeDelegateBase.m in Sources */,
				22F785E6265CF88B00175218 /* WifiDeviceToolkitSteps.m in Sources */,
				22A5225B25C3EDB000E76C88 /* FakeUSDKDevice.m in Sources */,
				2232841F25C01B74001CBE68 /* FakeUpDeviceBase.m in Sources */,
				22344EC3262FC0FA00FF4ACA /* FakeReportListener.m in Sources */,
				A0277544298A3D760014920F /* WashDeviceManagerSteps.m in Sources */,
				2232842025C01B74001CBE68 /* FakeUpDeviceReceiver.m in Sources */,
				2232842125C01B74001CBE68 /* FakeUpDeviceListener.m in Sources */,
				22F785E5265CF88B00175218 /* WashDeviceSteps.m in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXSourcesBuildPhase section */

/* Begin PBXTargetDependency section */
		AD8DD1FD2643F5C300086CBF /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			target = AD8DD1112643E5A300086CBF /* UPDevice */;
			targetProxy = AD8DD1FC2643F5C300086CBF /* PBXContainerItemProxy */;
		};
		AD8DD1FF2643F5CF00086CBF /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			target = AD8DD0FE2643E48600086CBF /* UPDeviceUtil */;
			targetProxy = AD8DD1FE2643F5CF00086CBF /* PBXContainerItemProxy */;
		};
		AD8DD2012643F5D600086CBF /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			target = AD8DD1112643E5A300086CBF /* UPDevice */;
			targetProxy = AD8DD2002643F5D600086CBF /* PBXContainerItemProxy */;
		};
		AD98A5F1265B7D4D00EDAEA3 /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			target = AD98A4FF265B74C700EDAEA3 /* updevice_api */;
			targetProxy = AD98A5F0265B7D4D00EDAEA3 /* PBXContainerItemProxy */;
		};
/* End PBXTargetDependency section */

/* Begin PBXVariantGroup section */
		F86314AC250A01E200C948B9 /* Main.storyboard */ = {
			isa = PBXVariantGroup;
			children = (
				F86314AD250A01E200C948B9 /* Base */,
			);
			name = Main.storyboard;
			sourceTree = "<group>";
		};
		F86314B1250A01E400C948B9 /* LaunchScreen.storyboard */ = {
			isa = PBXVariantGroup;
			children = (
				F86314B2250A01E400C948B9 /* Base */,
			);
			name = LaunchScreen.storyboard;
			sourceTree = "<group>";
		};
/* End PBXVariantGroup section */

/* Begin XCBuildConfiguration section */
		AD8DD1062643E48700086CBF /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CODE_SIGN_STYLE = Automatic;
				IPHONEOS_DEPLOYMENT_TARGET = 9.0;
				OTHER_LDFLAGS = "-ObjC";
				PRODUCT_NAME = "$(TARGET_NAME)";
				SKIP_INSTALL = YES;
				TARGETED_DEVICE_FAMILY = "1,2";
			};
			name = Debug;
		};
		AD8DD1072643E48700086CBF /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CODE_SIGN_STYLE = Automatic;
				IPHONEOS_DEPLOYMENT_TARGET = 9.0;
				OTHER_LDFLAGS = "-ObjC";
				PRODUCT_NAME = "$(TARGET_NAME)";
				SKIP_INSTALL = YES;
				TARGETED_DEVICE_FAMILY = "1,2";
			};
			name = Release;
		};
		AD8DD1192643E5A300086CBF /* Debug */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 59F3630A2EB70C2F08991990 /* Pods-UPDevice.debug.xcconfig */;
			buildSettings = {
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CODE_SIGN_STYLE = Automatic;
				IPHONEOS_DEPLOYMENT_TARGET = 9.0;
				OTHER_LDFLAGS = (
					"-ObjC",
					"$(inherited)",
					"-ld_classic",
				);
				PRODUCT_NAME = "$(TARGET_NAME)";
				SKIP_INSTALL = YES;
				TARGETED_DEVICE_FAMILY = "1,2";
			};
			name = Debug;
		};
		AD8DD11A2643E5A300086CBF /* Release */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = BF52CE274AFD8556CC64E316 /* Pods-UPDevice.release.xcconfig */;
			buildSettings = {
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CODE_SIGN_STYLE = Automatic;
				IPHONEOS_DEPLOYMENT_TARGET = 9.0;
				OTHER_LDFLAGS = (
					"-ObjC",
					"$(inherited)",
					"-ld_classic",
				);
				PRODUCT_NAME = "$(TARGET_NAME)";
				SKIP_INSTALL = YES;
				TARGETED_DEVICE_FAMILY = "1,2";
			};
			name = Release;
		};
		AD98A506265B74C700EDAEA3 /* Debug */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = B80DBE2ACF90E4399EE02E5B /* Pods-updevice_api.debug.xcconfig */;
			buildSettings = {
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CODE_SIGN_STYLE = Automatic;
				IPHONEOS_DEPLOYMENT_TARGET = 12.0;
				OTHER_LDFLAGS = (
					"-ObjC",
					"$(inherited)",
					"-ld_classic",
				);
				PRODUCT_NAME = "$(TARGET_NAME)";
				SKIP_INSTALL = YES;
				TARGETED_DEVICE_FAMILY = "1,2";
			};
			name = Debug;
		};
		AD98A507265B74C700EDAEA3 /* Release */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 633BDFCF19857B79B382F7E8 /* Pods-updevice_api.release.xcconfig */;
			buildSettings = {
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CODE_SIGN_STYLE = Automatic;
				IPHONEOS_DEPLOYMENT_TARGET = 12.0;
				OTHER_LDFLAGS = (
					"-ObjC",
					"$(inherited)",
					"-ld_classic",
				);
				PRODUCT_NAME = "$(TARGET_NAME)";
				SKIP_INSTALL = YES;
				TARGETED_DEVICE_FAMILY = "1,2";
			};
			name = Release;
		};
		F8631498250A018800C948B9 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++14";
				CLANG_CXX_LIBRARY = "libc++";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				COPY_PHASE_STRIP = NO;
				CURRENT_PROJECT_VERSION = 1;
				DEBUG_INFORMATION_FORMAT = dwarf;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				ENABLE_TESTABILITY = YES;
				EXCLUDED_ARCHS = "";
				GCC_C_LANGUAGE_STANDARD = gnu11;
				GCC_DYNAMIC_NO_PIC = NO;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_OPTIMIZATION_LEVEL = 0;
				GCC_PREPROCESSOR_DEFINITIONS = (
					"DEBUG=1",
					"$(inherited)",
				);
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 12.0;
				MTL_ENABLE_DEBUG_INFO = INCLUDE_SOURCE;
				MTL_FAST_MATH = YES;
				ONLY_ACTIVE_ARCH = YES;
				SDKROOT = iphoneos;
				VERSIONING_SYSTEM = "apple-generic";
				VERSION_INFO_PREFIX = "";
			};
			name = Debug;
		};
		F8631499250A018800C948B9 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++14";
				CLANG_CXX_LIBRARY = "libc++";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				COPY_PHASE_STRIP = NO;
				CURRENT_PROJECT_VERSION = 1;
				DEBUG_INFORMATION_FORMAT = "dwarf-with-dsym";
				ENABLE_NS_ASSERTIONS = NO;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				GCC_C_LANGUAGE_STANDARD = gnu11;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 12.0;
				MTL_ENABLE_DEBUG_INFO = NO;
				MTL_FAST_MATH = YES;
				SDKROOT = iphoneos;
				VALIDATE_PRODUCT = YES;
				VERSIONING_SYSTEM = "apple-generic";
				VERSION_INFO_PREFIX = "";
			};
			name = Release;
		};
		F86314B8250A01E400C948B9 /* Debug */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = CE3BE4FF448563987E285035 /* Pods-UPDeviceDebugger.debug.xcconfig */;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				CODE_SIGN_IDENTITY = "iPhone Developer";
				CODE_SIGN_STYLE = Manual;
				DEVELOPMENT_TEAM = PP27UD8NYZ;
				ENABLE_BITCODE = NO;
				"EXCLUDED_ARCHS[sdk=*]" = arm64;
				INFOPLIST_FILE = UPDeviceDebugger/Info.plist;
				IPHONEOS_DEPLOYMENT_TARGET = 12.0;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
				);
				OTHER_LDFLAGS = (
					"$(inherited)",
					"-ld_classic",
				);
				PRODUCT_BUNDLE_IDENTIFIER = com.haier.uhome.Uplus;
				PRODUCT_NAME = "$(TARGET_NAME)";
				PROVISIONING_PROFILE_SPECIFIER = Uplus99Dev;
				TARGETED_DEVICE_FAMILY = "1,2";
			};
			name = Debug;
		};
		F86314B9250A01E400C948B9 /* Release */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = EE39A92C771E28C3D6036669 /* Pods-UPDeviceDebugger.release.xcconfig */;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				CODE_SIGN_IDENTITY = "iPhone Developer";
				CODE_SIGN_STYLE = Manual;
				DEVELOPMENT_TEAM = PP27UD8NYZ;
				ENABLE_BITCODE = NO;
				INFOPLIST_FILE = UPDeviceDebugger/Info.plist;
				IPHONEOS_DEPLOYMENT_TARGET = 12.0;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
				);
				OTHER_LDFLAGS = (
					"$(inherited)",
					"-ld_classic",
				);
				PRODUCT_BUNDLE_IDENTIFIER = com.haier.uhome.Uplus;
				PRODUCT_NAME = "$(TARGET_NAME)";
				PROVISIONING_PROFILE_SPECIFIER = Uplus99Dev;
				TARGETED_DEVICE_FAMILY = "1,2";
			};
			name = Release;
		};
		F86314C7250A01F000C948B9 /* Debug */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 88ED39B27363FAF9CE965C37 /* Pods-UPDeviceTests.debug.xcconfig */;
			buildSettings = {
				CODE_SIGN_STYLE = Automatic;
				DEVELOPMENT_TEAM = 6A7RH6BGBK;
				INFOPLIST_FILE = UPDeviceTests/Info.plist;
				IPHONEOS_DEPLOYMENT_TARGET = 12.0;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
					"@loader_path/Frameworks",
				);
				OTHER_LDFLAGS = (
					"$(inherited)",
					"-ld_classic",
				);
				PRODUCT_BUNDLE_IDENTIFIER = "Zhu-KaiQi.UPDeviceTests";
				PRODUCT_NAME = "$(TARGET_NAME)";
				TARGETED_DEVICE_FAMILY = "1,2";
			};
			name = Debug;
		};
		F86314C8250A01F000C948B9 /* Release */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 8A6C8EB9EC9E184964C16C36 /* Pods-UPDeviceTests.release.xcconfig */;
			buildSettings = {
				CODE_SIGN_STYLE = Automatic;
				DEVELOPMENT_TEAM = 6A7RH6BGBK;
				INFOPLIST_FILE = UPDeviceTests/Info.plist;
				IPHONEOS_DEPLOYMENT_TARGET = 12.0;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
					"@loader_path/Frameworks",
				);
				OTHER_LDFLAGS = (
					"$(inherited)",
					"-ld_classic",
				);
				PRODUCT_BUNDLE_IDENTIFIER = "Zhu-KaiQi.UPDeviceTests";
				PRODUCT_NAME = "$(TARGET_NAME)";
				TARGETED_DEVICE_FAMILY = "1,2";
			};
			name = Release;
		};
/* End XCBuildConfiguration section */

/* Begin XCConfigurationList section */
		AD8DD1052643E48700086CBF /* Build configuration list for PBXNativeTarget "UPDeviceUtil" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				AD8DD1062643E48700086CBF /* Debug */,
				AD8DD1072643E48700086CBF /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		AD8DD1182643E5A300086CBF /* Build configuration list for PBXNativeTarget "UPDevice" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				AD8DD1192643E5A300086CBF /* Debug */,
				AD8DD11A2643E5A300086CBF /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		AD98A508265B74C700EDAEA3 /* Build configuration list for PBXNativeTarget "updevice_api" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				AD98A506265B74C700EDAEA3 /* Debug */,
				AD98A507265B74C700EDAEA3 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		F863148C250A018800C948B9 /* Build configuration list for PBXProject "UPDevice" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				F8631498250A018800C948B9 /* Debug */,
				F8631499250A018800C948B9 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		F86314B7250A01E400C948B9 /* Build configuration list for PBXNativeTarget "UPDeviceDebugger" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				F86314B8250A01E400C948B9 /* Debug */,
				F86314B9250A01E400C948B9 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		F86314C6250A01F000C948B9 /* Build configuration list for PBXNativeTarget "UPDeviceTests" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				F86314C7250A01F000C948B9 /* Debug */,
				F86314C8250A01F000C948B9 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
/* End XCConfigurationList section */
	};
	rootObject = F8631489250A018800C948B9 /* Project object */;
}
