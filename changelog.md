# 7.6.0
## TASK:
> -x_haieruplus-10988 iOS_全屋接入4键智能开关卡片显示<br /> 
> -x_haieruplus-10962 iOS_智家APP开启全局module<br /> 
## BUG:
> -x_haieruplus-12410 iOS UpDevice库 和 UpDevicePlugin库异常上报<br /> 

# 7.4.0
## TASK:
> -x_haieruplus-4971 iOS_设备支持获取应用侧属性描述文件<br /> 

# 7.3.0
## TASK:
> -x_haieruplus-3434 iOS_设备卡片快捷方式启动带有附件设备或者子设备的设备功能完善<br /> 
> -x_haieruplus-3287 iOS_获取组设备的成员列表接口设备信息补全（产业需求）<br /> 
## BUG:
> -x_haieruplus-3865 UPDevice库UpDeviceReporter崩溃问题<br /> 
> -x_haieruplus-3867 【iOS】清缓存重启APP(或者新安装后首次进入智家)，智能灯具、空调等设备卡片不显示开关按钮，线上版本7.15.0也有<br /> 

# 7.2.0
## BUG:
> -x_haieruplus-2660 灯组创建失败-超时<br /> 

# 7.1.0
## TASK:
> -ZHIJIAAPP-46281 iOS_减少UPDevice和logicengine模块日志输出, 提升Bug分析效率<br /> 
> -ZHIJIAAPP-46282 iOS_减少属性上报过程非必要属性计算提升UI状态刷新速度<br /> 
## BUG:
> -ZHIJIAAPP-47065 iPhone12 pro，退出登录，app必现闪退<br /> 

# 7.0.0
## TASK:
> -ZHIJIAAPP-43134 iOS_UPDevice、logicengine增加获取蓝牙连接状态接口<br /> 

# 6.9.0
## TASK:
> -ZHIJIAAPP-41067 iOS_JY-UPDevice解藕配置文件下载和设备订阅<br /> 
> -ZHIJIAAPP-41737 iOS_JY-设备上报属性功能优化<br /> 
## BUG:
> -ZHIJIAAPP-41711 设备鉴权后没有获取到typeid<br /> 

# 6.8.0
## TASK:
> -ZHIJIAAPP-38219 iOS_iOS 空安全隐患的修改(二期)<br /> 
> -ZHIJIAAPP-38272 iOS_音箱组合需求开发<br /> 
> -ZHIJIAAPP-38228 iOS_快连设备自发现完成后补充更新设备详细信息<br /> 
> -ZHIJIAAPP-40007 iOS_JY-适配usdk8.15.0新快连接口<br /> 

# 6.7.0
## TASK:
> -ZHIJIAAPP-36144 iOS_US-详情页框架能力支持-绑定者用户中心userid<br /> 
## BUG:
> -ZHIJIAAPP-36805 快连过程中，关闭手机蓝牙，app崩溃<br /> 
> -ZHIJIAAPP-37083 快连失败，重试添加多次，都是立即提示添加设备失败，请重试<br /> 
> -ZHIJIAAPP-37007 快连3.0设备连接 补充监听超时回调<br /> 

# 6.6.0
## TASK:
> -ZHIJIAAPP-35189 iOS_uSDK日志接入日志系统<br /> 
> -ZHIJIAAPP-35276 iOS_快连3.0二期功能开发<br /> 
> -ZHIJIAAPP-35176 iOS_US-详情页设置Wi-Fi公共模块<br /> 

# 6.5.0
## TASK:
> -ZHIJIAAPP-33557 iOS_iOS 空安全隐患的修改<br /> 
> -ZHIJIAAPP-33497 iOS_US-设备信号弱监听需求<br /> 
> -ZHIJIAAPP-33709 iOS_US-离线体验优化-设备卡片<br /> 

# 6.4.0
## TASK:
> -ZHIJIAAPP-32121 iOS_US-埋点需求-智慧家
> -ZHIJIAAPP-32936 iOS_US-可视门铃（HCD-18B20-U1）UE审核
