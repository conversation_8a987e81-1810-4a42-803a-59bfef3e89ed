//
//  UpDeviceToolkit.h
//  updevice_api
//
//  Created by <PERSON><PERSON> on 2018/12/17.
//  Copyright © 2018 Haierfc. All rights reserved.
//

#import <Foundation/Foundation.h>
#import "UpDeviceAttribute.h"
#import "UpDeviceBaseInfo.h"
#import "UpDeviceExecutable.h"
#import "UpDeviceResult.h"
#import "UpDeviceCaution.h"
#import "UpDeviceCommand.h"
#import "UpDeviceConnection.h"
#import "UpDeviceToolkitState.h"
#import "UpDeviceToolkitListener.h"
#import "UpDeviceDetectListener.h"
#import "UpDeviceReportListener.h"
#import "DeviceFOTAInfo.h"
#import "DeviceFOTAStatusInfo.h"
#import "UPDeviceNetType.h"
#import "UpDeviceTracker.h"
#import "uSDKDeviceWrapper.h"
static const NSTimeInterval TIMEOUT_MINIMUM = 5;
static const NSTimeInterval TIMEOUT_DEFAULT = 15;
static const NSTimeInterval TIMEOUT_MAXIMUM = 60;

@protocol UpDeviceToolkit <NSObject, UpDeviceExecutable>
/**
 * 本工具包支持的协议，如：WiFi(uSDK),Bluetooth等
 *
 * @return 支持的协议
 */
- (NSString *)getSupportProtocol;

/**
 * 获取设备工具的版本号
 *
 * @return 版本号，如：1.0.0
 */
- (NSString *)getToolkitVersion;

/**
 * 获取设备工具的运行状态
 *
 * @return 状态，见{@link UpDeviceToolkitState}
 */
- (UpDeviceToolkitState)getToolkitState;

/**
 * 连接设备工具，监听设备列表变化
 *
 * @param deviceToolkitListener 工具状态监听器
 * @param deviceDetectListener  设备列表监听器
 */
- (void)attachToolkit:(id<UpDeviceToolkitListener>)deviceToolkitListener detectListener:(id<UpDeviceDetectListener>)deviceDetectListener finishBlock:(void (^)(UpDeviceResult *result))finishBlock;
/**
 * 断开设备工具，取消监听设备列表变化
 *
*/
- (void)detachToolkit:(void (^)(UpDeviceResult *result))finishBlock;

// -------------------- 设备相关通用接口 --------------------


/**
 * 连接远程网关
 */
- (void)connectRemoteDevices:(NSMutableArray<id<UpDeviceBaseInfo>> *)baseInfoList params:(NSMutableDictionary *)params finishBlock:(void (^)(UpDeviceResult *result))finishBlock;

/**
 * 断开远程网关
 */
- (void)disconnectRemoteDevices:(void (^)(UpDeviceResult *result))finishBlock;

/**
 * 获取设备列表
 *
 * finishBlock 操作结果+设备列表
 */
- (void)getDeviceBaseInfoList:(void (^)(UpDeviceResult *result))finishBlock;

/**
 * 连接设备，监听设备/子设备状态变化
 *
 * @param deviceId 设备ID或子设备ID
 * @param listener 设备监听器
 * @finishBlock 操作结果
 */
- (void)attachDevice:(NSString *)deviceId reportListener:(id<UpDeviceReportListener>)listener finishBlock:(void (^)(UpDeviceResult *result))finishBlock;

/**
 * 断开设备，监听设备/子设备状态变化
 *
 * @param deviceId 设备ID或子设备ID
 * @finishBlock 操作结果
 */
- (void)detachDevice:(NSString *)deviceId finishBlock:(void (^)(UpDeviceResult *result))finishBlock;

/**
 * 获取设备/子设备基础信息
 *
 * @param deviceId 设备ID或子设备ID
 * @finishBlock 操作结果+设备/子设备基础信息
 */
- (void)getDeviceBaseInfo:(NSString *)deviceId finishBlock:(void (^)(UpDeviceResult *result))finishBlock;

/**
 * 获取设备/子设备连接状态
 *
 * @param deviceId 设备ID或子设备ID
 * @finishBlock 操作结果+连接状态
 */
- (void)getDeviceConnection:(NSString *)deviceId finishBlock:(void (^)(UpDeviceResult *result))finishBlock;

/**
 * 获取设备控制状态
 *
 * @param deviceId 设备ID或子设备ID
 * @finishBlock 操作结果+连接状态
 */
- (void)getDeviceControlState:(NSString *)deviceId finishBlock:(void (^)(UpDeviceResult *result))finishBlock;

/**
 * 获取设备故障码
 *
 * @param deviceId 设备ID或子设备ID
 * @finishBlock 操作结果+连接状态
 */
- (void)getDeviceFaultInformationCode:(NSString *)deviceId finishBlock:(void (^)(UpDeviceResult *result))finishBlock;

/**
 * 获取设备/子设备真实在线状态
 *
 * @param deviceId 设备ID或子设备ID
 * @finishBlock 操作结果+真实在线状态
 */
- (void)getDeviceOnlineStatus:(NSString *)deviceId finishBlock:(void (^)(UpDeviceResult *result))finishBlock;

/// 获取设备/子设备真实在线状态V2
/// @param deviceId 设备ID或子设备ID
/// @param finishBlock 操作结果+真实在线状态
- (void)getDeviceOnlineStateV2:(NSString *)deviceId finishBlock:(void (^)(UpDeviceResult *result))finishBlock;

/// 获取设备/子设备wifi在线状态
/// @param deviceId 设备ID或子设备ID
/// @Return wifi在线状态
- (UpDeviceRealOnline)getDeviceWifiOnlineState:(NSString *)deviceId;

/**
 * 获取设备睡眠状态
 *
 * @param deviceId 设备ID或子设备ID
 * @finishBlock 操作结果+设备睡眠状态
 */
- (void)getDeviceSleepState:(NSString *)deviceId finishBlock:(void (^)(UpDeviceResult *result))finishBlock;

/**
 * 获取设备网络质量等级
 *
 * @param deviceId 设备ID或子设备ID
 * @finishBlock 操作结果+设备网络质量等级
 */
- (void)getDeviceNetWorkLevel:(NSString *)deviceId finishBlock:(void (^)(UpDeviceResult *result))finishBlock;


/**
 * 获取设备的子设备基础信息列表
 *
 * @param deviceId 设备ID，<strong>此方法不能传入子设备ID</strong>
 * @finishBlock 操作结果+子设备基础信息列表
 */
- (void)getSubDevBaseInfoList:(NSString *)deviceId finishBlock:(void (^)(UpDeviceResult *result))finishBlock;
/**
 * 获取设备/子设备属性列表
 *
 * @param deviceId 设备ID或子设备ID
 * @finishBlock 操作结果+属性列表
 */
- (void)getDeviceAttributeList:(NSString *)deviceId finishBlock:(void (^)(UpDeviceResult *result))finishBlock;

/**
 * 获取设备/子设备告警列表
 *
 * @param deviceId 设备ID或子设备ID
 * @finishBlock 操作结果+告警列表
 */
- (void)getDeviceCautionList:(NSString *)deviceId finishBlock:(void (^)(UpDeviceResult *result))finishBlock;

/**
 * 获取指定名称的设备/子设备属性数据
 *
 * @param deviceId 设备ID或子设备ID
 * @param name     属性名称
 * @param timeout  超时时间（秒），取值范围{@link UpDeviceToolkit#TIMEOUT_MINIMUM} ~ {@link UpDeviceToolkit#TIMEOUT_MAXIMUM}
 * @finishBlock 操作结果+属性
 */
- (void)getDeviceAttributeByName:(NSString *)name deviceId:(NSString *)deviceId timeout:(NSTimeInterval)timeout finishBlock:(void (^)(UpDeviceResult *result))finishBlock;

/**
 * 下发指令给设备/子设备
 *
 * @param deviceId 设备ID或子设备ID
 * @param command  设备指令
 * @param timeout  超时时间（秒），取值范围{@link UpDeviceToolkit#TIMEOUT_MINIMUM} ~ {@link UpDeviceToolkit#TIMEOUT_MAXIMUM}
 * @finishBlock 操作结果
 */
- (void)executeDeviceCommand:(NSString *)deviceId command:(id<UpDeviceCommand>)command timeout:(NSTimeInterval)timeout finishBlock:(void (^)(UpDeviceResult *result))finishBlock;

/**
 * 下发指令给设备/子设备,结果返回 traceId
 *
 * @param deviceId 设备ID或子设备ID
 * @param command  设备指令
 * @param timeout  超时时间（秒），取值范围{@link UpDeviceToolkit#TIMEOUT_MINIMUM} ~ {@link UpDeviceToolkit#TIMEOUT_MAXIMUM}
 * @finishBlock 操作结果
 * @since 7.14.0
 */
@optional
- (void)executeCommandWithResult:(NSString *)deviceId command:(id<UpDeviceCommand>)command timeout:(NSTimeInterval)timeout finishBlock:(void (^)(UpDeviceResult *result))finishBlock;

/**
 *  @brief  开始设备底板固件的升级，执行成功后，设备的最新升级状态 会通过uSDKDevice的代理方法 持续上报
 *
 *  @param deviceId 设备ID或子设备ID
 *  @param finishBlock 操作结果
 *
 *  @since  1.1.0
 */
- (void)startBoardFOTA:(NSString *)deviceId finishBlock:(void (^)(UpDeviceResult *result))finishBlock;

/**
 *  @brief  检查设备底板固件的版本信息
 *
 *  @param deviceId 设备ID或子设备ID
 *  @param finishBlock 操作结果,成功时DeviceFOTAInfo在result的extraData中。（DeviceFOTAInfo ：设备固件版本信息） 注意：当isNeedFOTA字段为 YES 时，表明设备有需要更新的固件版本。当isNeedFOTA字段为 NO 时，表明设备不支持升级或没有需要更新的固件版本。
 *
 *  @since  1.1.0
 */
- (void)checkBoardFOTAInfoSuccess:(NSString *)deviceId finishBlock:(void (^)(UpDeviceResult *result))finishBlock;

/**
 *  @brief  查询当前设备底板固件的升级状态
 *
 *  @param deviceId 设备ID或子设备ID
 *  @param finishBlock 操作结果,成功时DeviceFOTAStatusInfo在result的extraData中。(DeviceFOTAStatusInfo 设备升级进度信息)
 *
 *  @since  1.1.0
 */
- (void)fetchBoardFOTAStatusSuccess:(NSString *)deviceId finishBlock:(void (^)(UpDeviceResult *result))finishBlock;

/**
 *  @brief  开始设备模块升级
 *
 *  @param deviceId 设备ID或子设备ID
 *  @param finishBlock 操作结果,上报进度时DeviceOTAStatusInfo在result的extraData中。(DeviceOTAStatusInfo 设备（模块）升级进度信息)。成功h或失败时只返回结果，不返回extraData。
 *
 *  @since  1.3.0
 */
- (void)startModuleUpdateSuccess:(NSString *)deviceId finishBlock:(void (^)(UpDeviceResult *result))finishBlock;

/**
 *  @brief  开始获取蓝牙历史数据
 *
 *  @param deviceId 设备ID或子设备ID
 *  @param finishBlock 操作结果。
 *
 *  @since  1.3.1
 */
- (void)fetchBLEHistoryData:(NSString *)deviceId finishBlock:(void (^)(UpDeviceResult *result))finishBlock;

/**
 *  @brief  取消获取蓝牙历史数据
 *
 *  @param deviceId 设备ID或子设备ID
 *  @param finishBlock 操作结果。
 *
 *  @since  1.3.1
 */
- (void)cancelFetchBLEHistoryData:(NSString *)deviceId finishBlock:(void (^)(UpDeviceResult *result))finishBlock;

/**
 *  @brief  判断小循环升级状态
 *
 *  @param deviceId 设备ID或子设备ID
 *
 *  @return 小循环升级状态
 *
 *  @since  1.3.2
 */
- (BOOL)isModuleNeedOta:(NSString *)deviceId;

/**
 *  @brief  标识设备是否被绑定了
 *
 *  @param deviceId 设备ID或子设备ID
 *
 *  @return 设备是否被绑定
 *
 *  @since  1.4.0
 */
- (BOOL)isBound:(NSString *)deviceId;

/**
 *  @brief  获取设备绑定信息
 *
 *  @param deviceId 设备ID或子设备ID
 *  @param finishBlock 操作结果。
 *
 *  @since  1.4.0
 */
- (void)getDeviceBindInfo:(NSString *)deviceId finishBlock:(void (^)(UpDeviceResult *result))finishBlock;

/**
 *  @brief  刷新已绑定的设备列表，成功只表示命令发送成功，云平台收到请求后，会下发最新的绑定设备列表，如果与USDK内部缓存的列表有变化（增加或减少），则通过代理方法deviceManager:didAddDevices:和deviceManager:didRemoveDevices:通知App
 *
 *  @param finishBlock 操作结果。
 *
 *  @since  1.4.0
 */
- (void)refreshUsdkDeviceList:(void (^)(UpDeviceResult *result))finishBlock;

/**
 *  @brief  获取模块版本号
 *
 *  @param deviceId 设备ID或子设备ID
 *
 *  @return 设备模块版本。
 *
 *  @since  1.4.6
 */
- (NSString *)getSmartLinkSoftwareVersion:(NSString *)deviceId;

/**
 *  @brief  通过子机获得子机列表
 *
 *  @param deviceId 子设备ID,不可传入主机ID
 *  @param finishBlock 操作结果。
 *
 *  @since  1.4.7
 */
- (void)getSubDevListBySubDev:(NSString *)deviceId finishBlock:(void (^)(UpDeviceResult *result))finishBlock;

/**
 * 获取设备本地wifi连接状态
 *
 * @param deviceId 设备ID或子设备ID 
 * @finishBlock 操作结果+设备睡眠状态
 */
- (void)getDeviceWifiLocalState:(NSString *)deviceId finishBlock:(void (^)(UpDeviceResult *result))finishBlock;

/**
 * 获取设备离线原因
 *
 * @param deviceId 设备ID或子设备ID
 * @finishBlock 操作结果+设备离线原因
 * @since v7.1.1
 */
- (void)getDeviceOfflineCause:(NSString *)deviceId finishBlock:(void (^)(UpDeviceResult *result))finishBlock;

/**
 * 获取设备离线天数
 *
 * @param deviceId 设备ID或子设备ID
 * @finishBlock 操作结果+设备离线天数
 * @since v7.19.0
 */
- (void)getDeviceOfflineDays:(NSString *)deviceId finishBlock:(void (^)(UpDeviceResult *result))finishBlock;

/**
 * 获取设备仅配网状态
 *
 * @param deviceId 设备ID或子设备ID
 * @finishBlock 操作结果+设备仅配网状态
 * @since v7.20.0
 */
- (void)getDeviceOnlyConfigState:(NSString *)deviceId finishBlock:(void (^)(UpDeviceResult *result))finishBlock;

/**
 * 获取设备是否支持仅配网
 *
 * @param deviceId 设备ID或子设备ID
 * @Return 是否支持仅配网
 * @since v7.27.0
 */
- (BOOL)isSupportOnlyConfig:(NSString *)deviceId;

/**
 * 获取设备是否要走仅配网流程
 *
 * @param deviceId 设备ID或子设备ID
 * @Return 是否要走仅配网流程
 * @since v7.27.0
 */
- (BOOL)isOnlyConfigFlow:(NSString *)deviceId;

/**
 标记设备进入焦点（详情页）
 
 进入焦点后，如果大循环控制超时，则提升蓝牙通道的优先级高于大循环且低于小循环通道
 @param deviceId 设备ID或子设备ID
 @return 重复进入返回NO
 @since 1.5.2
 */
- (BOOL)inFocus:(NSString *)deviceId;


/**
 标记设备退出焦点（详情页）
 @param deviceId 设备ID或子设备ID
 @return 重复退出返回NO
 @since 1.5.2
 */
- (BOOL)outFocus:(NSString *)deviceId;

/**
 *  @brief  获取网络质量
 *
 *  @param deviceId 设备ID或子设备ID
 *  @param finishBlock 操作结果,成功时DeviceNetWorkQualityInfo在result的extraData中。
 *
 *  @since  1.6.0
 */
- (void)getNetworkQuality:(NSString *)deviceId finishBlock:(void (^)(UpDeviceResult *result))finishBlock;

/// 获取设备网络类型
/// @param deviceId 设备ID或子设备ID
- (DeviceNetType)getNetType:(NSString *)deviceId;

/// 获取可与当前设备分到同一组的设备列表, 当前设备要求有zigbee能力或BLEMesh能力
/// @param deviceId 设备id
/// @param finishBlock 完成回调
- (void)fetchGroupableDeviceList:(NSString *)deviceId finishBlock:(void (^)(UpDeviceResult *result))finishBlock;

/// 创建分组
/// @param deviceId 设备id
/// @param finishBlock 完成回调
- (void)createDeviceGroup:(NSString *)deviceId finishBlock:(void (^)(UpDeviceResult *result))finishBlock;

/// 向组设备中添加设备，要求当前device对象为组设备
/// @param deviceId 设备id
/// @param deviceIds 需要添加到组设备的设备id列表
/// @param finishBlock 完成回调
- (void)addDevicesToGroup:(NSString *)deviceId deviceIds:(NSArray<NSString *> *)deviceIds finishBlock:(void (^)(UpDeviceResult *result))finishBlock;

/// 从组设备中移除设备，要求当前device对象为组设备
/// @param deviceId 设备id
/// @param deviceIds 需要移除组设备的设备id列表
/// @param finishBlock 完成回调
- (void)removeDevicesFromGroup:(NSString *)deviceId deviceIds:(NSArray<NSString *> *)deviceIds finishBlock:(void (^)(UpDeviceResult *result))finishBlock;

///  删除组设备，要求当前device对象为组设备
/// @param deviceId 设备id
/// @param finishBlock 完成回调
- (void)deleteDeviceGroup:(NSString *)deviceId finishBlock:(void (^)(UpDeviceResult *result))finishBlock;

/// 组设备获取当前组内的所有子设备，非组设备返回空集合
/// @param deviceId 设备id
/// @param finishBlock 完成回调
- (void)getGroupMemberList:(NSString *)deviceId finishBlock:(void (^)(UpDeviceResult *result))finishBlock;

/// 订阅资源
/// @param deviceId deviceId
/// @param resourceName 资源名称
/// @param finishBlock 完成回调
- (void)attachResourceWithDecode:(NSString *)deviceId resourceName:(NSString *)resourceName finishBlock:(void (^)(UpDeviceResult *result))finishBlock;

/// 订阅未解码资源
/// @param deviceId 设备唯一标识符
/// @param resourceName 资源名称
/// @param finishBlock 完成回调
- (void)attachResource:(NSString *)deviceId resourceName:(NSString *)resourceName finishBlock:(void (^)(UpDeviceResult *result))finishBlock;

/// 解订阅未解码资源
/// @param deviceId 设备唯一标识符
/// @param resourceName 资源名称
/// @param finishBlock 完成回调
- (void)detachResource:(NSString *)deviceId resourceName:(NSString *)resourceName finishBlock:(void (^)(UpDeviceResult *result))finishBlock;

/// 是否组设备
/// @param deviceId deviceId
- (BOOL)isGroup:(NSString *)deviceId;

///开始设备固件升级指令
/// @param deviceId deviceId
/// @param traceId 链式跟踪打点，全流程打点唯一标识
/// @param firmwareId 整机固件唯一标识
/// @param finishBlock 完成回调
- (void)startFOTAWithDeviceFOTA:(NSString *)deviceId traceId:(NSString *)traceId firmwareId:(NSString *)firmwareId finishBlock:(void (^)(UpDeviceResult *result))finishBlock;

/**
 * 不连接设备，只监听设备/子设备状态变化
 *
 * @param deviceId 设备ID或子设备ID
 * @param listener 设备监听器
 * @param finishBlock 完成回调
 */
- (void)attachDeviceWithoutConnect:(NSString *)deviceId reportListener:(id<UpDeviceReportListener>)listener finishBlock:(void (^)(UpDeviceResult *result))finishBlock;

/**
 * 取消监听设备/子设备状态变化
 *
 * @param deviceId 设备ID或子设备ID
 * @param finishBlock 完成回调
 */
- (void)detachDeviceWithoutConnect:(NSString *)deviceId finishBlock:(void (^)(UpDeviceResult *result))finishBlock;

/**
 *  @brief  无路由先绑定接口
 *
 *  @param deviceId 设备ID
 *  @param progressBlock 进度回调
 *  @param finishBlock 操作结果回调。
 *
 *  @since  6.2.9
 */
- (void)bindDeviceWithoutWifi:(NSString *)deviceId progressBlock:(void (^)(UpDeviceResult *result))progressBlock finishBlock:(void (^)(UpDeviceResult *result))finishBlock;

/**
 *  @brief  修改设备连接的路由器或密码
 *
 *  @param deviceId 设备ID
 *  @param info 配网信息
 *  @param progressBlock 进度回调
 *  @param finishBlock 操作结果回调。
 *
 *  @since  6.2.9
 */
- (void)updateRouterInfo:(NSString *)deviceId routerInfo:(NSDictionary *)info progressBlock:(void (^)(UpDeviceResult *result))progressBlock finishBlock:(void (^)(UpDeviceResult *result))finishBlock;

/**
 *  @brief  获取已入网设备WiFi网络的路由器SSID和密码
 *
 *  @param finishBlock 操作结果回调。
 *
 *  @since  6.2.9
 */
- (void)getConfigRouterInfo:(void (^)(UpDeviceResult *result))finishBlock;

/**
 *  @brief  连接usdk设备
 *
 *  @param deviceId 设备ID
 *  @param finishBlock 操作结果回调。
 *
 *  @since  6.2.9
 */
- (void)connectDevice:(NSString *)deviceId finishBlock:(void (^)(UpDeviceResult *result))finishBlock;

/**
 *  @brief  断开usdk设备连接
 *
 *  @param deviceId 设备ID
 *  @param finishBlock 操作结果回调。
 *
 *  @since  6.2.9
 */
- (void)disConnectDevice:(NSString *)deviceId finishBlock:(void (^)(UpDeviceResult *result))finishBlock;
/**
 *  @brief  快连专用连接usdk设备
 *
 *  @param deviceId 设备ID
 *  @param finishBlock 操作结果回调。
    *  @since  6.6.0
 */
- (void)QCConnectDevice:(NSString *)deviceId finishBlock:(void (^)(UpDeviceResult *))finishBlock;

/**
 *  @brief  快连专用断开usdk设备连接
 *
 *  @param deviceId 设备ID
 *  @param finishBlock 操作结果回调。
 *
 *  @since  6.6.0
 */
- (void)QCDisConnectDevice:(NSString *)deviceId finishBlock:(void (^)(UpDeviceResult *))finishBlock;

/**
 * 获取设备蓝牙连接状态
 *
 * @param deviceId 设备ID或子设备ID
 * @finishBlock 操作结果+设备蓝牙连接状态
 */
- (void)getDeviceBleState:(NSString *)deviceId finishBlock:(void (^)(UpDeviceResult *))finishBlock;

/**
 * 获取设备蓝牙连接状态
 *
 * @param deviceId 设备ID或子设备ID
 * @finishBlock 操作结果+设备蓝牙连接状态
 */
- (void)getDeviceBleState:(NSString *)deviceId finishBlock:(void (^)(UpDeviceResult *))finishBlock;

// 获取uSDK包装对象，返回usdk的部分属性
- (uSDKDeviceWrapper *)getuSDKDeviceWrapper:(NSString *)deviceId;

- (void)setTracker:(id<UpDeviceTracker>)tracker;
@end
