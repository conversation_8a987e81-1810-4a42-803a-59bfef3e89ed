//
//  UpDeviceReportListener.h
//  updevice_api
//
//  Created by <PERSON><PERSON> on 2018/12/17.
//  Copyright © 2018 Haierfc. All rights reserved.
//

#import <Foundation/Foundation.h>
#import "UpDeviceAttribute.h"
#import "UpDeviceBaseInfo.h"
#import "UpDeviceExecutable.h"
#import "UpDeviceResult.h"
#import "UpDeviceCaution.h"
#import "UpDeviceCommand.h"
#import "UpDeviceConnection.h"
#import "UpDeviceFOTAStatusInfo.h"
#import "UpDeviceBLEHistoryInfo.h"
#import "UpDeviceControlState.h"
#import "UpDeviceOnlineStatus.h"
#import "UpDeviceOnlineStateV2.h"
#import "UpDeviceOnlyConfigState.h"
#import "UpDeviceNetworkLevel.h"
#import "UpDeviceQCConnectTimeoutType.h"
#import "UpDeviceOfflineCause.h"
#import "uSDKDeviceWrapper.h"

@protocol UpDeviceReportListener <NSObject>
/**
 * 设备基础信息变化
 *
 * @param deviceBaseInfo 设备基础信息
 */
- (void)onDeviceInfoChange:(id<UpDeviceBaseInfo>)deviceBaseInfo;

/**
 * 设备连接状态变化
 *
 * @param deviceBaseInfo 设备基础信息
 * @param connection     连接状态
 */
- (void)onConnectionChange:(id<UpDeviceBaseInfo>)deviceBaseInfo connection:(UpDeviceConnection)connection;

/**
 * 设备的可控状态变化
 *
 * @param deviceBaseInfo 设备基础信息
 * @param state     可控状态
 */
- (void)onControlStateChange:(id<UpDeviceBaseInfo>)deviceBaseInfo controlState:(UpDeviceControlState)state;

/**
 * 设备故障码变化
 *
 * @param deviceBaseInfo 设备基础信息
 * @param code     故障码
 */
- (void)onFaultInformationCodeChange:(id<UpDeviceBaseInfo>)deviceBaseInfo code:(NSInteger)code;

/**
 * 真实在线状态上报
 *
 * @param deviceBaseInfo 设备基础信息
 * @param status 真实在线状态
 */
- (void)onRealOnlineChange:(id<UpDeviceBaseInfo>)deviceBaseInfo status:(UpDeviceRealOnline)status;

/**
 * 真实在线状态上报V2
 *
 * @param deviceBaseInfo 设备基础信息
 * @param status 真实在线状态
 */
- (void)onRealOnlineChangeV2:(id<UpDeviceBaseInfo>)deviceBaseInfo status:(UpDeviceRealOnlineV2)status;


/**
 * 子设备列表变化<br/>
 * <strong>当监听的是子机时，此方法不会被调用</strong>
 *
 * @param deviceBaseInfo 设备基础信息
 * @param subDevInfoList 子设备列表
 */
- (void)onSubDevListChange:(id<UpDeviceBaseInfo>)deviceBaseInfo subDevInfoList:(NSArray<id<UpDeviceBaseInfo>> *)subDevInfoList;

/**
 * 设备属性变化
 *
 * @param deviceBaseInfo 设备基础信息
 * @param attributeList  属性列表
 */
- (void)onAttributesChange:(id<UpDeviceBaseInfo>)deviceBaseInfo attributeList:(NSArray<id<UpDeviceAttribute>> *)attributeList;

/**
 * 设备告警变化
 *
 * @param deviceBaseInfo 设备基础信息
 * @param cautionList    告警列表
 */
- (void)onDeviceCaution:(id<UpDeviceBaseInfo>)deviceBaseInfo cautionList:(NSArray<id<UpDeviceCaution>> *)cautionList;

/**
 * 数据接收接口
 *
 * @param deviceBaseInfo 设备基础信息
 * @param name           数据名称
 * @param data           数据
 */
- (void)onDeviceReceive:(id<UpDeviceBaseInfo>)deviceBaseInfo name:(NSString *)name data:(id)data;

/**
 * 设备底板固件升级状态变化
 *
 * @param deviceBaseInfo 设备基础信息
 * @param statusInfo 设备底板固件升级状态信息
 */
- (void)onDeviceUpdateBoardFOTAStatus:(id<UpDeviceBaseInfo>)deviceBaseInfo FOTAStatusInfo:(id<UpDeviceFOTAStatusInfo>)statusInfo;

/**
 * 蓝牙历史数据上报
 *
 * @param deviceBaseInfo 设备基础信息
 * @param historyInfo 蓝牙历史数据信息
 */
- (void)onDeviceReceiveBLEHistoryData:(id<UpDeviceBaseInfo>)deviceBaseInfo BLEHistoryInfo:(id<UpDeviceBLEHistoryInfo>)historyInfo;

/**
 * 蓝牙实时数据上报
 *
 * @param deviceBaseInfo 设备基础信息
 * @param data 蓝牙实时数据
 */
- (void)onDeviceReceiveBLERealTimeData:(id<UpDeviceBaseInfo>)deviceBaseInfo data:(NSData *)data;


/**
 * 设备睡眠状态上报
 *
 * @param deviceBaseInfo 设备基础信息
 * @param state 真实在线状态
 */
- (void)onSleepStateChange:(id<UpDeviceBaseInfo>)deviceBaseInfo state:(UpDeviceSleepState)state;


/**
 * 设备网络质量
 *
 * @param deviceBaseInfo 设备基础信息
 * @param level 设备网络质量
 */
- (void)onNetworkLevelChange:(id<UpDeviceBaseInfo>)deviceBaseInfo level:(UpDeviceNetworkLevel)level;


/// 快连超时回调
/// @param deviceBaseInfo 设备基础信息
/// @param timeoutType 超时类型
- (void)onQCConnectTimeout:(id<UpDeviceBaseInfo>)deviceBaseInfo timeoutType:(UpDeviceQCConnectTimeoutType)timeoutType;


/// 蓝牙状态变化
/// @param deviceBaseInfo 设备基础信息
/// @param state 蓝牙状态
- (void)onBleStateChange:(id<UpDeviceBaseInfo>)deviceBaseInfo state:(UpDeviceConnection)state;

/// 离线原因状态变化
/// @param deviceBaseInfo  设备基础信息
/// @param offlineCause 离线原因状态变化
/// @since v7.1.1
///
- (void)onOfflineCauseChange:(id<UpDeviceBaseInfo>)deviceBaseInfo offlineCause:(UpDeviceOfflineCause)offlineCause;

/// 离线天数变化
/// @param deviceBaseInfo  设备基础信息
/// @param offlineDays 离线天数状态变化
/// @since v7.19.0
///
- (void)onOfflineDaysChange:(id<UpDeviceBaseInfo>)deviceBaseInfo offlineDays:(NSInteger)offlineDays;

/// 仅配网状态变化
/// @param deviceBaseInfo  设备基础信息
/// @param onlyConfigState 待配网状态
/// @since v7.19.0
///
- (void)onOnlyConfigStateChange:(id<UpDeviceBaseInfo>)deviceBaseInfo onlyConfigState:(UpDeviceOnlyConfigState)onlyConfigState;


- (void)onuSDKDeviceDataChange:(id<UpDeviceBaseInfo>)deviceBaseInfo wrapperData:(uSDKDeviceWrapper *)wrapperData;
@end
