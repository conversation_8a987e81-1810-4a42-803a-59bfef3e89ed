//
//  UpDeviceToolkitListener.h
//  updevice_api
//
//  Created by <PERSON><PERSON> on 2018/12/17.
//  Copyright © 2018 Haierfc. All rights reserved.
//

#import <Foundation/Foundation.h>
#import "UpDeviceToolkitState.h"

@protocol UpDeviceToolkitListener <NSObject>
/**
 * @brief ToolkitState
 * @param protocol 字符串
 * @param state 状态
 * @since 3.0.0
 */
- (void)onStateChange:(NSString *)protocol state:(UpDeviceToolkitState)state;

@end
