//
//  EmptyDeviceToolkit.m
//  updevice_api
//
//  Created by f on 28/12/2018.
//  Copyright © 2018 Haierfc. All rights reserved.
//

#import "EmptyDeviceToolkit.h"
#import "UpDeviceResult.h"
#import "UPDeviceLog.h"

@interface EmptyDeviceToolkit () {
    NSString *_supportProtocol;
    NSString *_toolkitVersion;
    UpDeviceToolkitState _toolkitState;
}

@end

@implementation EmptyDeviceToolkit
- (UpDeviceResult *)createEmptyObservable:(NSString *)action
{
    UpDeviceResult *result = [[UpDeviceResult alloc] initWithErrorCode:ErrorCode_FAILURE extraData:action extraCode:nil extraInfo:nil];
    return result;
}

- (void)attachToolkit:(id<UpDeviceToolkitListener>)deviceToolkitListener detectListener:(id<UpDeviceDetectListener>)deviceDetectListener finishBlock:(void (^)(UpDeviceResult *result))finishBlock
{
    UPLogWarning(kUPDevicePrefix, @"%s[%d]该方法为空实现方法，不应该被调用！", __PRETTY_FUNCTION__, __LINE__);
    if (finishBlock) {
        finishBlock([self createEmptyObservable:@"attachToolkit"]);
    }
}

- (void)detachToolkit:(void (^)(UpDeviceResult *result))finishBlock
{
    UPLogWarning(kUPDevicePrefix, @"%s[%d]该方法为空实现方法，不应该被调用！", __PRETTY_FUNCTION__, __LINE__);
    if (finishBlock) {
        finishBlock([self createEmptyObservable:@"detachToolkit"]);
    }
}

- (void)getDeviceBaseInfoList:(void (^)(UpDeviceResult *result))finishBlock
{
    UPLogWarning(kUPDevicePrefix, @"%s[%d]该方法为空实现方法，不应该被调用！", __PRETTY_FUNCTION__, __LINE__);
    if (finishBlock) {
        finishBlock([self createEmptyObservable:@"getDeviceBaseInfoList"]);
    }
}

- (void)attachDevice:(NSString *)deviceId reportListener:(id<UpDeviceReportListener>)listener finishBlock:(void (^)(UpDeviceResult *result))finishBlock
{
    UPLogWarning(kUPDevicePrefix, @"%s[%d]该方法为空实现方法，不应该被调用！", __PRETTY_FUNCTION__, __LINE__);
    if (finishBlock) {
        finishBlock([self createEmptyObservable:@"attachDevice"]);
    }
}

- (void)connectRemoteDevices:(NSMutableArray<id<UpDeviceBaseInfo>> *)baseInfoList params:(NSMutableDictionary *)params finishBlock:(void (^)(UpDeviceResult *result))finishBlock
{
    UPLogWarning(kUPDevicePrefix, @"%s[%d]该方法为空实现方法，不应该被调用！", __PRETTY_FUNCTION__, __LINE__);
    if (finishBlock) {
        finishBlock([self createEmptyObservable:@"connectRemoteDevices"]);
    }
}

- (void)disconnectRemoteDevices:(void (^)(UpDeviceResult *result))finishBlock
{
    UPLogWarning(kUPDevicePrefix, @"%s[%d]该方法为空实现方法，不应该被调用！", __PRETTY_FUNCTION__, __LINE__);
    [self createEmptyObservable:@"disconnectRemoteDevices"];
}

- (void)detachDevice:(NSString *)deviceId finishBlock:(void (^)(UpDeviceResult *result))finishBlock
{
    UPLogWarning(kUPDevicePrefix, @"%s[%d]该方法为空实现方法，不应该被调用！", __PRETTY_FUNCTION__, __LINE__);
    if (finishBlock) {
        finishBlock([self createEmptyObservable:@"detachDevice"]);
    }
}

- (void)getDeviceBaseInfo:(NSString *)deviceId finishBlock:(void (^)(UpDeviceResult *result))finishBlock
{
    UPLogWarning(kUPDevicePrefix, @"%s[%d]该方法为空实现方法，不应该被调用！", __PRETTY_FUNCTION__, __LINE__);
    if (finishBlock) {
        finishBlock([self createEmptyObservable:@"getDeviceBaseInfo"]);
    }
}

- (void)getDeviceConnection:(NSString *)deviceId finishBlock:(void (^)(UpDeviceResult *result))finishBlock
{
    UPLogWarning(kUPDevicePrefix, @"%s[%d]该方法为空实现方法，不应该被调用！", __PRETTY_FUNCTION__, __LINE__);
    if (finishBlock) {
        finishBlock([self createEmptyObservable:@"getDeviceConnection"]);
    }
}

- (void)getDeviceOnlineStatus:(NSString *)deviceId finishBlock:(void (^)(UpDeviceResult *))finishBlock
{
    UPLogWarning(kUPDevicePrefix, @"%s[%d]该方法为空实现方法，不应该被调用！", __PRETTY_FUNCTION__, __LINE__);
    if (finishBlock) {
        finishBlock([self createEmptyObservable:@"getDeviceOnlineStatus"]);
    }
}

- (void)getDeviceOnlineStateV2:(NSString *)deviceId finishBlock:(void (^)(UpDeviceResult *))finishBlock
{
    UPLogWarning(kUPDevicePrefix, @"%s[%d]该方法为空实现方法，不应该被调用！", __PRETTY_FUNCTION__, __LINE__);

    if (finishBlock) {
        finishBlock([self createEmptyObservable:@"getDeviceOnlineStateV2"]);
    }
}

- (UpDeviceRealOnline)getDeviceWifiOnlineState:(NSString *)deviceId
{
    UPLogWarning(kUPDevicePrefix, @"%s[%d]该方法为空实现方法，不应该被调用！", __PRETTY_FUNCTION__, __LINE__);
    return UpDeviceRealOnline_OFFLINE;
}

- (void)getDeviceSleepState:(NSString *)deviceId finishBlock:(void (^)(UpDeviceResult *result))finishBlock
{
    UPLogWarning(kUPDevicePrefix, @"%s[%d]该方法为空实现方法，不应该被调用！", __PRETTY_FUNCTION__, __LINE__);
    if (finishBlock) {
        finishBlock([self createEmptyObservable:@"getDeviceSleepState"]);
    }
}

- (void)getDeviceNetWorkLevel:(NSString *)deviceId finishBlock:(void (^)(UpDeviceResult *))finishBlock
{
    UPLogWarning(kUPDevicePrefix, @"%s[%d]该方法为空实现方法，不应该被调用！", __PRETTY_FUNCTION__, __LINE__);
    if (finishBlock) {
        finishBlock([self createEmptyObservable:@"getDeviceNetWorkLevel"]);
    }
}

- (void)getSubDevBaseInfoList:(NSString *)deviceId finishBlock:(void (^)(UpDeviceResult *result))finishBlock
{
    UPLogWarning(kUPDevicePrefix, @"%s[%d]该方法为空实现方法，不应该被调用！", __PRETTY_FUNCTION__, __LINE__);
    if (finishBlock) {
        finishBlock([self createEmptyObservable:@"getSubDevBaseInfoList"]);
    }
}

- (void)getDeviceAttributeList:(NSString *)deviceId finishBlock:(void (^)(UpDeviceResult *result))finishBlock
{
    UPLogWarning(kUPDevicePrefix, @"%s[%d]该方法为空实现方法，不应该被调用！", __PRETTY_FUNCTION__, __LINE__);
    if (finishBlock) {
        finishBlock([self createEmptyObservable:@"getDeviceAttributeList"]);
    }
}

- (void)getDeviceCautionList:(NSString *)deviceId finishBlock:(void (^)(UpDeviceResult *result))finishBlock
{
    UPLogWarning(kUPDevicePrefix, @"%s[%d]该方法为空实现方法，不应该被调用！", __PRETTY_FUNCTION__, __LINE__);
    if (finishBlock) {
        finishBlock([self createEmptyObservable:@"getDeviceCautionList"]);
    }
}

- (void)getDeviceWifiLocalState:(NSString *)deviceId finishBlock:(void (^)(UpDeviceResult *result))finishBlock
{
    UPLogWarning(kUPDevicePrefix, @"%s[%d]该方法为空实现方法，不应该被调用！", __PRETTY_FUNCTION__, __LINE__);
    if (finishBlock) {
        finishBlock([self createEmptyObservable:@"getDeviceWifiLocalState"]);
    }
}

- (void)getDeviceOfflineCause:(NSString *)deviceId finishBlock:(void (^)(UpDeviceResult *result))finishBlock
{
    UPLogWarning(kUPDevicePrefix, @"%s[%d]该方法为空实现方法，不应该被调用！", __PRETTY_FUNCTION__, __LINE__);
    if (finishBlock) {
        finishBlock([self createEmptyObservable:@"getDeviceOfflineCause"]);
    }
}

- (void)getDeviceOfflineDays:(NSString *)deviceId finishBlock:(void (^)(UpDeviceResult *result))finishBlock
{
    UPLogWarning(kUPDevicePrefix, @"%s[%d]该方法为空实现方法，不应该被调用！", __PRETTY_FUNCTION__, __LINE__);
    if (finishBlock) {
        finishBlock([self createEmptyObservable:@"getDeviceOfflineDays"]);
    }
}

- (void)getDeviceOnlyConfigState:(NSString *)deviceId finishBlock:(void (^)(UpDeviceResult *result))finishBlock
{
    UPLogWarning(kUPDevicePrefix, @"%s[%d]该方法为空实现方法，不应该被调用！", __PRETTY_FUNCTION__, __LINE__);
    if (finishBlock) {
        finishBlock([self createEmptyObservable:@"getDeviceOnlyConfigState"]);
    }
}

- (BOOL)isSupportOnlyConfig:(NSString *)deviceId
{
    UPLogWarning(kUPDevicePrefix, @"%s[%d]该方法为空实现方法，不应该被调用！", __PRETTY_FUNCTION__, __LINE__);
    return NO;
}

- (BOOL)isOnlyConfigFlow:(NSString *)deviceId
{
    UPLogWarning(kUPDevicePrefix, @"%s[%d]该方法为空实现方法，不应该被调用！", __PRETTY_FUNCTION__, __LINE__);
    return NO;
}

- (void)getDeviceAttributeByName:(NSString *)name deviceId:(NSString *)deviceId timeout:(NSTimeInterval)timeout finishBlock:(void (^)(UpDeviceResult *result))finishBlock
{
    UPLogWarning(kUPDevicePrefix, @"%s[%d]该方法为空实现方法，不应该被调用！", __PRETTY_FUNCTION__, __LINE__);
    if (finishBlock) {
        finishBlock([self createEmptyObservable:@"getDeviceAttributeByName"]);
    }
}

- (void)executeDeviceCommand:(NSString *)deviceId command:(id<UpDeviceCommand>)command timeout:(NSTimeInterval)timeout finishBlock:(void (^)(UpDeviceResult *result))finishBlock
{
    UPLogWarning(kUPDevicePrefix, @"%s[%d]该方法为空实现方法，不应该被调用！", __PRETTY_FUNCTION__, __LINE__);
    if (finishBlock) {
        finishBlock([self createEmptyObservable:@"executeDeviceCommand"]);
    }
}

- (UpDeviceToolkitState)getToolkitState
{
    UPLogWarning(kUPDevicePrefix, @"%s[%d]该方法为空实现方法，不应该被调用！", __PRETTY_FUNCTION__, __LINE__);
    return STOPPED;
}

- (void)execute:(NSString *)action params:(NSDictionary *)params finishBlock:(void (^)(UpDeviceResult *))finishBlock
{
    UPLogWarning(kUPDevicePrefix, @"%s[%d]该方法为空实现方法，不应该被调用！", __PRETTY_FUNCTION__, __LINE__);
    if (finishBlock) {
        finishBlock([self createEmptyObservable:action]);
    }
}

#pragma mark - FOTA
- (void)checkBoardFOTAInfoSuccess:(NSString *)deviceId finishBlock:(void (^)(UpDeviceResult *))finishBlock
{
    UPLogWarning(kUPDevicePrefix, @"%s[%d]该方法为空实现方法，不应该被调用！", __PRETTY_FUNCTION__, __LINE__);
    if (finishBlock) {
        finishBlock([self createEmptyObservable:@"checkBoardFOTAInfoSuccess"]);
    }
}


- (void)fetchBoardFOTAStatusSuccess:(NSString *)deviceId finishBlock:(void (^)(UpDeviceResult *))finishBlock
{
    UPLogWarning(kUPDevicePrefix, @"%s[%d]该方法为空实现方法，不应该被调用！", __PRETTY_FUNCTION__, __LINE__);
    if (finishBlock) {
        finishBlock([self createEmptyObservable:@"fetchBoardFOTAStatusSuccess"]);
    }
}


- (void)startBoardFOTA:(NSString *)deviceId finishBlock:(void (^)(UpDeviceResult *))finishBlock
{
    UPLogWarning(kUPDevicePrefix, @"%s[%d]该方法为空实现方法，不应该被调用！", __PRETTY_FUNCTION__, __LINE__);
    if (finishBlock) {
        finishBlock([self createEmptyObservable:@"startBoardFOTA"]);
    }
}

- (void)startModuleUpdateSuccess:(NSString *)deviceId finishBlock:(void (^)(UpDeviceResult *))finishBlock
{
    UPLogWarning(kUPDevicePrefix, @"%s[%d]该方法为空实现方法，不应该被调用！", __PRETTY_FUNCTION__, __LINE__);
    if (finishBlock) {
        finishBlock([self createEmptyObservable:@"startModuleUpdateSuccess"]);
    }
}

- (void)fetchBLEHistoryData:(NSString *)deviceId finishBlock:(void (^)(UpDeviceResult *))finishBlock
{
    UPLogWarning(kUPDevicePrefix, @"%s[%d]该方法为空实现方法，不应该被调用！", __PRETTY_FUNCTION__, __LINE__);
    if (finishBlock) {
        finishBlock([self createEmptyObservable:@"fetchBLEHistoryData"]);
    }
}

- (void)cancelFetchBLEHistoryData:(NSString *)deviceId finishBlock:(void (^)(UpDeviceResult *))finishBlock
{
    UPLogWarning(kUPDevicePrefix, @"%s[%d]该方法为空实现方法，不应该被调用！", __PRETTY_FUNCTION__, __LINE__);
    if (finishBlock) {
        finishBlock([self createEmptyObservable:@"cancelFetchBLEHistoryData"]);
    }
}

- (BOOL)isModuleNeedOta:(NSString *)deviceId
{
    UPLogWarning(kUPDevicePrefix, @"%s[%d]该方法为空实现方法，不应该被调用！", __PRETTY_FUNCTION__, __LINE__);
    return NO;
}

- (BOOL)isBound:(NSString *)deviceId
{
    UPLogWarning(kUPDevicePrefix, @"%s[%d]该方法为空实现方法，不应该被调用！", __PRETTY_FUNCTION__, __LINE__);
    return NO;
}

- (NSString *)getSmartLinkSoftwareVersion:(NSString *)deviceId
{
    UPLogWarning(kUPDevicePrefix, @"%s[%d]该方法为空实现方法，不应该被调用！", __PRETTY_FUNCTION__, __LINE__);
    return @"";
}

- (void)getDeviceBindInfo:(NSString *)deviceId finishBlock:(void (^)(UpDeviceResult *result))finishBlock
{
    UPLogWarning(kUPDevicePrefix, @"%s[%d]该方法为空实现方法，不应该被调用！", __PRETTY_FUNCTION__, __LINE__);
    if (finishBlock) {
        finishBlock([self createEmptyObservable:@"getDeviceBindInfo"]);
    }
}

- (void)refreshUsdkDeviceList:(void (^)(UpDeviceResult *result))finishBlock
{
    UPLogWarning(kUPDevicePrefix, @"%s[%d]该方法为空实现方法，不应该被调用！", __PRETTY_FUNCTION__, __LINE__);
    if (finishBlock) {
        finishBlock([self createEmptyObservable:@"refreshUsdkDeviceList"]);
    }
}

- (void)getSubDevListBySubDev:(NSString *)deviceId finishBlock:(void (^)(UpDeviceResult *result))finishBlock
{
    UPLogWarning(kUPDevicePrefix, @"%s[%d]该方法为空实现方法，不应该被调用！", __PRETTY_FUNCTION__, __LINE__);
    if (finishBlock) {
        finishBlock([self createEmptyObservable:@"getSubDevListBySubDev"]);
    }
}

- (BOOL)inFocus:(NSString *)deviceId
{
    UPLogWarning(kUPDevicePrefix, @"%s[%d]This method is an empty implementation method and should not be called！", __PRETTY_FUNCTION__, __LINE__);
    return NO;
}

- (BOOL)outFocus:(NSString *)deviceId
{
    UPLogWarning(kUPDevicePrefix, @"%s[%d]This method is an empty implementation method and should not be called！", __PRETTY_FUNCTION__, __LINE__);
    return NO;
}

- (void)getNetworkQuality:(NSString *)deviceId finishBlock:(void (^)(UpDeviceResult *result))finishBlock
{
    UPLogWarning(kUPDevicePrefix, @"%s[%d]This method is an empty implementation method and should not be called", __PRETTY_FUNCTION__, __LINE__);
    if (finishBlock) {
        finishBlock([self createEmptyObservable:@"getNetworkQuality"]);
    }
}

- (DeviceNetType)getNetType:(NSString *)deviceId
{
    UPLogWarning(kUPDevicePrefix, @"%s[%d]This method is an empty implementation method and should not be called！", __PRETTY_FUNCTION__, __LINE__);
    return NO;
}

- (void)fetchGroupableDeviceList:(NSString *)deviceId finishBlock:(void (^)(UpDeviceResult *))finishBlock
{
    UPLogWarning(kUPDevicePrefix, @"%s[%d]This method is an empty implementation method and should not be called", __PRETTY_FUNCTION__, __LINE__);
    if (finishBlock) {
        finishBlock([self createEmptyObservable:@"fetchGroupableDeviceList"]);
    }
}

- (void)createDeviceGroup:(NSString *)deviceId finishBlock:(void (^)(UpDeviceResult *))finishBlock
{
    UPLogWarning(kUPDevicePrefix, @"%s[%d]This method is an empty implementation method and should not be called", __PRETTY_FUNCTION__, __LINE__);
    if (finishBlock) {
        finishBlock([self createEmptyObservable:@"createDeviceGroup"]);
    }
}

- (void)addDevicesToGroup:(NSString *)deviceId deviceIds:(NSArray<NSString *> *)deviceIds finishBlock:(void (^)(UpDeviceResult *))finishBlock
{
    UPLogWarning(kUPDevicePrefix, @"%s[%d]This method is an empty implementation method and should not be called", __PRETTY_FUNCTION__, __LINE__);
    if (finishBlock) {
        finishBlock([self createEmptyObservable:@"addDevicesToGroup"]);
    }
}

- (void)removeDevicesFromGroup:(NSString *)deviceId deviceIds:(NSArray<NSString *> *)deviceIds finishBlock:(void (^)(UpDeviceResult *))finishBlock
{
    UPLogWarning(kUPDevicePrefix, @"%s[%d]This method is an empty implementation method and should not be called", __PRETTY_FUNCTION__, __LINE__);
    if (finishBlock) {
        finishBlock([self createEmptyObservable:@"removeDevicesFromGroup"]);
    }
}

- (void)deleteDeviceGroup:(NSString *)deviceId finishBlock:(void (^)(UpDeviceResult *))finishBlock
{
    UPLogWarning(kUPDevicePrefix, @"%s[%d]This method is an empty implementation method and should not be called", __PRETTY_FUNCTION__, __LINE__);
    if (finishBlock) {
        finishBlock([self createEmptyObservable:@"deleteDeviceGroup"]);
    }
}

- (void)getGroupMemberList:(NSString *)deviceId finishBlock:(void (^)(UpDeviceResult *))finishBlock
{
    UPLogWarning(kUPDevicePrefix, @"%s[%d]This method is an empty implementation method and should not be called", __PRETTY_FUNCTION__, __LINE__);
    if (finishBlock) {
        finishBlock([self createEmptyObservable:@"getGroupMemberList"]);
    }
}
- (void)attachResourceWithDecode:(NSString *)deviceId resourceName:(NSString *)resourceName finishBlock:(void (^)(UpDeviceResult *))finishBlock
{
    UPLogWarning(kUPDevicePrefix, @"%s[%d]This method is an empty implementation method and should not be called", __PRETTY_FUNCTION__, __LINE__);
    if (finishBlock) {
        finishBlock([self createEmptyObservable:@"attachResourceWithDecode"]);
    }
}
- (void)attachResource:(NSString *)deviceId resourceName:(NSString *)resourceName finishBlock:(void (^)(UpDeviceResult *result))finishBlock
{
    UPLogWarning(kUPDevicePrefix, @"%s[%d]This method is an empty implementation method and should not be called", __PRETTY_FUNCTION__, __LINE__);
    if (finishBlock) {
        finishBlock([self createEmptyObservable:@"attachResource"]);
    }
}
- (void)detachResource:(NSString *)deviceId resourceName:(NSString *)resourceName finishBlock:(void (^)(UpDeviceResult *result))finishBlock
{
    UPLogWarning(kUPDevicePrefix, @"%s[%d]This method is an empty implementation method and should not be called", __PRETTY_FUNCTION__, __LINE__);
    if (finishBlock) {
        finishBlock([self createEmptyObservable:@"detachResource"]);
    }
}
- (BOOL)isGroup:(NSString *)deviceId
{
    UPLogWarning(kUPDevicePrefix, @"%s[%d]该方法为空实现方法，不应该被调用！", __PRETTY_FUNCTION__, __LINE__);
    return NO;
}

- (void)startFOTAWithDeviceFOTA:(NSString *)deviceId traceId:(NSString *)traceId firmwareId:(NSString *)firmwareId finishBlock:(void (^)(UpDeviceResult *))finishBlock
{
    UPLogWarning(kUPDevicePrefix, @"%s[%d]This method is an empty implementation method and should not be called", __PRETTY_FUNCTION__, __LINE__);
    if (finishBlock) {
        finishBlock([self createEmptyObservable:@"startFOTAWithDeviceFOTA"]);
    }
}
@end
