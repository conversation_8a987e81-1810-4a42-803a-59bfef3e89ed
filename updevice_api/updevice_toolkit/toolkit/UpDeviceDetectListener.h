//
//  UpDeviceDetectListener.h
//  updevice_api
//
//  Created by <PERSON><PERSON> on 2018/12/17.
//  Copyright © 2018 Haierfc. All rights reserved.
//

#import <Foundation/Foundation.h>
#import "UpDeviceBaseInfo.h"


/**
 * 设备列表监听器
 */
@protocol UpDeviceDetectListener <NSObject>
@property (nonatomic, strong) id<UpDeviceDetectListener> detectListener;
/**
 * 发现设备
 *
 * @param baseInfoList 设备基础信息列表
 */
- (void)onFind:(NSArray<id<UpDeviceBaseInfo>> *)baseInfoList;

/**
 * 丢失设备，监听者应释放对应设备所持有的资源
 *
 * @param baseInfoList 设备基础信息列表
 */
- (void)onLose:(NSArray<id<UpDeviceBaseInfo>> *)baseInfoList;

/**
 * 接收到usdk的信道信息，设备列表变化（绑定或解绑设备）
 * @since 1.2.0
 */
- (void)onDeviceListChanged;
@end
