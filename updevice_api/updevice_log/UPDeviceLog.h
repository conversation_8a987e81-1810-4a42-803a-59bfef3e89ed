//
//  UPDeviceLog.h
//  UPDevice
//
//  Created by <PERSON> on 7/31/15.
//  Copyright (c) 2015 海尔优家智能科技（北京）有限公司. All rights reserved.
//

static NSString *const kUPDevicePrefix = @"UPDevice";

#import <UPLog/UPLog.h>

/**
 * 打印级别为UPDeviceLogLevelAll的日志
 */
#define UPDeviceLogAll(format, ...) UPLogVerbose(kUPDevicePrefix, format, ##__VA_ARGS__);

/**
 * 打印消息日志
 */
#define UPDeviceLogInfo(format, ...) UPLogInfo(kUPDevicePrefix, format, ##__VA_ARGS__);

/**
 * 打印调试信息日志
 */
#define UPDeviceLogDebug(format, ...) UPLogDebug(kUPDevicePrefix, format, ##__VA_ARGS__);

/**
 * 打印警告信息日志
 */
#define UPDeviceLogWarning(format, ...) UPLogWarning(kUPDevicePrefix, format, ##__VA_ARGS__);

/**
 * 打印错误信息日志
 */
#define UPDeviceLogError(format, ...) UPLogError(kUPDevicePrefix, format, ##__VA_ARGS__);
