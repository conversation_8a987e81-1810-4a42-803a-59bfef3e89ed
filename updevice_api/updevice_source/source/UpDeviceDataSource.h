//
//  UpDeviceDataSource.h
//  updevice_api
//
//  Created by <PERSON><PERSON> on 2018/12/18.
//  Copyright © 2018 Haierfc. All rights reserved.
//

#import <Foundation/Foundation.h>
#import "UpDeviceResult.h"

@protocol UpDeviceDataSource <NSObject>

//设备列表获取接口实现
- (void)getDeviceList:(BOOL)immediate finishBlock:(void (^)(UpDeviceResult *result))finishBlock;

//获取设备信息
- (void)getDeviceInfo:(NSString *)deviceId protocol:(NSString *)protocol immediate:(BOOL)immediate finishBlock:(void (^)(UpDeviceResult *result))finishBlock;

//获取指定家庭下组设备列表
- (void)getGroupMemberListWithFamilyId:(NSString *)familyId finishBlock:(void (^)(UpDeviceResult *result))finishBlock;

@end
