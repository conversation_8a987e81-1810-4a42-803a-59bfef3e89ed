//
//  UpDeviceBaseInfo.h
//  updevice_api
//
//  Created by <PERSON><PERSON> on 2018/12/17.
//  Copyright © 2018 Haierfc. All rights reserved.
//

#import <Foundation/Foundation.h>

static NSString *const PROTOCOL_UNKNOWN = @"--unknown++";

@protocol UpDeviceBaseInfo <NSObject>
// -------------------- 设备通用 --------------------

/**
 * 支持的协议，如：WiFi(uSDK),Bluetooth等
 *
 * @return 协议名称
 */
- (NSString *)protocol;

/**
 * 设备的唯一标识符
 *
 * @return 设备ID
 */
- (NSString *)deviceId;

/**
 * 设备类型标识符
 *
 * @return 类型ID
 */
- (NSString *)typeId;

/**
 * 设备类型名称
 *
 * @return 名称
 */
- (NSString *)typeName;

/**
 * 设备类型代码
 * <strong>说明：对应服务器返回的deviceType</strong>
 *
 * @return 代码
 */
- (NSString *)typeCode;

/**
 * 设备型号
 *
 * @return 型号
 */
- (NSString *)model;

/**
 * 设备产品编码
 *
 * @return 产品编码
 */
- (NSString *)prodNo;

// -------------------- 子设备相关 --------------------

/**
 * 获取主设备ID
 *
 * @return 主设备ID
 */
- (NSString *)parentId;

/**
 * 获取子机序号
 *
 * @return 子机序号
 */
- (NSString *)subDevNo;

// -------------------- 子设备相关 --------------------

// -------------------- 数据更新 --------------------

- (BOOL)updateBaseInfo:(id<UpDeviceBaseInfo>)baseInfo;

// -------------------- 数据更新 --------------------
@end
