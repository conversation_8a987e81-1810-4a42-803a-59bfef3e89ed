//
//  UpDeviceControlState.h
//  UPDevice
//
//  Created by MAC on 2021/9/28.
//  Copyright © 2021 海尔优家智能科技（北京）有限公司. All rights reserved.
//

/**
 * 设备的可控状态枚举值
 */
typedef NS_ENUM(NSInteger, UpDeviceControlState) {
    /**
     * @brief 不可控制
     */
    UpDeviceControlState_None = 0,
    /**
     * @brief  可控制
     */
    UpDeviceControlState_Controllable,
    /**
     * @brief 靠近可控制
     */
    UpDeviceControlState_NearControllable,
    /**
     * @brief 可控制且设备侧鉴权通过
     */
    UpDeviceControlState_ControllableAndAuthorized,
};
