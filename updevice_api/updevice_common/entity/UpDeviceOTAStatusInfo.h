//
//  UpDeviceOTAStatusInfo.h
//  Pods
//
//  Created by gump on 22/8/2019.
//

/**
 模块升级进度阶段

 - UPDeviceOTAStageSendUpgradePackage: 发送升级包
 - UPDeviceOTAStageModuleRestart: 模块重启
 */
typedef NS_ENUM(NSUInteger, UPDeviceOTAStage) {
    UPDeviceOTAStageSendUpgradePackage,
    UPDeviceOTAStageModuleRestart
};

/**
 设备（模块）升级进度信息
 */
@protocol UpDeviceOTAStatusInfo <NSObject>

/**
 升级进度阶段
 */
- (UPDeviceOTAStage)upgradeStatus;

/**
 升级进度百分比
 */
- (float)upgradeProgress;

@end
