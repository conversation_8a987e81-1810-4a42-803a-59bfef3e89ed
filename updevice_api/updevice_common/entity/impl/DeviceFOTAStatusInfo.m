//
//  DeviceFOTAStatusInfo.m
//  updevice_api
//
//  Created by gump on 11/6/2019.
//  Copyright © 2019 Haierfc. All rights reserved.
//

#import "DeviceFOTAStatusInfo.h"

@interface DeviceFOTAStatusInfo () {
    NSInteger _upgradeStatus;
    NSError *_upgradeErrInfo;
}

@end

@implementation DeviceFOTAStatusInfo

+ (DeviceFOTAStatusInfo *)DeviceFOTAStatusInfo:(NSInteger)upgradeStatus upgradeErrInfo:(NSError *)upgradeErrInfo
{
    return [[DeviceFOTAStatusInfo alloc] initWithUpgradeStatus:upgradeStatus upgradeErrInfo:upgradeErrInfo];
}

- (instancetype)initWithUpgradeStatus:(NSInteger)upgradeStatus upgradeErrInfo:(NSError *)upgradeErrInfo
{
    self = [super init];
    if (self) {
        _upgradeStatus = upgradeStatus;
        _upgradeErrInfo = upgradeErrInfo;
    }

    return self;
}

#pragma mark - UpDeviceFOTAStatusInfo

- (NSInteger)upgradeStatus
{
    return _upgradeStatus;
}

- (NSError *)upgradeErrInfo
{
    return _upgradeErrInfo;
}

@end
