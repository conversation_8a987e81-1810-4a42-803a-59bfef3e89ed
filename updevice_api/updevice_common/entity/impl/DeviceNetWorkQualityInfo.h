//
//  DeviceNetWorkQualityInfo.h
//  updevice_api
//
//  Created by gump on 7/8/2020.
//

#import <Foundation/Foundation.h>
#import "UpDeviceNetWorkQualityInfo.h"
/*
*网络质量信息
*/
@interface DeviceNetWorkQualityInfo : NSObject <UpDeviceNetWorkQualityInfo>
+ (DeviceNetWorkQualityInfo *)DeviceNetWorkQualityInfo;
/**
 uSDK与设备的连接状态
 @since 1.6.0
 */
@property (nonatomic, assign) UpDeviceConnectStatus connectStatus;
/**
设备id标识
@since 1.6.0
*/
@property (nonatomic, strong) NSString *machineId;
/**
 设备在线状态，YES：在线；NO：离线
@since 1.6.0
*/
@property (nonatomic, assign) BOOL isOnLine;
/**
 设备状态变化时间
@since 1.6.0
*/
@property (nonatomic, assign) NSTimeInterval statusLastChangeTime;
/**
 设备连接网络类型，如：wifi
@since 1.6.0
*/
@property (nonatomic, strong) NSString *netType;
/**
设备连接的网络名称
@since 1.6.0
*/
@property (nonatomic, strong) NSString *ssid;
/**
网络信号强度，最大值4，最小值-96
@since 1.6.0
*/
@property (nonatomic, assign) NSInteger rssi;
/**
网络信号强度百分比
@since 1.6.0
*/
@property (nonatomic, assign) NSInteger prssi;
/**
网络信号质量等级，0：未知；1：优；2：良；3：合格；4：差
@since 1.6.0
*/
@property (nonatomic, assign) NSInteger signalLevel;
/**
广域网丢包率
@since 1.6.0
*/
@property (nonatomic, assign) NSInteger ilostRatio;
/**
广域网延时(ms)
@since 1.6.0
*/
@property (nonatomic, assign) NSInteger its;
/**
内网ip
@since 1.6.0
*/
@property (nonatomic, strong) NSString *lanIP;
/**
模块版本描述，版本格式：软件版本号/软件类型/硬件版本号/硬件类型
@since 1.6.0
*/
@property (nonatomic, strong) NSString *moduleVersion;
@end
