//
//  DeviceNetWorkQualityInfo.m
//  updevice_api
//
//  Created by gump on 7/8/2020.
//

#import "DeviceNetWorkQualityInfo.h"

@implementation DeviceNetWorkQualityInfo
+ (DeviceNetWorkQualityInfo *)DeviceNetWorkQualityInfo
{
    return [[DeviceNetWorkQualityInfo alloc] init];
}

- (instancetype)init
{
    self = [super init];
    if (self) {
        self.netType = @"";
        self.ssid = @"";
        self.lanIP = @"";
        self.moduleVersion = @"";
    }

    return self;
}

#pragma mark - UpDeviceNetWorkQualityInfo
- (UpDeviceConnectStatus)connectStatus
{
    return _connectStatus;
}
- (NSString *)machineId
{
    return _machineId;
}

- (BOOL)isOnLine
{
    return _isOnLine;
}

- (NSTimeInterval)statusLastChangeTime
{
    return _statusLastChangeTime;
}

- (NSString *)netType
{
    return _netType;
}

- (NSString *)ssid
{
    return _ssid;
}

- (NSInteger)rssi
{
    return _rssi;
}

- (NSInteger)prssi
{
    return _prssi;
}

- (NSInteger)signalLevel
{
    return _signalLevel;
}

- (NSInteger)ilostRatio
{
    return _ilostRatio;
}

- (NSInteger)its
{
    return _its;
}

- (NSString *)lanIP
{
    return _lanIP;
}

- (NSString *)moduleVersion
{
    return _moduleVersion;
}
@end
