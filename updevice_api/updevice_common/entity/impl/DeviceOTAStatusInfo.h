//
//  DeviceOTAStatusInfo.h
//  AFNetworking
//
//  Created by gump on 20/8/2019.
//

#import <Foundation/Foundation.h>
#import "UpDeviceOTAStatusInfo.h"

/**
 设备（模块）升级进度信息
 */
@interface DeviceOTAStatusInfo : NSObject <UpDeviceOTAStatusInfo>

+ (DeviceOTAStatusInfo *)DeviceOTAStatusInfo:(UPDeviceOTAStage)upgradeStatus
                             upgradeProgress:(float)upgradeProgress;

- (instancetype)initWithUpgradeStatus:(NSInteger)upgradeStatus
                      upgradeProgress:(float)upgradeProgress;

@end
