//
//  DeviceCommand.m
//  updevice_api
//
//  Created by f on 28/12/2018.
//  Copyright © 2018 Haierfc. All rights reserved.
//

#import "DeviceCommand.h"

@interface DeviceCommand () {
    NSString *_groupName;
    NSMutableDictionary<NSString *, NSString *> *_attributes;
}
@end

@implementation DeviceCommand

+ (DeviceCommand *)DeviceCommand:(nullable NSString *)groupName Attributes:(nullable NSMutableDictionary<NSString *, NSString *> *)attributes
{
    return [[DeviceCommand alloc] initWithGroupName:groupName Attributes:attributes];
}

- (instancetype)initWithGroupName:(nullable NSString *)groupName Attributes:(nullable NSMutableDictionary<NSString *, NSString *> *)attributes
{
    if (self = [super init]) {
        if (groupName != nil && groupName.length > 0) {
            _groupName = groupName;
        }
        _attributes = attributes;
    }

    return self;
}

- (NSString *)groupName
{
    return _groupName;
}

- (NSMutableDictionary<NSString *, NSString *> *)attributes
{
    if (_attributes == nil) {
        _attributes = [NSMutableDictionary dictionary];
    }

    return _attributes;
}

- (void)setGroupName:(NSString *)groupName
{
    _groupName = groupName;
}

- (void)setAttributes:(NSMutableDictionary<NSString *, NSString *> *)attributes
{
    _attributes = attributes;
}

- (void)putAttribute:(NSString *)name Value:(NSString *)value
{
    if (name == nil || value == nil) {
        return;
    }

    [_attributes setObject:value forKey:name];
}

- (void)putAttributes:(NSMutableDictionary<NSString *, NSString *> *)attributes
{
    if (attributes == nil) {
        return;
    }

    for (NSString *name in attributes.allKeys) {
        NSString *value = [attributes objectForKey:name];
        [self putAttribute:value Value:name];
    }
}

@end
