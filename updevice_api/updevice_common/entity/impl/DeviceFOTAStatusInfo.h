//
//  DeviceFOTAStatusInfo.h
//  updevice_api
//
//  Created by gump on 11/6/2019.
//  Copyright © 2019 Haierfc. All rights reserved.
//

#import <Foundation/Foundation.h>
#import "UpDeviceFOTAStatusInfo.h"

/*
 *设备升级进度信息
 */

@interface DeviceFOTAStatusInfo : NSObject <UpDeviceFOTAStatusInfo>

+ (DeviceFOTAStatusInfo *)DeviceFOTAStatusInfo:(NSInteger)upgradeStatus upgradeErrInfo:(NSError *)upgradeErrInfo;

- (instancetype)initWithUpgradeStatus:(NSInteger)upgradeStatus upgradeErrInfo:(NSError *)upgradeErrInfo;

@end
