//
//  DeviceOTAStatusInfo.m
//  AFNetworking
//
//  Created by gump on 20/8/2019.
//

#import "DeviceOTAStatusInfo.h"

@interface DeviceOTAStatusInfo () {
    UPDeviceOTAStage _upgradeStatus;
    float _upgradeProgress;
}

@end

@implementation DeviceOTAStatusInfo

+ (DeviceOTAStatusInfo *)DeviceOTAStatusInfo:(UPDeviceOTAStage)upgradeStatus
                             upgradeProgress:(float)upgradeProgress
{
    return [[DeviceOTAStatusInfo alloc] initWithUpgradeStatus:upgradeStatus
                                              upgradeProgress:upgradeProgress];
}

- (instancetype)initWithUpgradeStatus:(NSInteger)upgradeStatus
                      upgradeProgress:(float)upgradeProgress
{
    self = [super init];
    if (self) {
        _upgradeStatus = upgradeStatus;
        _upgradeProgress = upgradeProgress;
    }

    return self;
}

- (UPDeviceOTAStage)upgradeStatus
{
    return _upgradeStatus;
}

- (float)upgradeProgress
{
    return _upgradeProgress;
}

@end
