//
//  DeviceCaution.m
//  updevice_api
//
//  Created by f on 28/12/2018.
//  Copyright © 2018 Haierfc. All rights reserved.
//

#import "DeviceCaution.h"

@interface DeviceCaution () {
    NSString *_name;
    NSString *_value;
    NSString *_time;
}

@end

@implementation DeviceCaution

+ (DeviceCaution *)DeviceCaution:(NSString *)name Value:(NSString *)value Time:(NSString *)time
{
    return [[DeviceCaution alloc] initWithName:name Value:value Time:time];
}

- (instancetype)initWithName:(NSString *)name Value:(NSString *)value Time:(NSString *)time
{
    if (self = [super init]) {
        _name = name;
        _value = value;
        _time = time;
    }

    return self;
}

- (NSString *)name
{
    return _name;
}

- (NSString *)value
{
    return _value;
}

- (NSString *)time
{
    return _time;
}

- (void)setName:(NSString *)name
{
    _name = name;
}

- (void)setValue:(NSString *)value
{
    _value = value;
}

- (void)setTime:(NSString *)time
{
    _time = time;
}


@end
