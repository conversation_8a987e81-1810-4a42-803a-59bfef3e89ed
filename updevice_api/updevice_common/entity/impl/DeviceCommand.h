//
//  DeviceCommand.h
//  updevice_api
//
//  Created by f on 28/12/2018.
//  Copyright © 2018 Haierfc. All rights reserved.
//

#import <Foundation/Foundation.h>
#import "UpDeviceCommand.h"

NS_ASSUME_NONNULL_BEGIN

@interface DeviceCommand : NSObject <UpDeviceCommand>

+ (DeviceCommand *)DeviceCommand:(nullable NSString *)groupName Attributes:(nullable NSMutableDictionary<NSString *, NSString *> *)attributes;

- (instancetype)initWithGroupName:(nullable NSString *)groupName Attributes:(nullable NSMutableDictionary<NSString *, NSString *> *)attributes;

- (void)setGroupName:(NSString *)groupName;

- (void)setAttributes:(NSMutableDictionary<NSString *, NSString *> *)attributes;

- (void)putAttribute:(NSString *)name Value:(NSString *)value;
- (void)putAttributes:(NSMutableDictionary<NSString *, NSString *> *)attributes;

@end

NS_ASSUME_NONNULL_END
