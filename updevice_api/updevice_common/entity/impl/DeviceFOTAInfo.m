//
//  DeviceFOTAInfo.m
//  updevice_api
//
//  Created by gump on 11/6/2019.
//  Copyright © 2019 Haierfc. All rights reserved.
//

#import "DeviceFOTAInfo.h"

@interface DeviceFOTAInfo () {
    BOOL _isNeedFOTA;
    NSString *_currentVersion;
    NSString *_newestVersion;
    NSString *_newestVersionDescription;
    NSString *_model;
    NSTimeInterval _timeoutInterval;
}

@end

@implementation DeviceFOTAInfo

+ (DeviceFOTAInfo *)DeviceFOTAInfo:(BOOL)isNeedFOTA currentVersion:(NSString *)currentVersion newestVersion:(NSString *)newestVersion newestVersionDescription:(NSString *)newestVersionDescription model:(NSString *)model timeoutInterval:(NSTimeInterval)timeoutInterval
{
    return [[DeviceFOTAInfo alloc] initWithIsNeedFOTA:isNeedFOTA currentVersion:currentVersion newestVersion:newestVersion newestVersionDescription:newestVersionDescription model:model timeoutInterval:timeoutInterval];
}

- (instancetype)initWithIsNeedFOTA:(BOOL)isNeedFOTA currentVersion:(NSString *)currentVersion newestVersion:(NSString *)newestVersion newestVersionDescription:(NSString *)newestVersionDescription model:(NSString *)model timeoutInterval:(NSTimeInterval)timeoutInterval
{
    self = [super init];
    if (self) {
        _isNeedFOTA = isNeedFOTA;
        _currentVersion = currentVersion;
        _newestVersion = newestVersion;
        _newestVersionDescription = newestVersionDescription;
        _model = model;
        _timeoutInterval = timeoutInterval;
    }

    return self;
}

#pragma mark - UpDeviceFOTAInfo
- (BOOL)isNeedFOTA
{
    return _isNeedFOTA;
}

- (NSString *)currentVersion
{
    return _currentVersion;
}

- (NSString *)newestVersion
{
    return _newestVersion;
}

- (NSString *)newestVersionDescription
{
    return _newestVersionDescription;
}

- (NSString *)model
{
    return _model;
}

- (NSTimeInterval)timeoutInterval
{
    return _timeoutInterval;
}

@end
