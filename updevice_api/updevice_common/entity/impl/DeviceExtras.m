//
//  DeviceExtras.m
//  updevice_api
//
//  Created by f on 28/12/2018.
//  Copyright © 2018 Haierfc. All rights reserved.
//

#import "DeviceExtras.h"

@interface DeviceExtras () {
    NSMutableDictionary<NSString *, NSObject *> *_extras;
}
@end

@implementation DeviceExtras

- (int)getExtrasCount
{
    return (int)[self getExtras].count;
}

- (NSObject *)getExtra:(NSString *)key
{
    if (key == nil) {
        return nil;
    }

    return [_extras objectForKey:key];
}

- (void)putExtra:(NSString *)key value:(NSObject *)value
{
    if (key == nil || value == nil) {
        return;
    }

    [[self getExtras] setValue:value forKey:key];
}

- (NSDictionary<NSString *, NSObject *> *)getExtras
{
    if (_extras == nil) {
        _extras = [NSMutableDictionary dictionary];
    }

    return _extras;
}

- (void)putExtras:(NSDictionary<NSString *, NSObject *> *)extras
{
    if (extras == nil) {
        return;
    }

    for (NSString *key in extras.allKeys) {
        NSObject *value = [extras objectForKey:key];
        [self putExtra:key value:value];
    }
}

- (NSMutableDictionary<NSString *, NSObject *> *)extras
{
    if (_extras == nil) {
        _extras = [NSMutableDictionary dictionary];
    }

    return _extras;
}
@end
