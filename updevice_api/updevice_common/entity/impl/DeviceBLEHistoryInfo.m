//
//  DeviceBLEHistoryInfo.m
//  updevice_api
//
//  Created by gump on 27/8/2019.
//

#import "DeviceBLEHistoryInfo.h"

@interface DeviceBLEHistoryInfo () {
    NSData *_data;
    NSUInteger _currentCount;
    NSUInteger _totalCount;
}

@end

@implementation DeviceBLEHistoryInfo

+ (DeviceBLEHistoryInfo *)DeviceBLEHistoryInfo:(NSData *)data currentCount:(NSUInteger)currentCount totalCount:(NSUInteger)totalCount
{
    return [[DeviceBLEHistoryInfo alloc] initWithData:data currentCount:currentCount totalCount:totalCount];
}

- (instancetype)initWithData:(NSData *)data currentCount:(NSUInteger)currentCount totalCount:(NSUInteger)totalCount
{
    self = [super init];
    if (self) {
        _data = data;
        _currentCount = currentCount;
        _totalCount = totalCount;
    }

    return self;
}

- (NSData *)data
{
    return _data;
}

- (NSUInteger)currentCount
{
    return _currentCount;
}

- (NSUInteger)totalCount
{
    return _totalCount;
}
@end
