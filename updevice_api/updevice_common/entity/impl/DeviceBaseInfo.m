//
//  DeviceBaseInfo.m
//  updevice_api
//
//  Created by f on 28/12/2018.
//  Copyright © 2018 Haierfc. All rights reserved.
//

#import "DeviceBaseInfo.h"

@interface DeviceBaseInfo () {
    NSString *_protocol;
    NSString *_deviceId;
    NSString *_typeId;
    NSString *_typeName;
    NSString *_typeCode;
    NSString *_model;
    NSString *_prodNo;
    NSString *_parentId;
    NSString *_subDevNo;
}

@end

@implementation DeviceBaseInfo

+ (DeviceBaseInfo *)DeviceBaseInfo:(NSString *)protocol DeviceId:(NSString *)deviceId TypeId:(NSString *)typeId TypeName:(NSString *)typeName typeCode:(NSString *)typeCode Model:(NSString *)model ProdNo:(NSString *)prodNo ParentId:(NSString *)parentId SubDevNo:(NSString *)subDevNo
{
    return [[DeviceBaseInfo alloc] initWithProtocol:protocol DeviceId:deviceId TypeId:typeId TypeName:typeName typeCode:typeCode Model:model ProdNo:prodNo ParentId:parentId SubDevNo:subDevNo];
}

- (instancetype)initWithProtocol:(NSString *)protocol DeviceId:(NSString *)deviceId TypeId:(NSString *)typeId TypeName:(NSString *)typeName typeCode:(NSString *)typeCode Model:(NSString *)model ProdNo:(NSString *)prodNo ParentId:(NSString *)parentId SubDevNo:(NSString *)subDevNo
{
    if (self = [super init]) {
        _protocol = protocol;
        _deviceId = deviceId;
        _typeId = typeId;
        _typeName = typeName;
        _typeCode = typeCode;
        _model = model;
        _prodNo = prodNo;
        _parentId = parentId;
        _subDevNo = subDevNo;
    }

    return self;
}

#pragma mark - UpDeviceBaseInfo
- (NSString *)protocol
{
    return _protocol;
}

- (NSString *)deviceId
{
    return _deviceId;
}

- (NSString *)typeId
{
    return _typeId;
}

- (NSString *)typeName
{
    return _typeName;
}

- (NSString *)typeCode
{
    return _typeCode;
}

- (NSString *)model
{
    return _model;
}
- (NSString *)prodNo
{
    return _prodNo;
}

- (NSString *)parentId
{
    return _parentId;
}
- (NSString *)subDevNo
{
    return _subDevNo;
}

- (BOOL)updateBaseInfo:(id<UpDeviceBaseInfo>)baseInfo
{
    if (baseInfo == nil || self == baseInfo) {
        return NO;
    }
    if (baseInfo.typeId) {
        self.typeId = baseInfo.typeId;
    }
    if (baseInfo.typeName) {
        self.typeName = baseInfo.typeName;
    }
    if (baseInfo.typeCode) {
        self.typeCode = baseInfo.typeCode;
    }
    if (baseInfo.model) {
        self.model = baseInfo.model;
    }
    if (baseInfo.prodNo) {
        self.prodNo = baseInfo.prodNo;
    }
    if (baseInfo.parentId) {
        self.parentId = baseInfo.parentId;
    }
    if (baseInfo.subDevNo) {
        self.subDevNo = baseInfo.subDevNo;
    }
    return YES;
}

- (void)setProtocol:(NSString *)protocol
{
    _protocol = protocol;
    if (![protocol isKindOfClass:[NSString class]]) {
        _protocol = nil;
    }
}
- (void)setDeviceId:(NSString *)deviceId
{
    _deviceId = deviceId;
    if (![deviceId isKindOfClass:[NSString class]]) {
        _deviceId = nil;
    }
}

- (void)setTypeId:(NSString *)typeId
{
    _typeId = typeId;
    if (![typeId isKindOfClass:[NSString class]]) {
        _typeId = nil;
    }
}

- (void)setTypeName:(NSString *)typeName
{
    _typeName = typeName;
    if (![typeName isKindOfClass:[NSString class]]) {
        _typeName = nil;
    }
}

- (void)setTypeCode:(NSString *)typeCode
{
    _typeCode = typeCode;
    if (![typeCode isKindOfClass:[NSString class]]) {
        _typeCode = nil;
    }
}

- (void)setModel:(NSString *)model
{
    _model = model;
    if (![model isKindOfClass:[NSString class]]) {
        _model = nil;
    }
}
- (void)setProdNo:(NSString *)prodNo
{
    _prodNo = prodNo;
    if (![prodNo isKindOfClass:[NSString class]]) {
        _prodNo = nil;
    }
}

- (void)setParentId:(NSString *)parentId
{
    _parentId = parentId;
    if (![parentId isKindOfClass:[NSString class]]) {
        _parentId = nil;
    }
}
- (void)setSubDevNo:(NSString *)subDevNo
{
    _subDevNo = subDevNo;
    if (![subDevNo isKindOfClass:[NSString class]]) {
        _subDevNo = nil;
    }
}

- (BOOL)isEqual:(DeviceBaseInfo *)object
{
    if (self == object) {
        return YES;
    }

    if (object == nil) {
        return NO;
    }

    if (![object isKindOfClass:[self class]]) {
        return NO;
    }

    if (![self.protocol isEqualToString:object.protocol]) {
        return NO;
    }

    return [self.deviceId isEqualToString:object.deviceId];
}

@end
