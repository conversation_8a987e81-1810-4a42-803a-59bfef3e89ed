//
//  DeviceFOTAInfo.h
//  updevice_api
//
//  Created by gump on 11/6/2019.
//  Copyright © 2019 Haierfc. All rights reserved.
//

#import <Foundation/Foundation.h>
#import "UpDeviceFOTAInfo.h"
/*
 *设备底板固件升级信息类
 */
@interface DeviceFOTAInfo : NSObject <UpDeviceFOTAInfo>

+ (DeviceFOTAInfo *)DeviceFOTAInfo:(BOOL)isNeedFOTA currentVersion:(NSString *)currentVersion newestVersion:(NSString *)newestVersion newestVersionDescription:(NSString *)newestVersionDescription model:(NSString *)model timeoutInterval:(NSTimeInterval)timeoutInterval;

- (instancetype)initWithIsNeedFOTA:(BOOL)isNeedFOTA currentVersion:(NSString *)currentVersion newestVersion:(NSString *)newestVersion newestVersionDescription:(NSString *)newestVersionDescription model:(NSString *)model timeoutInterval:(NSTimeInterval)timeoutInterval;
@end
