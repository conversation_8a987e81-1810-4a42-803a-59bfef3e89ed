//
//  DeviceAttribute.m
//  updevice_api
//
//  Created by f on 28/12/2018.
//  Copyright © 2018 Haierfc. All rights reserved.
//

#import "DeviceAttribute.h"

@interface DeviceAttribute () {
    NSString *_name;
    NSString *_value;
}

@end

@implementation DeviceAttribute

- (NSString *)name
{
    return _name;
}

- (NSString *)value
{
    return _value;
}

- (void)setName:(NSString *)name
{
    _name = name;
}

- (void)setValue:(NSString *)value
{
    _value = value;
}

@end
