//
//  DeviceInfo.m
//  updevice_api
//
//  Created by <PERSON><PERSON> on 2019/1/8.
//  Copyright © 2019 Haierfc. All rights reserved.
//

#import "DeviceInfo.h"
#import "UpDeviceExtras.h"

static NSString *const key_aggregationParentId = @"DI-Product.aggregationParentId";

@interface DeviceInfo ()

@property (nonatomic, strong) NSMutableDictionary *extrasInfo;

@end

@implementation DeviceInfo


+ (DeviceInfo *)DeviceInfo:(DeviceBaseInfo *)deviceBaseInfo
{
    return [[DeviceInfo alloc] initWithDeviceBaseInfo:deviceBaseInfo];
}

- (instancetype)initWithDeviceBaseInfo:(DeviceBaseInfo *)deviceBaseInfo
{
    if (self = [super init]) {
        _deviceBaseInfo = deviceBaseInfo;
        _extrasInfo = [NSMutableDictionary dictionary];
        //test case: 2004-4,after add extrasInfo, assert deviceinfo failure
        if ([deviceBaseInfo isKindOfClass:[DeviceInfo class]]) {
            DeviceInfo *kDeviceInfo = (DeviceInfo *)deviceBaseInfo;
            if ([kDeviceInfo.extrasInfo isKindOfClass:NSDictionary.class]) {
                _extrasInfo = kDeviceInfo.extrasInfo;
            }
        }
    }
    return self;
}

#pragma mark - UpDeviceBaseInfo
- (NSString *)deviceId
{
    return _deviceBaseInfo.deviceId;
}

- (NSString *)model
{
    return _deviceBaseInfo.model;
}

- (NSString *)parentId
{
    return _deviceBaseInfo.parentId;
}

- (NSString *)prodNo
{
    return _deviceBaseInfo.prodNo;
}

- (NSString *)protocol
{
    return _deviceBaseInfo.protocol;
}

- (NSString *)subDevNo
{
    return _deviceBaseInfo.subDevNo;
}

- (NSString *)typeId
{
    return _deviceBaseInfo.typeId;
}

- (NSString *)typeName
{
    return _deviceBaseInfo.typeName;
}

- (NSString *)typeCode
{
    return _deviceBaseInfo.typeCode;
}

- (BOOL)updateBaseInfo:(id<UpDeviceBaseInfo>)baseInfo
{
    return [_deviceBaseInfo updateBaseInfo:baseInfo];
}

- (NSObject *)getExtra:(NSString *)key
{
    if (![key isKindOfClass:[NSString class]]) {
        return nil;
    }
    return self.extrasInfo[key];
}

- (NSDictionary *)getExtras
{
    return [self.extrasInfo copy];
}

- (int)getExtrasCount
{
    return (int)self.extrasInfo.count;
}

- (void)putExtra:(NSString *)key value:(NSObject *)value
{
    if (![key isKindOfClass:[NSString class]]) {
        return;
    }
    [self.extrasInfo setValue:value forKeyPath:key];
}

- (void)putQCExtra:(NSString *)key Value:(NSObject *)object
{
    if (![key isKindOfClass:[NSString class]]) {
        return;
    }
    [self.extrasInfo setValue:object forKey:key];
}

- (void)putExtras:(NSDictionary *)extras
{
    if (![extras isKindOfClass:[NSDictionary class]]) {
        return;
    }
    if (self.extrasInfo[key_aggregationParentId] != nil) {
        if (extras[key_aggregationParentId] == nil) {
            [self.extrasInfo removeObjectForKey:key_aggregationParentId];
        }
    }
    [self.extrasInfo addEntriesFromDictionary:extras];
}

#pragma mark - UpDeviceInfo
- (id<UpDeviceInfo>)cloneForSubDev:(id<UpDeviceBaseInfo>)subDevBaseInfo
{
    if (subDevBaseInfo == nil) {
        return nil;
    }
    if ([subDevBaseInfo isKindOfClass:[DeviceBaseInfo class]]) {
        DeviceBaseInfo *baseInfo = (DeviceBaseInfo *)subDevBaseInfo;
        if (baseInfo.typeId == nil) {
            [baseInfo setTypeId:[self typeId]];
        }
        if (baseInfo.typeName == nil) {
            [baseInfo setTypeName:[self typeName]];
        }
        if (baseInfo.model == nil) {
            [baseInfo setModel:[self model]];
        }
        if (baseInfo.prodNo == nil) {
            [baseInfo setProdNo:[self prodNo]];
        }
    }
    return [DeviceInfo DeviceInfo:subDevBaseInfo];
}

- (void)updateInfo:(id<UpDeviceInfo>)info
{
    if ([self updateBaseInfo:info]) {
        [self putExtras:[info getExtras]];
    }
}

@end
