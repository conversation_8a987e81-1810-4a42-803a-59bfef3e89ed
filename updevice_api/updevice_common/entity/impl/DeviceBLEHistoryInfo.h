//
//  DeviceBLEHistoryInfo.h
//  updevice_api
//
//  Created by gump on 27/8/2019.
//

#import <Foundation/Foundation.h>
#import "UpDeviceBLEHistoryInfo.h"

/**
 蓝牙历史数据信息
 */
@interface DeviceBLEHistoryInfo : NSObject <UpDeviceBLEHistoryInfo>

+ (DeviceBLEHistoryInfo *)DeviceBLEHistoryInfo:(NSData *)data currentCount:(NSUInteger)currentCount totalCount:(NSUInteger)totalCount;

- (instancetype)initWithData:(NSData *)data currentCount:(NSUInteger)currentCount totalCount:(NSUInteger)totalCount;

@end
