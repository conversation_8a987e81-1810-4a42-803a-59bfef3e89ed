//
//  DeviceInfo.h
//  updevice_api
//
//  Created by <PERSON><PERSON> on 2019/1/8.
//  Copyright © 2019 Haierfc. All rights reserved.
//

#import <Foundation/Foundation.h>
#import "UpDeviceInfo.h"
#import "DeviceBaseInfo.h"

@interface DeviceInfo : NSObject <UpDeviceInfo>
@property (nonatomic, strong) DeviceBaseInfo *deviceBaseInfo;

+ (DeviceInfo *)DeviceInfo:(DeviceBaseInfo *)deviceBaseInfo;
- (instancetype)initWithDeviceBaseInfo:(DeviceBaseInfo *)deviceBaseInfo;
//快连补充参数专用.(协议里的put Extra存值不进去)
- (void)putQCExtra:(NSString *)key Value:(NSObject *)object;

@end
