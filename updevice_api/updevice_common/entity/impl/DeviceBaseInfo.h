//
//  DeviceBaseInfo.h
//  updevice_api
//
//  Created by f on 28/12/2018.
//  Copyright © 2018 Haierfc. All rights reserved.
//

#import <Foundation/Foundation.h>
#import "UpDeviceBaseInfo.h"

NS_ASSUME_NONNULL_BEGIN

@interface DeviceBaseInfo : NSObject <UpDeviceBaseInfo>

+ (DeviceBaseInfo *)DeviceBaseInfo:(nullable NSString *)protocol DeviceId:(nullable NSString *)deviceId TypeId:(nullable NSString *)typeId TypeName:(nullable NSString *)typeName typeCode:(nullable NSString *)typeCode Model:(nullable NSString *)model ProdNo:(nullable NSString *)prodNo ParentId:(nullable NSString *)parentId SubDevNo:(nullable NSString *)subDevNo;

- (instancetype)initWithProtocol:(nullable NSString *)protocol DeviceId:(nullable NSString *)deviceId TypeId:(nullable NSString *)typeId TypeName:(nullable NSString *)typeName typeCode:(nullable NSString *)typeCode Model:(nullable NSString *)model ProdNo:(nullable NSString *)prodNo ParentId:(nullable NSString *)parentId SubDevNo:(nullable NSString *)subDevNo;

- (void)setProtocol:(NSString *)protocol;
- (void)setDeviceId:(NSString *)deviceId;
- (void)setTypeId:(NSString *)typeId;
- (void)setTypeName:(NSString *)typeName;
- (void)setTypeCode:(NSString *)typeCode;
- (void)setModel:(NSString *)model;
- (void)setProdNo:(NSString *)prodNo;
- (void)setParentId:(NSString *)parentId;
- (void)setSubDevNo:(NSString *)subDevNo;

@end

NS_ASSUME_NONNULL_END
