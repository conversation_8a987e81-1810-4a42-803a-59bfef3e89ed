//
//  DeviceCaution.h
//  updevice_api
//
//  Created by f on 28/12/2018.
//  Copyright © 2018 Haierfc. All rights reserved.
//

#import <Foundation/Foundation.h>
#import "UpDeviceCaution.h"

NS_ASSUME_NONNULL_BEGIN

@interface DeviceCaution : NSObject <UpDeviceCaution>

+ (DeviceCaution *)DeviceCaution:(NSString *)name Value:(NSString *)value Time:(NSString *)time;

- (instancetype)initWithName:(NSString *)name Value:(NSString *)value Time:(NSString *)time;

- (void)setName:(NSString *)name;

- (void)setValue:(NSString *)value;

- (void)setTime:(NSString *)time;

@end

NS_ASSUME_NONNULL_END
