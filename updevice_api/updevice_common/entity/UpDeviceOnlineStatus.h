//
//  UpDeviceOnlineStatus.h
//  UPDevice
//
//  Created by gump on 2021/8/27.
//  Copyright © 2021 海尔优家智能科技（北京）有限公司. All rights reserved.
//
/**
 * RealOnline枚举值
 */
typedef NS_ENUM(NSInteger, UpDeviceRealOnline) {
    /**
     * @brief usdk报设备在线，和云平台连接没有关系
     */
    UpDeviceRealOnline_ONLINE,
    /**
     * @brief usdk报设备离线，和云平台连接没有关系
     */
    UpDeviceRealOnline_OFFLINE,
};

/**
 * sleepState枚举值
 */
typedef NS_ENUM(NSInteger, UpDeviceSleepState) {
    /**
     * 设备未休眠状态
     */
    UpDeviceSleepStateUnsleeping = 0,
    /**
     * 设备休眠中状态
     */
    UpDeviceSleepStateSleeping,
    /**
     * 设备唤醒中状态
     */
    UpDeviceSleepStateWakingUp,
    /**
     * 低功耗模式状态
     * @since 7.9.0
     */
    UpDeviceSleepStateLowPower,
};
