//
//  UpDeviceOfflineCause.h
//  UPDevice
//
//  Created by pc on 2023/11/10.
//  Copyright © 2023 海尔优家智能科技（北京）有限公司. All rights reserved.
//

#ifndef UpDeviceOfflineCause_h
#define UpDeviceOfflineCause_h

/**
 * 设备主动离线原因
 *  @since v7.1.1
 */
typedef NS_ENUM(NSInteger, UpDeviceOfflineCause) {
    /**
     * @brief 设备无主动离线原因
     * @since v7.1.1
     */
    UpDeviceOfflineCause_None = 0,
    /**
     * @brief 设备普通离线
     * @since v7.1.1
     */
    UpDeviceOfflineCause_Normal = 1,
    /**
     * @brief 设备进入低功耗离线
     * @since v7.1.1
     */
    UpDeviceOfflineCause_LowPower = 2,
    /**
     * @brief 设备关闭 wifi 离线
     * @since v7.1.1
     */
    UpDeviceOfflineCause_WIFI_Closed = 3,
};

#endif /* UpDeviceOfflineCause_h */
