//
//  UpDeviceException.h
//  updevice_api
//
//  Created by <PERSON><PERSON> on 2018/12/18.
//  Copyright © 2018 Haierfc. All rights reserved.
//

#import <Foundation/Foundation.h>
#import "UpDeviceResult.h"
extern NSString *const CODE_NOT_SUPPORT;
extern NSString *const CODE_NOT_READY;
extern NSString *const CODE_WRONG_STATE;
extern NSString *const CODE_UNKNOWN_ACTION;
extern NSString *const CODE_MISSING_PARAMS;
extern NSString *const CODE_DUPLICATE_BROKER;

extern NSString *const INFO_NOT_SUPPORT;
extern NSString *const INFO_NOT_READY;
extern NSString *const INFO_WRONG_STATE;
extern NSString *const INFO_UNKNOWN_ACTION;
extern NSString *const INFO_MISSING_PARAMS;
extern NSString *const INFO_DUPLICATE_BROKER;
@interface UpDeviceException : NSObject
+ (UpDeviceException *)UpDeviceException:(UpDeviceResult *)result;
+ (UpDeviceException *)UpDeviceException:(NSString *)extraCode extraInfo:(NSString *)extraInfo;

- (instancetype)initWithResult:(UpDeviceResult *)result;
- (instancetype)initWithExtraData:(NSString *)extraCode extraInfo:(NSString *)extraInfo;
- (NSString *)getExtraCode;
- (NSString *)getExtraInfo;
@end

@interface DuplicateBrokerException : UpDeviceException {
}

- (instancetype)initWithAction:(NSString *)protocol;
@end

@interface MissingParamsException : UpDeviceException {
}
- (instancetype)initWithAction:(NSString *)params;

@end
