//
//  UpDeviceException.m
//  updevice_api
//
//  Created by <PERSON><PERSON> on 2018/12/18.
//  Copyright © 2018 Haierfc. All rights reserved.
//

#import "UpDeviceException.h"
NSString *const CODE_NOT_SUPPORT = @"100001";
NSString *const CODE_NOT_READY = @"100002";
NSString *const CODE_WRONG_STATE = @"100003";
NSString *const CODE_UNKNOWN_ACTION = @"100004";
NSString *const CODE_MISSING_PARAMS = @"100005";
NSString *const CODE_DUPLICATE_BROKER = @"100006";

NSString *const INFO_NOT_SUPPORT = @"不支持的协议";
NSString *const INFO_NOT_READY = @"未准备就绪";
NSString *const INFO_WRONG_STATE = @"设备状态错误，不能执行当前操";
NSString *const INFO_UNKNOWN_ACTION = @"未知的操作";
NSString *const INFO_MISSING_PARAMS = @"缺少必要参数";
NSString *const INFO_DUPLICATE_BROKER = @"重复的Broker";

@interface UpDeviceException ()
@property (nonatomic, strong) NSString *extraCode;
@property (nonatomic, strong) NSString *extraInfo;

@end

@implementation UpDeviceException

+ (UpDeviceException *)UpDeviceException:(UpDeviceResult *)result
{
    return [[UpDeviceException alloc] initWithResult:result];
}

+ (UpDeviceException *)UpDeviceException:(NSString *)extraCode extraInfo:(NSString *)extraInfo
{
    return [[UpDeviceException alloc] initWithExtraData:extraCode extraInfo:extraInfo];
}

- (instancetype)initWithResult:(UpDeviceResult *)result
{
    if (self = [super init]) {
        self.extraCode = result.extraData;
        self.extraInfo = result.extraInfo;
    }
    return self;
}
- (instancetype)initWithExtraData:(NSString *)extraCode extraInfo:(NSString *)extraInfo
{
    if (self = [super init]) {
        self.extraCode = extraCode;
        self.extraInfo = extraInfo;
    }
    return self;
}
- (NSString *)getExtraCode
{
    return self.extraCode;
}
- (NSString *)getExtraInfo
{
    return self.extraInfo;
}
@end

@interface NotSupportException : UpDeviceException

@end

@implementation NotSupportException

- (instancetype)initWithExtraData:(NSString *)extraCode extraInfo:(NSString *)extraInfo
{
    if (self = [super init]) {
        self.extraCode = CODE_NOT_SUPPORT;
        self.extraInfo = INFO_NOT_SUPPORT;
    }
    return self;
}
@end

@interface NotReadyException : UpDeviceException

@end

@implementation NotReadyException

- (instancetype)initWithExtraData:(NSString *)extraCode extraInfo:(NSString *)extraInfo
{
    if (self = [super init]) {
        self.extraCode = CODE_NOT_READY;
        self.extraInfo = INFO_NOT_READY;
    }
    return self;
}
@end

@interface WrongStateException : UpDeviceException {
    NSString *_currentStateName;
}

@end

@implementation WrongStateException

- (instancetype)initWithCurrentStateName:(NSString *)currentStateName
{
    if (self = [super initWithExtraData:CODE_WRONG_STATE extraInfo:INFO_WRONG_STATE]) {
        _currentStateName = currentStateName;
    }
    return self;
}
- (NSString *)currentStateName
{
    return _currentStateName;
}
@end

@interface UnknownActionException : UpDeviceException {
}

@end

@implementation UnknownActionException

- (instancetype)initWithAction:(NSString *)action
{
    NSString *actStr = [NSString stringWithFormat:@"%@: '%@'", INFO_UNKNOWN_ACTION, action];
    if (self = [super initWithExtraData:CODE_UNKNOWN_ACTION extraInfo:actStr]) {
    }
    return self;
}
@end


@implementation MissingParamsException

- (instancetype)initWithAction:(NSString *)params
{
    NSString *actStr = [NSString stringWithFormat:@"%@: %@", INFO_MISSING_PARAMS, params];
    if (self = [super initWithExtraData:CODE_MISSING_PARAMS extraInfo:actStr]) {
    }
    return self;
}
@end


@implementation DuplicateBrokerException

- (instancetype)initWithAction:(NSString *)protocol
{
    NSString *actStr = [NSString stringWithFormat:@"%@: '%@'", INFO_DUPLICATE_BROKER, protocol];
    if (self = [super initWithExtraData:CODE_DUPLICATE_BROKER extraInfo:actStr]) {
    }
    return self;
}
@end
