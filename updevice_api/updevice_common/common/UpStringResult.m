//
//  UpStringResult.m
//  updevice_api
//
//  Created by <PERSON><PERSON> on 2018/12/18.
//  Copyright © 2018 Haierfc. All rights reserved.
//

#import "UpStringResult.h"

@implementation UpStringResult
+ (UpStringResult *)UpStringResult:(UpDeviceResultErrorCode)errorCode extraData:(NSString *)extraData
{
    UpStringResult *res = [[UpStringResult alloc] initWithErrorCode:errorCode extraCode:nil extraData:extraData extraInfo:nil];
    return res;
}
+ (UpStringResult *)UpStringResult:(UpDeviceResultErrorCode)errorCode extraCode:(NSString *)extraCode extraData:(id)extraData extraInfo:(NSString *)extraInfo
{
    UpStringResult *res = [[UpStringResult alloc] initWithErrorCode:errorCode extraCode:extraCode extraData:extraData extraInfo:extraInfo];
    return res;
}
- (instancetype)initWithErrorCode:(UpDeviceResultErrorCode)errorCode extraCode:(NSString *)extraCode extraData:(id)extraData extraInfo:(NSString *)extraInfo
{
    if (self = [super initWithErrorCode:ErrorCode_SUCCESS extraData:extraData extraCode:extraCode extraInfo:extraInfo]) {
    }
    return self;
}

@end


@implementation UpSuccessResult

- (instancetype)initWithExtraData:(NSString *)extraData
{
    if (self = [super initWithErrorCode:ErrorCode_SUCCESS extraCode:nil extraData:extraData extraInfo:nil]) {
    }
    return self;
}
@end

@implementation UpFailureResult

- (instancetype)initWithExtraData:(NSString *)extraData
{
    if (self = [super initWithErrorCode:ErrorCode_FAILURE extraCode:nil extraData:extraData extraInfo:nil]) {
    }
    return self;
}
@end
