//
//  UpDeviceSetMap.h
//  updevice_api
//
//  Created by f on 27/12/2018.
//  Copyright © 2018 Haierfc. All rights reserved.
//

#import <Foundation/Foundation.h>

NS_ASSUME_NONNULL_BEGIN

@protocol UpDeviceSetMap <NSObject>

- (int)total;

- (int)size;

- (int)size:(NSString *)key;

- (BOOL)isEmpty;

- (BOOL)isEmpty:(NSString *)key;

- (void)add:(id)target;

- (void)put:(NSString *)key Target:(id)target;

- (void)remove:(id)target;

- (void)remove:(NSString *)key Target:(id)target;

- (NSArray *)get;

- (nullable NSArray *)get:(NSString *)key;

- (void)clear;

- (void)clear:(NSString *)key;

- (void)clearAll;

@end

NS_ASSUME_NONNULL_END
