//
//  UpDeviceCarrier.m
//  updevice_api
//
//  Created by f on 27/12/2018.
//  Copyright © 2018 Haierfc. All rights reserved.
//

#import "UpDeviceCarrier.h"

@interface UpDeviceCarrier ()

@property (nonatomic, strong) NSString *universalKey;
@property (nonatomic, strong) NSMutableDictionary<NSString *, NSMutableSet *> *targetSetMap;

@end

@implementation UpDeviceCarrier

+ (UpDeviceCarrier *)UpDeviceCarrier:(NSString *)universalKey
{
    return [[UpDeviceCarrier alloc] initWithUniversalKey:universalKey];
}

- (instancetype)initWithUniversalKey:(NSString *)universalKey
{
    if (self = [super init]) {
        self.universalKey = universalKey ? universalKey : [NSUUID UUID].UUIDString;

        self.targetSetMap = [NSMutableDictionary dictionary];
    }

    return self;
}

- (NSMutableSet *)getTargetSet:(NSString *)key
{
    if (key == nil) {
        return nil;
    }

    NSMutableSet *targetSet;
    @synchronized(self.targetSetMap)
    {
        targetSet = [self.targetSetMap objectForKey:key];
        if (targetSet == nil) {
            targetSet = [NSMutableSet set];
            [self.targetSetMap setObject:targetSet forKey:key];
        }
    }

    return targetSet;
}

#pragma mark - UpDeviceSetMap

- (int)total
{
    int count = 0;

    for (NSString *key in self.targetSetMap.allKeys) {
        NSSet *set;
        set = [self.targetSetMap objectForKey:key];
        if (set == nil) {
            continue;
        }
        count += set.count;
    }

    return count;
}

- (int)size
{
    return [self size:self.universalKey];
}

- (int)size:(NSString *)key
{
    if (key == nil) {
        return 0;
    }

    int count = 0;
    NSMutableSet *set = [self.targetSetMap objectForKey:key];
    if (set) {
        count = (int)set.count;
    }

    return count;
}

- (BOOL)isEmpty
{
    return [self isEmpty:self.universalKey];
}

- (BOOL)isEmpty:(NSString *)key
{
    if (key == nil) {
        return YES;
    }

    NSSet *set = [self.targetSetMap objectForKey:key];
    if (set) {
        return set.count == 0;
    }

    return YES;
}

- (void)add:(id)target
{
    [self put:self.universalKey Target:target];
}

- (void)put:(NSString *)key Target:(id)target
{
    if (key == nil || target == nil) {
        return;
    }

    NSMutableSet *set = [self getTargetSet:key];
    [set addObject:target];
}

- (void)remove:(id)target
{
    [self remove:self.universalKey Target:target];
}

- (void)remove:(NSString *)key Target:(id)target
{
    if (key == nil || target == nil) {
        return;
    }

    NSMutableSet *set = [self.targetSetMap objectForKey:key];
    if (set) {
        [set removeObject:target];
    }
}

- (NSArray *)get
{
    return [self get:self.universalKey];
}

- (nullable NSArray *)get:(NSString *)key
{
    if (key == nil) {
        return nil;
    }
    return [[NSSet setWithSet:[self getTargetSet:key]] allObjects];
}

- (void)clear
{
    [self clear:self.universalKey];
}

- (void)clear:(NSString *)key
{
    if (key == nil) {
        return;
    }

    NSMutableSet *set = [self.targetSetMap objectForKey:key];
    if (set) {
        [set removeAllObjects];
    }
}

- (void)clearAll
{
    [self.targetSetMap removeAllObjects];
}

@end
