//
//  UpDeviceCarrier.h
//  updevice_api
//
//  Created by f on 27/12/2018.
//  Copyright © 2018 Haierfc. All rights reserved.
//

#import <Foundation/Foundation.h>
#import "UpDeviceSetMap.h"

NS_ASSUME_NONNULL_BEGIN

@interface UpDeviceCarrier : NSObject <UpDeviceSetMap>

+ (UpDeviceCarrier *)UpDeviceCarrier:(nullable NSString *)universalKey;

- (instancetype)initWithUniversalKey:(nullable NSString *)universalKey;

- (NSMutableSet *)getTargetSet:(NSString *)key;

@end

NS_ASSUME_NONNULL_END
