//
//  UpDeviceResult.h
//  updevice_api
//
//  Created by <PERSON><PERSON> on 2018/12/17.
//  Copyright © 2018 Haierfc. All rights reserved.
//

#import <Foundation/Foundation.h>

/**
 * ToolkitState枚举值
 */
typedef NS_ENUM(NSInteger, UpDeviceResultErrorCode) {
    /**
     * @brief  成功
     */
    ErrorCode_SUCCESS = 0,
    /**
     * @brief 失败
     */
    ErrorCode_FAILURE,
    /**
     * @brief 无效
     */
    ErrorCode_INVALID,
    /**
     * @brief 超时
     */
    ErrorCode_TIMEOUT,
};
@interface UpDeviceResult : NSObject
@property (nonatomic, assign) BOOL isSuccessful;
@property (nonatomic, assign) UpDeviceResultErrorCode errorCode;
@property (nonatomic, strong) id extraData;
@property (nonatomic, strong) NSString *extraCode;
@property (nonatomic, strong) NSString *extraInfo;

+ (UpDeviceResult *)UpDeviceResult:(UpDeviceResultErrorCode)ErrorCode extraData:(id)extraData;

- (instancetype)initWithErrorCode:(UpDeviceResultErrorCode)errorCode extraData:(id)extraData extraCode:(NSString *)extraCode extraInfo:(NSString *)extraInfo;
@end
