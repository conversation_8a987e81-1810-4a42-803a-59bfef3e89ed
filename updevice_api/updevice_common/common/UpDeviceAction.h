//
//  UpDeviceAction.h
//  updevice_api
//
//  Created by f on 27/12/2018.
//  Copyright © 2018 Haierfc. All rights reserved.
//

#import <Foundation/Foundation.h>
#import "UpDeviceExecutable.h"

NS_ASSUME_NONNULL_BEGIN

@interface UpDeviceAction : NSObject <UpDeviceExecutable>

@property (nonatomic, strong) NSString *name;

+ (UpDeviceAction *)UpDeviceAction:(NSString *)name;

- (instancetype)initWithName:(NSString *)name;

- (nullable id)getParam:(NSDictionary<NSString *, id> *)params Key:(NSString *)key Class:(Class)className;


// 虚函数，空实现
- (void)execute:(NSDictionary *)params finishBlock:(void (^)(UpDeviceResult *result))finishBlock;

@end

NS_ASSUME_NONNULL_END
