//
//  UpDeviceResult.m
//  updevice_api
//
//  Created by <PERSON><PERSON> on 2018/12/17.
//  Copyright © 2018 Haierfc. All rights reserved.
//

#import "UpDeviceResult.h"

@implementation UpDeviceResult


+ (UpDeviceResult *)UpDeviceResult:(UpDeviceResultErrorCode)ErrorCode extraData:(id)extraData;
{
    return [[UpDeviceResult alloc] initWithErrorCode:ErrorCode extraData:extraData extraCode:nil extraInfo:nil];
}

- (instancetype)initWithErrorCode:(UpDeviceResultErrorCode)errorCode extraData:(id)extraData extraCode:(NSString *)extraCode extraInfo:(NSString *)extraInfo
{

    if (self = [super init]) {
        self.errorCode = errorCode;
        self.extraData = extraData;
        self.extraCode = extraCode;
        self.extraInfo = extraInfo;
    }
    return self;
}

- (BOOL)isSuccessful
{
    return _errorCode == ErrorCode_SUCCESS;
}
@end
