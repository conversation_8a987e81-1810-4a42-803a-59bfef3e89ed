//
//  UpDeviceAction.m
//  updevice_api
//
//  Created by f on 27/12/2018.
//  Copyright © 2018 Haierfc. All rights reserved.
//

#import "UpDeviceAction.h"
#import "UPDeviceLog.h"

@implementation UpDeviceAction

+ (UpDeviceAction *)UpDeviceAction:(NSString *)name
{
    return [[UpDeviceAction alloc] initWithName:name];
}

- (instancetype)initWithName:(NSString *)name
{
    if (self = [super init]) {
        self.name = name;
    }

    return self;
}

- (nullable id)getParam:(NSDictionary<NSString *, id> *)params Key:(NSString *)key Class:(Class)className
{
    if (params == nil || [params objectForKey:key] == nil || className == nil) {
        UPLogError(kUPDevicePrefix, @"%s[%d]参数获取失败，入参格式有误！key:%@,params:%@,className:%@", __PRETTY_FUNCTION__, __LINE__, key, params, className);
        return nil;
    }

    id object = [params objectForKey:key];
    if ([object isKindOfClass:className]) {
        return object;
    }
    UPLogError(kUPDevicePrefix, @"%s[%d]参数获取失败，参数值不符合类型要求！key:%@,params:%@,className:%@,value:%@", __PRETTY_FUNCTION__, __LINE__, key, params, className, object);
    return nil;
}

#pragma mark - UpDeviceExecutable

- (void)execute:(NSString *)action params:(NSDictionary *)params finishBlock:(void (^)(UpDeviceResult *))finishBlock
{
    if (![self.name isKindOfClass:[NSString class]]) {
        UPLogError(kUPDevicePrefix, @"%s[%d]Action的名称格式不正确！name:%@", __PRETTY_FUNCTION__, __LINE__, self.name);
        return;
    }
    if ([self.name isEqualToString:action]) {
        UPLogError(kUPDevicePrefix, @"%s[%d]执行操作！name:%@,params:%@", __PRETTY_FUNCTION__, __LINE__, action, params);
        [self execute:params
            finishBlock:^(UpDeviceResult *_Nonnull result) {
              if ([result isSuccessful]) {
                  UPLogDebug(kUPDevicePrefix, @"%s[%d]操作（action:%@,params:%@）执行成功！", __PRETTY_FUNCTION__, __LINE__, action, params);
              }
              else {
                  UPLogError(kUPDevicePrefix, @"%s[%d]操作（action:%@,params:%@）执行失败！error:%@", __PRETTY_FUNCTION__, __LINE__, action, params, result.extraInfo);
              }
              finishBlock(result);
            }];
    }
    else {
        UPLogWarning(kUPDevicePrefix, @"%s[%d]Action对象的名称与要执行的action名称不一致！name:%@,actionName:%@", __PRETTY_FUNCTION__, __LINE__, self.name, action);
    }
}

- (void)execute:(NSDictionary *)params finishBlock:(void (^)(UpDeviceResult *result))finishBlock
{
    UPLogWarning(kUPDevicePrefix, @"%s[%d]调用了父类的空方法！name:%@", __PRETTY_FUNCTION__, __LINE__, self.name);
}

- (BOOL)isEqual:(UpDeviceAction *)object
{
    if (object == nil) {
        return NO;
    }

    if (![object isKindOfClass:[self class]]) {
        return NO;
    }

    if (object == self) {
        return YES;
    }

    return [self.name isEqualToString:object.name];
}

- (id)copyWithZone:(NSZone *)zone
{

    UpDeviceAction *action = [[[self class] allocWithZone:zone] init];
    action.name = [self.name copy];

    return action;
}

@end
