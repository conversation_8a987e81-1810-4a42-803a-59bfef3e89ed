//
//  UpDeviceHelper.m
//  updevice_api
//
//  Created by f on 28/12/2018.
//  Copyright © 2018 Haierfc. All rights reserved.
//

#import "UpDeviceHelper.h"
#import "UpDeviceBaseInfo.h"
#import "UPDeviceLog.h"

@implementation UpDeviceHelper

+ (NSString *)genUniqueId:(NSString *)protocol DeviceId:(NSString *)deviceId
{
    if (deviceId == nil) {
        UPLogWarning(kUPDevicePrefix, @"%s[%d]入参deviceId格式错误！deviceId:%@", __PRETTY_FUNCTION__, __LINE__, deviceId);
        return nil;
    }

    if (protocol == nil) {
        UPLogWarning(kUPDevicePrefix, @"%s[%d]入参protocol格式错误！protocol:%@", __PRETTY_FUNCTION__, __LINE__, protocol);
        protocol = PROTOCOL_UNKNOWN;
    }
    NSString *uniqueId = [NSString stringWithFormat:@"%@%@%@", protocol, @"~>", deviceId];
    UPLogDebug(kUPDevicePrefix, @"%s[%d]入参(protocol:%@,deviceId:%@)所对应的uniqueID为%@.", __PRETTY_FUNCTION__, __LINE__, protocol, deviceId, uniqueId);
    return uniqueId;
}

+ (BOOL)isNeedCleanAttributes:(NSString *)typeId
{
    return [@"201c51890c31c308050100218007530000000000000000000000000000000040" isEqualToString:typeId] ||
           [@"201c51890c31c308050100218007530000000001000000000000000000000040" isEqualToString:typeId] ||
           [@"201c51890c31c308050100218007530000000002000000000000000000000040" isEqualToString:typeId] ||
           [@"201c51890c31c308310100218007530000000003000000000000000000000040" isEqualToString:typeId] ||
           [@"201c51890c31c308050100218007610000000000000000000000000000000040" isEqualToString:typeId] ||
           [@"201c51890c31c308050100218007610000000001000000000000000000000040" isEqualToString:typeId] ||
           [@"201c51890c31c308050100218007610000000002000000000000000000000040" isEqualToString:typeId] ||
           [@"201c51890c31c308050100218007614200000001000000000000000000000040" isEqualToString:typeId] ||
           [@"201c51890c31c308050100218007614200000001010000000000000000000040" isEqualToString:typeId] ||
           [@"201c51890c31c308050100218007614200000001020000000000000000000040" isEqualToString:typeId] ||
           [@"201c51890c31c308050100218007614200000000000000000000000000000040" isEqualToString:typeId] ||
           [@"201c51890c31c308050100218007614200000000010000000000000000000040" isEqualToString:typeId] ||
           [@"201c51890c31c308050100218008010000000000000000000000000000000040" isEqualToString:typeId] ||
           [@"201c51890c31c308050100218008010000000001000000000000000000000040" isEqualToString:typeId] ||
           [@"201c51890c31c308050100218007614200000000020000000000000000000040" isEqualToString:typeId];
}

@end
