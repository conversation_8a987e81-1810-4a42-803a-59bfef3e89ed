//
//  UpDeviceCenter.h
//  updevice_api
//
//  Created by <PERSON><PERSON> on 2018/12/17.
//  Copyright © 2018 Haierfc. All rights reserved.
//

#import <Foundation/Foundation.h>
#import "UpDeviceAttribute.h"
#import "UpDeviceConnection.h"
#import "UpDeviceCaution.h"
#import "UpDeviceInfo.h"
#import "UpDeviceState.h"
#import "UpDevice.h"
#import "UpDeviceFilter.h"
#import "UpDeviceException.h"

/**
 设备变化属性变化回调
 */
@protocol UpDeviceChangeObserver <NSObject, NSCopying>

/// 按照设备ID订阅 设备变化事件
@optional
- (void)onDevicesPropertyChanged:(NSArray<id<UpDevice>> *_Nonnull)devices;

/// 按照家庭 ID订阅 设备变化事件
@optional
- (void)onFamilyDevicesPropertyChanged:(NSArray<id<UpDevice>> *_Nonnull)devices;

@end

/**
 * 设备列表监听器
 */
@protocol Listener <NSObject>

- (void)onDeviceListChange:(NSArray<id<UpDevice>> *)deviceList;

@end
/**
 * 设备中心，定义获取设备列表及设备代理的相关接口
 */
@protocol UpDeviceCenter <NSObject>
/**
 * 获取支持的协议名称，目前改为只支持一种协议
 * 通过{@link UpDeviceToolkit#getSupportProtocol()}方法返回的值
 *
 * @return 协议名称
 */
- (NSString *)getSupportProtocol;
/**
 * 获取设备代理器
 *
 * @return 代理器对象
*/
- (id<UpDeviceBroker>)getBroker;
/**
 * 获取是否就绪标识
 *
 * @return true - 已就绪，false - 未就绪
 */
- (BOOL)isReady;
/**
 * 启动设备中心，用于启动设备中心持有的{@link UpDeviceToolkit}，并设置内部相关状态
 *
 * @return 可订阅的启动结果
 */
- (void)prepare:(void (^)(UpDeviceResult *result))finishBlock;
/**
 * 关闭设备中心，用于重置内部状态，并关闭设备中心持有的{@link UpDeviceToolkit}
 *
 * @return 可订阅的关闭的启动结果
 */
- (void)release:(void (^)(UpDeviceResult *result))finishBlock;
/**
 *  7.21.0
 *  由UpDeviceInit传递UserDomain 设备列表变化事件
 *  将此次刷新事件由Listener 接口的onDeviceListChange传递
 */
- (void)notifyDeviceListChanged;

/// 从守护进程中得到设备
/// @param deviceId 设备ID
- (id<UpDevice>)getDevice:(NSString *)deviceId;

/// 从守护进程中得到Scanner设备
/// @param deviceId 设备ID
- (id<UpDevice>)getScannerDevice:(NSString *)deviceId;

/**
 * 更新设备列表
 *
 * @param immediate 立即更新标识，ture - 立即请求网络，false - 如有缓存则使用缓存
 * @return 可订阅的更新结果
 */
- (void)updateDeviceList:(BOOL)immediate finishBlock:(void (^)(UpDeviceResult *result))finishBlock;

/// 获取指定家庭下家庭下组设备列表
/// @param familyId 家庭id
/// @param finishBlock 回调结果
- (void)getGroupMemberListWithFamilyId:(NSString *)familyId finishBlock:(void (^)(UpDeviceResult *result))finishBlock;

/**
 * 更新设备信息
 *
 * @param protocol  协议名称，目前改为只支持一种协议，该参数无效
 * @param deviceId  设备ID
 * @param immediate immediate 立即更新标识，ture - 立即请求网络，false - 如有缓存则使用缓存
 * @return 可订阅的更新结果
 * @deprecated 使用 {@link UpDeviceCenter#updateDeviceInfo(String, boolean)}
 */
- (void)updateDeviceInfo:(NSString *)protocol deviceId:(NSString *)deviceId immediate:(BOOL)immediate finishBlock:(void (^)(UpDeviceResult *result))finishBlock;
/**
 * 注册设备列表监听器
 *
 * @param listener  监听器对象
 * @param immediate 是否立即通知列表变化标识，true - 立即通知，false - 不立即通知、
 */
- (void)attach:(id<Listener>)listener immediate:(BOOL)immediate;
/**
 * 取消注册设备列表监听器
 *
 * @param listener 监听器对象
 */
- (void)detach:(id<Listener>)listener;
/**
 * @param protocol 协议名称，目前改为只支持一种协议，该参数无效
 * @param deviceId 设备ID
 * @return 设备对象，当指定设备ID不存在时，返回null
 * @deprecated 使用 {@link UpDeviceCenter#getDevice(String)}
 */
- (id<UpDevice>)getDevice:(NSString *)protocol deviceId:(NSString *)deviceId;
/**
 * 获取设备列表
 *
 * @return 设备列表副本，不会为null，当没有设备时，返回空列表
 */
- (NSArray<id<UpDevice>> *)getDeviceList;
/**
 * 使用指定过滤器获取过滤后的设备列表
 *
 * @param filter 过滤器
 * @return 过滤后的设备列表副本，不会为null，当没有设备时，返回空列表
 */
- (NSArray<id<UpDevice>> *)getDeviceList:(id<UpDeviceFilter>)filter;
/**
 * 清除设备列表，本方法会销毁所有设备对象
 */
- (void)clearDeviceList;

/// ####### 逻辑下沉 #######

/**
* 按照家庭ID获取设备列表的接口
*
* @param familyId 家庭ID
* @return
* 1. 过滤后的设备列表副本 （当没有设备时，返回空列表）
* 2. 没有加载过设备列表，会返回 nil
*/
- (nullable NSArray<id<UpDevice>> *)getDeviceListWithFamilyId:(NSString *_Nonnull)familyId;

/**
* 按照家庭ID订阅设备属性变化
*
* @param familyId 家庭ID
* @param observer 监听者
 */
- (void)subscribeDeviceChangeWithFamilyId:(NSString *_Nullable)familyId observer:(id<UpDeviceChangeObserver> _Nonnull)observer;

/**
* 解除家庭ID订阅设备属性变化
*
* @param familyId 家庭ID
* @param observer 监听者
 */
- (void)unsubscribeDeviceChangeWithFamilyId:(NSString *_Nullable)familyId observer:(id<UpDeviceChangeObserver> _Nonnull)observer;

/**
* 按照设备ID订阅设备属性变化
*
* @param deviceIds 设备ID集合
* @param observer 监听者
 */
- (void)subscribeDeviceChangeWithDeviceIds:(NSArray<NSString *> *_Nullable)deviceIds observer:(id<UpDeviceChangeObserver> _Nonnull)observer;

/**
* 解除设备ID订阅设备属性变化
*
* @param deviceIds 设备ID集合
* @param observer 监听者
 */
- (void)unsubscribeDeviceChangeWithDeviceIds:(NSArray<NSString *> *_Nullable)deviceIds observer:(id<UpDeviceChangeObserver> _Nonnull)observer;

/**
 * 下发指令
 @param deviceId 下发指令的ID
 @param subDeviceId 子设备ID
 @param command 指令
 @param completeResult 下发指令的结果
 */
- (void)executeOperation:(nonnull NSString *)deviceId subDevice:(nullable NSString *)subDeviceId command:(id<UpDeviceCommand> _Nonnull)command complete:(void (^_Nonnull)(UpDeviceResult *_Nonnull))completeResult;

@end
