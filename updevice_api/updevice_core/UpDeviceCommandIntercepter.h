//
//  UpDeviceCommandIntercepter.h
//  updevice_api
//
//  Created by <PERSON><PERSON> on 2025/08/06.
//  Copyright © 2025 Haierfc. All rights reserved.
//

#import <Foundation/Foundation.h>

@protocol UpDevice;
@protocol UpDeviceCommand;

@protocol UpDeviceCommandIntercepter <NSObject>

@required
/**
 * 判断是否匹配该设备
 *
 * @param device 设备对象
 * @return YES - 匹配，NO - 不匹配
 */
- (BOOL)isMatch:(id<UpDevice>)device;

@optional
/**
 * 命令执行前的拦截处理
 *
 * @param device 设备对象
 * @param command 命令对象
 */
- (void)beforeCommandExecution:(id<UpDevice>)device command:(id<UpDeviceCommand>)command;

/**
 * 命令执行成功后的处理
 *
 * @param device 设备对象
 * @param command 命令对象
 */
- (void)onCommandSuccess:(id<UpDevice>)device command:(id<UpDeviceCommand>)command;

/**
 * 命令执行失败后的处理
 *
 * @param device 设备对象
 * @param command 命令对象
 */
- (void)onCommandFailure:(id<UpDevice>)device command:(id<UpDeviceCommand>)command;

@end
