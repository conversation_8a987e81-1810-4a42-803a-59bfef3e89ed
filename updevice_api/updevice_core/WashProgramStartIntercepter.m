//
//  WashProgramStartIntercepter.m
//  updevice_api
//
//  Created by <PERSON><PERSON> on 2025/01/08.
//  Copyright © 2025 Haierfc. All rights reserved.
//

#import "WashProgramStartIntercepter.h"
#import "UpDevice.h"
#import "UpDeviceCommand.h"
#import "UPDeviceLog.h"
#import "UpDeviceCardManager.h"

// 定义设备类型枚举
typedef NS_ENUM(NSInteger, UPDeviceClassType) {
    UPDeviceClassType_First = 0,  // 设备大类
    UPDeviceClassType_Second = 1  // 设备中类
};

@implementation WashProgramStartIntercepter

#pragma mark - UpDeviceCommandIntercepter Protocol

- (BOOL)isMatch:(id<UpDevice>)device {
    if (!device || !device.getInfo) {
        return NO;
    }
    
    NSString *typeId = device.getInfo.typeId;
    if (!typeId || typeId.length == 0) {
        return NO;
    }
    
    return [self isWashingMachineTypeid:typeId];
}

- (void)onCommandSuccess:(id<UpDevice>)device command:(id<UpDeviceCommand>)command {
    if (!device || !command) {
        return;
    }
    
    NSDictionary<NSString *, NSString *> *attributes = command.attributes;
    if (!attributes || attributes.count == 0) {
        return;
    }
    
    // 定义洗衣机程序启动相关的命令key
    NSArray<NSString *> *washProgramKeys = @[
        @"grAdCommonWash",
        @"grAdvancedWash", 
        @"grCardWash",
        @"grCommonDry",
        @"grCommonWash",
        @"grCommonWashDN",
        @"grCommonWashUP",
        @"grDryCloudProg",
        @"grDryerSuperAirWash",
        @"grDryOnline",
        @"grDrySet",
        @"grFPA",
        @"grFreeWash",
        @"grOnlineWash",
        @"grOnlineWashDN",
        @"grOnlineWashUP",
        @"grSuperAirWash",
        @"grSuperAirWashDN",
        @"grSuperAirWashOnline",
        @"grWasherDefSet"
    ];
    
    // 检查命令属性中是否包含洗衣机程序启动相关的key
    for (NSString *key in attributes.allKeys) {
        if ([washProgramKeys containsObject:key]) {
            UPDeviceLogDebug(@"[%s][%d]Wash program start detected for device: %@, command key: %@", 
                           __PRETTY_FUNCTION__, __LINE__, device.getInfo.deviceId, key);
            
            // 在这里可以添加具体的洗衣机程序启动处理逻辑
            [self handleWashProgramStart:device command:command commandKey:key];
            break;
        }
    }
}

#pragma mark - Private Methods

- (BOOL)isWashingMachineTypeid:(NSString *)typeId {
    NSString *hexResult = [UpDeviceCardManager deviceClassNumber:typeId deviceClassType:UPDeviceClassType_First];
    
    // 根据机型编码对应表 4:波轮洗衣机 5:滚筒洗衣机 31:干衣机(此数值为16进制数）
    if ([hexResult isEqualToString:@"4"] || [hexResult isEqualToString:@"5"] || [hexResult isEqualToString:@"31"]) {
        return YES;
    }
    return NO;
}

- (void)handleWashProgramStart:(id<UpDevice>)device command:(id<UpDeviceCommand>)command commandKey:(NSString *)commandKey {
    // 处理洗衣机程序启动的具体逻辑
    // 这里可以根据需要添加具体的业务逻辑，比如：
    // - 记录洗衣机程序启动事件
    // - 发送通知
    // - 更新设备状态
    // - 统计数据等
    
    UPDeviceLogInfo(@"[%s][%d]Handling wash program start - Device: %@, Command: %@, Key: %@", 
                   __PRETTY_FUNCTION__, __LINE__, device.getInfo.deviceId, command.groupName, commandKey);
}

@end
