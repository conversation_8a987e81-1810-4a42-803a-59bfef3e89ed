//
//  UpDeviceBroker.h
//  updevice_api
//
//  Created by <PERSON><PERSON> on 2018/12/18.
//  Copyright © 2018 Haierfc. All rights reserved.
//

#import <Foundation/Foundation.h>
#import "UpDeviceResult.h"
#import "UpDeviceToolkit.h"
#import "UpDeviceToolkitState.h"
#import "UpDeviceToolkitListener.h"
#import "UpDeviceDetectListener.h"
#import "UpDeviceReportListener.h"

/**
 * 设备监听经纪人
 */
@protocol UpDeviceBroker <NSObject>

/**
 * 判断设备工具是否可用
 *
 * @return true - 可用, false - 不可用
 */
- (BOOL)isToolkitReady;

/**
 * 通过协议名称获取设备工具
 *
 * @return 设备工具
 */
- (id<UpDeviceToolkit>)getToolkit;

/**
 * 启动设备工具
 *
 * @finishBlock 可订阅启动结果
 */
- (void)prepareToolkit:(void (^)(UpDeviceResult *result))finishBlock;
/**
 * 停止设备工具
 *
 * @finishBlock 可订阅停止结果
 */
- (void)releaseToolkit:(void (^)(UpDeviceResult *result))finishBlock;
/**
 * 添加设备工具包监听器
 *
 * @param deviceToolkitListener 工具状态监听器
 * @param deviceDetectListener  设备列表监听器
 * @param immediate             true - 立即使用现有数据回调注册的监听器, false - 不立即回调
 */
- (void)attachToolkit:(id<UpDeviceToolkitListener>)deviceToolkitListener detectListener:(id<UpDeviceDetectListener>)deviceDetectListener immediate:(BOOL)immediate finishBlock:(void (^)(UpDeviceResult *result))finishBlock;

/**
 * 移除指定的设备工具包监听器
 *
 * @param deviceToolkitListener 工具状态监听器
 * @param deviceDetectListener  设备列表监听器
 */
- (void)detachToolkit:(id<UpDeviceToolkitListener>)deviceToolkitListener detectListener:(id<UpDeviceDetectListener>)deviceDetectListener finishBlock:(void (^)(UpDeviceResult *result))finishBlock;

/**
 * 添加指定的设备监听器
 *
 * @param deviceId  设备ID
 * @param listener  设备监听器
 * @param immediate true - 立即使用现有数据回调注册的监听器, false - 不立即回调
 */
- (void)attachDevice:(NSString *)deviceId reportListener:(id<UpDeviceReportListener>)listener immediate:(BOOL)immediate finishBlock:(void (^)(UpDeviceResult *result))finishBlock;

/**
 * 移除指定的设备监听器
 *
 * @param deviceId 设备ID
 * @param listener 设备监听器
 */
- (void)detachDevice:(NSString *)deviceId reportListener:(id<UpDeviceReportListener>)listener finishBlock:(void (^)(UpDeviceResult *result))finishBlock;

/**
 * 添加快连设备的设备监听器
 *
 * @param deviceId  设备ID
 * @param listener  设备监听器
 * @param immediate true - 立即使用现有数据回调注册的监听器, false - 不立即回调
 */
- (void)attachWithoutConnectDevice:(NSString *)deviceId reportListener:(id<UpDeviceReportListener>)listener immediate:(BOOL)immediate finishBlock:(void (^)(UpDeviceResult *result))finishBlock;

/**
 * 移除快连的设备监听器
 *
 * @param deviceId 设备ID
 * @param listener 设备监听器
 */
- (void)detachWithoutConnectDevice:(NSString *)deviceId reportListener:(id<UpDeviceReportListener>)listener finishBlock:(void (^)(UpDeviceResult *result))finishBlock;


/**
 * 获取连接网关时的参数列表
 *
 * @return 参数列表
 */
- (NSMutableDictionary *)gatewayParams;

/**
 * 设置连接网关时的参数列表
 *
 * @param params 参数列表
 */
- (void)setGatewayParams:(NSMutableDictionary *)params;
@end
