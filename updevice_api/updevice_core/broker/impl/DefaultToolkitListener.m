//
//  DefaultToolkitListener.m
//  updevice_api
//
//  Created by <PERSON><PERSON> on 2018/12/19.
//  Copyright © 2018 Haierfc. All rights reserved.
//

#import "DefaultToolkitListener.h"
#import "UPDeviceLog.h"

@implementation DefaultToolkitListener

+ (DefaultToolkitListener *)DefaultToolkitListener:(DefaultDeviceBroker *)broker
{
    return [[DefaultToolkitListener alloc] initWithBroker:broker];
}

- (instancetype)initWithSuBroker:(DefaultDeviceBroker *)broker;
{
    if (self = [super initWithBroker:broker]) {
    }
    return self;
}
- (void)onStateChange:(NSString *)protocol state:(UpDeviceToolkitState)state
{
    UPLogDebug(kUPDevicePrefix, @"%s[%d]%@协议的toolkit状态发生变化！state:%ld", __PRETTY_FUNCTION__, __LINE__, protocol, (long)state);
    NSArray *listeners = [self get];
    DefaultDeviceBroker *broker = [self getBroker];
    DefaultNotification *notif = [broker getNotification];
    [notif notifyToolkitStateChange:protocol state:state listeners:listeners];
}

@end
