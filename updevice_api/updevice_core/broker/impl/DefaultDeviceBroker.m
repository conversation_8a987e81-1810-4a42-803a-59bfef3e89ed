//
//  DefaultDeviceBroker.m
//  updevice_api
//
//  Created by <PERSON><PERSON> on 2018/12/18.
//  Copyright © 2018 Haierfc. All rights reserved.
//

#import "DefaultDeviceBroker.h"
#import "UpStringResult.h"
#import "UPDeviceLog.h"
#import "UpDeviceOnlineStatus.h"
#import "UPStorage.h"
#import <upuserdomain/UpUserDomainHolder.h>
#import <MJExtension/MJExtension.h>
#import "DeviceAttribute.h"
#import "uSDKDeviceWrapper.h"

static NSString *const kDeviceCachePrefix = @"device_cache";
static NSString *const kDeviceFamilyId = @"DI-Relation.familyId";
static NSString *const kDeviceCardSortId = @"DI-Product.cardSort";


@interface DefaultDeviceBroker ()

@property (nonatomic, strong) DefaultToolkitListener *toolkitListener;
@property (nonatomic, strong) DefaultDetectListener *detectListener;
@property (nonatomic, strong) DefaultReportListener *reportListener;
@property (nonatomic, strong) DefaultNotification *notification;
@property (nonatomic, strong) id<UpDeviceToolkit> toolkit;
@property (nonatomic, strong) NSMutableDictionary *gatewayParams;

@property (nonatomic, strong) UpDeviceCacheManager *cacheManager;

@end

@implementation DefaultDeviceBroker

+ (DefaultDeviceBroker *)DefaultDeviceBroker:(id<UpDeviceToolkit>)toolkit deviceManager:(id<UpDeviceCenter>)deviceManager
{
    return [[DefaultDeviceBroker alloc] initWithKit:toolkit deviceManager:deviceManager];
}

- (instancetype)initWithKit:(id<UpDeviceToolkit>)toolkit deviceManager:(id<UpDeviceCenter>)deviceManager
{
    if (self = [super init]) {
        if (_toolkit == nil) {
            UPLogError(kUPDevicePrefix, @"%s[%d]入参格式有误!toolkit:%@", __PRETTY_FUNCTION__, __LINE__, toolkit);
        }
        self.toolkit = toolkit;
        self.toolkitListener = [[DefaultToolkitListener alloc] initWithBroker:self];
        self.cacheManager = [[UpDeviceCacheManager alloc] initWith:deviceManager];
        self.detectListener = [[DefaultDetectListener alloc] initWithBroker:self];
        self.reportListener = [[DefaultReportListener alloc] initWithBroker:self];
        self.notification = [[DefaultNotification alloc] init];
        _gatewayParams = [NSMutableDictionary dictionary];
    }
    return self;
}

- (DefaultNotification *)getNotification
{
    return _notification;
}
- (BOOL)isToolkitReady
{
    if ([_toolkit getToolkitState] == STARTED) {
        return YES;
    }
    return NO;
}
- (id<UpDeviceToolkit>)getToolkit
{
    return _toolkit;
}
- (void)prepareToolkit:(void (^)(UpDeviceResult *result))finishBlock
{
    UPLogDebug(kUPDevicePrefix, @"%s[%d]开始Prepare...", __PRETTY_FUNCTION__, __LINE__);
    [_toolkit attachToolkit:_toolkitListener
             detectListener:_detectListener
                finishBlock:^(UpDeviceResult *result) {
                  if ([result isSuccessful]) {
                      UPLogDebug(kUPDevicePrefix, @"%s[%d]Prepare成功！", __PRETTY_FUNCTION__, __LINE__);
                  }
                  else {
                      UPLogError(kUPDevicePrefix, @"%s[%d]Prepare失败！error:%@", __PRETTY_FUNCTION__, __LINE__, result.extraInfo);
                  }
                  finishBlock(result);
                }];
}
- (void)releaseToolkit:(void (^)(UpDeviceResult *result))finishBlock
{
    UPLogDebug(kUPDevicePrefix, @"%s[%d]开始Release...", __PRETTY_FUNCTION__, __LINE__);
    [_toolkit detachToolkit:^(UpDeviceResult *result) {
      if ([result isSuccessful]) {
          UPLogDebug(kUPDevicePrefix, @"%s[%d]Release成功！", __PRETTY_FUNCTION__, __LINE__);
      }
      else {
          UPLogError(kUPDevicePrefix, @"%s[%d]Release失败！error:%@", __PRETTY_FUNCTION__, __LINE__, result.extraInfo);
      }
      finishBlock(result);
    }];
}
- (void)attachToolkit:(id<UpDeviceToolkitListener>)deviceToolkitListener detectListener:(id<UpDeviceDetectListener>)deviceDetectListener immediate:(BOOL)immediate finishBlock:(void (^)(UpDeviceResult *result))finishBlock
{
    UPLogDebug(kUPDevicePrefix, @"%s[%d]开始连接toolkit.", __PRETTY_FUNCTION__, __LINE__);
    NSString *protocol = _toolkit.getSupportProtocol;
    [_toolkitListener add:deviceToolkitListener];
    [_detectListener add:deviceDetectListener];
    NSString *str = [NSString stringWithFormat:@"连接'%@'工具成功", protocol];
    UpDeviceResult *resul = [[UpSuccessResult alloc] initWithExtraData:str];
    NSArray *array = [NSArray arrayWithObject:deviceToolkitListener];
    if (deviceToolkitListener != nil && [resul isSuccessful] && immediate) {
        UpDeviceToolkitState state = [_toolkit getToolkitState];
        [_notification notifyToolkitStateChange:protocol state:state listeners:array];
    }
    UPLogDebug(kUPDevicePrefix, @"%s[%d]toolkit连接成功.", __PRETTY_FUNCTION__, __LINE__);
    finishBlock(resul);
}
- (void)detachToolkit:(id<UpDeviceToolkitListener>)deviceToolkitListener detectListener:(id<UpDeviceDetectListener>)deviceDetectListener finishBlock:(void (^)(UpDeviceResult *result))finishBlock
{
    UPLogDebug(kUPDevicePrefix, @"%s[%d]开始断开toolkit连接.", __PRETTY_FUNCTION__, __LINE__);
    NSString *protocol = _toolkit.getSupportProtocol;
    [_toolkitListener remove:deviceToolkitListener];
    [_detectListener remove:deviceDetectListener];
    NSString *str = [NSString stringWithFormat:@"断开'%@'工具成功", protocol];
    UpDeviceResult *resul = [[UpSuccessResult alloc] initWithExtraData:str];
    UPLogDebug(kUPDevicePrefix, @"%s[%d]toolkit连接已成功断开.", __PRETTY_FUNCTION__, __LINE__);
    if (finishBlock) {
        finishBlock(resul);
    }
}
- (void)attachDevice:(NSString *)deviceId reportListener:(id<UpDeviceReportListener>)listener immediate:(BOOL)immediate finishBlock:(void (^)(UpDeviceResult *result))finishBlock
{
    UPLogDebug(kUPDevicePrefix, @"%s[%d]开始连接设备:%@", __PRETTY_FUNCTION__, __LINE__, deviceId);
    NSString *protocol = _toolkit.getSupportProtocol;
    [_reportListener put:deviceId target:listener];
    NSArray *listeners = [_reportListener get:deviceId];
    NSString *combineKey = [_reportListener combineKey:protocol deviceId:deviceId];
    NSString *str;
    UpDeviceResult *resul;
    if (listeners != nil && [listeners containsObject:listener]) {
        str = [NSString stringWithFormat:@"连接设备'%@'成功", combineKey];
        resul = [[UpSuccessResult alloc] initWithExtraData:str];
    }
    else {
        str = [NSString stringWithFormat:@"连接设备'%@'失败", combineKey];
        resul = [[UpFailureResult alloc] initWithExtraData:str];
    }

    [_toolkit attachDevice:deviceId
            reportListener:_reportListener
               finishBlock:^(UpDeviceResult *result) {
                 if ([result isSuccessful]) {
                     if (![NSThread isMainThread]) {
                         dispatch_async(dispatch_get_main_queue(), ^{
                           [self notifyOnAttach:deviceId listener:listener];
                         });
                     }
                     else {
                         [self notifyOnAttach:deviceId listener:listener];
                     }

                     if (finishBlock) {
                         finishBlock(result);
                     }
                 }
                 else {
                     if (finishBlock) {
                         finishBlock(result);
                     }
                 }
               }];
}

- (void)attachWithoutConnectDevice:(NSString *)deviceId reportListener:(id<UpDeviceReportListener>)listener immediate:(BOOL)immediate finishBlock:(void (^)(UpDeviceResult *result))finishBlock
{
    UPLogDebug(kUPDevicePrefix, @"%s[%d]开始连接设备:%@", __PRETTY_FUNCTION__, __LINE__, deviceId);
    NSString *protocol = _toolkit.getSupportProtocol;
    [_reportListener put:deviceId target:listener];
    NSArray *listeners = [_reportListener get:deviceId];
    NSString *combineKey = [_reportListener combineKey:protocol deviceId:deviceId];
    NSString *str;
    UpDeviceResult *resul;
    if (listeners != nil && [listeners containsObject:listener]) {
        str = [NSString stringWithFormat:@"连接设备'%@'成功", combineKey];
        resul = [[UpSuccessResult alloc] initWithExtraData:str];
    }
    else {
        str = [NSString stringWithFormat:@"连接设备'%@'失败", combineKey];
        resul = [[UpFailureResult alloc] initWithExtraData:str];
    }

    [_toolkit attachDeviceWithoutConnect:deviceId
                          reportListener:_reportListener
                             finishBlock:^(UpDeviceResult *result) {
                               if ([result isSuccessful]) {
                                   [self notifyOnAttach:deviceId listener:listener];
                                   finishBlock(result);
                               }
                               else {
                                   finishBlock(result);
                               }

                             }];
}


- (void)notifyOnAttach:(NSString *)deviceId listener:(id<UpDeviceReportListener>)listener
{
    if (deviceId == nil || listener == nil || ![listener conformsToProtocol:@protocol(UpDeviceReportListener)]) {
        UPLogDebug(kUPDevicePrefix, @"%s[%d] Invalid parameters (deviceId == nil || listener == nil)", __PRETTY_FUNCTION__, __LINE__);
        return;
    }

    id<UpDeviceBaseInfo> deviceBaseInfo = [self getDeviceBaseInfo:deviceId];
    if (deviceBaseInfo == nil || ![deviceBaseInfo conformsToProtocol:@protocol(UpDeviceBaseInfo)]) {
        UPLogDebug(kUPDevicePrefix, @"%s[%d] Failed to read device Info with deviceId :%@", __PRETTY_FUNCTION__, __LINE__, deviceId);
        return;
    }

    uSDKDeviceWrapper *wrapper = [_toolkit getuSDKDeviceWrapper:deviceId];
    if (wrapper == nil) {
        return;
    }

    if (wrapper.attributeList == nil || wrapper.attributeList.count == 0) {
        NSDictionary *deviceCacheDict = [self.cacheManager readDeviceCacheWith:deviceId];
        if ([deviceCacheDict isKindOfClass:[NSDictionary class]]) {
            NSArray<id<UpDeviceAttribute>> *attributeList = deviceCacheDict[kDeviceInfoKey_attributes];
            if ([attributeList isKindOfClass:[NSArray class]]) {
                wrapper.attributeList = attributeList;
            }
        }
        else {
            UPLogDebug(kUPDevicePrefix, @"%s[%d] Failed to read device cache with deviceId :%@", __PRETTY_FUNCTION__, __LINE__, deviceId);
        }
    }

    NSArray *listeners = [NSArray arrayWithObject:listener];
    [_notification notifyuSDKDeviceWrapperDataChange:deviceBaseInfo wrapperData:wrapper listeners:listeners];
}
- (void)setGatewayParams:(NSMutableDictionary *)params
{
    //    [self.gatewayParams removeAllObjects];
    if (params != nil) {
        [self.gatewayParams addEntriesFromDictionary:params];
    }
}
- (void)detachDevice:(NSString *)deviceId reportListener:(id<UpDeviceReportListener>)listener finishBlock:(void (^)(UpDeviceResult *))finishBlock
{
    UPLogDebug(kUPDevicePrefix, @"%s[%d]开始断开设备连接:%@", __PRETTY_FUNCTION__, __LINE__, deviceId);
    [_reportListener remove:deviceId];
    __weak typeof(self) weakSelf = self;
    [_toolkit detachDevice:deviceId
               finishBlock:^(UpDeviceResult *result) {
                 if ([result isSuccessful] && [weakSelf.reportListener isEmpty:deviceId]) {
                     UPLogDebug(kUPDevicePrefix, @"%s[%d]设备连接已断开:%@", __PRETTY_FUNCTION__, __LINE__, deviceId);
                     finishBlock(result);
                 }
                 else {
                     if ([result isSuccessful]) {
                         UPLogDebug(kUPDevicePrefix, @"%s[%d]设备连接已断开:%@", __PRETTY_FUNCTION__, __LINE__, deviceId);
                     }
                     else {
                         UPLogError(kUPDevicePrefix, @"%s[%d]设备(%@)连接断开失败!error:%@", __PRETTY_FUNCTION__, __LINE__, deviceId, result.extraInfo);
                     }
                     finishBlock(result);
                 }
               }];
}

- (void)detachWithoutConnectDevice:(NSString *)deviceId reportListener:(id<UpDeviceReportListener>)listener finishBlock:(void (^)(UpDeviceResult *result))finishBlock
{
    UPLogDebug(kUPDevicePrefix, @"%s[%d]开始断开设备连接:%@", __PRETTY_FUNCTION__, __LINE__, deviceId);
    [_reportListener remove:deviceId];
    __weak typeof(self) weakSelf = self;
    [_toolkit detachDeviceWithoutConnect:deviceId
                             finishBlock:^(UpDeviceResult *result) {
                               if ([result isSuccessful] && [weakSelf.reportListener isEmpty:deviceId]) {
                                   UPLogDebug(kUPDevicePrefix, @"%s[%d]设备连接已断开:%@", __PRETTY_FUNCTION__, __LINE__, deviceId);
                                   finishBlock(result);
                               }
                               else {
                                   if ([result isSuccessful]) {
                                       UPLogDebug(kUPDevicePrefix, @"%s[%d]设备连接已断开:%@", __PRETTY_FUNCTION__, __LINE__, deviceId);
                                   }
                                   else {
                                       UPLogError(kUPDevicePrefix, @"%s[%d]设备(%@)连接断开失败!error:%@", __PRETTY_FUNCTION__, __LINE__, deviceId, result.extraInfo);
                                   }
                                   finishBlock(result);
                               }

                             }];
}

- (void)detachDevice:(NSString *)deviceId listener:(id<UpDeviceReportListener>)listener immediate:(BOOL)immediate finishBlock:(void (^)(UpDeviceResult *result))finishBlock
{
    UPLogDebug(kUPDevicePrefix, @"%s[%d]开始断开设备连接:%@", __PRETTY_FUNCTION__, __LINE__, deviceId);
    [_reportListener remove:deviceId];
    __weak typeof(self) weakSelf = self;

    [_toolkit detachDevice:deviceId
               finishBlock:^(UpDeviceResult *result) {
                 if ([result isSuccessful] && [weakSelf.reportListener isEmpty:deviceId]) {
                     UPLogDebug(kUPDevicePrefix, @"%s[%d]设备连接已断开:%@", __PRETTY_FUNCTION__, __LINE__, deviceId);
                     finishBlock(result);
                 }
                 else {
                     if ([result isSuccessful]) {
                         UPLogDebug(kUPDevicePrefix, @"%s[%d]设备连接已断开:%@", __PRETTY_FUNCTION__, __LINE__, deviceId);
                     }
                     else {
                         UPLogError(kUPDevicePrefix, @"%s[%d]设备(%@)连接断开失败!error:%@", __PRETTY_FUNCTION__, __LINE__, deviceId, result.extraInfo);
                     }
                     finishBlock(result);
                 }
               }];
}
- (id<UpDeviceBaseInfo>)getDeviceBaseInfo:(NSString *)deviceId
{
    __block id<UpDeviceBaseInfo> deviceBaseInf = nil;
    dispatch_group_t group = dispatch_group_create();
    dispatch_group_enter(group);
    [_toolkit getDeviceBaseInfo:deviceId
                    finishBlock:^(UpDeviceResult *result) {

                      if (result.isSuccessful) {
                          deviceBaseInf = result.extraData;
                      }
                      else {
                          UPLogError(kUPDevicePrefix, @"%s[%d]设备(%@)基本信息获取失败!error:%@", __PRETTY_FUNCTION__, __LINE__, deviceId, result.extraInfo);
                      }
                      dispatch_group_leave(group);
                    }];
    dispatch_group_wait(group, DISPATCH_TIME_FOREVER);
    return deviceBaseInf;
}
- (UpDeviceConnection)getDeviceConnection:(NSString *)deviceId
{
    __block UpDeviceConnection connection = UpDeviceConnection_OFFLINE;
    [_toolkit getDeviceConnection:deviceId
                      finishBlock:^(UpDeviceResult *result) {
                        if (result.isSuccessful) {
                            connection = [result.extraData integerValue];
                        }
                        else {
                            UPLogError(kUPDevicePrefix, @"%s[%d]设备(%@)连接状态获取失败!error:%@", __PRETTY_FUNCTION__, __LINE__, deviceId, result.extraInfo);
                        }
                      }];
    return connection;
}

- (UpDeviceControlState)getDeviceControlState:(NSString *)deviceId
{
    __block UpDeviceControlState state = UpDeviceControlState_None;
    [_toolkit getDeviceControlState:deviceId
                        finishBlock:^(UpDeviceResult *result) {
                          if (result.isSuccessful) {
                              state = [result.extraData integerValue];
                          }
                          else {
                              UPLogError(kUPDevicePrefix, @"%s[%d]device(%@)get ControlState fail!error:%@", __PRETTY_FUNCTION__, __LINE__, deviceId, result.extraInfo);
                          }
                        }];

    return state;
}

- (NSInteger)getDeviceFaultInformationCode:(NSString *)deviceId
{
    __block NSInteger code;
    [_toolkit getDeviceFaultInformationCode:deviceId
                                finishBlock:^(UpDeviceResult *result) {
                                  if (result.isSuccessful) {
                                      code = [result.extraData integerValue];
                                  }
                                  else {
                                      UPLogError(kUPDevicePrefix, @"%s[%d]device(%@)get FaultInformationCode fail!error:%@", __PRETTY_FUNCTION__, __LINE__, deviceId, result.extraInfo);
                                  }
                                }];
    return code;
}

- (UpDeviceRealOnline)getDeviceRealOnlineStatus:(NSString *)deviceId
{
    __block UpDeviceRealOnline realOnlineStatus = UpDeviceRealOnline_ONLINE;
    [_toolkit getDeviceOnlineStatus:deviceId
                        finishBlock:^(UpDeviceResult *result) {
                          if (result.isSuccessful) {
                              realOnlineStatus = [result.extraData integerValue];
                          }
                          else {
                              UPLogError(kUPDevicePrefix, @"%s[%d]device(%@)realOnlineStatus fail!error:%@", __PRETTY_FUNCTION__, __LINE__, deviceId, result.extraInfo);
                          }
                        }];
    return realOnlineStatus;
}

- (UpDeviceRealOnlineV2)getDeviceRealOnlineStateV2:(NSString *)deviceId
{
    __block UpDeviceRealOnlineV2 realOnlineStateV2 = UpDeviceRealOnlineV2_ONLINE_READY;

    [_toolkit getDeviceOnlineStateV2:deviceId
                         finishBlock:^(UpDeviceResult *result) {
                           if (result.isSuccessful) {
                               realOnlineStateV2 = [result.extraData integerValue];
                           }
                           else {
                               UPLogError(kUPDevicePrefix, @"%s[%d]device(%@)realOnlineStateV2 fail! error:%@", __PRETTY_FUNCTION__, __LINE__, deviceId, result.extraInfo);
                           }
                         }];
    return realOnlineStateV2;
}

- (UpDeviceSleepState)getDeviceSleepState:(NSString *)deviceId
{
    __block UpDeviceSleepState sleepState;
    [_toolkit getDeviceSleepState:deviceId
                      finishBlock:^(UpDeviceResult *result) {
                        if (result.isSuccessful) {
                            sleepState = [result.extraData integerValue];
                        }
                        else {
                            UPLogError(kUPDevicePrefix, @"%s[%d]device(%@)sleepState fail!error:%@", __PRETTY_FUNCTION__, __LINE__, deviceId, result.extraInfo);
                        }
                      }];
    return sleepState;
}

- (UpDeviceNetworkLevel)getDeviceNetworkLevel:(NSString *)deviceId
{
    __block UpDeviceNetworkLevel networkLevel;
    [_toolkit getDeviceNetWorkLevel:deviceId
                        finishBlock:^(UpDeviceResult *result) {
                          if (result.isSuccessful) {
                              networkLevel = [result.extraData integerValue];
                          }
                          else {
                              UPLogError(kUPDevicePrefix, @"%s[%d]device(%@)networkLevel fail!error:%@", __PRETTY_FUNCTION__, __LINE__, deviceId, result.extraInfo);
                          }

                        }];
    return networkLevel;
}

- (UpDeviceConnection)getDeviceBleState:(NSString *)deviceId
{
    __block UpDeviceConnection bleState;
    [_toolkit getDeviceBleState:deviceId
                    finishBlock:^(UpDeviceResult *result) {
                      if (result.isSuccessful) {
                          UPLogDebug(kUPDevicePrefix, @"%s[%d]device(%@)BrokerGetBleState success!state:%@", __PRETTY_FUNCTION__, __LINE__, deviceId, result.extraData)
                              bleState = [result.extraData integerValue];
                      }
                      else {
                          UPLogError(kUPDevicePrefix, @"%s[%d]device(%@)DeviceBleState fail!error:%@", __PRETTY_FUNCTION__, __LINE__, deviceId, result.extraInfo);
                      }
                    }];
    return bleState;
}


- (NSArray<id<UpDeviceBaseInfo>> *)getSubDevBaseInfoList:(NSString *)deviceId
{
    __block NSArray *list;
    dispatch_group_t group = dispatch_group_create();
    dispatch_group_enter(group);
    [_toolkit getSubDevBaseInfoList:deviceId
                        finishBlock:^(UpDeviceResult *result) {
                          if (result.isSuccessful) {
                              list = result.extraData;
                          }
                          else {
                              UPLogError(kUPDevicePrefix, @"%s[%d]设备(%@)子设备基本信息获取失败!error:%@", __PRETTY_FUNCTION__, __LINE__, deviceId, result.extraInfo);
                          }
                          dispatch_group_leave(group);
                        }];
    dispatch_group_wait(group, DISPATCH_TIME_FOREVER);
    return list;
}

- (NSArray *)getDeviceAttributeList:(NSString *)deviceId
{
    __block NSArray *list;
    [_toolkit getDeviceAttributeList:deviceId
                         finishBlock:^(UpDeviceResult *result) {
                           if (result.isSuccessful) {
                               list = result.extraData;
                           }
                           else {
                               UPLogError(kUPDevicePrefix, @"%s[%d]设备(%@)属性列表获取失败!error:%@", __PRETTY_FUNCTION__, __LINE__, deviceId, result.extraInfo);
                           }
                         }];
    return list;
}
- (NSArray *)getDeviceCautionList:(NSString *)deviceId
{
    __block NSArray *list;
    [_toolkit getDeviceCautionList:deviceId
                       finishBlock:^(UpDeviceResult *result) {
                         if (result.isSuccessful) {
                             list = result.extraData;
                         }
                         else {
                             UPLogError(kUPDevicePrefix, @"%s[%d]设备(%@)报警信息列表获取失败!error:%@", __PRETTY_FUNCTION__, __LINE__, deviceId, result.extraInfo);
                         }
                       }];
    return list;
}

- (UpDeviceConnection)getDeviceWifiLocalState:(NSString *)deviceId
{
    __block UpDeviceConnection localState = UpDeviceConnection_OFFLINE;
    [_toolkit getDeviceWifiLocalState:deviceId
                          finishBlock:^(UpDeviceResult *result) {
                            if (result.isSuccessful) {
                                localState = [result.extraData integerValue];
                            }
                            else {
                                UPLogError(kUPDevicePrefix, @"%s[%d]device(%@)locatState fail! error:%@", __PRETTY_FUNCTION__, __LINE__, deviceId, result.extraInfo);
                            }

                          }];
    return localState;
}

- (UpDeviceOfflineCause)getDeviceOfflineCause:(NSString *)deviceId
{
    __block UpDeviceOfflineCause offlineCause = UpDeviceOfflineCause_None;
    [_toolkit getDeviceOfflineCause:deviceId
                        finishBlock:^(UpDeviceResult *result) {
                          if (result.isSuccessful) {
                              offlineCause = [result.extraData integerValue];
                          }
                          else {
                              UPLogError(kUPDevicePrefix, @"%s[%d]device(%@)get offlineCause fail! error:%@", __PRETTY_FUNCTION__, __LINE__, deviceId, result.extraInfo);
                          }
                        }];
    return offlineCause;
}

- (NSInteger)getDeviceOfflineDays:(NSString *)deviceId
{
    __block NSInteger offlineDays = 0;
    [_toolkit getDeviceOfflineDays:deviceId
                       finishBlock:^(UpDeviceResult *result) {
                         if (result.isSuccessful) {
                             offlineDays = [result.extraData integerValue];
                         }
                         else {
                             UPLogError(kUPDevicePrefix, @"%s[%d]device(%@)get deviceOfflineDays fail! error:%@", __PRETTY_FUNCTION__, __LINE__, deviceId, result.extraInfo);
                         }
                       }];
    return offlineDays;
}

- (UpDeviceOnlyConfigState)getDeviceOnlyConfigState:(NSString *)deviceId
{
    __block UpDeviceOnlyConfigState onlyConfigState = UpDeviceOnlyConfigStateUnConfigurable;
    [_toolkit getDeviceOnlyConfigState:deviceId
                           finishBlock:^(UpDeviceResult *result) {
                             if (result.isSuccessful) {
                                 onlyConfigState = [result.extraData integerValue];
                             }
                             else {
                                 UPLogError(kUPDevicePrefix, @"%s[%d]device(%@)get deviceOnlyConfigState fail! error:%@", __PRETTY_FUNCTION__, __LINE__, deviceId, result.extraInfo);
                             }
                           }];
    return onlyConfigState;
}
@end
