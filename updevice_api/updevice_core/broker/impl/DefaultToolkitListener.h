//
//  DefaultToolkitListener.h
//  updevice_api
//
//  Created by <PERSON><PERSON> on 2018/12/19.
//  Copyright © 2018 Haierfc. All rights reserved.
//

#import <Foundation/Foundation.h>
#import "UpDeviceBaseInfo.h"
#import "UpDeviceToolkitListener.h"
#import "DefaultBrokerHolder.h"
#import "DefaultDeviceBroker.h"


@interface DefaultToolkitListener : DefaultBrokerHolder <UpDeviceToolkitListener>

+ (DefaultToolkitListener *)DefaultToolkitListener:(DefaultDeviceBroker *)broker;
- (instancetype)initWithSuBroker:(DefaultDeviceBroker *)broker;
@end
