//
//  DefaultReportListener.m
//  updevice_api
//
//  Created by <PERSON><PERSON> on 2018/12/19.
//  Copyright © 2018 Haierfc. All rights reserved.
//

#import "DefaultReportListener.h"
#import "UPDeviceLog.h"

@implementation DefaultReportListener

+ (DefaultReportListener *)DefaultReportListener:(DefaultDeviceBroker *)broker
{
    return [[DefaultReportListener alloc] initWithBroker:broker];
}

- (instancetype)initWithSuBroker:(DefaultDeviceBroker *)broker;
{
    if (self = [super initWithBroker:broker]) {
    }
    return self;
}

- (void)onDeviceInfoChange:(id<UpDeviceBaseInfo>)deviceBaseInfo
{
    if ([deviceBaseInfo conformsToProtocol:@protocol(UpDeviceBaseInfo)]) {
    }
    else {
        UPLogError(kUPDevicePrefix, @"%s[%d]上报的设备信息接口实现对象格式有误！Object:%@", __PRETTY_FUNCTION__, __LINE__, deviceBaseInfo);
    }
    NSArray *listeners = [self get:deviceBaseInfo.deviceId];
    DefaultNotification *notif = [self notificati:deviceBaseInfo];
    [notif notifyDeviceInfoChange:deviceBaseInfo listeners:listeners];
}

- (DefaultNotification *)notificati:(id<UpDeviceBaseInfo>)deviceBaseInfo
{
    DefaultDeviceBroker *broker = [self getBroker];
    DefaultNotification *notif = [broker getNotification];
    return notif;
}

- (void)onConnectionChange:(id<UpDeviceBaseInfo>)deviceBaseInfo connection:(UpDeviceConnection)connection
{
    if ([deviceBaseInfo conformsToProtocol:@protocol(UpDeviceBaseInfo)]) {
    }
    else {
        UPLogError(kUPDevicePrefix, @"%s[%d]上报的设备信息接口实现对象格式有误！Object:%@", __PRETTY_FUNCTION__, __LINE__, deviceBaseInfo);
    }
    NSArray *listeners = [self get:deviceBaseInfo.deviceId];
    DefaultNotification *notif = [self notificati:deviceBaseInfo];
    [notif notifyConnectionChange:deviceBaseInfo connection:connection listeners:listeners];
}

- (void)onControlStateChange:(id<UpDeviceBaseInfo>)deviceBaseInfo controlState:(UpDeviceControlState)state
{
    NSArray *listeners = [self get:deviceBaseInfo.deviceId];
    DefaultNotification *notif = [self notificati:deviceBaseInfo];
    [notif notifyControlStateChange:deviceBaseInfo state:state listeners:listeners];
}

- (void)onFaultInformationCodeChange:(id<UpDeviceBaseInfo>)deviceBaseInfo code:(NSInteger)code
{
    NSArray *listeners = [self get:deviceBaseInfo.deviceId];
    DefaultNotification *notif = [self notificati:deviceBaseInfo];
    [notif notifyFaultInformationCodeChange:deviceBaseInfo code:code listeners:listeners];
}

- (void)onRealOnlineChange:(id<UpDeviceBaseInfo>)deviceBaseInfo status:(UpDeviceRealOnline)status
{
    NSArray *listeners = [self get:deviceBaseInfo.deviceId];
    DefaultNotification *notif = [self notificati:deviceBaseInfo];
    [notif notifyRealOnlineChange:deviceBaseInfo status:status listeners:listeners];
}

- (void)onRealOnlineChangeV2:(id<UpDeviceBaseInfo>)deviceBaseInfo status:(UpDeviceRealOnlineV2)status
{
    NSArray *listeners = [self get:deviceBaseInfo.deviceId];
    DefaultNotification *notif = [self notificati:deviceBaseInfo];
    [notif notifyRealOnlineChangeV2:deviceBaseInfo status:status listeners:listeners];
}

- (void)onAttributesChange:(id<UpDeviceBaseInfo>)deviceBaseInfo attributeList:(NSArray<id<UpDeviceAttribute>> *)attributeList
{
    if ([deviceBaseInfo conformsToProtocol:@protocol(UpDeviceBaseInfo)]) {
    }
    else {
        UPLogError(kUPDevicePrefix, @"%s[%d]上报的设备信息接口实现对象格式有误！Object:%@", __PRETTY_FUNCTION__, __LINE__, deviceBaseInfo);
    }
    NSArray *listeners = [self get:deviceBaseInfo.deviceId];
    DefaultNotification *notif = [self notificati:deviceBaseInfo];
    [notif notifyAttributesChange:deviceBaseInfo list:attributeList listeners:listeners];
}

- (void)onDeviceUpdateBoardFOTAStatus:(id<UpDeviceBaseInfo>)deviceBaseInfo FOTAStatusInfo:(id<UpDeviceFOTAStatusInfo>)statusInfo
{
    if ([deviceBaseInfo conformsToProtocol:@protocol(UpDeviceBaseInfo)]) {
    }
    else {
        UPLogError(kUPDevicePrefix, @"%s[%d]上报的设备信息接口实现对象格式有误！Object:%@", __PRETTY_FUNCTION__, __LINE__, deviceBaseInfo);
    }
    NSArray *listeners = [self get:deviceBaseInfo.deviceId];
    DefaultNotification *notif = [self notificati:deviceBaseInfo];
    [notif notifyUpdateBoardFOTAStatus:deviceBaseInfo FOTAStatusInfo:statusInfo listeners:listeners];
}

- (void)onDeviceReceiveBLEHistoryData:(id<UpDeviceBaseInfo>)deviceBaseInfo BLEHistoryInfo:(id<UpDeviceBLEHistoryInfo>)historyInfo
{
    if ([deviceBaseInfo conformsToProtocol:@protocol(UpDeviceBaseInfo)]) {
    }
    else {
        UPLogError(kUPDevicePrefix, @"%s[%d]上报的设备信息接口实现对象格式有误！Object:%@", __PRETTY_FUNCTION__, __LINE__, deviceBaseInfo);
    }
    NSArray *listeners = [self get:deviceBaseInfo.deviceId];
    DefaultNotification *notif = [self notificati:deviceBaseInfo];
    [notif notifyBLEHistoryData:deviceBaseInfo BLEHistoryInfo:historyInfo listeners:listeners];
}

- (void)onDeviceReceiveBLERealTimeData:(id<UpDeviceBaseInfo>)deviceBaseInfo data:(NSData *)data
{
    if ([deviceBaseInfo conformsToProtocol:@protocol(UpDeviceBaseInfo)]) {
    }
    else {
        UPLogError(kUPDevicePrefix, @"%s[%d]上报的设备信息接口实现对象格式有误！Object:%@", __PRETTY_FUNCTION__, __LINE__, deviceBaseInfo);
    }
    NSArray *listeners = [self get:deviceBaseInfo.deviceId];
    DefaultNotification *notif = [self notificati:deviceBaseInfo];
    [notif notifyBLERealTimeData:deviceBaseInfo BLERealTimeData:data listeners:listeners];
}

- (void)onDeviceCaution:(id<UpDeviceBaseInfo>)deviceBaseInfo cautionList:(NSArray<id<UpDeviceCaution>> *)cautionList
{
    if ([deviceBaseInfo conformsToProtocol:@protocol(UpDeviceBaseInfo)]) {
    }
    else {
        UPLogError(kUPDevicePrefix, @"%s[%d]上报的设备信息接口实现对象格式有误！Object:%@", __PRETTY_FUNCTION__, __LINE__, deviceBaseInfo);
    }
    NSArray *listeners = [self get:deviceBaseInfo.deviceId];
    DefaultNotification *notif = [self notificati:deviceBaseInfo];
    [notif notifyDeviceCaution:deviceBaseInfo list:cautionList listeners:listeners];
}

- (void)onSubDevListChange:(id<UpDeviceBaseInfo>)deviceBaseInfo subDevInfoList:(NSArray<id<UpDeviceBaseInfo>> *)subDevInfoList
{
    if ([deviceBaseInfo conformsToProtocol:@protocol(UpDeviceBaseInfo)]) {
    }
    else {
        UPLogError(kUPDevicePrefix, @"%s[%d]上报的设备信息接口实现对象格式有误！Object:%@", __PRETTY_FUNCTION__, __LINE__, deviceBaseInfo);
    }
    NSArray *listeners = [self get:deviceBaseInfo.deviceId];
    DefaultNotification *notif = [self notificati:deviceBaseInfo];
    [notif notifySubDevListChange:deviceBaseInfo list:subDevInfoList listeners:listeners];
}
- (NSString *)combineKey:(NSString *)protocol deviceId:(NSString *)deviceId
{
    if (!protocol) {
        return nil;
    }
    NSString *key = protocol;
    if (deviceId) {
        key = [NSString stringWithFormat:@"%@->%@", key, deviceId];
    }
    return key;
}
- (void)onDeviceReceive:(id<UpDeviceBaseInfo>)deviceBaseInfo name:(NSString *)name data:(id)data
{
    if ([deviceBaseInfo conformsToProtocol:@protocol(UpDeviceBaseInfo)]) {
    }
    else {
        UPLogError(kUPDevicePrefix, @"%s[%d]上报的设备信息接口实现对象格式有误！Object:%@", __PRETTY_FUNCTION__, __LINE__, deviceBaseInfo);
    }
    NSString *deviceId = [deviceBaseInfo deviceId];
    NSMutableArray *listeners = (NSMutableArray *)[self get:deviceId];
    DefaultDeviceBroker *broker = [self getBroker];
    DefaultNotification *notif = [broker getNotification];
    [notif notifyDeviceReceive:deviceBaseInfo name:name data:data listeners:listeners];
}

- (void)onSleepStateChange:(id<UpDeviceBaseInfo>)deviceBaseInfo state:(UpDeviceSleepState)state
{
    if ([deviceBaseInfo conformsToProtocol:@protocol(UpDeviceBaseInfo)]) {
    }
    else {
        UPLogError(kUPDevicePrefix, @"%s[%d]上报的设备信息接口实现对象格式有误！Object:%@", __PRETTY_FUNCTION__, __LINE__, deviceBaseInfo);
    }
    NSString *deviceId = [deviceBaseInfo deviceId];
    NSMutableArray *listeners = (NSMutableArray *)[self get:deviceId];
    DefaultDeviceBroker *broker = [self getBroker];
    DefaultNotification *notif = [broker getNotification];
    [notif notifySleepStateChange:deviceBaseInfo state:state listeners:listeners];
}

- (void)onNetworkLevelChange:(id<UpDeviceBaseInfo>)deviceBaseInfo level:(UpDeviceNetworkLevel)level
{
    if ([deviceBaseInfo conformsToProtocol:@protocol(UpDeviceBaseInfo)]) {
    }
    else {
        UPLogError(kUPDevicePrefix, @"%s[%d]上报的设备信息接口实现对象格式有误！Object:%@", __PRETTY_FUNCTION__, __LINE__, deviceBaseInfo);
    }
    NSString *deviceId = [deviceBaseInfo deviceId];
    NSMutableArray *listeners = (NSMutableArray *)[self get:deviceId];
    DefaultDeviceBroker *broker = [self getBroker];
    DefaultNotification *notif = [broker getNotification];
    [notif notifyNetworkLevelChange:deviceBaseInfo level:level listeners:listeners];
}

- (void)onQCConnectTimeout:(id<UpDeviceBaseInfo>)deviceBaseInfo timeoutType:(UpDeviceQCConnectTimeoutType)timeoutType
{
    if ([deviceBaseInfo conformsToProtocol:@protocol(UpDeviceBaseInfo)]) {
    }
    else {
        UPLogError(kUPDevicePrefix, @"%s[%d]上报的设备信息接口实现对象格式有误！Object:%@", __PRETTY_FUNCTION__, __LINE__, deviceBaseInfo);
    }
    NSString *deviceId = [deviceBaseInfo deviceId];
    NSMutableArray *listeners = (NSMutableArray *)[self get:deviceId];
    DefaultDeviceBroker *broker = [self getBroker];
    DefaultNotification *notif = [broker getNotification];
    [notif notifyQCConnectTimeoutType:deviceBaseInfo timeoutType:timeoutType listeners:listeners];
}

- (void)onBleStateChange:(id<UpDeviceBaseInfo>)deviceBaseInfo state:(UpDeviceConnection)state
{
    if ([deviceBaseInfo conformsToProtocol:@protocol(UpDeviceBaseInfo)]) {
    }
    else {
        UPLogError(kUPDevicePrefix, @"%s[%d]上报的设备信息接口实现对象格式有误！Object:%@", __PRETTY_FUNCTION__, __LINE__, deviceBaseInfo);
    }
    NSString *deviceId = [deviceBaseInfo deviceId];
    if (![deviceId isKindOfClass:[NSString class]] || deviceId.length == 0) {
        UPLogError(kUPDevicePrefix, @"%s[%d]onBleStateChange deviceId is null", __PRETTY_FUNCTION__, __LINE__);
        return;
    }
    NSMutableArray *listeners = (NSMutableArray *)[self get:deviceId];
    if (!listeners || listeners.count == 0) {
        UPLogError(kUPDevicePrefix, @"%s[%d]onBleStateChange listeners is null", __PRETTY_FUNCTION__, __LINE__);
        return;
    }
    DefaultDeviceBroker *broker = [self getBroker];
    if (!broker) {
        UPLogError(kUPDevicePrefix, @"%s[%d]onBleStateChange broker is null", __PRETTY_FUNCTION__, __LINE__);
        return;
    }
    DefaultNotification *notif = [broker getNotification];
    if (!notif) {
        UPLogError(kUPDevicePrefix, @"%s[%d]onBleStateChange notif is null", __PRETTY_FUNCTION__, __LINE__);
        return;
    }
    [notif notifyBleStateChange:deviceBaseInfo state:state listeners:listeners];
}

- (void)onOfflineCauseChange:(id<UpDeviceBaseInfo>)deviceBaseInfo offlineCause:(UpDeviceOfflineCause)offlineCause
{
    if ([deviceBaseInfo conformsToProtocol:@protocol(UpDeviceBaseInfo)]) {
    }
    else {
        UPLogError(kUPDevicePrefix, @"%s[%d]The format of the device  is incorrect！Object:%@", __PRETTY_FUNCTION__, __LINE__, deviceBaseInfo);
    }
    NSString *deviceId = [deviceBaseInfo deviceId];
    if (![deviceId isKindOfClass:[NSString class]] || deviceId.length == 0) {
        UPLogError(kUPDevicePrefix, @"%s[%d]onOfflineCauseChange deviceId is null", __PRETTY_FUNCTION__, __LINE__);
        return;
    }
    NSArray *listeners = (NSArray *)[self get:deviceId];
    if (![listeners isKindOfClass:NSArray.class] || listeners.count == 0) {
        UPLogError(kUPDevicePrefix, @"%s[%d]onOfflineCauseChange listeners is null", __PRETTY_FUNCTION__, __LINE__);
        return;
    }
    DefaultDeviceBroker *broker = [self getBroker];
    if (!broker) {
        UPLogError(kUPDevicePrefix, @"%s[%d]onOfflineCauseChange broker is null", __PRETTY_FUNCTION__, __LINE__);
        return;
    }
    DefaultNotification *notif = [broker getNotification];
    if (!notif) {
        UPLogError(kUPDevicePrefix, @"%s[%d]onOfflineCauseChange notif is null", __PRETTY_FUNCTION__, __LINE__);
        return;
    }
    [notif notifyOfflineCauseChange:deviceBaseInfo offlineCause:offlineCause listeners:listeners];
}

- (void)onOfflineDaysChange:(id<UpDeviceBaseInfo>)deviceBaseInfo offlineDays:(NSInteger)offlineDays
{
    if ([deviceBaseInfo conformsToProtocol:@protocol(UpDeviceBaseInfo)]) {
    }
    else {
        UPLogError(kUPDevicePrefix, @"%s[%d]The format of the device  is incorrect！Object:%@", __PRETTY_FUNCTION__, __LINE__, deviceBaseInfo);
    }
    NSString *deviceId = [deviceBaseInfo deviceId];
    if (![deviceId isKindOfClass:[NSString class]] || deviceId.length == 0) {
        UPLogError(kUPDevicePrefix, @"%s[%d]onOfflineDaysChange deviceId is null", __PRETTY_FUNCTION__, __LINE__);
        return;
    }
    NSArray *listeners = (NSArray *)[self get:deviceId];
    if (![listeners isKindOfClass:NSArray.class] || listeners.count == 0) {
        UPLogError(kUPDevicePrefix, @"%s[%d]onOfflineDaysChange listeners is null", __PRETTY_FUNCTION__, __LINE__);
        return;
    }
    DefaultDeviceBroker *broker = [self getBroker];
    if (!broker) {
        UPLogError(kUPDevicePrefix, @"%s[%d]onOfflineDaysChange broker is null", __PRETTY_FUNCTION__, __LINE__);
        return;
    }
    DefaultNotification *notif = [broker getNotification];
    if (!notif) {
        UPLogError(kUPDevicePrefix, @"%s[%d]onOfflineDaysChange notif is null", __PRETTY_FUNCTION__, __LINE__);
        return;
    }
    [notif notifyOfflineDaysChange:deviceBaseInfo offlineDays:offlineDays listeners:listeners];
}

- (void)onOnlyConfigStateChange:(id<UpDeviceBaseInfo>)deviceBaseInfo onlyConfigState:(UpDeviceOnlyConfigState)onlyConfigState
{
    if ([deviceBaseInfo conformsToProtocol:@protocol(UpDeviceBaseInfo)]) {
    }
    else {
        UPLogError(kUPDevicePrefix, @"%s[%d]The format of the device  is incorrect！Object:%@", __PRETTY_FUNCTION__, __LINE__, deviceBaseInfo);
    }
    NSString *deviceId = [deviceBaseInfo deviceId];
    if (![deviceId isKindOfClass:[NSString class]] || deviceId.length == 0) {
        UPLogError(kUPDevicePrefix, @"%s[%d]onOnlyConfigStateChange deviceId is null", __PRETTY_FUNCTION__, __LINE__);
        return;
    }
    NSArray *listeners = (NSArray *)[self get:deviceId];
    if (![listeners isKindOfClass:NSArray.class] || listeners.count == 0) {
        UPLogError(kUPDevicePrefix, @"%s[%d]onOnlyConfigStateChange listeners is null", __PRETTY_FUNCTION__, __LINE__);
        return;
    }
    DefaultDeviceBroker *broker = [self getBroker];
    if (!broker) {
        UPLogError(kUPDevicePrefix, @"%s[%d]onOnlyConfigStateChange broker is null", __PRETTY_FUNCTION__, __LINE__);
        return;
    }
    DefaultNotification *notif = [broker getNotification];
    if (!notif) {
        UPLogError(kUPDevicePrefix, @"%s[%d]onOnlyConfigStateChange notif is null", __PRETTY_FUNCTION__, __LINE__);
        return;
    }
    [notif notifyOnlyConfigStateChange:deviceBaseInfo onlyConfigState:onlyConfigState listeners:listeners];
}

@end
