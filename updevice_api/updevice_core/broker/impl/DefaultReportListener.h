//
//  DefaultReportListener.h
//  updevice_api
//
//  Created by <PERSON><PERSON> on 2018/12/19.
//  Copyright © 2018 Haierfc. All rights reserved.
//

#import <Foundation/Foundation.h>
#import "UpDeviceBaseInfo.h"
#import "UpDeviceReportListener.h"
#import "DefaultBrokerHolder.h"
#import "DefaultDeviceBroker.h"

@interface DefaultReportListener : DefaultBrokerHolder <UpDeviceReportListener>

+ (DefaultReportListener *)DefaultReportListener:(DefaultDeviceBroker *)broker;
- (instancetype)initWithSuBroker:(DefaultDeviceBroker *)broker;
- (NSString *)combineKey:(NSString *)protocol deviceId:(NSString *)deviceId;

@end
