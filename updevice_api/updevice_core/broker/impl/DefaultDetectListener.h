//
//  DefaultDetectListener.h
//  updevice_api
//
//  Created by <PERSON><PERSON> on 2018/12/19.
//  Copyright © 2018 Haierfc. All rights reserved.
//

#import <Foundation/Foundation.h>
#import "UpDeviceBaseInfo.h"
#import "UpDeviceDetectListener.h"
#import "DefaultBrokerHolder.h"
#import "DefaultDeviceBroker.h"

@class DefaultDeviceBroker;
@interface DefaultDetectListener : DefaultBrokerHolder <UpDeviceDetectListener>

+ (DefaultDetectListener *)DefaultDetectListener:(DefaultDeviceBroker *)broker;
- (instancetype)initWithSuBroker:(DefaultDeviceBroker *)broker;
- (void)onFind:(NSArray<id<UpDeviceBaseInfo>> *)baseInfoList;
- (void)onLose:(NSArray<id<UpDeviceBaseInfo>> *)baseInfoList;
@end
