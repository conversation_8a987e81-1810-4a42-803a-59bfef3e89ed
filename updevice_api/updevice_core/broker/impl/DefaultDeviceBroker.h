//
//  DefaultDeviceBroker.h
//  updevice_api
//
//  Created by <PERSON><PERSON> on 2018/12/18.
//  Copyright © 2018 Haierfc. All rights reserved.
//

#import <Foundation/Foundation.h>
#import "DefaultNotification.h"
#import "DefaultToolkitListener.h"
#import "DefaultDetectListener.h"
#import "DefaultReportListener.h"
#import "UpDeviceToolkit.h"
#import "UpDeviceBroker.h"
#import "UpDeviceCacheManager.h"

@interface DefaultDeviceBroker : NSObject <UpDeviceBroker>

+ (DefaultDeviceBroker *)DefaultDeviceBroker:(id<UpDeviceToolkit>)toolkit deviceManager:(id<UpDeviceCenter>)deviceManager;

- (instancetype)initWithKit:(id<UpDeviceToolkit>)toolkit deviceManager:(id<UpDeviceCenter>)deviceManager;

- (DefaultNotification *)getNotification;
@end
