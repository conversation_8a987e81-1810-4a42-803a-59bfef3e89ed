//
//  DefaultNotification.m
//  updevice_api
//
//  Created by <PERSON><PERSON> on 2018/12/19.
//  Copyright © 2018 Haierfc. All rights reserved.
//

#import "DefaultNotification.h"
#import "UpDeviceToolkitState.h"
#import "UpDeviceToolkitListener.h"
#import "UpDeviceBaseInfo.h"
#import "UpDeviceDetectListener.h"
#import "UpDeviceReportListener.h"

@implementation DefaultNotification

- (void)notifyToolkitStateChange:(NSString *)protocol state:(UpDeviceToolkitState)state listeners:(NSArray<id<UpDeviceToolkitListener>> *)listeners
{

    if (listeners == nil || listeners.count == 0) {
        return;
    }
    for (id<UpDeviceToolkitListener> listener in listeners) {
        [listener onStateChange:protocol state:state];
    }
}
- (void)notifyDeviceFind:(NSArray<id<UpDeviceBaseInfo>> *)list listeners:(NSArray<id<UpDeviceDetectListener>> *)listeners
{

    if (listeners == nil || listeners.count == 0) {
        return;
    }
    for (id<UpDeviceDetectListener> listener in listeners) {
        [listener onFind:list];
    }
}
- (void)notifyDeviceLose:(NSArray<id<UpDeviceBaseInfo>> *)list listeners:(NSArray<id<UpDeviceDetectListener>> *)listeners
{

    if (listeners == nil || listeners.count == 0) {
        return;
    }
    for (id<UpDeviceDetectListener> listener in listeners) {
        [listener onLose:list];
    }
}
- (void)notifyDeviceInfoChange:(id<UpDeviceBaseInfo>)deviceBaseInfo listeners:(NSArray<id<UpDeviceReportListener>> *)listeners
{

    if (listeners == nil || listeners.count == 0) {
        return;
    }
    for (id<UpDeviceReportListener> listener in listeners) {
        [listener onDeviceInfoChange:deviceBaseInfo];
    }
}
- (void)notifyConnectionChange:(id<UpDeviceBaseInfo>)deviceBaseInfo connection:(UpDeviceConnection)connection listeners:(NSArray<id<UpDeviceReportListener>> *)listeners
{

    if (listeners == nil || listeners.count == 0) {
        return;
    }
    for (id<UpDeviceReportListener> listener in listeners) {
        [listener onConnectionChange:deviceBaseInfo connection:connection];
    }
}

- (void)notifyControlStateChange:(id<UpDeviceBaseInfo>)deviceBaseInfo state:(UpDeviceControlState)state listeners:(NSArray<id<UpDeviceReportListener>> *)listeners
{
    if (listeners == nil || listeners.count == 0) {
        return;
    }
    for (id<UpDeviceReportListener> listener in listeners) {
        [listener onControlStateChange:deviceBaseInfo controlState:state];
    }
}

- (void)notifyFaultInformationCodeChange:(id<UpDeviceBaseInfo>)deviceBaseInfo code:(NSInteger)code listeners:(NSArray<id<UpDeviceReportListener>> *)listeners
{
    if (listeners == nil || listeners.count == 0) {
        return;
    }
    for (id<UpDeviceReportListener> listener in listeners) {
        [listener onFaultInformationCodeChange:deviceBaseInfo code:code];
    }
}

- (void)notifyRealOnlineChange:(id<UpDeviceBaseInfo>)deviceBaseInfo status:(UpDeviceRealOnline)status listeners:(NSArray<id<UpDeviceReportListener>> *)listeners
{
    if (listeners == nil || listeners.count == 0) {
        return;
    }
    for (id<UpDeviceReportListener> listener in listeners) {
        [listener onRealOnlineChange:deviceBaseInfo status:status];
    }
}

- (void)notifyRealOnlineChangeV2:(id<UpDeviceBaseInfo>)deviceBaseInfo status:(UpDeviceRealOnlineV2)status listeners:(NSArray<id<UpDeviceReportListener>> *)listeners
{
    if (listeners == nil || listeners.count == 0) {
        return;
    }

    for (id<UpDeviceReportListener> listener in listeners) {
        [listener onRealOnlineChangeV2:deviceBaseInfo status:status];
    }
}

- (void)notifyDeviceReceive:(id<UpDeviceBaseInfo>)deviceBaseInfo name:(NSString *)name data:(id)data listeners:(NSMutableArray<id<UpDeviceReportListener>> *)listeners
{
    if (listeners == nil) {
        return;
    }
    for (id<UpDeviceReportListener> listener in listeners) {
        [listener onDeviceReceive:deviceBaseInfo name:name data:data];
    }
}
- (void)notifySubDevListChange:(id<UpDeviceBaseInfo>)deviceBaseInfo list:(NSArray<id<UpDeviceBaseInfo>> *)list listeners:(NSArray<id<UpDeviceReportListener>> *)listeners
{

    if (listeners == nil || listeners.count == 0) {
        return;
    }
    for (id<UpDeviceReportListener> listener in listeners) {
        [listener onSubDevListChange:deviceBaseInfo subDevInfoList:list];
    }
}
- (void)notifyAttributesChange:(id<UpDeviceBaseInfo>)deviceBaseInfo list:(NSArray<id<UpDeviceAttribute>> *)list listeners:(NSArray<id<UpDeviceReportListener>> *)listeners
{

    if (listeners == nil || listeners.count == 0) {
        return;
    }
    for (id<UpDeviceReportListener> listener in listeners) {
        [listener onAttributesChange:deviceBaseInfo attributeList:list];
    }
}
- (void)notifyDeviceCaution:(id<UpDeviceBaseInfo>)deviceBaseInfo list:(NSArray<id<UpDeviceCaution>> *)list listeners:(NSArray<id<UpDeviceReportListener>> *)listeners
{

    if (listeners == nil || listeners.count == 0) {
        return;
    }
    for (id<UpDeviceReportListener> listener in listeners) {
        [listener onDeviceCaution:deviceBaseInfo cautionList:list];
    }
}

- (void)notifyUpdateBoardFOTAStatus:(id<UpDeviceBaseInfo>)deviceBaseInfo FOTAStatusInfo:(id<UpDeviceFOTAStatusInfo>)statusInfo listeners:(NSMutableArray<id<UpDeviceReportListener>> *)listeners
{
    if (listeners == nil || listeners.count == 0) {
        return;
    }
    for (id<UpDeviceReportListener> listener in listeners) {
        [listener onDeviceUpdateBoardFOTAStatus:deviceBaseInfo FOTAStatusInfo:statusInfo];
    }
}

- (void)notifyDeviceListChanged:(NSArray<id<UpDeviceDetectListener>> *)listeners
{
    if (listeners == nil || listeners.count == 0) {
        return;
    }
    for (id<UpDeviceDetectListener> listener in listeners) {
        [listener onDeviceListChanged];
    }
}

- (void)notifyBLEHistoryData:(id<UpDeviceBaseInfo>)deviceBaseInfo BLEHistoryInfo:(id<UpDeviceBLEHistoryInfo>)BLEHistoryInfo listeners:(NSMutableArray<id<UpDeviceReportListener>> *)listeners
{
    if (listeners == nil || listeners.count == 0) {
        return;
    }
    for (id<UpDeviceReportListener> listener in listeners) {
        [listener onDeviceReceiveBLEHistoryData:deviceBaseInfo BLEHistoryInfo:BLEHistoryInfo];
    }
}

- (void)notifyBLERealTimeData:(id<UpDeviceBaseInfo>)deviceBaseInfo BLERealTimeData:(NSData *)data listeners:(NSMutableArray<id<UpDeviceReportListener>> *)listeners
{
    if (listeners == nil || listeners.count == 0) {
        return;
    }
    for (id<UpDeviceReportListener> listener in listeners) {
        [listener onDeviceReceiveBLERealTimeData:deviceBaseInfo data:data];
    }
}

- (void)notifySleepStateChange:(id<UpDeviceBaseInfo>)deviceBaseInfo state:(UpDeviceSleepState)state listeners:(NSArray<id<UpDeviceReportListener>> *)listeners
{
    if (listeners == nil || listeners.count == 0) {
        return;
    }
    for (id<UpDeviceReportListener> listener in listeners) {
        [listener onSleepStateChange:deviceBaseInfo state:state];
    }
}

- (void)notifyNetworkLevelChange:(id<UpDeviceBaseInfo>)deviceBaseInfo level:(UpDeviceNetworkLevel)level listeners:(NSArray<id<UpDeviceReportListener>> *)listeners
{
    if (listeners == nil || listeners.count == 0) {
        return;
    }
    for (id<UpDeviceReportListener> listener in listeners) {
        [listener onNetworkLevelChange:deviceBaseInfo level:level];
    }
}

- (void)notifyQCConnectTimeoutType:(id<UpDeviceBaseInfo>)deviceBaseInfo timeoutType:(UpDeviceQCConnectTimeoutType)timeoutType listeners:(NSArray<id<UpDeviceReportListener>> *)listeners
{
    if (listeners == nil || listeners.count == 0) {
        return;
    }
    for (id<UpDeviceReportListener> listener in listeners) {
        [listener onQCConnectTimeout:deviceBaseInfo timeoutType:timeoutType];
    }
}

- (void)notifyBleStateChange:(id<UpDeviceBaseInfo>)deviceBaseInfo state:(UpDeviceConnection)state listeners:(NSArray<id<UpDeviceReportListener>> *)listeners
{
    if (listeners == nil || listeners.count == 0) {
        return;
    }
    for (id<UpDeviceReportListener> listener in listeners) {
        [listener onBleStateChange:deviceBaseInfo state:state];
    }
}

- (void)notifyOfflineCauseChange:(id<UpDeviceBaseInfo>)deviceBaseInfo offlineCause:(UpDeviceOfflineCause)offlineCause listeners:(NSArray<id<UpDeviceReportListener>> *)listeners
{
    if (listeners == nil || listeners.count == 0) {
        return;
    }
    for (id<UpDeviceReportListener> listener in listeners) {
        [listener onOfflineCauseChange:deviceBaseInfo offlineCause:offlineCause];
    }
}

- (void)notifyOfflineDaysChange:(id<UpDeviceBaseInfo>)deviceBaseInfo offlineDays:(NSInteger)offlineDays listeners:(NSArray<id<UpDeviceReportListener>> *)listeners
{
    if (listeners == nil || listeners.count == 0) {
        return;
    }
    for (id<UpDeviceReportListener> listener in listeners) {
        [listener onOfflineDaysChange:deviceBaseInfo offlineDays:offlineDays];
    }
}

- (void)notifyOnlyConfigStateChange:(id<UpDeviceBaseInfo>)deviceBaseInfo onlyConfigState:(UpDeviceOnlyConfigState)onlyConfigState listeners:(NSArray<id<UpDeviceReportListener>> *)listeners
{
    if (listeners == nil || listeners.count == 0) {
        return;
    }
    for (id<UpDeviceReportListener> listener in listeners) {
        [listener onOnlyConfigStateChange:deviceBaseInfo onlyConfigState:onlyConfigState];
    }
}

- (void)notifyuSDKDeviceWrapperDataChange:(id<UpDeviceBaseInfo>)deviceBaseInfo wrapperData:(uSDKDeviceWrapper *)wrapper listeners:(NSArray<id<UpDeviceReportListener>> *)listeners
{
    if (listeners == nil || listeners.count == 0) {
        return;
    }
    for (id<UpDeviceReportListener> listener in listeners) {
        [listener onuSDKDeviceDataChange:deviceBaseInfo wrapperData:wrapper];
    }
}
@end
