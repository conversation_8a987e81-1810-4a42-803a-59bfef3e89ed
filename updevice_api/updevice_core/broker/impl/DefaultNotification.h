//
//  DefaultNotification.h
//  updevice_api
//
//  Created by <PERSON><PERSON> on 2018/12/19.
//  Copyright © 2018 Haierfc. All rights reserved.
//

#import <Foundation/Foundation.h>
#import "UpDeviceToolkitState.h"
#import "UpDeviceToolkitListener.h"
#import "UpDeviceBaseInfo.h"
#import "UpDeviceDetectListener.h"
#import "UpDeviceReportListener.h"
#import "UpDeviceBLEHistoryInfo.h"
#import "UpDeviceOnlineStatus.h"
#import "UpDeviceConnection.h"
#import "UpDeviceQCConnectTimeoutType.h"
#import "uSDKDeviceWrapper.h"

@interface DefaultNotification : NSObject
- (void)notifyToolkitStateChange:(NSString *)protocol state:(UpDeviceToolkitState)state listeners:(NSArray<id<UpDeviceToolkitListener>> *)listeners;
- (void)notifyDeviceFind:(NSArray<id<UpDeviceBaseInfo>> *)list listeners:(NSArray<id<UpDeviceDetectListener>> *)listeners;
- (void)notifyDeviceLose:(NSArray<id<UpDeviceBaseInfo>> *)list listeners:(NSArray<id<UpDeviceDetectListener>> *)listeners;
- (void)notifyDeviceInfoChange:(id<UpDeviceBaseInfo>)deviceBaseInfo listeners:(NSArray<id<UpDeviceReportListener>> *)listeners;
- (void)notifyConnectionChange:(id<UpDeviceBaseInfo>)deviceBaseInfo connection:(UpDeviceConnection)connection listeners:(NSArray<id<UpDeviceReportListener>> *)listeners;
- (void)notifyControlStateChange:(id<UpDeviceBaseInfo>)deviceBaseInfo state:(UpDeviceControlState)state listeners:(NSArray<id<UpDeviceReportListener>> *)listeners;
- (void)notifyFaultInformationCodeChange:(id<UpDeviceBaseInfo>)deviceBaseInfo code:(NSInteger)code listeners:(NSArray<id<UpDeviceReportListener>> *)listeners;
- (void)notifyRealOnlineChange:(id<UpDeviceBaseInfo>)deviceBaseInfo status:(UpDeviceRealOnline)status listeners:(NSArray<id<UpDeviceReportListener>> *)listeners;
- (void)notifySubDevListChange:(id<UpDeviceBaseInfo>)deviceBaseInfo list:(NSArray<id<UpDeviceBaseInfo>> *)list listeners:(NSArray<id<UpDeviceReportListener>> *)listeners;
- (void)notifyAttributesChange:(id<UpDeviceBaseInfo>)deviceBaseInfo list:(NSArray<id<UpDeviceAttribute>> *)list listeners:(NSArray<id<UpDeviceReportListener>> *)listeners;
- (void)notifyDeviceCaution:(id<UpDeviceBaseInfo>)deviceBaseInfo list:(NSArray<id<UpDeviceCaution>> *)list listeners:(NSArray<id<UpDeviceReportListener>> *)listeners;
- (void)notifyDeviceReceive:(id<UpDeviceBaseInfo>)deviceBaseInfo name:(NSString *)name data:(id)data listeners:(NSMutableArray<id<UpDeviceReportListener>> *)listeners;
- (void)notifyUpdateBoardFOTAStatus:(id<UpDeviceBaseInfo>)deviceBaseInfo FOTAStatusInfo:(id<UpDeviceFOTAStatusInfo>)statusInfo listeners:(NSMutableArray<id<UpDeviceReportListener>> *)listeners;
- (void)notifyDeviceListChanged:(NSArray<id<UpDeviceDetectListener>> *)listeners;
- (void)notifyBLEHistoryData:(id<UpDeviceBaseInfo>)deviceBaseInfo BLEHistoryInfo:(id<UpDeviceBLEHistoryInfo>)BLEHistoryInfo listeners:(NSMutableArray<id<UpDeviceReportListener>> *)listeners;
- (void)notifyBLERealTimeData:(id<UpDeviceBaseInfo>)deviceBaseInfo BLERealTimeData:(NSData *)data listeners:(NSMutableArray<id<UpDeviceReportListener>> *)listeners;
- (void)notifySleepStateChange:(id<UpDeviceBaseInfo>)deviceBaseInfo state:(UpDeviceSleepState)state listeners:(NSArray<id<UpDeviceReportListener>> *)listeners;

- (void)notifyuSDKDeviceWrapperDataChange:(id<UpDeviceBaseInfo>)deviceBaseInfo wrapperData:(uSDKDeviceWrapper *)wrapper listeners:(NSArray<id<UpDeviceReportListener>> *)listeners;

/// 真实在线状态通知V2
/// @param deviceBaseInfo deviceBaseInfo
/// @param status 新的设备状态
/// @param listeners 监听者
- (void)notifyRealOnlineChangeV2:(id<UpDeviceBaseInfo>)deviceBaseInfo status:(UpDeviceRealOnlineV2)status listeners:(NSArray<id<UpDeviceReportListener>> *)listeners;

- (void)notifyNetworkLevelChange:(id<UpDeviceBaseInfo>)deviceBaseInfo level:(UpDeviceNetworkLevel)level listeners:(NSArray<id<UpDeviceReportListener>> *)listeners;

//快连超时回调
- (void)notifyQCConnectTimeoutType:(id<UpDeviceBaseInfo>)deviceBaseInfo
                       timeoutType:(UpDeviceQCConnectTimeoutType)timeoutType
                         listeners:(NSArray<id<UpDeviceReportListener>> *)listeners;
//蓝牙连接状态变化

- (void)notifyBleStateChange:(id<UpDeviceBaseInfo>)deviceBaseInfo state:(UpDeviceConnection)state listeners:(NSArray<id<UpDeviceReportListener>> *)listeners;

/// 设备离线原因变化通知
/// @param deviceBaseInfo 设备基本信息
/// @param offlineCause 离线原因
/// @param listeners 监听者
/// @since v7.11.0
- (void)notifyOfflineCauseChange:(id<UpDeviceBaseInfo>)deviceBaseInfo offlineCause:(UpDeviceOfflineCause)offlineCause listeners:(NSArray<id<UpDeviceReportListener>> *)listeners;

/// 设备离线天数变化通知
/// @param deviceBaseInfo 设备基本信息
/// @param offlineDays 离线天数
/// @param listeners 监听者
/// @since v7.19.0
- (void)notifyOfflineDaysChange:(id<UpDeviceBaseInfo>)deviceBaseInfo offlineDays:(NSInteger)offlineDays listeners:(NSArray<id<UpDeviceReportListener>> *)listeners;

/// 仅配网状态变化通知
/// @param deviceBaseInfo 设备基本信息
/// @param onlyConfigState 仅配网状态
/// @param listeners 监听者
/// @since v7.20.0
- (void)notifyOnlyConfigStateChange:(id<UpDeviceBaseInfo>)deviceBaseInfo onlyConfigState:(UpDeviceOnlyConfigState)onlyConfigState listeners:(NSArray<id<UpDeviceReportListener>> *)listeners;
@end
