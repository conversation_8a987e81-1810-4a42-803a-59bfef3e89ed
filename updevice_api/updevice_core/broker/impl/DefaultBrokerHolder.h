//
//  DefaultBrokerHolder.h
//  updevice_api
//
//  Created by <PERSON><PERSON> on 2018/12/18.
//  Copyright © 2018 Haierfc. All rights reserved.
//

#import <Foundation/Foundation.h>
#import "UpDeviceBrokerHolder.h"
#import "UpDeviceSetMap.h"

@class DefaultDeviceBroker;

extern NSString *const UNIVERSAL_DEVICE_ID;

@interface DefaultBrokerHolder : NSObject <UpDeviceBrokerHolder, UpDeviceSetMap>

+ (DefaultBrokerHolder *)DefaultBrokerHolder:(DefaultDeviceBroker *)broker;
- (instancetype)initWithBroker:(DefaultDeviceBroker *)broker;
- (void)put:(NSString *)deviceId target:(id)target;
- (void)remove:(id)target deviceId:(NSString *)deviceId;
@end
