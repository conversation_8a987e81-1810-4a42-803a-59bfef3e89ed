//
//  DefaultDetectListener.m
//  updevice_api
//
//  Created by <PERSON><PERSON> on 2018/12/19.
//  Copyright © 2018 Haierfc. All rights reserved.
//

#import "DefaultDetectListener.h"

@implementation DefaultDetectListener
@synthesize detectListener;

+ (DefaultDetectListener *)DefaultDetectListener:(DefaultDeviceBroker *)broker
{
    return [[DefaultDetectListener alloc] initWithBroker:broker];
}

- (instancetype)initWithSuBroker:(DefaultDeviceBroker *)broker;
{
    if (self = [super initWithBroker:broker]) {
    }
    return self;
}
- (void)onFind:(NSArray<id<UpDeviceBaseInfo>> *)baseInfoList
{
    NSArray *listeners = [self get];
    DefaultDeviceBroker *broker = [self getBroker];
    DefaultNotification *notif = [broker getNotification];
    [notif notifyDeviceFind:baseInfoList listeners:listeners];
}

- (void)onLose:(NSArray<id<UpDeviceBaseInfo>> *)baseInfoList
{
    NSArray *listeners = [self get];
    DefaultDeviceBroker *broker = [self getBroker];
    DefaultNotification *notif = [broker getNotification];
    [notif notifyDeviceLose:baseInfoList listeners:listeners];
}

- (void)onDeviceListChanged
{
    NSArray *listeners = [self get];
    DefaultDeviceBroker *broker = [self getBroker];
    DefaultNotification *notif = [broker getNotification];
    [notif notifyDeviceListChanged:listeners];
}
@end
