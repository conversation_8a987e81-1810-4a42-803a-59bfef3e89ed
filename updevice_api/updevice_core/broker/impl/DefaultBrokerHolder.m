//
//  DefaultBrokerHolder.m
//  updevice_api
//
//  Created by <PERSON><PERSON> on 2018/12/18.
//  Copyright © 2018 Haierfc. All rights reserved.
//

#import "DefaultBrokerHolder.h"
#import "UpDeviceCarrier.h"
#import "UPDeviceLog.h"

NSString *const UNIVERSAL_DEVICE_ID = @"universal-device-id";

@interface DefaultBrokerHolder ()
@property (nonatomic, strong) DefaultDeviceBroker *broker;
@property (nonatomic, strong) UpDeviceCarrier *carrier;

@end
@implementation DefaultBrokerHolder

+ (DefaultBrokerHolder *)DefaultBrokerHolder:(DefaultDeviceBroker *)broker
{
    return [[DefaultBrokerHolder alloc] initWithBroker:broker];
}

- (instancetype)initWithBroker:(DefaultDeviceBroker *)broker;
{
    if (self = [super init]) {
        self.broker = broker;
        self.carrier = [UpDeviceCarrier UpDeviceCarrier:UNIVERSAL_DEVICE_ID];
    }
    return self;
}

- (id)getBroker
{
    return _broker;
}
- (void)add:(id)target
{
    [_carrier add:target];
}
- (void)put:(NSString *)deviceId target:(id)target
{
    [_carrier put:deviceId Target:target];
}
- (NSArray *)get
{
    return [_carrier get];
}
- (NSArray *)get:(NSString *)deviceId
{
    return [_carrier get:deviceId];
}
- (void)remove:(id)target
{
    [_carrier remove:target];
}
- (void)remove:(id)target deviceId:(NSString *)deviceId
{
    [_carrier remove:target Target:deviceId];
}
- (BOOL)isEmpty
{
    return [_carrier isEmpty];
}
- (BOOL)isEmpty:(NSString *)key
{
    return [_carrier isEmpty:key];
}
- (int)size
{
    return [_carrier size];
}
- (int)size:(NSString *)key
{
    return [_carrier size:key];
}
- (int)total
{
    return [_carrier total];
}

- (void)clear
{
    [_carrier clear];
}
- (void)clear:(NSString *)key
{
    [_carrier clear:key];
}
- (void)clearAll
{
    [_carrier clearAll];
}

- (void)put:(nonnull NSString *)key Target:(nonnull id)target
{
    UPLogWarning(kUPDevicePrefix, @"%s[%d]调用了未实现的方法！", __PRETTY_FUNCTION__, __LINE__);
}


- (void)remove:(nonnull NSString *)key Target:(nonnull id)target
{
    UPLogWarning(kUPDevicePrefix, @"%s[%d]调用了未实现的方法！", __PRETTY_FUNCTION__, __LINE__);
}

@end
