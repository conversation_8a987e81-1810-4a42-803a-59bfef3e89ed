//
//  UpDeviceFactory.h
//  updevice_api
//
//  Created by f on 28/12/2018.
//  Copyright © 2018 Haierfc. All rights reserved.
//

#import <Foundation/Foundation.h>
#import "UpDeviceInfo.h"
#import "UpDevice.h"
#import "UpDeviceBroker.h"
#import "UpDeviceFactory.h"

NS_ASSUME_NONNULL_BEGIN

@protocol UpDeviceFactory <NSObject>
/**
 * UpDevice对象创建方法
 *
 * @param uniqueId   唯一识别码
 * @param deviceInfo 设备信息
 * @param broker     设备工具代理
 * @param factory    子设备工厂
 * @return UpDevice对象
 */
- (nullable id<UpDevice>)create:(NSString *)uniqueId deviceInfo:(id<UpDeviceInfo>)deviceInfo broker:(id<UpDeviceBroker>)broker factory:(id<UpDeviceFactory>)factory;

@end

NS_ASSUME_NONNULL_END
