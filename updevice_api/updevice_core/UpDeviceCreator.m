//
//  UpDeviceCreator.m
//  updevice_api
//
//  Created by f on 28/12/2018.
//  Copyright © 2018 Haierfc. All rights reserved.
//

#import "UpDeviceCreator.h"
#import "UpCommonDevice.h"
#import "UPDeviceLog.h"

@interface UpDeviceCreator () {
    NSMutableArray<id<UpDeviceFactory>> *_factories;
    id<UpDeviceFactory> _fallbackFactory;
}
@end

@implementation UpDeviceCreator
- (instancetype)init
{
    self = [super init];
    if (self) {
        _factories = [NSMutableArray array];
    }
    return self;
}

- (void)append:(id<UpDeviceFactory>)factory
{
    if (factory == nil) {
        return;
    }
    [_factories addObject:factory];
}

- (void)remove:(id<UpDeviceFactory>)factory
{
    if (factory == nil) {
        return;
    }
    [_factories removeObject:factory];
}

- (void)setFallbackFactory:(id<UpDeviceFactory>)fallbackFactory
{
    _fallbackFactory = fallbackFactory;
}

- (id<UpDevice>)tryCreate:(NSString *)uniqueId deviceInfo:(id<UpDeviceInfo>)deviceInfo broker:(id<UpDeviceBroker>)broker factory:(id<UpDeviceFactory>)factory
{
    id<UpDevice> device = nil;
    for (int i = 0; i < _factories.count; i++) {
        id<UpDeviceFactory> deviceFactory = [_factories objectAtIndex:i];
        device = [deviceFactory create:uniqueId deviceInfo:deviceInfo broker:broker factory:factory];
        if (device) {
            break;
        }
    }
    if (device == nil && _fallbackFactory != nil) {
        device = [_fallbackFactory create:uniqueId deviceInfo:deviceInfo broker:broker factory:factory];
    }
    return device;
}

- (nullable id<UpDevice>)create:(NSString *)uniqueId deviceInfo:(id<UpDeviceInfo>)deviceInfo broker:(id<UpDeviceBroker>)broker factory:(id<UpDeviceFactory>)factory
{
    id<UpDevice> device = [self tryCreate:uniqueId deviceInfo:deviceInfo broker:broker factory:factory];
    if (device == nil) {
        UPLogWarning(kUPDevicePrefix, @"%s[%d]设备(%@)的实例对象创建失败，将创建UpCommonDevice实例对象！", __PRETTY_FUNCTION__, __LINE__, uniqueId);
        device = [UpCommonDevice UpCommonDevice:uniqueId deviceInfo:deviceInfo broker:broker Factory:factory];
    }
    return device;
}

@end
