//
//  UpDeviceFilters.m
//  updevice_api
//
//  Created by <PERSON><PERSON> on 2019/1/10.
//  Copyright © 2019 Haierfc. All rights reserved.
//

#import "UpDeviceFilters.h"
#import "UPDeviceLog.h"

@interface FilterDataProviderRealize : NSObject <FilterDataProvider>
@end

@implementation FilterDataProviderRealize
- (id)getDataFromDevice:(id<UpDevice>)device
{
    return device.uniqueId;
}
@end

@implementation NotNullFilter
+ (NotNullFilter *)NotNullFilter:(NSMutableArray<id<UpDeviceFilter>> *)filters
{
    NotNullFilter *composite = [[NotNullFilter alloc] initWithFilter:filters];
    return composite;
}

- (instancetype)initWithFilter:(NSMutableArray<id<UpDeviceFilter>> *)filters
{
    if (self = [super init]) {
        if (filters != nil) {
        }
    }
    return self;
}

- (BOOL)accept:(id<UpDevice>)device
{
    if (device != nil) {
        return YES;
    }
    return NO;
}
@end

@implementation StringFilter
+ (StringFilter *)StringFilter:(NSMutableArray *)collection provider:(id<FilterDataProvider>)provider
{
    StringFilter *composite = [[StringFilter alloc] initWithFilter:collection provider:provider];
    return composite;
}

- (instancetype)initWithFilter:(NSMutableArray *)collection provider:(id<FilterDataProvider>)provider
{
    if (self = [super init]) {
        if (provider == nil) {
            UPLogWarning(kUPDevicePrefix, @"%s[%d]InfoDataProvider<String> cannot be NULL!", __PRETTY_FUNCTION__, __LINE__);
        }
        self.provider = provider;
        if (collection != nil) {
            self.checkSet = collection;
        }
    }
    return self;
}

- (BOOL)accept:(id<UpDevice>)device
{
    NSString *uniqueId = [_provider getDataFromDevice:device];
    if ([self.checkSet containsObject:uniqueId]) {
        return YES;
    }
    return NO;
}

@end

@implementation CompositeFilter
+ (CompositeFilter *)CompositeFilter:(NSMutableArray<id<UpDeviceFilter>> *)filters
{
    CompositeFilter *composite = [[CompositeFilter alloc] initWithFilter:filters];
    return composite;
}

- (instancetype)initWithFilter:(NSMutableArray<id<UpDeviceFilter>> *)filters
{
    if (self = [super init]) {
        if (filters != nil) {
            self.filters = filters;
        }
    }
    return self;
}

- (BOOL)accept:(id<UpDevice>)device
{
    for (id<UpDeviceFilter> filter in self.filters) {
        if (filter == nil) {
            continue;
        }
        BOOL acBool = [filter accept:device];
        if (!acBool) {
            return NO;
        }
    }
    return YES;
}
@end

@implementation UpDeviceFilters
- (id<UpDeviceFilter>)filterByUniqueId:(NSMutableArray *)strings
{
    FilterDataProviderRealize *freal = [FilterDataProviderRealize new];
    id<FilterDataProvider> providers = freal;
    return [self createStringFilter:providers array:strings];
}

- (id<UpDeviceFilter>)createStringFilter:(id<FilterDataProvider>)provider array:(NSMutableArray *)array
{
    NSMutableArray *newArray = [NSMutableArray array];
    for (NSString *str in array) {
        if (str == nil) {
            continue;
        }
        [newArray addObject:str];
    }
    NotNullFilter *nitf = [NotNullFilter NotNullFilter:nil];
    StringFilter *strf = [StringFilter StringFilter:newArray provider:provider];
    NSMutableArray *neArray = [NSMutableArray arrayWithObjects:nitf, strf, nil];
    return [CompositeFilter CompositeFilter:neArray];
}

@end
