//
//  UpDeviceBase.h
//  updevice_api
//
//  Created by <PERSON><PERSON> on 2018/12/19.
//  Copyright © 2018 Haierfc. All rights reserved.
//

#import <Foundation/Foundation.h>
#import "UpDeviceBroker.h"
#import "UpDeviceResult.h"
#import "UpDeviceAttribute.h"
#import "UpDeviceBaseInfo.h"
#import "UpDeviceCaution.h"
#import "UpDeviceCommand.h"
#import "UpDeviceConnection.h"
#import "UpDeviceInfo.h"
#import "UpDeviceReportListener.h"
#import "UpExtendDevice.h"
#import "UpDeviceFactory.h"
#import "UpDeviceInfo.h"
#import "UpDeviceBroker.h"
#import "UpDeviceCommandIntercepter.h"

@interface UpDeviceBase : NSObject <UpExtendDevice>

+ (UpDeviceBase *)UpDeviceBase:(id<UpDeviceInfo>)deviceInfo broker:(id<UpDeviceBroker>)broker factory:(id<UpDeviceFactory>)factory;

- (instancetype)initWithDeviceInfo:(NSString *)uniqueId deviceInfo:(id<UpDeviceInfo>)deviceInfo broker:(id<UpDeviceBroker>)broker factory:(id<UpDeviceFactory>)factory;

- (BOOL)isSubDev;

- (id<UpDeviceInfo>)getInfo;

//- (id<UpDevice>)getDevice;
//- (id<UpDeviceFactory>)getFactory;
//- (void)setState:(UpDeviceState)state;

- (NSString *)protocol;

- (NSString *)deviceId;

- (NSString *)uniqueId;

/**
 * 模块是否需要升级
 *
 * @since 1.3.0
 */
- (BOOL)isModuleNeedOTA;

- (void)onPrepared;

- (void)onReleased;

- (BOOL)checkState:(UpDeviceState)target;

- (id<UpDeviceToolkit>)getToolkit;
- (id<UpDevice>)getDeviceById:(NSString *)targetId;
//自定义设备变化通知发送
- (void)sendCustomNotfiDeviceChange:(NSInteger)event;
//型号缺失等情况下，重置准备状态
- (void)resetLogicEngineConfigState;
@end
