//
//  UPDeviceNetType.h
//  updevice_api
//
//  Created by <PERSON><PERSON><PERSON><PERSON> on 2021/2/20.
//  Copyright © 2021 Haierfc. All rights reserved.
//

#import <Foundation/Foundation.h>
typedef enum : NSUInteger {
    /**
     *  未知
     */
    DeviceNetTypeUnknown = -1,
    /**
     *  远程
     */
    DeviceNetTypeREMOTE = 0,
    /**
     *  本地
     */
    DeviceNetTypeLOCAL = 1,
    /**
     *  蓝牙，5.3.0添加
     */
    DeviceNetTypeBLE = 2,
    /**
     *  Mesh网关
     *  @since 7.0.0
     */
    DeviceNetTypeMESH_GATEWAY = 3,
    /**
     *  BLE Mesh
     *  @since 7.0.0
     */
    DeviceNetTypeBLE_MESH = 4,
} DeviceNetType;
NS_ASSUME_NONNULL_BEGIN

@protocol UPDeviceNetType <NSObject>
/// 获取设备网络类型
- (DeviceNetType)getNetType;
@end

NS_ASSUME_NONNULL_END
