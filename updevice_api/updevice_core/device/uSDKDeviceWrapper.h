//
//  uSDKDeviceWrapper.h
//  updevice_api
//
//  Created by <PERSON><PERSON><PERSON>   on 2025/4/9.
//  Copyright © 2025 海尔优家智能科技（北京）有限公司. All rights reserved.
//

#import <Foundation/Foundation.h>
#import "UpDeviceOnlineStatus.h"
#import "UpDeviceOnlineStateV2.h"
#import "UpDeviceOfflineCause.h"
#import "UpDeviceOnlyConfigState.h"
#import "UpDeviceConnection.h"
#import "UpDeviceControlState.h"
#import "UpDeviceNetworkLevel.h"
#import "UpDeviceBaseInfo.h"
#import "UpDeviceCaution.h"
#import "UpDeviceAttribute.h"

NS_ASSUME_NONNULL_BEGIN

@interface uSDKDeviceWrapper : NSObject
// 离线原因
@property (nonatomic, assign) UpDeviceOfflineCause offlineCause;
// 离线天数
@property (nonatomic, assign) NSInteger offlineDays;
// 仅配网状态
@property (nonatomic, assign) UpDeviceOnlyConfigState onlyConfigState;
// 连接状态
@property (nonatomic, assign) UpDeviceConnection connection;
// 控制状态
@property (nonatomic, assign) UpDeviceControlState controlState;
// 配网错误码
@property (nonatomic, assign) NSInteger faultInformationCode;
// 离在线状态
@property (nonatomic, assign) UpDeviceRealOnline realOnlineStatus;
// 离在线状态
@property (nonatomic, assign) UpDeviceRealOnlineV2 realOnlineStateV2;
// 网络质量
@property (nonatomic, assign) UpDeviceNetworkLevel networkLevel;
// 蓝牙状态
@property (nonatomic, assign) UpDeviceConnection bleState;
// 休眠状态
@property (nonatomic, assign) UpDeviceSleepState sleepState;
// 子设备信息列表
@property (nonatomic, strong) NSArray<id<UpDeviceBaseInfo>> *subDevInfoList;
// 属性列表
@property (nonatomic, strong) NSArray<id<UpDeviceAttribute>> *attributeList;
// 报警列表
@property (nonatomic, strong) NSArray<id<UpDeviceCaution>> *cautionList;
@end

NS_ASSUME_NONNULL_END
