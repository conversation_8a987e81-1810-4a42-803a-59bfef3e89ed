//
//  UpCompositeProcessor.m
//  updevice_api
//
//  Created by <PERSON><PERSON> on 2018/12/18.
//  Copyright © 2018 Haierfc. All rights reserved.
//

#import "UpCompositeProcessor.h"

@interface UpCompositeProcessor ()
@property (nonatomic, strong) NSMutableArray *processors;
@end

@implementation UpCompositeProcessor
+ (UpCompositeProcessor *)UpCompositeProcessor:(NSArray *)processors
{
    return [[UpCompositeProcessor alloc] initWithArray:processors];
}

- (instancetype)initWithArray:(NSArray *)processors
{
    if (self = [super init]) {
        self.processors = (NSMutableArray *)processors;
    }
    return self;
}

- (void)appendProcessor:(id<UpDeviceProcessor>)processor
{
    if (processor == nil) {
        return;
    }
    [self.processors addObject:processor];
}

- (void)removeProcessor:(id<UpDeviceProcessor>)processor
{
    if (processor == nil) {
        return;
    }
    [self.processors removeObject:processor];
}

- (void)processDeviceInfo:(id<UpDeviceBaseInfo>)baseInfo
{
    for (id<UpDeviceProcessor> processor in _processors) {
        [processor processDeviceInfo:baseInfo];
    }
}

- (void)processConnection:(UpDeviceConnection)connection
{
    for (id<UpDeviceProcessor> processor in _processors) {
        [processor processConnection:connection];
    }
}

- (void)processSubDevList:(NSArray<id<UpDeviceBaseInfo>> *)subDevList
{
    for (id<UpDeviceProcessor> processor in _processors) {
        [processor processSubDevList:subDevList];
    }
}

- (void)processAttributes:(NSArray<id<UpDeviceAttribute>> *)attributeList
{
    for (id<UpDeviceProcessor> processor in _processors) {
        [processor processAttributes:attributeList];
    }
}

- (void)processCautions:(NSArray<id<UpDeviceCaution>> *)cautionList
{
    for (id<UpDeviceProcessor> processor in _processors) {
        [processor processCautions:cautionList];
    }
}

- (void)processReceived:(NSString *)name data:(id)data
{
    for (id<UpDeviceProcessor> processor in _processors) {
        [processor processReceived:name data:data];
    }
}

- (void)processBleState:(UpDeviceConnection)state
{
    for (id<UpDeviceProcessor> processor in _processors) {
        [processor processBleState:state];
    }
}

@end

@interface Builder : NSObject
@property (nonatomic, strong) NSMutableArray *list;
@end

@implementation Builder
- (instancetype)initWithAppend:(id<UpDeviceProcessor>)processor
{
    if (self = [super init]) {
        if (processor != nil) {
            self.list = [NSMutableArray arrayWithObject:processor];
        }
    }
    return self;
}

- (instancetype)initWithRemove:(id<UpDeviceProcessor>)processor
{
    if (self = [super init]) {
        self.list = [NSMutableArray arrayWithObject:processor];
    }
    return self;
}

- (UpCompositeProcessor *)build
{
    UpCompositeProcessor *newPro = [[UpCompositeProcessor alloc] initWithArray:_list];
    return newPro;
}

@end
