//
//  UpDeviceProcessor.h
//  updevice_api
//
//  Created by <PERSON><PERSON> on 2018/12/18.
//  Copyright © 2018 Haierfc. All rights reserved.
//

#import <Foundation/Foundation.h>
#import "UpDeviceAttribute.h"
#import "UpDeviceConnection.h"
#import "UpDeviceBaseInfo.h"
#import "UpDeviceCaution.h"
#import "UpDeviceOnlineStateV2.h"
#import "UpDeviceOfflineCause.h"


@protocol UpDeviceProcessor <NSObject>

- (void)processDeviceInfo:(id<UpDeviceBaseInfo>)baseInfo;

- (void)processConnection:(UpDeviceConnection)connection;

- (void)processSubDevList:(NSArray<id<UpDeviceBaseInfo>> *)subDevList;

- (void)processAttributes:(NSArray<id<UpDeviceAttribute>> *)attributeList;

- (void)processCautions:(NSArray<id<UpDeviceCaution>> *)cautionList;

- (void)processReceived:(NSString *)name data:(id)data;

- (void)processOnlineStateV2:(UpDeviceRealOnlineV2)state;

- (void)processBleState:(UpDeviceConnection)state;

@optional
- (void)processOfflineCause:(UpDeviceOfflineCause)offlineCause;

@end
