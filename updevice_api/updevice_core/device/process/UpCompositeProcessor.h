//
//  UpCompositeProcessor.h
//  updevice_api
//
//  Created by <PERSON><PERSON> on 2018/12/18.
//  Copyright © 2018 Haierfc. All rights reserved.
//

#import <Foundation/Foundation.h>
#import "UpDeviceAttribute.h"
#import "UpDeviceConnection.h"
#import "UpDeviceBaseInfo.h"
#import "UpDeviceCaution.h"
#import "UpDeviceProcessor.h"

@interface UpCompositeProcessor : NSObject <UpDeviceProcessor>

+ (UpCompositeProcessor *)UpCompositeProcessor:(NSArray *)processors;

@end
