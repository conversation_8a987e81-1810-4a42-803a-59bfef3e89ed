//
//  UpCommonDevice.h
//  updevice_api
//
//  Created by <PERSON><PERSON> on 2018/12/19.
//  Copyright © 2018 Haierfc. All rights reserved.
//

#import <Foundation/Foundation.h>
#import "UpDeviceBase.h"
#import "UpDeviceBroker.h"

@interface UpCommonDevice : UpDeviceBase

+ (UpCommonDevice *)UpCommonDevice:(NSString *)uniqueId deviceInfo:(id<UpDeviceInfo>)deviceInfo broker:(id<UpDeviceBroker>)broker Factory:(id<UpDeviceFactory>)factory;

- (instancetype)initWithUniqueId:(NSString *)uniqueId deviceInfo:(id<UpDeviceInfo>)deviceInfo broker:(id<UpDeviceBroker>)broker factory:(id<UpDeviceFactory>)factory;

@end
