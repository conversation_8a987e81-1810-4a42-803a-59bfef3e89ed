//
//  UpDeviceCache.h
//  updevice_api
//
//  Created by <PERSON><PERSON> on 2018/12/19.
//  Copyright © 2018 Haierfc. All rights reserved.
//

#import <Foundation/Foundation.h>
#import "UpDeviceAttribute.h"
#import "UpDeviceCaution.h"
#import "UpDeviceConnection.h"
#import "UpDeviceInfo.h"
#import "UpDevice.h"
#import "DeviceFOTAStatusInfo.h"
#import "UpDeviceBLEHistoryInfo.h"
#import "UpDeviceOnlineStatus.h"
#import "UpDeviceOfflineCause.h"
/**
 * 用户缓存设备连接状态，属性和告警的实现类
 */
@interface UpDeviceCache : NSObject <UpDeviceStore>

- (instancetype)initWithState:(id<UpDeviceInfo>)deviceInfo state:(UpDeviceState)state;
+ (UpDeviceCache *)getUpDeviceCache:(id<UpDeviceInfo>)deviceInfo;
- (void)setState:(UpDeviceState)state;
- (void)setInfo:(id<UpDeviceInfo>)baseInfo;
- (void)setParent:(id)parent;
- (void)updateBaseInfo:(id<UpDeviceBaseInfo>)baseInfo;
- (void)setAttributeList:(NSArray *)collection;
- (void)setCautionList:(NSMutableArray<id<UpDeviceCaution>> *)collection;
- (void)setConnection:(UpDeviceConnection)connection;
- (void)setControlState:(UpDeviceControlState)state;
- (void)setFaultInformationCode:(NSInteger)code;
- (void)setRealOnlineStatus:(UpDeviceRealOnline)status;
- (void)setRealOnlineStateV2:(UpDeviceRealOnlineV2)stateV2;
- (void)setBleState:(UpDeviceConnection)state;
- (void)setWifiLocalState:(UpDeviceConnection)state;
- (void)setBleState:(UpDeviceConnection)state;
- (void)setSleepState:(UpDeviceSleepState)state;
- (void)setNetworkLevel:(UpDeviceNetworkLevel)level;
- (void)setSubDevList:(NSMutableArray<id<UpDevice>> *)collection;
- (void)setTheFOTAStatusInfo:(id<UpDeviceFOTAStatusInfo>)FOTAStatusInfo; //保存设备底板固件升级状态信息
- (void)setBLEHistoryInfo:(id<UpDeviceBLEHistoryInfo>)BLEHistoryInfo; //保存蓝牙历史数据
- (void)setBLERealData:(NSData *)data; //保存蓝牙实时数据
- (void)setDeviceOfflineCause:(UpDeviceOfflineCause)offlineCause;
- (void)setDeviceOfflineDays:(NSInteger)offlineDays;
- (void)setDeviceOnlyConfigState:(UpDeviceOnlyConfigState)onlyConfigState;
- (void)clearSubDevList; //清空子设备缓存
- (void)clearAttributeListValues; //清空属性列表的值
@end
