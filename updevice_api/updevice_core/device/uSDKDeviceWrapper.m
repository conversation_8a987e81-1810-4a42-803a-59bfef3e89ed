//
//  uSDKDeviceWrapper.m
//  updevice_api
//
//  Created by <PERSON><PERSON><PERSON>   on 2025/4/9.
//  Copyright © 2025 海尔优家智能科技（北京）有限公司. All rights reserved.
//

#import "uSDKDeviceWrapper.h"

@implementation uSDKDeviceWrapper

- (NSString *)description
{
    NSMutableString *description = [NSMutableString string];

    [description appendFormat:@"uSDKDeviceWrapper {\n"];

    [description appendFormat:@"  offlineCause: %@,\n", @(self.offlineCause).description];
    [description appendFormat:@"  offlineDays: %@,\n", @(self.offlineDays).description];
    [description appendFormat:@"  onlyConfigState: %@,\n", @(self.onlyConfigState).description];
    [description appendFormat:@"  connection: %@,\n", @(self.connection).description];
    [description appendFormat:@"  controlState: %@,\n", @(self.controlState).description];
    [description appendFormat:@"  faultInformationCode: %@,\n", @(self.faultInformationCode).description];
    [description appendFormat:@"  realOnlineStatus: %@,\n", @(self.realOnlineStatus).description];
    [description appendFormat:@"  realOnlineStateV2: %@,\n", @(self.realOnlineStateV2).description];
    [description appendFormat:@"  networkLevel: %@,\n", @(self.networkLevel).description];
    [description appendFormat:@"  bleState: %@,\n", @(self.bleState).description];
    [description appendFormat:@"  sleepState: %@,\n", @(self.sleepState).description];

    if (self.subDevInfoList && self.subDevInfoList.count > 0) {
        [description appendFormat:@"  subDevInfoList: %@\n", @(self.subDevInfoList.count).description];
    }
    else {
        [description appendString:@"  subDevInfoList: None,\n"];
    }

    if (self.attributeList && self.attributeList.count > 0) {
        [description appendFormat:@"  attributeList: %@\n", @(self.attributeList.count).description];
    }
    else {
        [description appendString:@"  attributeList: None,\n"];
    }

    if (self.cautionList && self.cautionList.count > 0) {
        [description appendFormat:@"  cautionList: %@\n", @(self.cautionList.count).description];
    }
    else {
        [description appendString:@"  cautionList: None,\n"];
    }

    [description appendString:@"}"];

    return description;
}
@end
