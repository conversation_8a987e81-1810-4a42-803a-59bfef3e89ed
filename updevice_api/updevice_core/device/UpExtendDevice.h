//
//  UpExtendDevice.h
//  updevice_api
//
//  Created by <PERSON><PERSON> on 2018/12/18.
//  Copyright © 2018 Haierfc. All rights reserved.
//

#import <Foundation/Foundation.h>
#import "UpDevice.h"
#import "UpDeviceProcessor.h"

/**
 扩展API状态
 */
typedef NS_ENUM(NSInteger, ExtApiState) {
    /**
     未知，当设备对象刚创建，还没有调用prepare方式时，不确定是否支持当前扩展API的状态
     */
    UNKNOWN = 0,
    /**
     不支持，调用prepare进行准备操作后，经查询当前设备不支持扩展API的状态
     */
    NOT_SUPPORT,
    /**
     支持，调用prepare进行准备操作后，经查询当前设备可支持扩展API的状态
     */
    SUPPORT,
    /**
     仅支持{@link UpDevice}定义的原生API
     */
    ONLY_NATIVE
};

/**
 * 扩展设备，基于{@link UpDevice}提供扩展特定API的能力
 *
 * @param <ExtApi> 扩展API接口
 */
@protocol UpExtendDevice <NSObject, UpDevice, UpDeviceProcessor>
/**
 * 获取扩展API接口
 *
 * @return 扩展API，当且仅当{@link UpExtendDevice#isExtApiPrepared()}返回true时，返回可用对象
 */
- (id<UpDevice>)getExtApi;
/**
 * 扩展API是否准备好标识
 * 当{@link UpExtendDevice#getExtApiState()}的值为{@link ExtApiState#SUPPORT}，且扩展API对象已创建时返回true
 *
 * @return true - 已准备好，扩展API可用，false - 未准备好
 */
- (BOOL)isExtApiPrepared;
/**
 * 获取当前设备是否支持指定的扩展API
 *
 * @return 扩展API状态，初始为{@link ExtApiState#UNKNOWN}，当调用prepare方法完成后，会变为其他状态
 */
- (ExtApiState)getExtApiState;
/**
 * 重新加载
 */
- (void)reload:(void (^)(UpDeviceResult *result))finishBlock;
/**
 * 准备扩展API，此接口的实现务必在本方法中进行扩展API的相关准备操作，以保证设备的准备流程完整可控
 */
- (void)onPrepareExtApi:(void (^)(UpDeviceResult *result))finishBlock;
/**
 * 释放扩展API，此接口的实现务必在本方法中进行扩展API的相关释放操作，以保证设备的释放流程完整可控
 */
- (void)onReleaseExtApi:(void (^)(UpDeviceResult *result))finishBlock;
/**
 * 重新加载扩展API，此接口的实现务必在本方法中进行扩展API的相关释放和准备操作，以保证设备的释放和准备流程完整可控
 */
- (void)onReloadExtApi:(void (^)(UpDeviceResult *result))finishBlock;
@end
