//
//  UpDeviceCacheManager.m
//  UPDevice
//
//  Created by <PERSON><PERSON><PERSON>   on 2025/3/7.
//

#import "UpDeviceCacheManager.h"
#import <upuserdomain/UpUserDomainHolder.h>
#import "UPStorage.h"
#import "UPDeviceLog.h"
#import "UPCore/UPFunctionToggle.h"
#import "DeviceAttribute.h"
#import "UpDeviceCardManager.h"

#pragma mark 全局常量定义
static NSString *const kDeviceCachePrefix = @"device_cache";
static NSString *const kDeviceReportStorageCacheToggle = @"deviceReportStorageCache";
static NSString *const kStorageCacheAllowed = @"storageCacheAllowed";
static NSString *const kDeviceCardSortId = @"DI-Product.cardSort";

/// 缓存设备数定义
static NSUInteger kDeviceMaxCount = 20;

@interface UpDeviceCacheManager () <UpUserDomainObserver, UpDeviceChangeObserver, Listener>
/**
 * 设备缓存映射表
 * 使用线程安全的字典存储设备缓存信息
 * key: 设备ID (NSString *)
 * value: 设备缓存对象 (NSDictionary *)
 */
@property (nonatomic, strong) NSMutableDictionary<NSString *, NSDictionary *> *readDeviceCacheMap;

// 待写入缓存数据集合
@property (nonatomic, strong) NSMutableDictionary<NSString *, NSDictionary *> *writeDeviceCacheMap;

// 串行任务队列
@property (nonatomic, strong) dispatch_queue_t serialQueue;

// 当前家庭Top kDeviceMaxCount缓存
@property (nonatomic, copy) NSMutableArray<NSString *> *cacheTop20Devices;
// 用来订阅设备属性上报
@property (nonatomic, copy) id<UpDeviceCenter> deviceManager;

@property (nonatomic, copy) NSString *lastSubscribeFamilyId;
@end

@implementation UpDeviceCacheManager

- (instancetype)initWith:(id<UpDeviceCenter>)deviceManager
{
    self = [super init];
    if (self) {
        _deviceManager = deviceManager;
        _readDeviceCacheMap = [NSMutableDictionary dictionary];
        _writeDeviceCacheMap = [NSMutableDictionary dictionary];
        _cacheTop20Devices = [NSMutableArray array];
        _serialQueue = dispatch_queue_create("com.updevice.cache.access", DISPATCH_QUEUE_SERIAL);
        [self startTasks];
    }
    return self;
}

#pragma mark UserDomain observer methods
- (void)onCurrentFamilyDidChanged:(id<UDUserDelegate>)user
{
    dispatch_async(self.serialQueue, ^{
      [self.readDeviceCacheMap removeAllObjects];

      [self unsubscribeLastDeviceChange];
      [UPStorage deleteNode:kDeviceCachePrefix];

      [self onDeviceListChange:[self.deviceManager getDeviceList]];
    });
}

- (void)onLogOut:(id<UpUserDomainDelegate>)userDomain
{
    dispatch_async(self.serialQueue, ^{
      [self unsubscribeLastDeviceChange];
      [UPStorage deleteNode:kDeviceCachePrefix];
    });
}

- (void)unsubscribeLastDeviceChange
{
    if (![self.lastSubscribeFamilyId isKindOfClass:[NSString class]]) {
        return;
    }
    [self.deviceManager unsubscribeDeviceChangeWithFamilyId:self.lastSubscribeFamilyId observer:self];
    self.lastSubscribeFamilyId = nil;
    [self.writeDeviceCacheMap removeAllObjects];
}

- (void)subscribeDeviceChangeWithFamilyId:(NSString *)familyId
{
    if (![familyId isKindOfClass:[NSString class]]) {
        return;
    }
    NSArray *deviceIds = [UpDeviceCardManager filterAndSortDeviceCardList:[self.deviceManager getDeviceList] with:familyId limitCount:kDeviceMaxCount];

    [self.cacheTop20Devices removeAllObjects];
    [self.cacheTop20Devices addObjectsFromArray:deviceIds];

    self.lastSubscribeFamilyId = familyId;
    [self.deviceManager subscribeDeviceChangeWithFamilyId:familyId observer:self];
}

#pragma mark UpDeviceCenter observer methods
- (void)onDeviceListChange:(NSArray<id<UpDevice>> *)deviceList
{
    if (deviceList.count == 0) {
        return;
    }

    NSString *familyId = UpUserDomainHolder.instance.userDomain.user.currentFamily.familyId;
    if (![familyId isKindOfClass:[NSString class]]) {
        return;
    }
    dispatch_async(self.serialQueue, ^{
      [self unsubscribeLastDeviceChange];
      [self subscribeDeviceChangeWithFamilyId:familyId];
    });
}
/**
 * 根据指定条件过滤设备列表
 *
 * @param deviceList 原始设备列表
 * @param filterBlock 过滤条件的 block，返回 YES 表示保留该设备，返回 NO 表示过滤掉
 * @return 过滤后的设备列表
 */
- (NSArray<id<UDDeviceDelegate>> *)filterDeviceList:(NSArray<id<UDDeviceDelegate>> *)deviceList
                                        filterBlock:(BOOL (^)(id<UDDeviceDelegate> device))filterBlock
{
    if (!deviceList || deviceList.count == 0 || !filterBlock) {
        return deviceList;
    }

    return [deviceList filteredArrayUsingPredicate:[NSPredicate predicateWithBlock:^BOOL(id<UDDeviceDelegate> device, NSDictionary *bindings) {
                         return filterBlock(device);
                       }]];
}

#pragma mark 启动任务
- (void)startTasks
{
    NSString *keypath = [NSString stringWithFormat:@"%@.%@", kDeviceReportStorageCacheToggle, kStorageCacheAllowed];
    BOOL toggleState = [[UPFunctionToggle shareInstance] boolForKey:keypath defaultValue:NO];
    //判断是否使用缓存方案
    if (!toggleState) {
        UPLogError(kUPDevicePrefix, @"%s[%d] UpDeviceCacheManager startTasks 失败! (Read configuration file without caching)", __PRETTY_FUNCTION__, __LINE__);
        return;
    }
    // 添加设备列表变化监听
    [self.deviceManager attach:self immediate:false];

    // 添加设备变化监听
    [[UpUserDomainHolder instance].userDomain addObserver:self];

    // 开始读取缓存
    [self readLastDevicesCache];

    // 启动定时器，1s 写入一次数据
    [self scheduleTask:1 selector:@selector(updateDeviceCache)];
}

#pragma mark Task1: 读取上一次的缓存
- (void)readLastDevicesCache
{
    dispatch_async(self.serialQueue, ^{
      NSArray<UPStringNode *> *values = [UPStorage getStringSubNodes:kDeviceCachePrefix];
      if (!values || values.count == 0) {
          return;
      }

      [values enumerateObjectsUsingBlock:^(UPStringNode *_Nonnull obj, NSUInteger idx, BOOL *_Nonnull stop) {
        NSData *data = [obj.value dataUsingEncoding:NSUTF8StringEncoding];
        NSError *error = nil;
        NSDictionary *dic = [NSJSONSerialization JSONObjectWithData:data options:NSJSONReadingAllowFragments error:&error];
        if ([dic isKindOfClass:[NSDictionary class]] && !error) {
            [self.readDeviceCacheMap setObject:dic forKey:obj.name];
        }
        else {
            UPLogError(kUPDevicePrefix, @"%s[%d] UpDeviceCacheManager loadDeviceCacheTask 失败！error:%@", __PRETTY_FUNCTION__, __LINE__, error.description);
        }
      }];
    });
}

#pragma mark Task2: 开启定时任务
- (void)scheduleTask:(NSTimeInterval)duration selector:(SEL)selector
{
    NSTimer *timer = [NSTimer timerWithTimeInterval:duration
                                             target:self
                                           selector:selector
                                           userInfo:nil
                                            repeats:YES];
    [[NSRunLoop mainRunLoop] addTimer:timer forMode:NSRunLoopCommonModes];
}

#pragma mark Task3: 更新缓存
- (void)updateDeviceCache
{
    if (self.writeDeviceCacheMap.allKeys == 0) {
        return;
    }

    dispatch_async(self.serialQueue, ^{
      [self.writeDeviceCacheMap enumerateKeysAndObjectsUsingBlock:^(NSString *_Nonnull key, NSDictionary *_Nonnull obj, BOOL *_Nonnull stop) {
        if (![obj isKindOfClass:[NSDictionary class]] || obj.count == 0) {
            return;
        }
        NSError *error;
        NSData *jsonData = [NSJSONSerialization dataWithJSONObject:obj options:NSJSONWritingFragmentsAllowed error:&error];
        NSString *metaData = [[NSString alloc] initWithData:jsonData encoding:NSUTF8StringEncoding];
        if (!error && metaData && metaData.length > 0) {
            // 写入缓存
            [UPStorage putStringValue:metaData
                                 name:[NSString stringWithFormat:@"%@/%@", kDeviceCachePrefix, key]];
        }
        else {
            UPLogError(kUPDevicePrefix, @"%s[%d] UpDeviceCacheManager updateDeviceCache 失败！error:%@", __PRETTY_FUNCTION__, __LINE__, error.description);
        }
      }];

      [self.writeDeviceCacheMap removeAllObjects];
    });
}
#pragma mark 外部方法：按照设备ID读取缓存
- (nullable id)readDeviceCacheWith:(NSString *)deviceId
{
    if (![deviceId isKindOfClass:[NSString class]]) {
        return nil;
    }

    NSDictionary *dic = [self.readDeviceCacheMap objectForKey:[NSString stringWithFormat:@"%@/%@", kDeviceCachePrefix, deviceId]];

    if (![dic isKindOfClass:[NSDictionary class]]) {
        UPLogDebug(kUPDevicePrefix, @"%s[%d] Failed to read device cache, dic is not NSDictionary class for deviceId :%@", __PRETTY_FUNCTION__, __LINE__, deviceId);
        return nil;
    }
    NSMutableDictionary *deviceInfos = [NSMutableDictionary dictionary];
    NSDictionary *atts = dic[kDeviceInfoKey_attributes];
    if ([atts isKindOfClass:[NSDictionary class]]) {
        NSMutableArray<id<UpDeviceAttribute>> *attributes = [NSMutableArray array];
        [atts enumerateKeysAndObjectsUsingBlock:^(id _Nonnull key, id _Nonnull obj, BOOL *_Nonnull stop) {
          if ([key isKindOfClass:[NSString class]] && [obj isKindOfClass:[NSString class]]) {
              DeviceAttribute *deviceAttr = [[DeviceAttribute alloc] init];
              deviceAttr.name = key;
              deviceAttr.value = obj;
              [attributes addObject:deviceAttr];
          }
        }];
        deviceInfos[kDeviceInfoKey_attributes] = attributes;
    }
    return [NSDictionary dictionaryWithDictionary:deviceInfos];
}

- (void)writeDeviceCacheWith:(NSString *)deviceId device:(id<UpDevice>)device
{
    if (!deviceId || !device) {
        UPLogDebug(kUPDevicePrefix, @"%s[%d] Invalid parameters (deviceId == nil || device == nil)", __PRETTY_FUNCTION__, __LINE__);
        return;
    }

    // 洗衣机设备不写入缓存
    if ([UpDeviceCardManager isWashingMachineWithTypeId:device.getInfo.typeId]) {
        return;
    }

    // TOP20 和 队列中是否存在这个设备待写入
    if (![self.cacheTop20Devices containsObject:device.getInfo.deviceId]) {
        return;
    }

    NSMutableDictionary *deviceInfos = [NSMutableDictionary dictionary];

    NSMutableDictionary *attributes = [NSMutableDictionary dictionary];
    NSArray<id<UpDeviceAttribute>> *list = [device getAttributeList];

    if ([list isKindOfClass:[NSArray class]] && list.count > 0) {
        [list enumerateObjectsUsingBlock:^(id<UpDeviceAttribute> attr, NSUInteger idx, BOOL *stop) {
          if ([attr name] && [attr value]) {
              attributes[[attr name]] = [attr value];
          }
        }];
        deviceInfos[kDeviceInfoKey_attributes] = attributes;
    }
    dispatch_async(self.serialQueue, ^{
      [self.writeDeviceCacheMap setObject:deviceInfos forKey:deviceId];
    });
}

#pragma mark 标记设备上报状态
- (void)onFamilyDevicesPropertyChanged:(NSArray<id<UpDevice>> *_Nonnull)devices
{
    NSArray<id<UpDevice>> *deviceCopy = [devices mutableCopy];
    [deviceCopy enumerateObjectsUsingBlock:^(id<UpDevice> _Nonnull obj, NSUInteger idx, BOOL *_Nonnull stop) {
      [self writeDeviceCacheWith:obj.getInfo.deviceId device:obj];
    }];
}

@end
