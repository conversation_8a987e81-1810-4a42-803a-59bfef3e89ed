//
//  UpDevice.h
//  updevice_api
//
//  Created by <PERSON><PERSON> on 2018/12/18.
//  Copyright © 2018 Haierfc. All rights reserved.
//

#import <Foundation/Foundation.h>
#import "UpDeviceBrokerHolder.h"
#import "UpDeviceStore.h"
#import "UpDeviceListener.h"
#import "UpDeviceReceiver.h"
#import "UpDeviceFOTA.h"
#import "UpDeviceBLE.h"
#import "UpDeviceSub.h"
#import "UpDeviceFocus.h"
#import "UpDeviceNetWorkQuality.h"
#import "UPDeviceNetType.h"
#import "UpDeviceConfigState.h" //单元测试版本需要删掉
#import "UpDeviceGroupOptDeleagte.h"
#import "UpAttachResourceDelegate.h"
#import "UpDeviceCardInfo.h"
#import "UpDeviceCardManager.h"
#import "UpDeviceCommandIntercepter.h"

@protocol UpDevice <NSObject, UpDeviceStore, UpDeviceBrokerHolder, UpDeviceFOTA, UpDeviceBLE, UpDeviceSub, UpDeviceFocus, UpDeviceNetWorkQuality, UPDeviceNetType, UpDeviceGroupOptDeleagte, UpAttachResourceDelegate, UpDeviceCard>
/**
 * 唯一ID
 * <p>因目前不在支持多协议，此ID逐渐弃用，新开发时应避免使用</p>
 *
 * @return 协议名称拼接设备ID的字符串
 * @deprecated 将在7.0版本删除
 */
- (NSString *)uniqueId;
/**
 * 获取是否就绪标识
 * <p>此标识用于标识与网器已建立连接，如使用了{@link UpExtendDevice}，还标识扩展API也已准备好</p>
 *
 * @return true - 已就绪，false - 未就绪
 */
- (BOOL)isReady;
/**
 * 准备，用于实际连接网器的方法，如使用了{@link UpExtendDevice}，此方法里需调用{@link UpExtendDevice#onPrepareExtApi()}
 *
 * @return 可订阅的操作结果
 */
- (void)prepare:(void (^)(UpDeviceResult *result))finishBlock;

/**
 * 准备，用于快连设备连接网器的方法
 */
- (void)prepareWithoutConnect:(void (^)(UpDeviceResult *result))finishBlock;

/**
 * 释放，用于实际断开网器的方法，如使用了{@link UpExtendDevice}，此方法里需调用{@link UpExtendDevice#onReleaseExtApi()} ()}
 *
 * @return 可订阅的操作结果
 */
- (void)release:(void (^)(UpDeviceResult *result))finishBlock;

/**
 * 释放，用于快连设备实际断开网器的方法
 */
- (void)releaseWithoutConnect:(void (^)(UpDeviceResult *result))finishBlock;
/**
 * 注册设备状态监听器
 *
 * @param listener 监听器
 */
- (void)attach:(id<UpDeviceListener>)listener;
/**
 * 取消注册设备状态监听器
 *
 * @param listener 监听器
 */
- (void)detach:(id<UpDeviceListener>)listener;
- (void)detachAll;
/**
 * 注册设备数据接收器
 *
 * @param receiver 接收器
 */
- (void)attachReceiver:(id<UpDeviceReceiver>)receiver;
/**
 * 取消注册设备数据接收器
 *
 * @param receiver 接收器
 */
- (void)detachReceiver:(id<UpDeviceReceiver>)receiver;
/**
 * 指令下发
 *
 * @param command 操作名称
 * @param timeout 超时时间，单位：秒
 * @param finishBlock 可订阅的操作结果
 */
- (void)executeCommand:(id<UpDeviceCommand>)command timeout:(NSTimeInterval)timeout finishBlock:(void (^)(UpDeviceResult *result))finishBlock;

/**
 * 指令下发，返回带有 traceId 的操作结果
 *
 * @param command 操作名称
 * @param timeout 超时时间，单位：秒
 * @param finishBlock 可订阅的操作结果
 * @since 7.14.0
 */
- (void)executeCommandWithResult:(id<UpDeviceCommand>)command timeout:(NSTimeInterval)timeout finishBlock:(void (^)(UpDeviceResult *result))finishBlock;

/**
 * 获取配置状态码
 *
 * @return 配置状态码
 */
- (UpDeviceConfigState)configState;
/**
 * 错误的方法，UpDevice怎么能依赖逻辑引擎
 *
 * @return 错误的结果
 * @deprecated 6.0删除
 */
- (NSArray *)getEngineAttributeList; //单元测试版本需要删掉
/**
 * 错误的方法，UpDevice怎么能依赖逻辑引擎
 *
 * @return 错误的结果
 * @deprecated 6.0删除
 */
- (NSArray *)getEngineCautionList; //单元测试版本需要删掉

/**
 * 连接usdk设备
 *
 * @param finishBlock 操作结果
 * @since 6.2.9
 */
- (void)connectDevice:(void (^)(UpDeviceResult *))finishBlock;

/**
 * 断开usdk设备连接
 *
 * @param finishBlock 操作结果
 * @since 6.2.9
 */
- (void)disConnectDevice:(void (^)(UpDeviceResult *))finishBlock;
/**
 * 快连专用连接usdk设备
 *
 * @param finishBlock 操作结果
 * @since 6.6.0
 */
- (void)QCConnectDevice:(void (^)(UpDeviceResult *))finishBlock;

/**
 * 快连专用断开usdk设备连接
 *
 * @param finishBlock 操作结果
 * @since 6.6.0
 */
- (void)QCDisConnectDevice:(void (^)(UpDeviceResult *))finishBlock;

@optional
/**
 * 获取命令拦截器数组
 *
 * @return 命令拦截器数组
 */
- (NSArray<id<UpDeviceCommandIntercepter>> *)commandIntercepters;

/**
 * 添加命令拦截器
 *
 * @param intercepter 命令拦截器
 */
- (void)addCommandIntercepter:(id<UpDeviceCommandIntercepter>)intercepter;

@end
