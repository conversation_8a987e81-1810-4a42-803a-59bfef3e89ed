//
//  UpDeviceBase.m
//  updevice_api
//
//  Created by <PERSON><PERSON> on 2018/12/19.
//  Copyright © 2018 Haierfc. All rights reserved.
//

#import "UpDeviceBase.h"
#import "UpDeviceBroker.h"
#import "UpDeviceReporter.h"
#import "UpDeviceCache.h"
#import "UpDevice.h"
#import "UpDeviceReportListener.h"
#import "UpDeviceHelper.h"
#import "UpCommonDevice.h"
#import "DeviceInfo.h"
#import "UPDeviceLog.h"
#import "UpDeviceInjection.h"

@interface UpDeviceBase () <UpDeviceReportListener>

@property (nonatomic, strong) NSString *uniqueId;
@property (nonatomic, strong) id<UpDeviceFactory> factory;
@property (nonatomic, strong) UpDeviceCache *cache;
@property (nonatomic, strong) UpDeviceReporter *reporter;
@property (nonatomic, strong) id<UpDeviceBroker> broker;
@property (nonatomic, strong) id<UpDeviceReportListener> deviceReportListener;
@property (nonatomic, assign) BOOL resourceBool;
@property (nonatomic, assign) BOOL attachBool; //是否已经attach
@property (nonatomic, assign) BOOL withoutConnect;

@end

@implementation UpDeviceBase
+ (UpDeviceBase *)UpDeviceBase:(id<UpDeviceInfo>)deviceInfo broker:(id<UpDeviceBroker>)broker factory:(id<UpDeviceFactory>)factory
{
    NSString *uniqueId = [UpDeviceHelper genUniqueId:deviceInfo.protocol DeviceId:deviceInfo.deviceId];
    return [[UpDeviceBase alloc] initWithDeviceInfo:uniqueId deviceInfo:deviceInfo broker:broker factory:factory];
}

- (instancetype)initWithDeviceInfo:(NSString *)uniqueId deviceInfo:(id<UpDeviceInfo>)deviceInfo broker:(id<UpDeviceBroker>)broker factory:(id<UpDeviceFactory>)factory
{
    if (self = [super init]) {
        self.uniqueId = uniqueId;
        self.broker = broker;
        self.factory = factory;
        self.cache = [UpDeviceCache getUpDeviceCache:deviceInfo];
        self.reporter = [[UpDeviceReporter alloc] init];
        self.deviceReportListener = self;
    }
    return self;
}

- (BOOL)isSubDev
{
    BOOL flag = NO;
    id<UpDevice> updev = [self getParent];
    if (updev) {
        flag = YES;
    }
    id<UpDeviceInfo> info = [self getInfo];
    if (!flag && info != nil && info.subDevNo != nil) {
        flag = YES;
    }
    return flag;
}

- (id<UpDeviceInfo>)getInfo
{
    return [_cache getInfo];
}

- (id)getParent
{
    return [_cache getParent];
}

- (id<UpDevice>)getDevice
{
    return self;
}

- (id<UpDeviceFactory>)getFactory
{
    return self.factory;
}

- (void)setState:(UpDeviceState)state
{
    [_cache setState:state];
}

- (NSString *)protocol
{
    return [self getInfo].protocol;
}

- (NSString *)deviceId
{
    return [self getInfo].deviceId;
}

- (NSString *)uniqueId
{
    return _uniqueId;
}

- (BOOL)isModuleNeedOTA
{
    id<UpDeviceBroker> broke = [self getBroker];
    id<UpDeviceToolkit> kit = [broke getToolkit];
    return [kit isModuleNeedOta:self.deviceId];
}

- (BOOL)isReady
{
    if ([self getState] == UpDeviceState_PREPARED) {
        return YES;
    }
    return NO;
}

- (UpDeviceState)getState
{
    return [_cache getState];
}
- (void)resetLogicEngineConfigState
{
    [self setState:UpDeviceState_RELEASED];
    self.resourceBool = NO;
}
- (void)prepare:(void (^)(UpDeviceResult *result))finishBlock
{
    NSString *deviceId = self.deviceId;
    BOOL stateBool = [self checkState:UpDeviceState_RELEASED];
    if (stateBool == NO) {
        self.resourceBool = NO;
        [self setState:UpDeviceState_RELEASED];
        UPLogError(kUPDevicePrefix, @"%s[%d]设备(%@)当前状态错误！", __PRETTY_FUNCTION__, __LINE__, deviceId);
        UpDeviceResult *result = [UpDeviceResult UpDeviceResult:ErrorCode_FAILURE extraData:nil];
        finishBlock(result);
        return;
    }
    [self setState:UpDeviceState_PREPARING];
    UPLogDebug(kUPDevicePrefix, @"%s[%d]开始准备设备(%@)！", __PRETTY_FUNCTION__, __LINE__, deviceId);
    __weak typeof(self) weakSelf = self;
    [self attachPrepareWithJudge:^(UpDeviceResult *result) {
      [[UpDeviceInjection getInstance].deviceManager GIOCountAttachTime];
      __strong typeof(weakSelf) strongSelf = weakSelf;
      if (result.isSuccessful) {
          NSBlockOperation *configOp = [NSBlockOperation blockOperationWithBlock:^{
            [strongSelf onPrepareExtApiWithJudge:^(UpDeviceResult *extResult) {
              if (extResult.isSuccessful) {
                  [strongSelf setState:UpDeviceState_PREPARED];
                  [strongSelf onPrepared];
                  if (finishBlock) {
                      finishBlock(extResult);
                  }
              }
              else {
                  UPLogError(kUPDevicePrefix, @"%s[%d]device(%@)prepare logcienStart！ error:%@", __PRETTY_FUNCTION__, __LINE__, deviceId, extResult.extraInfo);
                  [strongSelf setState:UpDeviceState_RELEASED];
                  if (finishBlock) {
                      finishBlock(extResult);
                  }
              }
            }];
          }];
          [[UpDeviceInjection getInstance].deviceManager addConfigOperation:configOp];
      }
      else {
          UPLogError(kUPDevicePrefix, @"%s[%d]device(%@)  prepareAttach fail error:%@", __PRETTY_FUNCTION__, __LINE__, deviceId, result.extraInfo);
          [strongSelf setState:UpDeviceState_RELEASED];
          finishBlock(result);
      }
    }];
}

- (void)attachPrepareWithJudge:(void (^)(UpDeviceResult *result))completeion
{
    if (self.attachBool) {
        UPLogDebug(kUPDevicePrefix, @"%s[%d]device(%@) is already attach！", __PRETTY_FUNCTION__, __LINE__, [self deviceId]);
        UpDeviceResult *result = [UpDeviceResult UpDeviceResult:ErrorCode_SUCCESS extraData:nil];
        if (completeion) {
            completeion(result);
        }
    }
    else {
        [self attachPrepare:[self deviceId]
                     finish:^(UpDeviceResult *result) {
                       if (result.isSuccessful) {
                           self.attachBool = YES;
                       }
                       if (completeion) {
                           completeion(result);
                       }
                     }];
    }
}

- (void)onPrepareExtApiWithJudge:(void (^)(UpDeviceResult *))completeion
{
    if (self.resourceBool) {
        UPLogDebug(kUPDevicePrefix, @"%s[%d]device(%@) is already PrepareExt！", __PRETTY_FUNCTION__, __LINE__, [self deviceId]);
        UpDeviceResult *result = [UpDeviceResult UpDeviceResult:ErrorCode_SUCCESS extraData:nil];
        if (completeion) {
            completeion(result);
        }
    }
    else {
        [self onPrepareExtApi:^(UpDeviceResult *result) {
          [self sendCustomNotfiDeviceChange:EVENT_BASE_INFO_CHANGE];
          if (result.isSuccessful) {
              self.resourceBool = YES;
          }
          if (completeion) {
              completeion(result);
          }
        }];
    }
}


- (void)attachPrepare:(NSString *)deviceId finish:(void (^)(UpDeviceResult *result))finishBlock
{
    id<UpDeviceBroker> broke = [self getBroker];
    if (self.withoutConnect) {
        [broke attachWithoutConnectDevice:[self deviceId]
                           reportListener:self.deviceReportListener
                                immediate:YES
                              finishBlock:^(UpDeviceResult *result) {
                                finishBlock(result);
                              }];
    }
    else {
        [broke attachDevice:[self deviceId]
             reportListener:self.deviceReportListener
                  immediate:YES
                finishBlock:^(UpDeviceResult *result) {
                  finishBlock(result);
                }];
    }
}

- (void)onPrepared
{
    UPLogWarning(kUPDevicePrefix, @"%s[%d]该方法为空方法，不应该被调用！", __PRETTY_FUNCTION__, __LINE__);
}

- (id<UpDeviceBroker>)getBroker
{
    return self.broker;
}

- (void)onReleased
{
    UPLogWarning(kUPDevicePrefix, @"%s[%d]该方法为空方法，不应该被调用！", __PRETTY_FUNCTION__, __LINE__);
}

- (void)release:(void (^)(UpDeviceResult *result))finishBlock
{
    NSString *deviceId = self.deviceId;
    UPLogDebug(kUPDevicePrefix, @"%s[%d]设备(%@)开始release！", __PRETTY_FUNCTION__, __LINE__, deviceId);
    BOOL stateBool = [self checkState:UpDeviceState_PREPARED];
    if (stateBool == NO) {
        [self setState:UpDeviceState_PREPARED];
        UPLogError(kUPDevicePrefix, @"%s[%d]设备(%@)当前状态错误！", __PRETTY_FUNCTION__, __LINE__, deviceId);
        UpDeviceResult *result = [UpDeviceResult UpDeviceResult:ErrorCode_FAILURE extraData:nil];
        if (finishBlock) {
            finishBlock(result);
        }
    }

    __weak typeof(self) weakSelf = self;


    [self onReleaseExtApi:^(UpDeviceResult *result) {
      if (result.isSuccessful) {
          id<UpDeviceBroker> broke = [weakSelf getBroker];
          if (self.withoutConnect) {
              [broke detachWithoutConnectDevice:[weakSelf deviceId]
                                 reportListener:weakSelf.deviceReportListener
                                    finishBlock:^(UpDeviceResult *result) {
                                      if (result.isSuccessful) {
                                          UPLogDebug(kUPDevicePrefix, @"%s[%d]快连设备(%@)release成功！", __PRETTY_FUNCTION__, __LINE__, deviceId);
                                          weakSelf.attachBool = NO;
                                          [weakSelf setState:UpDeviceState_RELEASED];
                                          [weakSelf onReleased];
                                      }
                                      else {
                                          UPLogError(kUPDevicePrefix, @"%s[%d]快连设备(%@)release失败！error:%@", __PRETTY_FUNCTION__, __LINE__, deviceId, result.extraInfo);
                                          [weakSelf setState:UpDeviceState_PREPARED];
                                      }
                                      finishBlock(result);
                                    }];
          }
          else {
              [broke detachDevice:[weakSelf deviceId]
                   reportListener:weakSelf.deviceReportListener
                      finishBlock:^(UpDeviceResult *result) {
                        if (result.isSuccessful) {
                            UPLogDebug(kUPDevicePrefix, @"%s[%d]设备(%@)release成功！", __PRETTY_FUNCTION__, __LINE__, deviceId);
                            weakSelf.attachBool = NO;
                            [weakSelf setState:UpDeviceState_RELEASED];
                            [weakSelf onReleased];
                        }
                        else {
                            UPLogError(kUPDevicePrefix, @"%s[%d]设备(%@)release失败！error:%@", __PRETTY_FUNCTION__, __LINE__, deviceId, result.extraInfo);
                            [weakSelf setState:UpDeviceState_PREPARED];
                        }
                        finishBlock(result);
                      }];
          }
      }
      else {
          UPLogError(kUPDevicePrefix, @"%s[%d]设备(%@)release失败！error:%@", __PRETTY_FUNCTION__, __LINE__, deviceId, result.extraInfo);
          [weakSelf setState:UpDeviceState_PREPARED];
          finishBlock(result);
      }
    }];
}
- (void)reload:(void (^)(UpDeviceResult *))finishBlock
{
    NSString *deviceId = self.deviceId;
    UPLogDebug(kUPDevicePrefix, @"%s[%d]设备(%@)开始reload！", __PRETTY_FUNCTION__, __LINE__, deviceId);
    [self setState:UpDeviceState_RELOADING];
    [self onReloadExtApi:^(UpDeviceResult *result) {
      if (result.isSuccessful) {
          UPLogDebug(kUPDevicePrefix, @"%s[%d]设备(%@)reload成功！", __PRETTY_FUNCTION__, __LINE__, deviceId);
          [self setState:UpDeviceState_PREPARED];
      }
      else {
          UPLogError(kUPDevicePrefix, @"%s[%d]设备(%@)reload失败！error:%@", __PRETTY_FUNCTION__, __LINE__, deviceId, result.extraInfo);
      }
    }];
}

- (id<UpDevice>)getExtApi
{
    UPLogWarning(kUPDevicePrefix, @"%s[%d]该方法为空方法，不应该被调用！", __PRETTY_FUNCTION__, __LINE__);
    return nil;
}

- (BOOL)isExtApiPrepared
{
    UPLogWarning(kUPDevicePrefix, @"%s[%d]该方法为空方法，不应该被调用！", __PRETTY_FUNCTION__, __LINE__);
    return NO;
}

- (void)onReleaseExtApi:(void (^)(UpDeviceResult *))finishBlock
{
    UPLogWarning(kUPDevicePrefix, @"%s[%d]该方法为空方法，不应该被调用！", __PRETTY_FUNCTION__, __LINE__);
}

- (void)onReloadExtApi:(void (^)(UpDeviceResult *))finishBlock
{
    UPLogWarning(kUPDevicePrefix, @"%s[%d]该方法为空方法，不应该被调用！", __PRETTY_FUNCTION__, __LINE__);
}

- (void)onPrepareExtApi:(void (^)(UpDeviceResult *))finishBlock
{
    UPLogWarning(kUPDevicePrefix, @"%s[%d]该方法为空方法，不应该被调用！", __PRETTY_FUNCTION__, __LINE__);
}

- (void)prepareWithoutConnect:(void (^)(UpDeviceResult *result))finishBlock
{
    self.withoutConnect = YES;
    [self prepare:finishBlock];
}

- (void)releaseWithoutConnect:(void (^)(UpDeviceResult *result))finishBlock
{
    self.withoutConnect = YES;
    [self release:finishBlock];
}

- (void)attach:(id<UpDeviceListener>)listener
{
    if (listener == nil) {
        return;
    }
    [_reporter add:listener];
    if ([NSThread isMainThread]) {
        [listener onDeviceReport:EVENT_ATTACHED device:[self getDevice]];
    }
    else {
        dispatch_async(dispatch_get_main_queue(), ^{
          [listener onDeviceReport:EVENT_ATTACHED device:[self getDevice]];
        });
    }
}

- (void)detach:(id<UpDeviceListener>)listener
{
    [_reporter remove:listener];
}

- (void)detachAll
{
    [_reporter removeAll];
}

- (void)attachReceiver:(id<UpDeviceReceiver>)receiver
{
    if (receiver == nil) {
        return;
    }
    [_reporter addReceiver:receiver];
}
- (void)detachReceiver:(id<UpDeviceReceiver>)receiver
{
    [_reporter removeReceiver:receiver];
}
- (void)executeCommand:(id<UpDeviceCommand>)command timeout:(NSTimeInterval)timeout finishBlock:(void (^)(UpDeviceResult *))finishBlock
{
    NSString *deviceId = self.deviceId;
    NSString *groupName = nil;
    NSDictionary *attributes = nil;
    if ([command conformsToProtocol:@protocol(UpDeviceCommand)]) {
        groupName = command.groupName;
        attributes = command.attributes;
        UPLogDebug(kUPDevicePrefix, @"%s[%d]设备(%@)执行命令！groupName:%@,cmds:%@", __PRETTY_FUNCTION__, __LINE__, deviceId, groupName, attributes);
    }
    else {
        UPLogError(kUPDevicePrefix, @"%s[%d]设备(%@)执行命令的命令参数对象未实现UpDeviceCommand协议！command:%@", __PRETTY_FUNCTION__, __LINE__, deviceId, command);
    }
    id<UpDeviceBroker> broke = [self getBroker];
    id<UpDeviceToolkit> kit = [broke getToolkit];
    [kit executeDeviceCommand:self.deviceId
                      command:command
                      timeout:timeout
                  finishBlock:^(UpDeviceResult *result) {
                    if (result.isSuccessful) {
                        [self setState:UpDeviceState_PREPARED];
                    }
                    finishBlock(result);
                  }];
}

- (void)executeCommandWithResult:(id<UpDeviceCommand>)command timeout:(NSTimeInterval)timeout finishBlock:(void (^)(UpDeviceResult *))finishBlock
{
    NSString *deviceId = self.deviceId;
    NSString *groupName = nil;
    NSDictionary *attributes = nil;
    if ([command conformsToProtocol:@protocol(UpDeviceCommand)]) {
        groupName = command.groupName;
        attributes = command.attributes;
        UPLogDebug(kUPDevicePrefix, @"%s[%d]设备(%@)执行命令！groupName:%@,cmds:%@", __PRETTY_FUNCTION__, __LINE__, deviceId, groupName, attributes);
    }
    else {
        UPLogError(kUPDevicePrefix, @"%s[%d]设备(%@)执行命令的命令参数对象未实现UpDeviceCommand协议！command:%@", __PRETTY_FUNCTION__, __LINE__, deviceId, command);
    }
    id<UpDeviceBroker> broke = [self getBroker];
    id<UpDeviceToolkit> kit = [broke getToolkit];
    [kit executeCommandWithResult:self.deviceId
                          command:command
                          timeout:timeout
                      finishBlock:^(UpDeviceResult *result) {
                        if (result.isSuccessful) {
                            [self setState:UpDeviceState_PREPARED];
                        }
                        finishBlock(result);
                      }];
}

- (BOOL)checkState:(UpDeviceState)target
{
    UpDeviceState state = [self getState];
    if (state == UpDeviceState_PREPARED || state == UpDeviceState_RELEASING || state == UpDeviceState_RELOADING) {
        UPLogWarning(kUPDevicePrefix, @"%s[%d]throw new UpDeviceException WrongStateException!", __PRETTY_FUNCTION__, __LINE__);
    }
    BOOL flag;
    if (target == state) {
        flag = YES;
    }
    else {
        flag = NO;
    }
    return flag;
}

- (UpDeviceConnection)getConnection
{
    return [_cache getConnection];
}

- (UpDeviceControlState)getControlState
{
    return [_cache getControlState];
}

- (NSInteger)getFaultInformationCode
{
    return [_cache getFaultInformationCode];
}

- (UpDeviceRealOnline)getDeviceOnlineStatus
{
    return [_cache getDeviceOnlineStatus];
}

- (UpDeviceRealOnlineV2)getDeviceOnlineStateV2
{

    return [_cache getDeviceOnlineStateV2];
}

- (UpDeviceSleepState)getSleepState
{
    return [_cache getSleepState];
}

- (UpDeviceNetworkLevel)getNetworkLevel
{
    return [_cache getNetworkLevel];
}

- (UpDeviceConnection)getWifiLocalState
{
    return [_cache getWifiLocalState];
}

- (UpDeviceConnection)getBleState
{
    return [_cache getBleState];
}

- (void)setWifiLocalState:(UpDeviceConnection)state
{
    [_cache setWifiLocalState:state];
}

- (void)setControlState:(UpDeviceControlState)state
{
    [_cache setControlState:state];
}

- (NSArray<id<UpDeviceAttribute>> *)getAttributeList
{
    return [_cache getAttributeList];
}

- (id<UpDeviceFOTAStatusInfo>)getDeviceFOTAStatusInfo
{
    return [_cache getDeviceFOTAStatusInfo];
}

- (id<UpDeviceBLEHistoryInfo>)getDeviceBLEHistoryInfo
{
    return [_cache getDeviceBLEHistoryInfo];
}

- (NSData *)getDeviceBLERealData
{
    return [_cache getDeviceBLERealData];
}

- (id<UpDeviceAttribute>)getAttributeByName:(NSString *)name
{
    return [_cache getAttributeByName:name];
}
- (NSArray<id<UpDeviceAttribute>> *)getAttributeByNames:(NSArray<NSString *> *)names
{
    return [_cache getAttributeByNames:names];
}
- (NSArray<id<UpDeviceCaution>> *)getCautionList
{
    return [_cache getCautionList];
}

- (void)setParent:(id<UpDevice>)parent
{
    return [_cache setParent:parent];
}

- (id<UpDevice>)getSubDevByNo:(NSString *)subDevNo
{
    return [_cache getSubDevByNo:subDevNo];
}

- (NSArray *)getSubDevList
{
    return [_cache getSubDevList];
}

/**
 * 返回当前设备对象或其子设备对象
 *
 * @param targetId 目标设备ID
 * @return 设备对象，未找到是返回null
 */
- (id<UpDevice>)getDeviceById:(NSString *)targetId
{
    NSString *deviceId = [self getInfo].deviceId;
    if ([deviceId isEqualToString:targetId]) {
        return self;
    }
    NSArray *subDevList = [self getSubDevList];
    NSString *subDevId;
    for (id<UpDevice> subDev in subDevList) {
        subDevId = [subDev getInfo].deviceId;
        if ([subDevId isEqualToString:targetId]) {
            return subDev;
        }
    }
    return nil;
}

- (id<UpDeviceToolkit>)getToolkit
{
    id<UpDeviceToolkit> toolkit = nil;
    id<UpDeviceBroker> broker = [self getBroker];
    if (broker != nil) {
        toolkit = [broker getToolkit];
    }
    return toolkit;
}

- (id<UpDevice>)obtainSubDev:(id<UpDeviceBaseInfo>)subDevBaseInfo
{
    NSString *subDevNo = subDevBaseInfo.subDevNo;
    id<UpDevice> subDev = [_cache getSubDevByNo:subDevNo];
    if (subDev == nil) {
        id<UpDeviceInfo> subDevInfo = [self createSubDevInfo:subDevBaseInfo];
        if (_factory != nil) {
            subDev = [_factory create:subDevNo deviceInfo:subDevInfo broker:_broker factory:_factory];
        }
        if (subDev == nil) {
            subDev = [UpCommonDevice UpCommonDevice:subDevNo deviceInfo:subDevInfo broker:_broker Factory:_factory];
        }
        [subDev setParent:self];
    }
    else {
        [[subDev getInfo] updateBaseInfo:subDevBaseInfo];
    }
    return subDev;
}

- (id<UpDeviceInfo>)createSubDevInfo:(id<UpDeviceBaseInfo>)subDevBaseInfo
{
    id<UpDeviceInfo> subDevInfo = nil;
    id<UpDeviceInfo> info = [_cache getInfo];
    if (info != nil) {
        subDevInfo = [info cloneForSubDev:subDevBaseInfo];
    }
    if (subDevInfo == nil) {
        subDevInfo = [DeviceInfo DeviceInfo:subDevBaseInfo];
    }
    return subDevInfo;
}

- (void)processAttributes:(NSArray<id<UpDeviceAttribute>> *)attributeList
{
    UPLogWarning(kUPDevicePrefix, @"%s[%d]该方法为空方法，不应该被调用！", __PRETTY_FUNCTION__, __LINE__);
}

- (void)processCautions:(NSArray<id<UpDeviceCaution>> *)cautionList
{
    UPLogWarning(kUPDevicePrefix, @"%s[%d]该方法为空方法，不应该被调用！", __PRETTY_FUNCTION__, __LINE__);
}

- (void)processConnection:(UpDeviceConnection)connection
{
    UPLogWarning(kUPDevicePrefix, @"%s[%d]该方法为空方法，不应该被调用！", __PRETTY_FUNCTION__, __LINE__);
}

- (void)processDeviceInfo:(id<UpDeviceBaseInfo>)baseInfo
{
}

- (void)processReceived:(NSString *)name data:(id)data
{
    UPLogWarning(kUPDevicePrefix, @"%s[%d]该方法为空方法，不应该被调用！", __PRETTY_FUNCTION__, __LINE__);
}

- (void)processSubDevList:(NSArray<id<UpDeviceBaseInfo>> *)subDevList
{
    UPLogWarning(kUPDevicePrefix, @"%s[%d]该方法为空方法，不应该被调用！", __PRETTY_FUNCTION__, __LINE__);
}

- (void)processOnlineStateV2:(UpDeviceRealOnlineV2)status
{
    UPLogWarning(kUPDevicePrefix, @"%s[%d]该方法为空方法，不应该被调用！", __PRETTY_FUNCTION__, __LINE__);
}

- (void)processBleState:(UpDeviceConnection)state
{
    UPLogWarning(kUPDevicePrefix, @"%s[%d]该方法为空方法，不应该被调用！", __PRETTY_FUNCTION__, __LINE__);
}


- (void)onAttributesChange:(id<UpDeviceBaseInfo>)deviceBaseInfo attributeList:(NSArray<id<UpDeviceAttribute>> *)attributeList
{
    if ([deviceBaseInfo conformsToProtocol:@protocol(UpDeviceBaseInfo)]) {
    }
    else {
        UPLogError(kUPDevicePrefix, @"%s[%d]上报的设备信息接口实现对象格式有误！Object:%@", __PRETTY_FUNCTION__, __LINE__, deviceBaseInfo);
    }
    [_cache updateBaseInfo:deviceBaseInfo];
    [self processDeviceInfo:[_cache getInfo]];
    [_cache setAttributeList:attributeList];
    [self processAttributes:attributeList];
    [self.reporter notifyDeviceChange:EVENT_ATTRIBUTES_CHANGE device:[self getDevice]];
}

- (void)onConnectionChange:(id<UpDeviceBaseInfo>)deviceBaseInfo connection:(UpDeviceConnection)connection
{
    if ([deviceBaseInfo conformsToProtocol:@protocol(UpDeviceBaseInfo)]) {
    }
    else {
        UPLogError(kUPDevicePrefix, @"%s[%d]上报的设备信息接口实现对象格式有误！Object:%@", __PRETTY_FUNCTION__, __LINE__, deviceBaseInfo);
    }
    [_cache updateBaseInfo:deviceBaseInfo];
    [self processDeviceInfo:[_cache getInfo]];
    [_cache setConnection:connection];
    [self processConnection:connection];

    [self.reporter notifyDeviceChange:EVENT_DEVICE_STATE_CHANGE device:[self getDevice]];
}

- (void)onControlStateChange:(id<UpDeviceBaseInfo>)deviceBaseInfo controlState:(UpDeviceControlState)state
{
    [_cache updateBaseInfo:deviceBaseInfo];
    [self processDeviceInfo:[_cache getInfo]];
    [_cache setControlState:state];
    [self.reporter notifyDeviceChange:EVENT_DEVICE_CONTROL_STATE device:[self getDevice]];
}

- (void)onFaultInformationCodeChange:(id<UpDeviceBaseInfo>)deviceBaseInfo code:(NSInteger)code
{
    [_cache updateBaseInfo:deviceBaseInfo];
    [self processDeviceInfo:[_cache getInfo]];
    [_cache setFaultInformationCode:code];
    [self.reporter notifyDeviceChange:EVENT_DEVICE_FAULT_INFO_STATE_CODE device:[self getDevice]];
}

- (void)onRealOnlineChange:(id<UpDeviceBaseInfo>)deviceBaseInfo status:(UpDeviceRealOnline)status
{
    [_cache updateBaseInfo:deviceBaseInfo];
    [self processDeviceInfo:[_cache getInfo]];
    [_cache setRealOnlineStatus:status];
    [self.reporter notifyDeviceChange:EVENT_ONLINE_CHANGE device:[self getDevice]];
}

- (void)onRealOnlineChangeV2:(id<UpDeviceBaseInfo>)deviceBaseInfo status:(UpDeviceRealOnlineV2)status
{
    [_cache updateBaseInfo:deviceBaseInfo];
    [self processDeviceInfo:[_cache getInfo]];
    if (status == UpDeviceRealOnlineV2_OFFLINE && [UpDeviceHelper isNeedCleanAttributes:deviceBaseInfo.typeId]) {
        [_cache clearAttributeListValues];
    }
    [_cache setRealOnlineStateV2:status];
    [self processOnlineStateV2:status];
    [self.reporter notifyDeviceChange:EVENT_ONLINE_V2_CHANGE device:[self getDevice]];
}

- (void)onDeviceCaution:(id<UpDeviceBaseInfo>)deviceBaseInfo cautionList:(NSArray<id<UpDeviceCaution>> *)cautionList
{
    if ([deviceBaseInfo conformsToProtocol:@protocol(UpDeviceBaseInfo)]) {
    }
    else {
        UPLogError(kUPDevicePrefix, @"%s[%d]上报的设备信息接口实现对象格式有误！Object:%@", __PRETTY_FUNCTION__, __LINE__, deviceBaseInfo);
    }
    [_cache updateBaseInfo:deviceBaseInfo];
    [self processDeviceInfo:[_cache getInfo]];
    [_cache setCautionList:(NSMutableArray *)cautionList];
    [self processCautions:cautionList];
    [self.reporter notifyDeviceChange:EVENT_DEVICE_CAUTION device:[self getDevice]];
}

- (void)onDeviceInfoChange:(id<UpDeviceBaseInfo>)deviceBaseInfo
{
    if ([deviceBaseInfo conformsToProtocol:@protocol(UpDeviceBaseInfo)]) {
    }
    else {
        UPLogError(kUPDevicePrefix, @"%s[%d]上报的设备信息接口实现对象格式有误！Object:%@", __PRETTY_FUNCTION__, __LINE__, deviceBaseInfo);
    }
    [_cache updateBaseInfo:deviceBaseInfo];
    [self processDeviceInfo:[_cache getInfo]];
    [self.reporter notifyDeviceChange:EVENT_BASE_INFO_CHANGE device:[self getDevice]];
}

- (void)onDeviceReceive:(id<UpDeviceBaseInfo>)deviceBaseInfo name:(NSString *)name data:(id)data
{
    if ([deviceBaseInfo conformsToProtocol:@protocol(UpDeviceBaseInfo)]) {
    }
    else {
        UPLogError(kUPDevicePrefix, @"%s[%d]上报的设备信息接口实现对象格式有误！Object:%@", __PRETTY_FUNCTION__, __LINE__, deviceBaseInfo);
    }
    [_cache updateBaseInfo:deviceBaseInfo];
    [self processReceived:name data:data];
    [self.reporter notifyDeviceReceive:[self getDevice] name:name data:data];
}

- (void)onSubDevListChange:(id<UpDeviceBaseInfo>)deviceBaseInfo subDevInfoList:(NSArray<id<UpDeviceBaseInfo>> *)subDevInfoList
{
    if (subDevInfoList == nil || subDevInfoList.count == 0) {
        return;
    }
    if ([deviceBaseInfo conformsToProtocol:@protocol(UpDeviceBaseInfo)]) {
    }
    else {
        UPLogError(kUPDevicePrefix, @"%s[%d]上报的设备信息接口实现对象格式有误！Object:%@", __PRETTY_FUNCTION__, __LINE__, deviceBaseInfo);
    }
    NSMutableArray *subDevList = [NSMutableArray array];
    for (id<UpDeviceBaseInfo> subDevBaseInfo in subDevInfoList) {
        id<UpDevice> subDev = [self obtainSubDev:subDevBaseInfo];
        [subDevList addObject:subDev];
    }
    [_cache setSubDevList:subDevList];
    [self processSubDevList:subDevList];
    [self.reporter notifyDeviceChange:EVENT_SUB_DEV_LIST_CHANGE device:[self getDevice]];

    [self performSelector:@selector(delayReporter) withObject:nil afterDelay:1.0];
}

- (void)onSleepStateChange:(id<UpDeviceBaseInfo>)deviceBaseInfo state:(UpDeviceSleepState)state
{
    if (!(state == UpDeviceSleepStateUnsleeping ||
          state == UpDeviceSleepStateSleeping ||
          state == UpDeviceSleepStateWakingUp ||
          state == UpDeviceSleepStateLowPower) ||
        deviceBaseInfo == nil) {
        UPDeviceLogError(@"[%s][%d]mac:%@,error sleepState:%ld", __PRETTY_FUNCTION__, __LINE__, deviceBaseInfo.deviceId, state);
        return;
    }
    [_cache updateBaseInfo:deviceBaseInfo];
    [self processDeviceInfo:[_cache getInfo]];
    [_cache setSleepState:state];
    [self.reporter notifyDeviceChange:EVENT_DEVICE_SLEEP_STATE device:[self getDevice]];
}

- (void)onNetworkLevelChange:(id<UpDeviceBaseInfo>)deviceBaseInfo level:(UpDeviceNetworkLevel)level
{
    if (!(level == UpDeviceNetworkLevelExcellent ||
          level == UpDeviceNetworkLevelGood ||
          level == UpDeviceNetworkLevelQualified ||
          level == UpDeviceNetworkLevelPoor ||
          level == UpDeviceNetworkLevelUnKnown) ||
        deviceBaseInfo == nil) {
        UPDeviceLogError(@"[%s][%d]mac:%@,error networkLevel:%ld", __PRETTY_FUNCTION__, __LINE__, deviceBaseInfo.deviceId, level);
        return;
    }
    [_cache updateBaseInfo:deviceBaseInfo];
    [self processDeviceInfo:[_cache getInfo]];
    [_cache setNetworkLevel:level];
    [self.reporter notifyDeviceChange:EVENT_DEVICE_NETWORK_LEVEL device:[self getDevice]];
}

- (void)onQCConnectTimeout:(id<UpDeviceBaseInfo>)deviceBaseInfo timeoutType:(UpDeviceQCConnectTimeoutType)timeoutType
{
    NSDictionary *dict = @{
        @"timeoutType" : [NSString stringWithFormat:@"%ld", timeoutType],
        @"deviceId" : deviceBaseInfo.deviceId
    };
    [self.reporter notifyDeviceChange:EVENT_DEVICE_QCCONNECT_TIMEOUTTYPE device:[self getDevice] extra:dict];
}

- (void)onBleStateChange:(id<UpDeviceBaseInfo>)deviceBaseInfo state:(UpDeviceConnection)state
{
    [_cache updateBaseInfo:deviceBaseInfo];
    [self processDeviceInfo:[_cache getInfo]];
    [_cache setBleState:state];
    [self processBleState:state];
    [self.reporter notifyDeviceChange:EVENT_DEVICE_BLESTATE_CHANGE device:[self getDevice]];
}

- (void)onuSDKDeviceDataChange:(id<UpDeviceBaseInfo>)deviceBaseInfo wrapperData:(uSDKDeviceWrapper *)wrapperData
{
    [_cache updateBaseInfo:deviceBaseInfo];
    [self processDeviceInfo:[_cache getInfo]];

    [_cache setDeviceOfflineCause:wrapperData.offlineCause];
    [_cache setDeviceOfflineDays:wrapperData.offlineDays];
    [_cache setDeviceOnlyConfigState:wrapperData.onlyConfigState];

    [_cache setConnection:wrapperData.connection];
    [self processConnection:[_cache getConnection]];

    [_cache setControlState:wrapperData.controlState];
    [_cache setFaultInformationCode:wrapperData.faultInformationCode];

    [_cache setRealOnlineStatus:wrapperData.realOnlineStatus];

    [_cache setRealOnlineStateV2:wrapperData.realOnlineStateV2];
    [self processOnlineStateV2:[_cache getDeviceOnlineStateV2]];

    [_cache setNetworkLevel:wrapperData.networkLevel];

    [_cache setBleState:wrapperData.bleState];
    [self processBleState:[_cache getBleState]];

    [_cache setSleepState:wrapperData.sleepState];

    if (wrapperData.subDevInfoList != nil) {
        NSMutableArray *subDevList = [NSMutableArray array];
        for (id<UpDeviceBaseInfo> subDevBaseInfo in wrapperData.subDevInfoList) {
            id<UpDevice> subDev = [self obtainSubDev:subDevBaseInfo];
            [subDevList addObject:subDev];
        }
        [_cache setSubDevList:subDevList];
        [self processSubDevList:[_cache getSubDevList]];
    }

    if (wrapperData.attributeList != nil) {
        [_cache setAttributeList:wrapperData.attributeList];
        [self processAttributes:[_cache getAttributeList]];
    }

    if (wrapperData.cautionList != nil) {
        [_cache setCautionList:[NSMutableArray arrayWithArray:wrapperData.cautionList]];
        [self processCautions:[_cache getCautionList]];
    }

    [self.reporter notifyDeviceChange:EVENT_BASE_INFO_CHANGE device:[self getDevice]];
}

- (void)delayReporter
{
    [self.reporter notifyDeviceChange:EVENT_SUB_DEV_LIST_CHANGE device:[self getDevice]];
}

- (UpDeviceConfigState)configState
{
    return UpDeviceConfigState_Unknown;
}

- (NSArray *)getEngineAttributeList
{
    return @[];
}

- (NSArray *)getEngineCautionList
{
    return @[];
}

- (void)connectDevice:(void (^)(UpDeviceResult *))finishBlock
{
    id<UpDeviceBroker> broke = [self getBroker];
    id<UpDeviceToolkit> kit = [broke getToolkit];
    [kit connectDevice:[self deviceId]
           finishBlock:^(UpDeviceResult *result) {
             if (finishBlock) {
                 finishBlock(result);
             }
           }];
}

- (void)disConnectDevice:(void (^)(UpDeviceResult *))finishBlock
{
    id<UpDeviceBroker> broke = [self getBroker];
    id<UpDeviceToolkit> kit = [broke getToolkit];
    [kit disConnectDevice:[self deviceId]
              finishBlock:^(UpDeviceResult *result) {
                if (finishBlock) {
                    finishBlock(result);
                }
              }];
}

- (void)QCConnectDevice:(void (^)(UpDeviceResult *))finishBlock
{
    id<UpDeviceBroker> broke = [self getBroker];
    id<UpDeviceToolkit> kit = [broke getToolkit];
    [kit QCConnectDevice:[self deviceId]
             finishBlock:^(UpDeviceResult *result) {
               if (finishBlock) {
                   finishBlock(result);
               }
             }];
}

- (void)QCDisConnectDevice:(void (^)(UpDeviceResult *))finishBlock
{
    id<UpDeviceBroker> broke = [self getBroker];
    id<UpDeviceToolkit> kit = [broke getToolkit];
    [kit QCDisConnectDevice:[self deviceId]
                finishBlock:^(UpDeviceResult *result) {
                  if (finishBlock) {
                      finishBlock(result);
                  }
                }];
}

- (void)getBleState:(void (^)(UpDeviceResult *))finishBlock
{
    id<UpDeviceBroker> broker = [self getBroker];
    if (!broker) {
        UPLogError(kUPDevicePrefix, @"%s[%d]device(%@)getBleState fail no broker", __PRETTY_FUNCTION__, __LINE__, self.getInfo.deviceId);
        UpDeviceResult *result = [UpDeviceResult UpDeviceResult:ErrorCode_FAILURE extraData:nil];
        if (finishBlock) {
            finishBlock(result);
        }
        return;
    }
    id<UpDeviceToolkit> kit = [broker getToolkit];
    if (!kit) {
        UPLogError(kUPDevicePrefix, @"%s[%d]device(%@)getBleState fail no kit", __PRETTY_FUNCTION__, __LINE__, self.getInfo.deviceId);
        UpDeviceResult *result = [UpDeviceResult UpDeviceResult:ErrorCode_FAILURE extraData:nil];
        if (finishBlock) {
            finishBlock(result);
        }
        return;
    }
    [kit getDeviceBleState:[self deviceId]
               finishBlock:^(UpDeviceResult *result) {
                 if (finishBlock) {
                     finishBlock(result);
                 }
               }];
}

- (void)onOfflineCauseChange:(id<UpDeviceBaseInfo>)deviceBaseInfo offlineCause:(UpDeviceOfflineCause)offlineCause
{
    [_cache updateBaseInfo:deviceBaseInfo];
    [_cache setDeviceOfflineCause:offlineCause];
    if ([self respondsToSelector:@selector(processOfflineCause:)]) {
        [self processOfflineCause:offlineCause];
    }
    [self.reporter notifyDeviceChange:EVENT_DEVICE_OFFLINE_CAUSE_CHANGE device:[self getDevice]];
}

- (void)onOfflineDaysChange:(id<UpDeviceBaseInfo>)deviceBaseInfo offlineDays:(NSInteger)offlineDays
{
    [_cache updateBaseInfo:deviceBaseInfo];
    [_cache setDeviceOfflineDays:offlineDays];
    //暂不给逻辑引擎值
    [self.reporter notifyDeviceChange:EVENT_DEVICE_OFFLINE_DAYS_CHANGE device:[self getDevice]];
}

- (void)onOnlyConfigStateChange:(id<UpDeviceBaseInfo>)deviceBaseInfo onlyConfigState:(UpDeviceOnlyConfigState)onlyConfigState
{
    [_cache updateBaseInfo:deviceBaseInfo];
    [_cache setDeviceOnlyConfigState:onlyConfigState];
    //暂不给逻辑引擎值
    [self.reporter notifyDeviceChange:EVENT_DEVICE_ONLY_CONFIG_STATE_CHANGE device:[self getDevice]];
}

- (UpDeviceOfflineCause)getDeviceOfflineCause
{
    return [_cache getDeviceOfflineCause];
}

- (NSInteger)getDeviceOfflineDays
{
    return [_cache getDeviceOfflineDays];
}

- (UpDeviceOnlyConfigState)getDeviceOnlyConfigState
{
    return [_cache getDeviceOnlyConfigState];
}

#pragma UpDeviceCard - Protocol
- (UpDeviceCardInfo *)getDeviceCardInfo:(NSString *)familyId
{
    UpDeviceCardInfo *newInfo = [UpDeviceCardInfo new];
    NSDictionary<NSString *, UpDeviceCardInfo *> *cardDic = self.getInfo.getExtras[@"DI-Product.cardInfoMap"];
    UpDeviceCardInfo *info = cardDic[familyId];
    if (info) {
        newInfo.cardSort = info.cardSort;
        newInfo.cardStatus = info.cardStatus;
    }
    else {
        NSNumber *cardSort = self.getInfo.getExtras[@"DI-Product.cardSort"];
        NSNumber *cardStatus = self.getInfo.getExtras[@"DI-Product.cardStatus"];
        newInfo.cardSort = cardSort.integerValue;
        newInfo.cardStatus = cardStatus.integerValue;
    }
    return newInfo;
}

- (NSString *)getDeviceCardDeviceId
{
    return self.getInfo.deviceId;
}

- (NSString *)getDeviceCardFamilyId
{
    return self.getInfo.getExtras[@"DI-Relation.familyId"];
}
- (BOOL)getDeviceCardIsSharedDevice
{
    return [self.getInfo.getExtras[@"DI-Product.sharedDeviceFlag"] boolValue];
}
- (BOOL)getDeviceCardIsAggregateDevice
{
    NSString *deviceAggregateType = self.getInfo.getExtras[@"DI-Basic.deviceAggregateType"];
    return [deviceAggregateType isKindOfClass:NSString.class] && deviceAggregateType.length > 0;
}
#pragma mark - subDevice
- (NSString *)getSmartLinkSoftwareVersion
{
    id<UpDeviceBroker> broke = [self getBroker];
    id<UpDeviceToolkit> kit = [broke getToolkit];
    return [kit getSmartLinkSoftwareVersion:self.deviceId];
}

- (void)getSubDevBaseInfoList:(id<UpDeviceBaseInfo>)deviceBaseInfo
{
    id<UpDeviceBroker> broke = [self getBroker];
    id<UpDeviceToolkit> kit = [broke getToolkit];

    __block NSArray *list;
    dispatch_group_t group = dispatch_group_create();
    dispatch_group_enter(group);
    [kit getSubDevBaseInfoList:deviceBaseInfo.deviceId
                   finishBlock:^(UpDeviceResult *result) {
                     if (result.isSuccessful) {
                         list = result.extraData;
                     }
                     dispatch_group_leave(group);
                   }];
    dispatch_group_wait(group, DISPATCH_TIME_FOREVER);
    if (list && list.count > 0) {
        [self onSubDevListChange:deviceBaseInfo subDevInfoList:list];
    }
}

#pragma mark - UpDeviceFOTA
- (void)startBoardFOTA:(void (^)(UpDeviceResult *))finishBlock
{
    id<UpDeviceBroker> broke = [self getBroker];
    id<UpDeviceToolkit> kit = [broke getToolkit];
    [kit startBoardFOTA:self.deviceId
            finishBlock:^(UpDeviceResult *result) {
              if (finishBlock) {
                  finishBlock(result);
              }

            }];
}

- (void)checkBoardFOTAInfoSuccess:(void (^)(UpDeviceResult *))finishBlock
{
    id<UpDeviceBroker> broke = [self getBroker];
    id<UpDeviceToolkit> kit = [broke getToolkit];
    [kit checkBoardFOTAInfoSuccess:self.deviceId
                       finishBlock:^(UpDeviceResult *result) {
                         if (finishBlock) {
                             finishBlock(result);
                         }
                       }];
}

- (void)fetchBoardFOTAStatusSuccess:(void (^)(UpDeviceResult *))finishBlock
{
    id<UpDeviceBroker> broke = [self getBroker];
    id<UpDeviceToolkit> kit = [broke getToolkit];
    [kit fetchBoardFOTAStatusSuccess:self.deviceId
                         finishBlock:^(UpDeviceResult *result) {
                           if (finishBlock) {
                               finishBlock(result);
                           }
                         }];
}

- (void)startModuleUpdateSuccess:(void (^)(UpDeviceResult *))finishBlock
{
    id<UpDeviceBroker> broke = [self getBroker];
    id<UpDeviceToolkit> kit = [broke getToolkit];
    [kit startModuleUpdateSuccess:self.deviceId
                      finishBlock:^(UpDeviceResult *result) {
                        if (finishBlock) {
                            finishBlock(result);
                        }
                      }];
}

- (void)onDeviceUpdateBoardFOTAStatus:(id<UpDeviceBaseInfo>)deviceBaseInfo FOTAStatusInfo:(id<UpDeviceFOTAStatusInfo>)statusInfo
{
    if ([deviceBaseInfo conformsToProtocol:@protocol(UpDeviceBaseInfo)]) {
    }
    else {
        UPLogError(kUPDevicePrefix, @"%s[%d]上报的设备信息接口实现对象格式有误！Object:%@", __PRETTY_FUNCTION__, __LINE__, deviceBaseInfo);
    }
    [_cache updateBaseInfo:deviceBaseInfo];
    [self processDeviceInfo:[_cache getInfo]];
    [_cache setTheFOTAStatusInfo:statusInfo];
    [self.reporter notifyDeviceChange:EVENT_DEVICE_FOTASTATUS_CHANGE device:[self getDevice]];
}

- (NSArray<id<UpDevice>> *)getSubDevListBySubDev
{
    id<UpDeviceBroker> broke = [self getBroker];
    id<UpDeviceToolkit> kit = [broke getToolkit];
    __block NSArray *list;
    dispatch_group_t group = dispatch_group_create();
    dispatch_group_enter(group);
    [kit getSubDevListBySubDev:self.deviceId
                   finishBlock:^(UpDeviceResult *result) {
                     if (result.isSuccessful) {
                         list = result.extraData;
                     }
                     dispatch_group_leave(group);
                   }];
    dispatch_group_wait(group, DISPATCH_TIME_FOREVER);
    NSMutableArray *subDevList = [NSMutableArray array];
    if (!list) {
        return nil;
    }
    for (id<UpDeviceBaseInfo> subDevBaseInfo in list) {
        id<UpDevice> subDev = [self obtainSubDev:subDevBaseInfo];
        [subDevList addObject:subDev];
    }
    return subDevList;
}

- (void)startFOTAWithDeviceFOTA:(NSString *)traceId firmwareId:(NSString *)firmwareId finishBlock:(void (^)(UpDeviceResult *))finishBlock
{
    id<UpDeviceBroker> broke = [self getBroker];
    id<UpDeviceToolkit> kit = [broke getToolkit];
    [kit startFOTAWithDeviceFOTA:self.deviceId
                         traceId:traceId
                      firmwareId:firmwareId
                     finishBlock:^(UpDeviceResult *result) {
                       if (finishBlock) {
                           finishBlock(result);
                       }
                     }];
}

#pragma mark - BLE
- (void)onDeviceReceiveBLEHistoryData:(id<UpDeviceBaseInfo>)deviceBaseInfo BLEHistoryInfo:(id<UpDeviceBLEHistoryInfo>)historyInfo
{
    if ([deviceBaseInfo conformsToProtocol:@protocol(UpDeviceBaseInfo)]) {
    }
    else {
        UPLogError(kUPDevicePrefix, @"%s[%d]上报的设备信息接口实现对象格式有误！Object:%@", __PRETTY_FUNCTION__, __LINE__, deviceBaseInfo);
    }
    [_cache updateBaseInfo:deviceBaseInfo];
    [self processDeviceInfo:[_cache getInfo]];
    [_cache setBLEHistoryInfo:historyInfo];
    [self.reporter notifyDeviceChange:EVENT_DEVICE_BLE_HISTORY device:[self getDevice]];
}

- (void)onDeviceReceiveBLERealTimeData:(id<UpDeviceBaseInfo>)deviceBaseInfo data:(NSData *)data
{
    if ([deviceBaseInfo conformsToProtocol:@protocol(UpDeviceBaseInfo)]) {
    }
    else {
        UPLogError(kUPDevicePrefix, @"%s[%d]上报的设备信息接口实现对象格式有误！Object:%@", __PRETTY_FUNCTION__, __LINE__, deviceBaseInfo);
    }
    [_cache updateBaseInfo:deviceBaseInfo];
    [self processDeviceInfo:[_cache getInfo]];
    [_cache setBLERealData:data];
    [self.reporter notifyDeviceChange:EVENT_DEVICE_BLE_REAL device:[self getDevice]];
}

- (void)fetchBLEHistoryData:(void (^)(UpDeviceResult *result))finishBlock
{
    id<UpDeviceBroker> broke = [self getBroker];
    id<UpDeviceToolkit> kit = [broke getToolkit];
    [kit fetchBLEHistoryData:self.deviceId
                 finishBlock:^(UpDeviceResult *result) {
                   if (finishBlock) {
                       finishBlock(result);
                   }
                 }];
}

- (void)cancelFetchBLEHistoryData:(void (^)(UpDeviceResult *result))finishBlock
{
    id<UpDeviceBroker> broke = [self getBroker];
    id<UpDeviceToolkit> kit = [broke getToolkit];
    [kit cancelFetchBLEHistoryData:self.deviceId
                       finishBlock:^(UpDeviceResult *result) {
                         if (finishBlock) {
                             finishBlock(result);
                         }
                       }];
}

#pragma mark - UpDeviceFocus
- (BOOL)inFocus
{
    id<UpDeviceBroker> broke = [self getBroker];
    id<UpDeviceToolkit> kit = [broke getToolkit];
    return [kit inFocus:self.deviceId];
}

- (BOOL)outFocus
{
    id<UpDeviceBroker> broke = [self getBroker];
    id<UpDeviceToolkit> kit = [broke getToolkit];
    return [kit outFocus:self.deviceId];
}

#pragma mark - UpDeviceNetWorkQuality
- (void)getNetworkQuality:(void (^)(UpDeviceResult *result))finishBlock
{
    id<UpDeviceBroker> broke = [self getBroker];
    id<UpDeviceToolkit> kit = [broke getToolkit];
    [kit getNetworkQuality:self.deviceId
               finishBlock:^(UpDeviceResult *result) {
                 if (finishBlock) {
                     finishBlock(result);
                 }
               }];
}

#pragma mark - UPDeviceNetType
- (DeviceNetType)getNetType
{
    id<UpDeviceBroker> broker = [self getBroker];
    id<UpDeviceToolkit> kit = [broker getToolkit];
    return [kit getNetType:self.deviceId];
}
#pragma mark 设备配置文件加载成功后刷新yUI
- (void)sendCustomNotfiDeviceChange:(NSInteger)event
{
    [self.reporter notifyDeviceChange:event device:[self getDevice]];
}

#pragma mark -  UpDeviceGroupOptDeleagte
- (void)fetchGroupableDeviceList:(void (^)(UpDeviceResult *_Nonnull))finishBlock
{
    id<UpDeviceToolkit> tooKit = [self getToolkit];
    [tooKit fetchGroupableDeviceList:self.deviceId
                         finishBlock:^(UpDeviceResult *result) {
                           if (finishBlock) {
                               finishBlock(result);
                           }
                         }];
}

- (void)createDeviceGroup:(void (^)(UpDeviceResult *_Nonnull))finishBlock
{
    id<UpDeviceToolkit> tooKit = [self getToolkit];
    [tooKit createDeviceGroup:self.deviceId
                  finishBlock:^(UpDeviceResult *result) {
                    if (finishBlock) {
                        finishBlock(result);
                    }
                  }];
}

- (void)addDevicesToGroup:(NSArray<NSString *> *)deviceIds finishBlock:(void (^)(UpDeviceResult *_Nonnull))finishBlock
{
    id<UpDeviceToolkit> tooKit = [self getToolkit];
    [tooKit addDevicesToGroup:self.deviceId
                    deviceIds:deviceIds
                  finishBlock:^(UpDeviceResult *result) {
                    if (finishBlock) {
                        finishBlock(result);
                    }
                  }];
}

- (void)removeDevicesFromGroup:(NSArray<NSString *> *)deviceIds finishBlock:(void (^)(UpDeviceResult *_Nonnull))finishBlock
{
    id<UpDeviceToolkit> tooKit = [self getToolkit];
    [tooKit removeDevicesFromGroup:self.deviceId
                         deviceIds:deviceIds
                       finishBlock:^(UpDeviceResult *result) {
                         if (finishBlock) {
                             finishBlock(result);
                         }
                       }];
}

- (void)deleteDeviceGroup:(void (^)(UpDeviceResult *_Nonnull))finishBlock
{
    id<UpDeviceToolkit> tooKit = [self getToolkit];
    [tooKit deleteDeviceGroup:self.deviceId
                  finishBlock:^(UpDeviceResult *result) {
                    if (finishBlock) {
                        finishBlock(result);
                    }
                  }];
}

- (void)getGroupMemberList:(void (^)(UpDeviceResult *_Nonnull))finishBlock
{
    id<UpDeviceToolkit> tooKit = [self getToolkit];
    [tooKit getGroupMemberList:self.deviceId
                   finishBlock:^(UpDeviceResult *result) {
                     if (finishBlock) {
                         finishBlock(result);
                     }
                   }];
}

- (BOOL)isGroup
{
    id<UpDeviceToolkit> tooKit = [self getToolkit];
    return [tooKit isGroup:self.deviceId];
}
#pragma mark - UpAttachResourceDelegate
- (void)attachResourceWithDecode:(NSString *)resourceName finishBlock:(void (^)(UpDeviceResult *_Nonnull))finishBlock
{
    id<UpDeviceBroker> broke = [self getBroker];
    id<UpDeviceToolkit> toolKit = [broke getToolkit];
    [toolKit attachResourceWithDecode:self.deviceId
                         resourceName:resourceName
                          finishBlock:^(UpDeviceResult *result) {
                            if (finishBlock) {
                                finishBlock(result);
                            }
                          }];
}

- (void)attachResource:(NSString *)resourceName finishBlock:(void (^)(UpDeviceResult *_Nonnull))finishBlock
{
    id<UpDeviceBroker> broke = [self getBroker];
    id<UpDeviceToolkit> toolKit = [broke getToolkit];
    [toolKit attachResource:self.deviceId
               resourceName:resourceName
                finishBlock:^(UpDeviceResult *result) {
                  if (finishBlock) {
                      finishBlock(result);
                  }
                }];
}

- (void)detachResource:(NSString *)resourceName finishBlock:(void (^)(UpDeviceResult *_Nonnull))finishBlock
{
    id<UpDeviceBroker> broke = [self getBroker];
    id<UpDeviceToolkit> toolKit = [broke getToolkit];
    [toolKit detachResource:self.deviceId
               resourceName:resourceName
                finishBlock:^(UpDeviceResult *result) {
                  if (finishBlock) {
                      finishBlock(result);
                  }
                }];
}


@end
