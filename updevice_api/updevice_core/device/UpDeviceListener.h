//
//  UpDeviceListener.h
//  updevice_api
//
//  Created by <PERSON><PERSON> on 2018/12/18.
//  Copyright © 2018 Haierfc. All rights reserved.
//

#import <Foundation/Foundation.h>

/**
 * 已连接
 */
static const NSInteger EVENT_ATTACHED = 0x1000;
/**
 * 基础信息变化
 */
static const NSInteger EVENT_BASE_INFO_CHANGE = EVENT_ATTACHED + 1;
/**
 * 设备状态变化，状态指{@link UpDeviceState}
 */
static const NSInteger EVENT_DEVICE_STATE_CHANGE = EVENT_ATTACHED + 2;
/**
 * 子设备列表变化
 */
static const NSInteger EVENT_SUB_DEV_LIST_CHANGE = EVENT_ATTACHED + 3;
/**
 * 属性列表变化
 */
static const NSInteger EVENT_ATTRIBUTES_CHANGE = EVENT_ATTACHED + 4;
/**
 * 设备告警
 */
static const NSInteger EVENT_DEVICE_CAUTION = EVENT_ATTACHED + 5;
/**
 * FOTA状态变化
 */
static const NSInteger EVENT_DEVICE_FOTASTATUS_CHANGE = EVENT_ATTACHED + 6; //设备底板固件升级状态变化
/**
 * BLE历史数据
 */
static const NSInteger EVENT_DEVICE_BLE_HISTORY = EVENT_ATTACHED + 7; //蓝牙历史数据变化
/**
 * BLE实时数据
 */
static const NSInteger EVENT_DEVICE_BLE_REAL = EVENT_ATTACHED + 8; //蓝牙实时数据变化

/**
 * 设备真实在线状态变化
 */
static const NSInteger EVENT_ONLINE_CHANGE = EVENT_ATTACHED + 9;

/**
 * 可控状态变化
 */
static const NSInteger EVENT_DEVICE_CONTROL_STATE = EVENT_ATTACHED + 10;

/**
 * 配网变化
 */
static const NSInteger EVENT_DEVICE_FAULT_INFO_STATE_CODE = EVENT_ATTACHED + 11;

/**
 * 设备睡眠状态变化
 */
static const NSInteger EVENT_DEVICE_SLEEP_STATE = EVENT_ATTACHED + 12;
/**
 * 设备网络质量状态变化  stateV2 +13 此处+14
 */
static const NSInteger EVENT_DEVICE_NETWORK_LEVEL = EVENT_ATTACHED + 14;

/**
 * 设备真实在线状态变化V2
 */
static const NSInteger EVENT_ONLINE_V2_CHANGE = EVENT_ATTACHED + 13;

/**
 * 快连设备超时
 */
static const NSInteger EVENT_DEVICE_QCCONNECT_TIMEOUTTYPE = EVENT_ATTACHED + 15;

/**
 * 蓝牙状态变化
 */
static const NSInteger EVENT_DEVICE_BLESTATE_CHANGE = EVENT_ATTACHED + 16;

/**
 * 离线原因状态变化
 * @since v7.11.0
 */
static const NSInteger EVENT_DEVICE_OFFLINE_CAUSE_CHANGE = EVENT_ATTACHED + 17;

/**
 * 离线天数状态变化
 * 数字与Android发生了错乱，重新定义为20，双端一致
 * @since v7.19.0
 */
static const NSInteger EVENT_DEVICE_OFFLINE_DAYS_CHANGE = EVENT_ATTACHED + 20;

/**
 * 设备仅配网状态状态变化
 * @since v7.20.0
 */
static const NSInteger EVENT_DEVICE_ONLY_CONFIG_STATE_CHANGE = EVENT_ATTACHED + 21;

@protocol UpDeviceListener <NSObject>
/**
* 监听到设备事件
*
* @param event  事件代码
* @param device 设备对象
*/
- (void)onDeviceReport:(NSInteger)event device:(id)device;

/**
* 监听到设备事件
*
* @param event  事件代码
* @param device 设备对象
* @param extra 扩展属性
*/
- (void)onDeviceReport:(NSInteger)event device:(id)device extra:(id)extra;
@end
