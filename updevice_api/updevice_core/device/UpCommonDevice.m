//
//  UpCommonDevice.m
//  updevice_api
//
//  Created by <PERSON><PERSON> on 2018/12/19.
//  Copyright © 2018 Haierfc. All rights reserved.
//

#import "UpCommonDevice.h"
#import "UPDeviceLog.h"

@implementation UpCommonDevice

+ (UpCommonDevice *)UpCommonDevice:(NSString *)uniqueId deviceInfo:(id<UpDeviceInfo>)deviceInfo broker:(id<UpDeviceBroker>)broker Factory:(id<UpDeviceFactory>)factory
{
    return [[UpCommonDevice alloc] initWithDeviceInfo:uniqueId deviceInfo:deviceInfo broker:broker factory:factory];
}

- (instancetype)initWithUniqueId:(NSString *)uniqueId deviceInfo:(id<UpDeviceInfo>)deviceInfo broker:(id<UpDeviceBroker>)broker factory:(id<UpDeviceFactory>)factory
{
    if (self = [super initWithDeviceInfo:uniqueId deviceInfo:deviceInfo broker:broker factory:factory]) {
    }

    return self;
}

- (void)processAttributes:(NSArray<id<UpDeviceAttribute>> *)attributeList
{
    UPLogWarning(kUPDevicePrefix, @"%s[%d]该方法为空方法，不应该被调用！", __PRETTY_FUNCTION__, __LINE__);
}

- (void)processCautions:(NSArray<id<UpDeviceCaution>> *)cautionList
{
    UPLogWarning(kUPDevicePrefix, @"%s[%d]该方法为空方法，不应该被调用！", __PRETTY_FUNCTION__, __LINE__);
}

- (void)processConnection:(UpDeviceConnection)connection
{
    UPLogWarning(kUPDevicePrefix, @"%s[%d]该方法为空方法，不应该被调用！", __PRETTY_FUNCTION__, __LINE__);
}

- (void)processOnlineStateV2:(UpDeviceRealOnlineV2)state
{
    UPLogWarning(kUPDevicePrefix, @"%s[%d]该方法为空方法，不应该被调用！", __PRETTY_FUNCTION__, __LINE__);
}

- (void)processDeviceInfo:(id<UpDeviceBaseInfo>)baseInfo
{
}

- (void)processBleState:(UpDeviceConnection)state
{
    UPLogWarning(kUPDevicePrefix, @"%s[%d]该方法为空方法，不应该被调用！", __PRETTY_FUNCTION__, __LINE__);
}

- (void)processSubDevList:(NSArray<id<UpDeviceBaseInfo>> *)subDevList
{
    UPLogWarning(kUPDevicePrefix, @"%s[%d]该方法为空方法，不应该被调用！", __PRETTY_FUNCTION__, __LINE__);
}


- (id<UpDevice>)getExtApi
{
    return nil;
}

- (ExtApiState)getExtApiState
{
    return ONLY_NATIVE;
}

- (BOOL)isExtApiPrepared
{
    return NO;
}

- (void)onPrepareExtApi:(void (^)(UpDeviceResult *))finishBlock
{
    UPLogWarning(kUPDevicePrefix, @"%s[%d]该方法为空方法，不应该被调用！", __PRETTY_FUNCTION__, __LINE__);
}

- (void)onReleaseExtApi:(void (^)(UpDeviceResult *))finishBlock
{
    UPLogWarning(kUPDevicePrefix, @"%s[%d]该方法为空方法，不应该被调用！", __PRETTY_FUNCTION__, __LINE__);
}

- (void)onReloadExtApi:(void (^)(UpDeviceResult *))finishBlock
{
    UPLogWarning(kUPDevicePrefix, @"%s[%d]该方法为空方法，不应该被调用！", __PRETTY_FUNCTION__, __LINE__);
}

- (void)reload:(void (^)(UpDeviceResult *))finishBlock
{
    UPLogWarning(kUPDevicePrefix, @"%s[%d]该方法为空方法，不应该被调用！", __PRETTY_FUNCTION__, __LINE__);
}

- (void)processReceived:(NSString *)name data:(id)data
{
    UPLogWarning(kUPDevicePrefix, @"%s[%d]该方法为空方法，不应该被调用！", __PRETTY_FUNCTION__, __LINE__);
}

@end
