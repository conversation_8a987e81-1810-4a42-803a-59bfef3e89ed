//
//  UpGroupDevicesDeleagte.h
//  updevice_api
//
//  Created by 冉东军 on 2021/4/6.
//  Copyright © 2021 Haierfc. All rights reserved.
//

#import <Foundation/Foundation.h>

NS_ASSUME_NONNULL_BEGIN
/**
 * description: Zigbee组控
 * 问题背景：控制一组设备
 */
@protocol UpDeviceGroupOptDeleagte <NSObject>
/// 获取可与当前设备分到同一组的设备列表, 当前设备要求有zigbee能力或BLEMesh能力
/// @param finishBlock 完成回调
- (void)fetchGroupableDeviceList:(void (^)(UpDeviceResult *result))finishBlock;

/// 创建分组
/// @param finishBlock 完成回调
- (void)createDeviceGroup:(void (^)(UpDeviceResult *result))finishBlock;

/// 向组设备中添加设备，要求当前device对象为组设备
/// @param deviceIds 需要添加到组设备的设备id列表
/// @param finishBlock 完成回调
- (void)addDevicesToGroup:(NSArray<NSString *> *)deviceIds finishBlock:(void (^)(UpDeviceResult *result))finishBlock;

/// 从组设备中移除设备，要求当前device对象为组设备
/// @param deviceIds 需要移除组设备的设备id列表
/// @param finishBlock 完成回调
- (void)removeDevicesFromGroup:(NSArray<NSString *> *)deviceIds finishBlock:(void (^)(UpDeviceResult *result))finishBlock;

///  删除组设备，要求当前device对象为组设备
/// @param finishBlock 完成回调
- (void)deleteDeviceGroup:(void (^)(UpDeviceResult *result))finishBlock;

/// 组设备获取当前组内的所有子设备，非组设备返回空集合
/// @param finishBlock 完成回调
- (void)getGroupMemberList:(void (^)(UpDeviceResult *result))finishBlock;

/// 是否是组设备
- (BOOL)isGroup;
@end

NS_ASSUME_NONNULL_END
