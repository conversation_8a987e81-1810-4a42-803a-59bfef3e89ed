//
//  UpDeviceCacheManager.h
//  UPDevice
//
//  Created by <PERSON><PERSON><PERSON>   on 2025/3/7.
//

#import <Foundation/Foundation.h>
#import "UpDeviceCenter.h"

NS_ASSUME_NONNULL_BEGIN

static NSString *const kDeviceInfoKey_attributes = @"attributes";

// 定时持久化机制
// 设备列表的排序和过滤
@interface UpDeviceCacheManager : NSObject

- (instancetype)initWith:(id<UpDeviceCenter>)deviceManager;

// 读取某个设备的缓存
- (nullable id)readDeviceCacheWith:(NSString *)deviceId;
@end

NS_ASSUME_NONNULL_END
