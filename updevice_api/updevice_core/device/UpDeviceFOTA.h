//
//  UpDeviceFOTA.h
//  updevice_api
//
//  Created by gump on 11/6/2019.
//  Copyright © 2019 Haierfc. All rights reserved.
//

#import <Foundation/Foundation.h>
#import "DeviceFOTAInfo.h"
#import "DeviceFOTAStatusInfo.h"
/*
 *设备底板固件升级接口
 *
*/
@protocol UpDeviceFOTA <NSObject>

/**
 *  @brief  开始设备底板固件的升级，执行成功后，设备的最新升级状态 会通过uSDKDevice的代理方法 持续上报
 *
 *  @param finishBlock 操作结果
 *
 *  @since  1.1.0
 */
- (void)startBoardFOTA:(void (^)(UpDeviceResult *result))finishBlock;

/**
 *  @brief  检查设备底板固件的版本信息
 *
 *  @param finishBlock 操作结果,成功时DeviceFOTAInfo在result的extraData中。（DeviceFOTAInfo ：设备固件版本信息） 注意：当isNeedFOTA字段为 YES 时，表明设备有需要更新的固件版本。当isNeedFOTA字段为 NO 时，表明设备不支持升级或没有需要更新的固件版本。
 *
 *  @since  1.1.0
 */
- (void)checkBoardFOTAInfoSuccess:(void (^)(UpDeviceResult *result))finishBlock;

/**
 *  @brief  查询当前设备底板固件的升级状态
 *
 *  @param finishBlock 操作结果,成功时DeviceFOTAStatusInfo在result的extraData中。(DeviceFOTAStatusInfo 设备升级进度信息)
 *
 *  @since  1.1.0
 */
- (void)fetchBoardFOTAStatusSuccess:(void (^)(UpDeviceResult *result))finishBlock;

/**
 *  @brief  开始设备模块升级
 *
 *  @param finishBlock 操作结果,上报进度时DeviceOTAStatusInfo在result的extraData中。(DeviceOTAStatusInfo 设备（模块）升级进度信息)。成功h或失败时只返回结果，不返回extraData。
 *
 *  @since  1.3.0
 */
- (void)startModuleUpdateSuccess:(void (^)(UpDeviceResult *result))finishBlock;

/**
 *  @brief  获取模块版本号
 *
 *  @return 设备模块版本。
 *
 *  @since  1.4.6
 */
- (NSString *)getSmartLinkSoftwareVersion;

/**
 *  @brief  开始设备模块升级
 *
 *  @traceId 链式跟踪打点，全流程打点唯一标识
 *  @firmwareId 整机固件唯一标识
 *  @param finishBlock 接口执行完成时回调。
 *
 *  @since  6.1.3
 */
- (void)startFOTAWithDeviceFOTA:(NSString *)traceId firmwareId:(NSString *)firmwareId finishBlock:(void (^)(UpDeviceResult *result))finishBlock;
@end
