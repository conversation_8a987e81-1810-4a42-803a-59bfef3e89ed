//
//  UpDeviceBLE.h
//  Pods
//
//  Created by gump on 27/8/2019.
//

/*
 *蓝牙接口
 */
@protocol UpDeviceBLE <NSObject>

/**
 获取蓝牙历史数据, 获取成功后，数据将通过uSDKDeviceDelegate协议的device:didReceiveBLEHistoryData:currentCount:totalCount:方法进行上报
 
 @param finishBlock 操作结果
 @since 1.3.1
 */
- (void)fetchBLEHistoryData:(void (^)(UpDeviceResult *result))finishBlock;

/**
 取消获取蓝牙历史数据
 
 @param finishBlock 操作结果
 @since 1.3.1
 */

- (void)cancelFetchBLEHistoryData:(void (^)(UpDeviceResult *result))finishBlock;

/**
 获取设备蓝牙连接状态
 
 @param finishBlock 操作结果
  */
- (void)getBleState:(void (^)(UpDeviceResult *result))finishBlock;

@end
