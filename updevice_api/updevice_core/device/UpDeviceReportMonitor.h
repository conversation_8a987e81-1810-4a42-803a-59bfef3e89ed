//
//  UpDeviceReportLimiter.h
//  UPDevice
//
//  Created by <PERSON><PERSON><PERSON>   on 2025/3/18.
//

#import <Foundation/Foundation.h>
#import "UpDevice.h"
NS_ASSUME_NONNULL_BEGIN
@protocol UpDeviceReportMonitorRunnable <NSObject>
// 流控数据输出
@optional
- (void)monitorRunnable:(NSArray<id<UpDevice>> *_Nullable)deviceData;
@optional
- (void)monitorRunnable:(id<UpDevice>)deviceData event:(NSInteger)event;
@end

@protocol UpDeviceReportMonitorDelegate <NSObject>
// 数据输入
- (void)execute:(id<UpDevice> _Nullable)deviceData event:(NSInteger)event runnable:(id<UpDeviceReportMonitorRunnable> _Nullable)runnable;
@end

// 单设备流控
@interface UpDeviceReportMonitor : NSObject <UpDeviceReportMonitorDelegate>
@end

// 批量设备流控
@interface UpDeviceManagerReportMonitor : NSObject <UpDeviceReportMonitorDelegate>
@end
NS_ASSUME_NONNULL_END
