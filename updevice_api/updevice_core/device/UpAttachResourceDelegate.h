//
//  UpAttachResourceDelegate.h
//  updevice_api
//
//  Created by 冉东军 on 2021/3/17.
//  Copyright © 2021 Haierfc. All rights reserved.
//

#import <Foundation/Foundation.h>

NS_ASSUME_NONNULL_BEGIN

@protocol UpAttachResourceDelegate <NSObject>
/// 订阅资源
/// @param resourceName 资源名称
/// @param finishBlock 完成回调
- (void)attachResourceWithDecode:(NSString *)resourceName finishBlock:(void (^)(UpDeviceResult *result))finishBlock;

/// 订阅未解码资源
/// @param resourceName 资源名称
/// @param finishBlock 完成回调
- (void)attachResource:(NSString *)resourceName finishBlock:(void (^)(UpDeviceResult *_Nonnull))finishBlock;

/// 解订阅未解码资源
/// @param resourceName 资源名称
/// @param finishBlock 完成回调
- (void)detachResource:(NSString *)resourceName finishBlock:(void (^)(UpDeviceResult *_Nonnull))finishBlock;

@end

NS_ASSUME_NONNULL_END
