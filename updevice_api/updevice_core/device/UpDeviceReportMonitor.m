//
//  UpDeviceReportLimiter.m
//  UPDevice
//
//  Created by <PERSON><PERSON><PERSON>   on 2025/3/18.
//

#import "UpDeviceReportMonitor.h"
#import <os/lock.h>
@interface UpDeviceReportMonitor ()
@property (nonatomic, copy) id<UpDeviceReportMonitorRunnable> currentRunnable;
@property (nonatomic, assign) NSTimeInterval lastPostTimeInterval;
@property (nonatomic, assign) NSInteger postCount;
@property (nonatomic, assign) NSUInteger kMaxPostCount;
@property (nonatomic, assign) NSUInteger kReportInterval;
@property (nonatomic, assign) BOOL abnormal;
@property (nonatomic, assign) NSInteger currentEvent;
@end

@implementation UpDeviceReportMonitor

- (instancetype)init
{
    self = [super init];
    if (self) {
        _kMaxPostCount = 40;
        _kReportInterval = 1000;
        _lastPostTimeInterval = [self getCurrentTimestamp];
        _postCount = 0;
        _abnormal = NO;
        _currentEvent = 0;
    }
    return self;
}

#pragma mark 输入上报的数据
- (void)execute:(id<UpDevice> _Nullable)deviceData event:(NSInteger)event runnable:(id<UpDeviceReportMonitorRunnable> _Nullable)runnable
{
    if (!deviceData || !runnable) {
        return;
    }
    _currentRunnable = runnable;

    //存储当前Event
    _currentEvent = event;

    if (![self.currentRunnable respondsToSelector:@selector(monitorRunnable:event:)]) {
        return;
    }

    @synchronized(self)
    {
        NSTimeInterval currentTime = [self getCurrentTimestamp];
        NSTimeInterval timeElapsed = currentTime - self.lastPostTimeInterval;

        if (self.abnormal) {

            if (timeElapsed >= self.kReportInterval) {
                [self.currentRunnable monitorRunnable:deviceData event:self.currentEvent];
                self.lastPostTimeInterval = [self getCurrentTimestamp];
                dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(1 * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
                  [self.currentRunnable monitorRunnable:deviceData event:self.currentEvent];
                });
            }
        }
        else {

            if (timeElapsed >= self.kReportInterval) {
                self.postCount = 0;
                self.lastPostTimeInterval = [self getCurrentTimestamp];
            }

            self.postCount++;
            self.abnormal = self.postCount > self.kMaxPostCount;
            [self.currentRunnable monitorRunnable:deviceData event:self.currentEvent];
        }
    }
}

- (NSTimeInterval)getCurrentTimestamp
{
    return [[NSDate date] timeIntervalSince1970] * 1000;
}

@end

@interface UpDeviceManagerReportMonitor ()
@property (nonatomic, copy) id<UpDeviceReportMonitorRunnable> currentRunnable;
// 锁，配合cache Dictionary使用
@property (assign, nonatomic) os_unfair_lock readWritelock;
@property (nonatomic, strong) NSMutableDictionary *reportCacheDictionary;
@property (nonatomic, strong) NSOperationQueue *mainQueue;
@end

@implementation UpDeviceManagerReportMonitor

- (instancetype)init
{
    self = [super init];
    if (self) {
        _readWritelock = OS_UNFAIR_LOCK_INIT;
        _reportCacheDictionary = [[NSMutableDictionary alloc] init];
        _mainQueue = [NSOperationQueue mainQueue];
    }
    return self;
}


- (void)execute:(id<UpDevice> _Nullable)deviceData event:(NSInteger)event runnable:(id<UpDeviceReportMonitorRunnable> _Nullable)runnable
{
    // 批量设备流控，不处理event

    if (!deviceData || !runnable) {
        return;
    }
    _currentRunnable = runnable;
    if (![self.currentRunnable respondsToSelector:@selector(monitorRunnable:)]) {
        return;
    }

    if (self.mainQueue.operationCount == 0) {
        [self.mainQueue addOperation:[self createOperation:@selector(processDeviceReport:) device:deviceData]];
    }
    else {
        os_unfair_lock_lock(&_readWritelock);
        [self.reportCacheDictionary setObject:deviceData forKey:deviceData.getInfo.deviceId];
        os_unfair_lock_unlock(&_readWritelock);
    }
}

- (NSOperation *)createOperation:(SEL)selector device:(id<UpDevice>)deviceData
{
    NSInvocationOperation *operate = [[NSInvocationOperation alloc] initWithTarget:self selector:selector object:deviceData];
    [operate addObserver:self forKeyPath:@"isFinished" options:NSKeyValueObservingOptionNew context:nil];
    [operate setQueuePriority:NSOperationQueuePriorityHigh];
    return operate;
}

- (void)observeValueForKeyPath:(NSString *)keyPath ofObject:(id)object change:(NSDictionary<NSKeyValueChangeKey, id> *)change context:(void *)context
{
    os_unfair_lock_lock(&_readWritelock);
    if (self.reportCacheDictionary.count > 0) {
        [self.mainQueue addOperation:[self createOperation:@selector(processDeviceReportWithCache) device:nil]];
    }
    os_unfair_lock_unlock(&_readWritelock);
}

- (void)processDeviceReport:(id<UpDevice>)deviceData
{
    [self.currentRunnable monitorRunnable:@[ deviceData ]];
}

- (void)processDeviceReportWithCache
{
    os_unfair_lock_lock(&_readWritelock);
    if (self.reportCacheDictionary.count != 0) {
        [self.currentRunnable monitorRunnable:[self.reportCacheDictionary.allValues mutableCopy]];
    }
    [self.reportCacheDictionary removeAllObjects];
    os_unfair_lock_unlock(&_readWritelock);
}

@end
