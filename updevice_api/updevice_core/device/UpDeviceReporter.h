//
//  UpDeviceReporter.h
//  updevice_api
//
//  Created by <PERSON><PERSON> on 2018/12/19.
//  Copyright © 2018 Haierfc. All rights reserved.
//

#import <Foundation/Foundation.h>
#import "UpDeviceListener.h"
#import "UpDevice.h"

@interface UpDeviceReporter : NSObject
- (void)add:(id<UpDeviceListener>)listener;
- (void)remove:(id<UpDeviceListener>)listener;
- (void)addReceiver:(id<UpDeviceReceiver>)receiver;
- (void)removeReceiver:(id<UpDeviceReceiver>)receiver;
- (void)notifyDeviceChange:(NSInteger)event device:(id<UpDevice>)device listeners:(NSArray<id<UpDeviceListener>> *)listeners;
- (void)notifyDeviceChange:(NSInteger)event device:(id<UpDevice>)device;

//扩展方法 用于传一些临时值
- (void)notifyDeviceChange:(NSInteger)event device:(id<UpDevice>)device listeners:(NSArray<id<UpDeviceListener>> *)listeners extra:(id)extra;
- (void)notifyDeviceChange:(NSInteger)event device:(id<UpDevice>)device extra:(id)extra;

- (void)notifyDeviceReceive:(id<UpDevice>)device name:(NSString *)name data:(id)data;
- (void)removeAll;
@end
