//
//  UpDeviceReporter.m
//  updevice_api
//
//  Created by <PERSON><PERSON> on 2018/12/19.
//  Copyright © 2018 Haierfc. All rights reserved.
//

#import "UpDeviceReporter.h"
#import "UpDeviceReceiver.h"
#import "UPDeviceLog.h"
#import "UpDeviceBase.h"
#import "UpDeviceReportMonitor.h"
#import <os/lock.h>

@interface UpDeviceReporter () <UpDeviceReportMonitorRunnable>
@property (nonatomic, strong) NSMutableSet<id<UpDeviceListener>> *listenerSet;
@property (assign, nonatomic) os_unfair_lock readWritelock;


@property (nonatomic, strong) NSMutableSet<id<UpDeviceReceiver>> *receiverSet;

// 设备上报流量控制
@property (nonatomic, strong) UpDeviceReportMonitor *currentReportMonitor;

@end

@implementation UpDeviceReporter
- (instancetype)init;
{
    if (self = [super init]) {
        _listenerSet = [NSMutableSet set];
        _receiverSet = [NSMutableSet set];
        _currentReportMonitor = [[UpDeviceReportMonitor alloc] init];
        _readWritelock = OS_UNFAIR_LOCK_INIT;
    }
    return self;
}
- (void)add:(id<UpDeviceListener>)listener
{
    if (listener == nil) {
        return;
    }
    os_unfair_lock_lock(&_readWritelock);
    [_listenerSet addObject:listener];
    os_unfair_lock_unlock(&_readWritelock);
}

- (void)addReceiver:(id<UpDeviceReceiver>)receiver
{
    if (receiver == nil) {
        return;
    }
    [_receiverSet addObject:receiver];
}
- (void)removeReceiver:(id<UpDeviceReceiver>)receiver
{
    if (receiver == nil) {
        return;
    }
    [_receiverSet removeObject:receiver];
}
- (void)remove:(id<UpDeviceListener>)listener
{
    if (listener == nil) {
        return;
    }
    os_unfair_lock_lock(&_readWritelock);
    [_listenerSet removeObject:listener];
    os_unfair_lock_unlock(&_readWritelock);
}

- (void)removeAll
{
    os_unfair_lock_lock(&_readWritelock);
    [_listenerSet removeAllObjects];
    os_unfair_lock_unlock(&_readWritelock);
}

- (NSArray<id<UpDeviceListener>> *)getListenerList
{
    os_unfair_lock_lock(&_readWritelock);
    NSArray *array = [NSArray arrayWithArray:[_listenerSet allObjects]];
    os_unfair_lock_unlock(&_readWritelock);
    return array;
}
- (NSMutableArray<id<UpDeviceReceiver>> *)getReceiverList
{
    NSMutableArray *array = (NSMutableArray *)[_receiverSet allObjects];
    return array;
}
- (void)notifyDeviceChange:(NSInteger)event device:(id<UpDevice>)device
{
    //    [self notifyDeviceChange:event device:device listeners:[self getListenerList] extra:nil];
    [self.currentReportMonitor execute:device event:event runnable:self];
}

- (void)notifyDeviceChange:(NSInteger)event device:(id<UpDevice>)device extra:(id)extra
{
    [self notifyDeviceChange:event device:device listeners:[self getListenerList] extra:extra];
}

- (void)notifyDeviceChange:(NSInteger)event device:(id<UpDevice>)device listeners:(NSArray<id<UpDeviceListener>> *)listeners
{
    [self notifyDeviceChange:event device:device listeners:listeners extra:nil];
}

- (void)notifyDeviceChange:(NSInteger)event device:(id<UpDevice>)device listeners:(NSArray<id<UpDeviceListener>> *)listeners extra:(id)extra
{
    if (listeners == nil || listeners.count == 0) {
        return;
    }
    if ([device conformsToProtocol:@protocol(UpDevice)]) {
    }
    else {
        UPLogError(kUPDevicePrefix, @"%s[%d]上报的设备接口实现对象格式有误！Object:%@", __PRETTY_FUNCTION__, __LINE__, device);
    }

    void (^sendReport)(void) = ^() {
      for (id<UpDeviceListener> listener in listeners) {
          if (extra) {
              if ([listener respondsToSelector:@selector(onDeviceReport:device:extra:)]) {
                  [listener onDeviceReport:event device:device extra:extra];
              }
          }
          else {
              if ([listener respondsToSelector:@selector(onDeviceReport:device:)]) {
                  [listener onDeviceReport:event device:device];
              }
          }
      }
    };
    if ([NSThread isMainThread]) {
        sendReport();
    }
    else {
        dispatch_async(dispatch_get_main_queue(), sendReport);
    }
}


- (void)notifyDeviceReceive:(id<UpDevice>)device name:(NSString *)name data:(id)data
{
    if ([device conformsToProtocol:@protocol(UpDevice)]) {
        NSString *deviceId = device.getInfo.deviceId;
        UPLogDebug(kUPDevicePrefix, @"%s[%d]设备(%@)接收到数据！name:%@,data:%@", __PRETTY_FUNCTION__, __LINE__, deviceId, name, data);
    }
    else {
        UPLogError(kUPDevicePrefix, @"%s[%d]上报的设备接口实现对象格式有误！Object:%@", __PRETTY_FUNCTION__, __LINE__, device);
    }
    [self notifyDeviceReceive:device name:name data:data receivers:[self getReceiverList]];
}
- (void)notifyDeviceReceive:(id<UpDevice>)device name:(NSString *)name data:(id)data receivers:(NSArray<id<UpDeviceReceiver>> *)receivers
{
    if (receivers == nil) {
        return;
    }
    for (id<UpDeviceReceiver> listener in receivers) {
        [listener onDataReceived:device name:name data:data];
    }
}

- (void)monitorRunnable:(id<UpDevice>)deviceData event:(NSInteger)event
{
    [self notifyDeviceChange:event device:deviceData listeners:[self getListenerList] extra:nil];
}

@end
