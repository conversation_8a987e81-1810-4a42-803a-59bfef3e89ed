//
//  UpDeviceFocus.h
//  updevice_api
//
//  Created by gump on 7/2/2020.
//  Copyright © 2020 Haierfc. All rights reserved.
//

#import "UpDevice.h"

/**
 * description: 标记设备进入详情页的焦点
 * 问题背景：统帅MY-WIFI&BLE冰箱设备断网切换BLE控制等待约3分钟问题优化
 */
@protocol UpDeviceFocus <NSObject>

/**
 标记设备进入焦点（详情页）
 
 进入焦点后，如果大循环控制超时，则提升蓝牙通道的优先级高于大循环且低于小循环通道
 @return 重复进入返回NO
 @since 1.5.2
 */
- (BOOL)inFocus;


/**
 标记设备退出焦点（详情页）
 
 @return 重复退出返回NO
 @since 1.5.2
 */
- (BOOL)outFocus;

@end
