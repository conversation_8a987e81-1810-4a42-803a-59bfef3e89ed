//
//  UpDeviceCache.m
//  updevice_api
//
//  Created by <PERSON><PERSON> on 2018/12/19.
//  Copyright © 2018 Haierfc. All rights reserved.
//

#import "UpDeviceCache.h"
#import "UpDeviceState.h"
#import "UpDeviceOnlineStatus.h"

@interface UpDeviceCache ()

@property (nonatomic, strong) id<UpDeviceInfo> deviceInfo;
@property (atomic, assign) UpDeviceState stateRef;
@property (atomic, strong) NSMutableSet<id<UpDeviceInfo>> *baseInfoRef;
@property (atomic, assign) UpDeviceConnection connectionRef;
@property (atomic, assign) UpDeviceControlState controlStateRef;
@property (atomic, assign) NSInteger faultInformationCodeRef;
@property (nonatomic, strong) NSMutableDictionary *attributeMap;
@property (nonatomic, strong) NSMutableArray<id<UpDeviceCaution>> *cautionList;
@property (atomic, strong) NSMutableSet<id<UpDevice>> *parentRef;
@property (nonatomic, strong) NSMutableDictionary *subDevMap;
@property (nonatomic, strong) id<UpDeviceFOTAStatusInfo> FOTAStatusInfo; //设备升级进度信息
@property (nonatomic, strong) id<UpDeviceBLEHistoryInfo> myBLEHistoryInfo; //蓝牙历史数据
@property (nonatomic, strong) NSData *myBLERealData; //蓝牙实时数据
@property (nonatomic, assign) UpDeviceRealOnline myRealOnlineStatus; //设备真实在线状态
@property (nonatomic, assign) UpDeviceRealOnlineV2 myRealOnlineStateV2; //设备真实在线状态V2
@property (nonatomic, assign) UpDeviceSleepState mySleepState; //设备睡眠状态
@property (nonatomic, assign) UpDeviceNetworkLevel myNetworkLevel; //设备网络质量
@property (nonatomic, assign) UpDeviceConnection myLocalState; //设备本地wifi连接状态
@property (nonatomic, assign) UpDeviceConnection myBleState; //设备蓝牙连接状态
@property (nonatomic, assign) UpDeviceOfflineCause myOfflineCause; //设备离线原因
@property (nonatomic, assign) NSInteger myOfflineDays; //设备离线天数
@property (nonatomic, assign) UpDeviceOnlyConfigState myOnlyConfigState; //设备仅配网状态

@end
@implementation UpDeviceCache

+ (UpDeviceCache *)getUpDeviceCache:(id<UpDeviceInfo>)deviceInfo
{
    return [[UpDeviceCache alloc] initWithState:deviceInfo state:UpDeviceState_RELEASED];
}

- (instancetype)initWithState:(id<UpDeviceInfo>)deviceInfo state:(UpDeviceState)state
{
    if (self = [super init]) {
        self.deviceInfo = deviceInfo;
        self.stateRef = state;
        self.attributeMap = [NSMutableDictionary dictionary];
        self.cautionList = [NSMutableArray array];
        self.subDevMap = [NSMutableDictionary dictionary];
        self.connectionRef = UpDeviceConnection_OFFLINE;
        self.baseInfoRef = [NSMutableSet set];
        self.parentRef = [NSMutableSet set];
        self.FOTAStatusInfo = nil;
        self.myBLEHistoryInfo = nil;
        self.myBLERealData = nil;
        BOOL online = [[deviceInfo.getExtras objectForKey:@"DI-Basic.online"] boolValue];
        self.myRealOnlineStatus = online ? UpDeviceRealOnline_ONLINE : UpDeviceRealOnline_OFFLINE;
        self.myRealOnlineStateV2 = online ? UpDeviceRealOnlineV2_ONLINE_NOT_READY : UpDeviceRealOnlineV2_OFFLINE;
        self.myNetworkLevel = UpDeviceNetworkLevelUnKnown;
        self.myLocalState = UpDeviceConnection_OFFLINE;
        self.myBleState = UpDeviceConnection_DISCONNECTED;
        self.mySleepState = UpDeviceSleepStateUnsleeping;
        self.myOfflineCause = UpDeviceOfflineCause_None;
        self.myOfflineDays = 0;
        self.myOnlyConfigState = UpDeviceOnlyConfigStateUnConfigurable;
    }
    return self;
}
- (UpDeviceState)getState
{
    return self.stateRef;
}
- (void)setState:(UpDeviceState)state
{
    self.stateRef = state;
}

- (id<UpDeviceInfo>)getInfo
{
    return self.deviceInfo;
}

- (void)setInfo:(id<UpDeviceInfo>)baseInfo
{
    [self.baseInfoRef addObject:baseInfo];
}

- (void)updateBaseInfo:(id<UpDeviceBaseInfo>)baseInfo
{
    [self.deviceInfo updateBaseInfo:baseInfo];
}

- (UpDeviceConnection)getConnection
{
    return self.connectionRef;
}

- (void)setConnection:(UpDeviceConnection)connection
{
    self.connectionRef = connection;
}

- (UpDeviceControlState)getControlState
{
    return self.controlStateRef;
}

- (void)setControlState:(UpDeviceControlState)state
{
    self.controlStateRef = state;
}

- (NSInteger)getFaultInformationCode
{
    return self.faultInformationCodeRef;
}

- (void)setFaultInformationCode:(NSInteger)code
{
    self.faultInformationCodeRef = code;
}

- (UpDeviceRealOnline)getDeviceOnlineStatus
{
    return self.myRealOnlineStatus;
}

- (void)setRealOnlineStatus:(UpDeviceRealOnline)status
{
    self.myRealOnlineStatus = status;
}

- (UpDeviceRealOnlineV2)getDeviceOnlineStateV2
{
    return self.myRealOnlineStateV2;
}

- (void)setRealOnlineStateV2:(UpDeviceRealOnlineV2)stateV2
{

    self.myRealOnlineStateV2 = stateV2;
}

- (void)setSleepState:(UpDeviceSleepState)state
{
    self.mySleepState = state;
}

- (UpDeviceSleepState)getSleepState
{
    return self.mySleepState;
}

- (void)setNetworkLevel:(UpDeviceNetworkLevel)level
{
    self.myNetworkLevel = level;
}

- (UpDeviceNetworkLevel)getNetworkLevel
{
    return self.myNetworkLevel;
}

- (void)setWifiLocalState:(UpDeviceConnection)state
{
    self.myLocalState = state;
}

- (UpDeviceConnection)getWifiLocalState
{
    return self.myLocalState;
}

- (void)setBleState:(UpDeviceConnection)state
{
    self.myBleState = state;
}

- (UpDeviceConnection)getBleState
{
    return self.myBleState;
}

- (NSArray<id<UpDeviceAttribute>> *)getAttributeList
{
    @synchronized(_attributeMap)
    {
        if (_attributeMap == nil || _attributeMap.allValues.count == 0) {
            return nil;
        }

        NSMutableArray *list = [NSMutableArray arrayWithArray:_attributeMap.allValues];
        return list;
    }
}

- (void)clearAttributeList
{
    @synchronized(_attributeMap)
    {
        [_attributeMap removeAllObjects];
    }
}

- (void)setAttributeList:(NSArray *)collection
{
    @synchronized(_attributeMap)
    {
        if (collection == nil || collection.count == 0) {
            return;
        }
        for (id<UpDeviceAttribute> attribute in collection) {

            NSString *name = [attribute name];
            if (name == nil || attribute == nil) {
                continue;
            }
            [_attributeMap setObject:attribute forKey:name];
        }
    }
}

- (id<UpDeviceAttribute>)getAttributeByName:(NSString *)name
{
    if (name == nil) {
        return nil;
    }
    @synchronized(_attributeMap)
    {
        return _attributeMap[name];
    }
}
- (NSArray<id<UpDeviceAttribute>> *)getAttributeByNames:(NSArray<NSString *> *)names
{
    if (names.count == 0) {
        return @[];
    }
    NSMutableArray *array = [NSMutableArray array];
    for (NSString *name in names) {
        id<UpDeviceAttribute> attr = [self getAttributeByName:name];
        if (attr)
            [array addObject:attr];
    }
    return array;
}

- (NSMutableArray<id<UpDeviceCaution>> *)getCautionList
{
    @synchronized(_cautionList)
    {
        NSMutableArray *list = [NSMutableArray array];
        if (_cautionList) {
            list = [NSMutableArray arrayWithArray:_cautionList];
        }
        return list;
    }
}

- (void)clearCautionList
{
    @synchronized(_cautionList)
    {
        [_cautionList removeAllObjects];
    }
}

- (void)setCautionList:(NSMutableArray<id<UpDeviceCaution>> *)collection
{
    @synchronized(_cautionList)
    {
        _cautionList = collection;
    }
}

- (id)getParent
{
    return [_parentRef anyObject];
}

- (void)setParent:(id)parent
{
    [_parentRef addObject:parent];
}

- (id<UpDevice>)getSubDevByNo:(NSString *)subDevNo
{
    return self.subDevMap[subDevNo];
}

- (NSArray *)getSubDevList
{
    NSMutableArray *list = [NSMutableArray array];

    if (self.connectionRef != UpDeviceConnection_READY) {
        return list;
    }

    if (_subDevMap) {
        list = [NSMutableArray arrayWithArray:_subDevMap.allValues];
    }
    return list;
}

- (void)clearSubDevList
{
    [_subDevMap removeAllObjects];
}

- (void)setSubDevList:(NSMutableArray<id<UpDevice>> *)collection
{
    if (collection == nil || collection.count == 0) {
        return;
    }
    for (id<UpDevice> subDev in collection) {
        id<UpDeviceBaseInfo> dev = [subDev getInfo];
        NSString *subDevNo = [dev subDevNo];
        if (subDev) {
            [_subDevMap setObject:subDev forKey:subDevNo];
        }
    }
}

- (void)reset
{
    [self setState:UpDeviceState_RELEASED];
    [self setConnection:UpDeviceConnection_OFFLINE];
    [self clearAttributeList];
    [self clearCautionList];
}

- (void)setTheFOTAStatusInfo:(id<UpDeviceFOTAStatusInfo>)FOTAStatusInfo
{
    self.FOTAStatusInfo = FOTAStatusInfo;
}

- (id<UpDeviceFOTAStatusInfo>)getDeviceFOTAStatusInfo
{
    return self.FOTAStatusInfo;
}

- (void)setBLEHistoryInfo:(id<UpDeviceBLEHistoryInfo>)BLEHistoryInfo
{
    self.myBLEHistoryInfo = BLEHistoryInfo;
}
- (void)setBLERealData:(NSData *)data
{
    self.myBLERealData = data;
}

- (id<UpDeviceBLEHistoryInfo>)getDeviceBLEHistoryInfo
{
    return self.myBLEHistoryInfo;
}

- (NSData *)getDeviceBLERealData
{
    return self.myBLERealData;
}

- (void)setDeviceOfflineCause:(UpDeviceOfflineCause)offlineCause
{
    self.myOfflineCause = offlineCause;
}

- (UpDeviceOfflineCause)getDeviceOfflineCause
{
    return self.myOfflineCause;
}

- (void)setDeviceOfflineDays:(NSInteger)offlineDays
{
    self.myOfflineDays = offlineDays;
}

- (NSInteger)getDeviceOfflineDays
{
    return self.myOfflineDays;
}

- (void)setDeviceOnlyConfigState:(UpDeviceOnlyConfigState)onlyConfigState
{
    self.myOnlyConfigState = onlyConfigState;
}

- (UpDeviceOnlyConfigState)getDeviceOnlyConfigState
{
    return self.myOnlyConfigState;
}

- (void)clearAttributeListValues
{
    @synchronized(_attributeMap)
    {
        for (id key in _attributeMap.allKeys) {
            [_attributeMap setObject:@"" forKey:key];
        }
    }
}

@end
