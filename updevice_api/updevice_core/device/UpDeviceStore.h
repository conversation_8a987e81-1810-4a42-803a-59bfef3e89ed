//
//  UpDeviceStore.h
//  updevice_api
//
//  Created by <PERSON><PERSON> on 2018/12/18.
//  Copyright © 2018 Haierfc. All rights reserved.
//

#import <Foundation/Foundation.h>
#import "UpDeviceAttribute.h"
#import "UpDeviceConnection.h"
#import "UpDeviceCaution.h"
#import "UpDeviceInfo.h"
#import "UpDeviceState.h"
#import "UpDeviceFOTAStatusInfo.h"
#import "UpDeviceBLEHistoryInfo.h"
#import "UpDeviceControlState.h"
#import "UpDeviceOnlineStatus.h"
#import "UpDeviceOnlineStateV2.h"
#import "UpDeviceNetworkLevel.h"
#import "UpDeviceOfflineCause.h"
#import "UpDeviceOnlyConfigState.h"

/**
 * 用户缓存设备连接状态，属性和告警的接口
 */
@protocol UpDeviceStore <NSObject>

- (UpDeviceState)getState;
- (id<UpDeviceInfo>)getInfo;
- (UpDeviceConnection)getConnection;
- (UpDeviceControlState)getControlState;
- (void)setControlState:(UpDeviceControlState)state;
- (NSInteger)getFaultInformationCode;
- (UpDeviceRealOnline)getDeviceOnlineStatus;
- (UpDeviceRealOnlineV2)getDeviceOnlineStateV2;
- (UpDeviceSleepState)getSleepState;
- (UpDeviceNetworkLevel)getNetworkLevel;
- (UpDeviceConnection)getWifiLocalState;
- (UpDeviceConnection)getBleState;
- (void)setWifiLocalState:(UpDeviceConnection)state;
- (UpDeviceConnection)getBleState;
- (NSArray<id<UpDeviceAttribute>> *)getAttributeList;
- (id<UpDeviceAttribute>)getAttributeByName:(NSString *)name;
- (NSArray<id<UpDeviceAttribute>> *)getAttributeByNames:(NSArray<NSString *> *)names;
- (NSArray<id<UpDeviceCaution>> *)getCautionList;
- (id<UpDeviceFOTAStatusInfo>)getDeviceFOTAStatusInfo;
- (id<UpDeviceBLEHistoryInfo>)getDeviceBLEHistoryInfo;
- (NSData *)getDeviceBLERealData;
- (UpDeviceOfflineCause)getDeviceOfflineCause;
- (NSInteger)getDeviceOfflineDays;
- (UpDeviceOnlyConfigState)getDeviceOnlyConfigState;
// -------------------- 子设备相关 --------------------
- (id)getParent;
- (void)setParent:(id)parent;
- (NSArray *)getSubDevList;
- (id)getSubDevByNo:(NSString *)subDevNo;

@end
