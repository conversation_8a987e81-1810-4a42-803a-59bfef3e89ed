//
//  UpDeviceFilters.h
//  updevice_api
//
//  Created by <PERSON><PERSON> on 2019/1/10.
//  Copyright © 2019 Haierfc. All rights reserved.
//

#import <Foundation/Foundation.h>
#import "UpDeviceFilter.h"

@interface CompositeFilter : NSObject <UpDeviceFilter>
@property (nonatomic, strong) NSMutableArray<id<UpDeviceFilter>> *filters;
+ (CompositeFilter *)CompositeFilter:(NSMutableArray<id<UpDeviceFilter>> *)filters;
@end

@interface StringFilter : NSObject <UpDeviceFilter>
@property (nonatomic, strong) id<FilterDataProvider> provider;
@property (nonatomic, strong) NSMutableArray *checkSet;
+ (StringFilter *)StringFilter:(NSMutableArray *)collection provider:(id<FilterDataProvider>)provider;
@end

@interface NotNullFilter : NSObject <UpDeviceFilter>
+ (NotNullFilter *)NotNullFilter:(NSMutableArray<id<UpDeviceFilter>> *)filters;
@end

@interface UpDeviceFilters : NSObject
- (id<UpDeviceFilter>)createStringFilter:(id<FilterDataProvider>)provider array:(NSMutableArray *)array;
@end
