Feature: 资源管理器

  功能范围：
  主要提供设备资源相关接口
  外部依赖：
  - UpResource

  接口说明：
  1.根据设备id获取设备侧应用模型
  -设备id为空,接口调用失败
  -设备prodNo为空,接口调用失败
  -resource库本地有资源时，返回本地资源，否则获取服务器资源并返回

  Background:
    Given "UpResource"使用"模拟的"
    Given "UpDeviceManager"使用"模拟的"


#  pict 生成组合情况
#  localResoure1  {"active":true,"path":"localFakePath"}
#  localResoure2  {"active":true,"path":""}
#  localResoure3  {"active":false,"path":"localFakePath"}
#  localResoure4  空

#  serverResoure1 {"active":true,"path":"serverFakePath"}
#  serverResoure2 {"active":true,"path":""}
#  serverResoure3 {"active":false,"path":"serverFakePath"}
#  serverResoure4 空
#
#
#  deviceId:   fakeDeviceId
#  prodNo:    fakeProdNo
#  localResoure&locaContent:{"active":true,"path":"localFakePath"}&localFakeAppFuncModel ,  {"active":true,"path":"localFakePath"}&空字符串 ,  {"active":true,"path":""}& ,  {"active":false,"path":"localFakePath"}& , 空&空
#  resourceSuccessBool&serverResoure&serveContent: 成功&{"active":true,"path":"serverFakePath"}&serverFakeAppFuncModel ,成功&{"active":true,"path":"serverFakePath"}&空字符串 ,成功&{"active":true,"path":""}&serverFakeAppFuncModel ,成功&{"active":false,"path":"serverFakePath"}&serverFakeAppFuncModel ,失败& &serverFakeAppFuncModel,成功&空&serverFakeAppFuncModel
  Scenario Outline:[10000] 根据设备id获取设备侧应用模型,resource库本地有资源时，返回本地资源，否则获取服务器资源并返回
    Given 本地路径"localFakePath"文件内容为"<locaContent>"
    Given 本地路径"serverFakePath"文件内容为"<serveContent>"
    Given upDeviceManager中设备Id"fakeDeviceId"的设备信息为:
      | deviceId     | prodNo   |
      | fakeDeviceId | <prodNo> |
    Given UpResource库根据prodNo:"fakeProdNo"查询本地设备资源,接口返回:
      | resource       |
      | <localResoure> |
    Given UpResource库根据prodNo:"fakeProdNo"获取资源接口,服务器查询结果回调返回:
      | success               | resource        |
      | <resourceSuccessBool> | <serverResoure> |
    When 用户调用获取设备侧应用模型接口,参数为"<deviceId>"
    Then 获取设备侧应用模型接口结果为"<result>",回调结果的extraData值为"<appFuncModel>"

    Examples:
      | deviceId     | prodNo     | localResoure                            | locaContent           | resourceSuccessBool | serverResoure                            | serveContent           | result | appFuncModel           |
      | fakeDeviceId | fakeProdNo | {"active":false,"path":"localFakePath"} |                       | 成功                  | {"active":true,"path":""}                | serverFakeAppFuncModel | 失败     |                        |
      | fakeDeviceId | fakeProdNo |                                         |                       | 成功                  | {"active":true,"path":"serverFakePath"}  | 空字符串                   | 失败     |                        |
      | fakeDeviceId | fakeProdNo | {"active":true,"path":"localFakePath"}  | localFakeAppFuncModel | 成功                  | {"active":true,"path":""}                | serverFakeAppFuncModel | 成功     | localFakeAppFuncModel  |
      | fakeDeviceId | fakeProdNo | {"active":false,"path":"localFakePath"} |                       | 成功                  |                                          | serverFakeAppFuncModel | 失败     |                        |
      | fakeDeviceId | fakeProdNo | {"active":true,"path":"localFakePath"}  | 空字符串                  | 成功                  | {"active":true,"path":"serverFakePath"}  | serverFakeAppFuncModel | 成功     | serverFakeAppFuncModel |
      | fakeDeviceId | fakeProdNo |                                         |                       | 成功                  | {"active":true,"path":""}                | serverFakeAppFuncModel | 失败     |                        |
      | fakeDeviceId | fakeProdNo | {"active":true,"path":""}               |                       | 成功                  | {"active":true,"path":""}                | serverFakeAppFuncModel | 失败     |                        |
      | fakeDeviceId | fakeProdNo | {"active":true,"path":""}               |                       | 成功                  | {"active":true,"path":"serverFakePath"}  | serverFakeAppFuncModel | 成功     | serverFakeAppFuncModel |
      | fakeDeviceId | fakeProdNo | {"active":false,"path":"localFakePath"} |                       | 失败                  |                                          | serverFakeAppFuncModel | 失败     |                        |
      | fakeDeviceId | fakeProdNo | {"active":true,"path":"localFakePath"}  | 空字符串                  | 成功                  | {"active":true,"path":"serverFakePath"}  | 空字符串                   | 失败     |                        |
      | fakeDeviceId | fakeProdNo | {"active":true,"path":"localFakePath"}  | localFakeAppFuncModel | 成功                  | {"active":true,"path":"serverFakePath"}  | serverFakeAppFuncModel | 成功     | localFakeAppFuncModel  |
      | fakeDeviceId | fakeProdNo | {"active":false,"path":"localFakePath"} |                       | 成功                  | {"active":false,"path":"serverFakePath"} | serverFakeAppFuncModel | 失败     |                        |
      | fakeDeviceId | fakeProdNo |                                         |                       | 成功                  |                                          | serverFakeAppFuncModel | 失败     |                        |
      | fakeDeviceId | fakeProdNo | {"active":true,"path":"localFakePath"}  | 空字符串                  | 成功                  | {"active":true,"path":""}                | serverFakeAppFuncModel | 失败     |                        |
      | fakeDeviceId | fakeProdNo | {"active":true,"path":"localFakePath"}  | 空字符串                  | 成功                  |                                          | serverFakeAppFuncModel | 失败     |                        |
      | fakeDeviceId | fakeProdNo | {"active":true,"path":"localFakePath"}  | 空字符串                  | 失败                  |                                          | serverFakeAppFuncModel | 失败     |                        |
      | fakeDeviceId | fakeProdNo | {"active":true,"path":""}               |                       | 失败                  |                                          | serverFakeAppFuncModel | 失败     |                        |
      | fakeDeviceId | fakeProdNo | {"active":false,"path":"localFakePath"} |                       | 成功                  | {"active":true,"path":"serverFakePath"}  | 空字符串                   | 失败     |                        |
      | fakeDeviceId | fakeProdNo |                                         |                       | 成功                  | {"active":false,"path":"serverFakePath"} | serverFakeAppFuncModel | 失败     |                        |
      | fakeDeviceId | fakeProdNo | {"active":true,"path":""}               |                       | 成功                  |                                          | serverFakeAppFuncModel | 失败     |                        |
      | fakeDeviceId | fakeProdNo | {"active":true,"path":"localFakePath"}  | localFakeAppFuncModel | 成功                  | {"active":false,"path":"serverFakePath"} | serverFakeAppFuncModel | 成功     | localFakeAppFuncModel  |
      | fakeDeviceId | fakeProdNo | {"active":true,"path":"localFakePath"}  | localFakeAppFuncModel | 成功                  | {"active":true,"path":"serverFakePath"}  | 空字符串                   | 成功     | localFakeAppFuncModel  |
      | fakeDeviceId | fakeProdNo | {"active":true,"path":"localFakePath"}  | localFakeAppFuncModel | 失败                  |                                          | serverFakeAppFuncModel | 成功     | localFakeAppFuncModel  |
      | fakeDeviceId | fakeProdNo |                                         |                       | 成功                  | {"active":true,"path":"serverFakePath"}  | serverFakeAppFuncModel | 成功     | serverFakeAppFuncModel |
      | fakeDeviceId | fakeProdNo | {"active":true,"path":"localFakePath"}  | 空字符串                  | 成功                  | {"active":false,"path":"serverFakePath"} | serverFakeAppFuncModel | 失败     |                        |
      | fakeDeviceId | fakeProdNo | {"active":true,"path":""}               |                       | 成功                  | {"active":true,"path":"serverFakePath"}  | 空字符串                   | 失败     |                        |
      | fakeDeviceId | fakeProdNo | {"active":false,"path":"localFakePath"} |                       | 成功                  | {"active":true,"path":"serverFakePath"}  | serverFakeAppFuncModel | 成功     | serverFakeAppFuncModel |
      | fakeDeviceId | fakeProdNo | {"active":true,"path":"localFakePath"}  | localFakeAppFuncModel | 成功                  |                                          | serverFakeAppFuncModel | 成功     | localFakeAppFuncModel  |
      | fakeDeviceId | fakeProdNo |                                         |                       | 失败                  |                                          | serverFakeAppFuncModel | 失败     |                        |
      | fakeDeviceId | fakeProdNo | {"active":true,"path":""}               |                       | 成功                  | {"active":false,"path":"serverFakePath"} | serverFakeAppFuncModel | 失败     |                        |


   # pict
  # localResoure1:{"active":false,"path":"localFakePath"}
  # localResoure2:{"active":true,"path":""}
  # localResoure3:空对象
  # localResoure4:无
  # localResoure5:{"active":true,"path":"localFakePathError"}

  # localResoure:localResoure1,localResoure2,localResoure3,localResoure4,localResoure5
  # localResoure:{"active":false,"path":"localFakePath"} ,{"active":true,"path""},空对象,无,{"active":true,"path":"localFakePathError"}
  # locaContent:localFakeAppFuncModel ,空
  Scenario Outline:[10001] 根据设备id获取设备侧应用模型,resource库本地有资源时但信息不正确，会从服务器查询数据返回结果
    Given 本地路径"localFakePath"文件内容为"<locaContent>"
    Given 本地路径"serverFakePath"文件内容为"serverFakeAppFuncModel"
    Given upDeviceManager中设备Id"fakeDeviceId"的设备信息为:
      | deviceId     | prodNo     |
      | fakeDeviceId | fakeProdNo |
    Given UpResource库根据prodNo:"fakeProdNo"查询本地设备资源,接口返回:
      | resource       |
      | <localResoure> |
    Given UpResource库根据prodNo:"fakeProdNo"获取资源接口,服务器查询结果回调返回:
      | success | resource                                |
      | 成功      | {"active":true,"path":"serverFakePath"} |
    When 用户调用获取设备侧应用模型接口,参数为"fakeDeviceId"
    Then 获取设备侧应用模型接口结果为"成功",回调结果的extraData值为"serverFakeAppFuncModel"
    Then 异步调用UpResource查询服务器更新资源文件被调用"1"次
    Examples:
      | localResoure                                | locaContent           |
      |                                             | localFakeAppFuncModel |
      |                                             | 空                     |
      | {"active":true,"path":""}                   | localFakeAppFuncModel |
      | 空对象                                         | 空                     |
      | {"active":true,"path":"localFakePathError"} | 空                     |
      | {"active":false,"path":"localFakePath"}     | localFakeAppFuncModel |
      | {"active":false,"path":"localFakePath"}     | 空                     |
      | 空对象                                         | localFakeAppFuncModel |
      | {"active":true,"path":""}                   | 空                     |
      | {"active":true,"path":"localFakePathError"} | localFakeAppFuncModel |

#  pict
#  serverResultStatus: 成功,失败
#  serverResoure:serverResoure1,serverResoure2,serverResoure3,serverResoure4
#  serverResoure1 {"active":true,"path":"serverFakePath"}
#  serverResoure2 {"active":true,"path":""}
#  serverResoure3 {"active":false,"path":"serverFakePath"}
#  serverResoure4 空
#  serveContent:serverFakeAppFuncModel,空字符串

#serverResultStatus      serverResoure   serveContent
#成功    serverResoure3  空字符串
#成功    serverResoure2  serverFakeAppFuncModel
#成功    serverResoure1  serverFakeAppFuncModel
#失败    serverResoure1  空字符串
#失败    serverResoure4  serverFakeAppFuncModel
#失败    serverResoure2  空字符串
#失败    serverResoure3  serverFakeAppFuncModel
#成功    serverResoure4  空字符串

  Scenario Outline:[10002] 根据设备id获取设备侧应用模型,resource库本地无资源时，会从服务器查询数据返回结果
    Given 本地路径"localFakePath"文件内容为"localFakeAppFuncModel"
    Given 本地路径"serverFakePath"文件内容为"<serveContent>"
    Given upDeviceManager中设备Id"fakeDeviceId"的设备信息为:
      | deviceId     | prodNo     |
      | fakeDeviceId | fakeProdNo |
    Given UpResource库根据prodNo:"fakeProdNo"查询本地设备资源,接口返回:
      | resource |
      |          |
    Given UpResource库根据prodNo:"fakeProdNo"获取资源接口,服务器查询结果回调返回:
      | success              | resource        |
      | <serverResultStatus> | <serverResoure> |
    When 用户调用获取设备侧应用模型接口,参数为"fakeDeviceId"
    Then 获取设备侧应用模型接口结果为"<result>",回调结果的extraData值为"<appFuncModel>"
    Then 异步调用UpResource查询服务器更新资源文件被调用"1"次
    Examples:
      | serverResultStatus | serverResoure                            | serveContent           | result | appFuncModel           |
      | 成功                 | {"active":false,"path":"serverFakePath"} | 空字符串                   | 失败     |                        |
      | 成功                 | {"active":true,"path":""}                | serverFakeAppFuncModel | 失败     |                        |
      | 成功                 | {"active":true,"path":"serverFakePath"}  | serverFakeAppFuncModel | 成功     | serverFakeAppFuncModel |
      | 失败                 | {"active":true,"path":"serverFakePath"}  | 空字符串                   | 失败     |                        |
      | 失败                 |                                          | serverFakeAppFuncModel | 失败     |                        |
      | 失败                 | {"active":true,"path":""}                | 空字符串                   | 失败     |                        |
      | 失败                 | {"active":false,"path":"serverFakePath"} | serverFakeAppFuncModel | 失败     |                        |
      | 成功                 |                                          | 空字符串                   | 失败     |                        |



  Scenario Outline:[10003] 根据设备id获取设备侧应用模型,resource库本地有资源时先返回本地资源，然后会后台异步查询服务器更新资源文件（不关心响应结果）
    Given 本地路径"localFakePath"文件内容为"localFakeAppFuncModel"
    Given 本地路径"serverFakePath"文件内容为"serverFakeAppFuncModel"
    Given upDeviceManager中设备Id"fakeDeviceId"的设备信息为:
      | deviceId     | prodNo     |
      | fakeDeviceId | fakeProdNo |
    Given UpResource库根据prodNo:"fakeProdNo"查询本地设备资源,接口返回:
      | resource                               |
      | {"active":true,"path":"localFakePath"} |
    Given UpResource库根据prodNo:"fakeProdNo"获取资源接口,服务器查询结果回调返回:
      | success              | resource   |
      | <serverResultStatus> | <resource> |
    When 用户调用获取设备侧应用模型接口,参数为"fakeDeviceId"
    Then 获取设备侧应用模型接口结果为"成功",回调结果的extraData值为"localFakeAppFuncModel"
    Then 异步调用UpResource查询服务器更新资源文件被调用"1"次
    Examples:
      | serverResultStatus | resource                                 |
      | 成功                 | {"active":true,"path":"serverFakePath"}  |
      | 成功                 | {"active":false,"path":"serverFakePath"} |
      | 失败                 |                                          |



#  pict 生成组合情况
#  upDeviceManager:空对象, 正常
#  upResourceManager:空对象, 正常
#  deviceId:  空字符串, 空对象, fakeDeviceId, fakeDeviceIdError
#  prodNo:    空字符串, 空对象, fakeProdNo
  Scenario Outline:[10004] 根据设备id获取设备侧应用模型,deviceId、prodNo、UpDeviceManager、UpResourceManager不符合参数校验将返回失败
    Given "UpResource"使用"<upDeviceManager>"
    Given "UpDeviceManager"使用"<upResourceManager>"
    Given 本地路径"localFakePath"文件内容为"localFakeAppFuncModel"
    Given 本地路径"serverFakePath"文件内容为"serverFakeAppFuncModel"
    Given upDeviceManager中设备Id"fakeDeviceId"的设备信息为:
      | deviceId     | prodNo   |
      | fakeDeviceId | <prodNo> |
    Given UpResource库根据prodNo:"fakeProdNo"查询本地设备资源,接口返回:
      | resource                               |
      | {"active":true,"path":"localFakePath"} |
    Given UpResource库根据prodNo:"fakeProdNo"获取资源接口,服务器查询结果回调返回:
      | success | resource                                |
      | 成功      | {"active":true,"path":"serverFakePath"} |
    When 用户调用获取设备侧应用模型接口,参数为"<deviceId>"
    Then 获取设备侧应用模型接口结果为"<result>",回调结果的extraData值为"<appFuncModel>"
    Examples:
      | upDeviceManager | upResourceManager | deviceId          | prodNo     | result | appFuncModel          |
      | 空对象             | 模拟的               | fakeDeviceIdError | 空对象        | 失败     |                       |
      | 空对象             | 空对象               | fakeDeviceId      | 空字符串       | 失败     |                       |
      | 模拟的             | 模拟的               | fakeDeviceId      | 空对象        | 失败     |                       |
      | 模拟的             | 空对象               | 空对象               | fakeProdNo | 失败     |                       |
      | 空对象             | 模拟的               | 空字符串              | fakeProdNo | 失败     |                       |
      | 模拟的             | 空对象               | fakeDeviceIdError | 空字符串       | 失败     |                       |
      | 空对象             | 模拟的               | 空对象               | 空字符串       | 失败     |                       |
      | 模拟的             | 空对象               | 空字符串              | 空对象        | 失败     |                       |
      | 模拟的             | 模拟的               | fakeDeviceId      | fakeProdNo | 成功     | localFakeAppFuncModel |
      | 模拟的             | 模拟的               | 空字符串              | 空字符串       | 失败     |                       |
      | 模拟的             | 空对象               | fakeDeviceIdError | fakeProdNo | 失败     |                       |
      | 空对象             | 空对象               | 空对象               | 空对象        | 失败     |                       |
      | 模拟的             | 模拟的               | fakeDeviceIdError | 正常         | 失败     |                       |


