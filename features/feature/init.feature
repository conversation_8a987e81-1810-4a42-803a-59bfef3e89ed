Feature: 初始化设备管理器
    功能范围:
    主要提供初始化设备管理器功能，设备管理器是设备管理组件对外主要接口单元，通过设备管理器提供各类接口
    调用者可实现对Toolkit的准备或释放、更新设备列表、获取设备对象、获取设备列表、清除设备列表、连接或断开用户接入网关、订阅或取消订阅设备列表变化等功能

    初始化设备管理器主要需要以下参数：
    -设备工具：设备工具是对uSDK接口的封装，可对外提供一系列操作获取设备信息的方法。
    -设备数据源：设备数据源协议的实现类，提供获取设备列表、设备信息协议方法

    接口说明：
    1.初始化设备管理器
    根据指定参数初始化设备管理器，当任意参数为空对象时，初始化失败；所有参数均不为空对象时，初始化成功
    @gu
    Scenario Outline: [3001]初始化设备管理器，当任意参数为空对象时，初始化失败,当参数都不为空时，初始化成功
        When 初始化设备管理器,参数设备工具为"<DeviceToolkit>",设备数据源为"<DeviceDataSource>"
        Then 初始化设备管理器结果为"<Result>"
        Examples:
            | DeviceToolkit | DeviceDataSource | Result  |
            | null          | null             | fail    |
            | null          | fake             | fail    |
            | fake          | null             | fail    |
            | fake          | fake             | success |
