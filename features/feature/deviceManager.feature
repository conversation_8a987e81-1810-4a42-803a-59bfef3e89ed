Feature: 设备管理器

  功能范围：
  主要提供初始化设备管理器功能，设备管理器是设备管理组件对外主要接口单元，通过设备管理器提供各类接口
  调用者可实现对Toolkit的准备或释放、更新设备列表、获取设备对象、获取设备列表、清除设备列表、连接或断开用户接入网关、订阅或取消订阅设备列表变化等功能

  外部依赖：
  - 设备数据源
  - 设备工厂
  - Toolkit

  接口说明：
  1.准备Toolkit
  使用设备管理器前需准备Toolkit，进行uSDK的启动，准备成功后可通过设备管理器进行其他接口操作；

  2.释放Toolkit
  当用户不需要使用U+物联功能时可执行释放Toolkit，停止uSDK；

  3.更新设备列表
  当设备列表发生变化时，更新设备列表，返回更新后的设备列表；
  -当立即更新设备列表时，先执行Toolkit刷新uSDK的设备列表，然后获取用户的设备列表，更新设备信息，返回更新后的设备列表，连接用户接入网关，通知设备列表改变；
  -当非立即更新设备列表时，则直接从设备数据源获取用户的设备列表，更新设备信息，返回更新后的设备列表；

  4.获取设备对象
  通过协议和设备Id从设备缓存中获取设备对象，获取成功返回获取的设备对象，获取失败返回空对象；

  5.根据过滤条件获取设备列表
  通过过滤条件，从设备列表缓存中获取过滤后的设备列表，获取成功返回过滤后的设备列表，获取失败返回空列表；

  6.获取所有设备列表
  从设备列表缓存中获取所有设备列表，获取成功返回设备列表，获取失败返回空列表；

  7.清除设备列表
  从设备缓存中清除所有设备列表，清除成功后发送设备列表变化通知；

  8.连接用户接入网关
  连接用户接入网关，连接成功后可以获得远程和设备交互的能力；
  若第一次连接失败后，会先断开用户连接网关，断开成功后重新连接用户接入网关；

  9.断开用户接入网关
  当用户账号注销时，需断开远程连接，断开成功后会清空用户网关参数；

  10.订阅设备列表变化通知
  订阅者订阅设备列表变化通知，当立即通知标志为true时，会立即收到当前设备列表情况；
  当立即通知标记为false时，只有当设备列表变化时，订阅者才会收到设备列表变化通知；

  11.取消订阅设备列表变化通知
  订阅者取消订阅设备列表变化通知，当设备列表变化时，订阅者不会再收到设备列表变化通知；

  12.监听设备添加、删除、设备列表变化
  订阅者可订阅设备状态的变化,当有设备新增，删除以及设备列表变化时，可收到变化通知。

  Background:
    Given 初始化设备管理器
    Given 设备数据源的更新设备列表接口返回数据如下:
      | Protocol   | DeviceId        | TypeId        | TypeName        | TypeCode        | Model       | ProdNo       | ParentId        | SubDevNo        | Extra   |
      | haier-usdk | fake_device_id1 | fake_type_id1 | fake_type_name1 | fake_type_code1 | fake_model1 | fake_pro_no1 | fake_parent_id1 | fake_device_id2 | {"DI-Basic.displayName":"fake_name","DI-Basic.room":"fake_room","DI-Basic.roomId":"fake_room_id","DI-Basic.bindTime":"fake_bind_time","DI-Product.brand":"fake_brand","DI-Product.bind_type":"fake_bind_type","DI-Product.configType":"fake_configT_type","DI-Product.category":"fake_category","DI-Relation.ownerId":"fake_owner_id","DI-Product.imageAddr1":"fake_image_addr1"} |
      | haier-usdk | fake_device_id2 | fake_type_id2 | fake_type_name2 | fake_type_code2 | fake_model2 | fake_pro_no2 | fake_parent_id2 | fake_device_id3 | {"DI-Basic.displayName":"fake_name","DI-Basic.room":"fake_room","DI-Basic.roomId":"fake_room_id","DI-Basic.bindTime":"fake_bind_time","DI-Product.brand":"fake_brand","DI-Product.bind_type":"fake_bind_type","DI-Product.configType":"fake_configT_type","DI-Product.category":"fake_category","DI-Relation.ownerId":"fake_owner_id","DI-Product.imageAddr1":"fake_image_addr1"} |
    Given 设备代理的接入网关参数为"usdk-user-id=fakeUserId,usdk-access-token=fakeAccessToken"

  #准备Toolkit
  Scenario Outline: [2001]执行准备Toolkit，连接uSdk,执行成功返回成功结果,执行失败返回失败结果
    Given Toolkit的连接工具接口的执行结果为"<AttachResult>"
    When 调用设备管理器的准备Toolkit接口
    Then 设备管理器的准备Toolkit接口回调结果为"<PrepareResult>"
    Then Toolkit的准备工具接口被调用"<Count>"次
    Examples:
      | AttachResult  | PrepareResult  | Count |
      | 成功           | 成功            | 1     |
      | 失败           | 失败            | 1     |

  #释放Toolkit
  Scenario Outline: [2002]执行释放Toolkit接口，停止usdk,执行成功则返回成功结果，执行失败则返回失败结果
    Given Toolkit的释放工具接口的执行结果为"<DetachResult>"
    When 调用设备管理器的释放Toolkit接口
    Then 设备管理器的释放Toolkit接口回调结果为"<ReleaseResult>"
    Then Toolkit的释放工具接口被调用"<Count>"次
    Examples:
      | DetachResult  | ReleaseResult  | Count |
      | 成功           | 成功            | 1     |
      | 失败           | 失败            | 1     |

  #刷新设备列表
  Scenario: [2003]用户刷新设备列表,立即更新参数为true,先刷新uSDK设备列表,后刷新缓存设备列表,最后连接用户接入网关,通知设备列表变化
    Given Toolkit的更新设备列表接口的执行结果为"成功"
    Given Toolkit的连接用户接入网关的执行结果为"成功"
    When 订阅者"userA"订阅设备列表变化,立即通知标志为"FALSE"
    When 用户更新设备列表,立即更新标志为"TRUE"
    When 等待"1"秒
    Then Toolkit的更新设备列表接口被调用"1"次
    Then 设备数据源的更新设备列表接口被调用"1"次,参数立即更新为"TRUE"
    Then 设备工厂的创建设备接口被调用"2"次,参数设备列表如下:
      | Protocol   | DeviceId        | TypeId        | TypeName        | TypeCode        | Model       | ProdNo       | ParentId        | SubDevNo        | Extra   |
      | haier-usdk | fake_device_id1 | fake_type_id1 | fake_type_name1 | fake_type_code1 | fake_model1 | fake_pro_no1 | fake_parent_id1 | fake_device_id2 | {"DI-Basic.displayName":"fake_name","DI-Basic.room":"fake_room","DI-Basic.roomId":"fake_room_id","DI-Basic.bindTime":"fake_bind_time","DI-Product.brand":"fake_brand","DI-Product.bind_type":"fake_bind_type","DI-Product.configType":"fake_configT_type","DI-Product.category":"fake_category","DI-Relation.ownerId":"fake_owner_id","DI-Product.imageAddr1":"fake_image_addr1"} |
      | haier-usdk | fake_device_id2 | fake_type_id2 | fake_type_name2 | fake_type_code2 | fake_model2 | fake_pro_no2 | fake_parent_id2 | fake_device_id3 | {"DI-Basic.displayName":"fake_name","DI-Basic.room":"fake_room","DI-Basic.roomId":"fake_room_id","DI-Basic.bindTime":"fake_bind_time","DI-Product.brand":"fake_brand","DI-Product.bind_type":"fake_bind_type","DI-Product.configType":"fake_configT_type","DI-Product.category":"fake_category","DI-Relation.ownerId":"fake_owner_id","DI-Product.imageAddr1":"fake_image_addr1"} |
    Then Toolkit的连接用户接入网关接口被调用"1"次,参数网关信息为"usdk-user-id=fakeUserId,usdk-access-token=fakeAccessToken"
    Then 使用者收到更新设备列表接口的结果为"成功",更新后的设备列表如下:
      | Protocol   | DeviceId        | TypeId        | TypeName        | TypeCode        | Model       | ProdNo       | ParentId        | SubDevNo        | Extra   |
      | haier-usdk | fake_device_id1 | fake_type_id1 | fake_type_name1 | fake_type_code1 | fake_model1 | fake_pro_no1 | fake_parent_id1 | fake_device_id2 | {"DI-Basic.displayName":"fake_name","DI-Basic.room":"fake_room","DI-Basic.roomId":"fake_room_id","DI-Basic.bindTime":"fake_bind_time","DI-Product.brand":"fake_brand","DI-Product.bind_type":"fake_bind_type","DI-Product.configType":"fake_configT_type","DI-Product.category":"fake_category","DI-Relation.ownerId":"fake_owner_id","DI-Product.imageAddr1":"fake_image_addr1"} |
      | haier-usdk | fake_device_id2 | fake_type_id2 | fake_type_name2 | fake_type_code2 | fake_model2 | fake_pro_no2 | fake_parent_id2 | fake_device_id3 | {"DI-Basic.displayName":"fake_name","DI-Basic.room":"fake_room","DI-Basic.roomId":"fake_room_id","DI-Basic.bindTime":"fake_bind_time","DI-Product.brand":"fake_brand","DI-Product.bind_type":"fake_bind_type","DI-Product.configType":"fake_configT_type","DI-Product.category":"fake_category","DI-Relation.ownerId":"fake_owner_id","DI-Product.imageAddr1":"fake_image_addr1"} |
    Then 订阅者"userA"收到设备列表变化通知"1"次,变化的设备列表如下:
      | Protocol   | DeviceId        | TypeId        | TypeName        | TypeCode        | Model       | ProdNo       | ParentId        | SubDevNo        | Extra   |
      | haier-usdk | fake_device_id1 | fake_type_id1 | fake_type_name1 | fake_type_code1 | fake_model1 | fake_pro_no1 | fake_parent_id1 | fake_device_id2 | {"DI-Basic.displayName":"fake_name","DI-Basic.room":"fake_room","DI-Basic.roomId":"fake_room_id","DI-Basic.bindTime":"fake_bind_time","DI-Product.brand":"fake_brand","DI-Product.bind_type":"fake_bind_type","DI-Product.configType":"fake_configT_type","DI-Product.category":"fake_category","DI-Relation.ownerId":"fake_owner_id","DI-Product.imageAddr1":"fake_image_addr1"} |
      | haier-usdk | fake_device_id2 | fake_type_id2 | fake_type_name2 | fake_type_code2 | fake_model2 | fake_pro_no2 | fake_parent_id2 | fake_device_id3 | {"DI-Basic.displayName":"fake_name","DI-Basic.room":"fake_room","DI-Basic.roomId":"fake_room_id","DI-Basic.bindTime":"fake_bind_time","DI-Product.brand":"fake_brand","DI-Product.bind_type":"fake_bind_type","DI-Product.configType":"fake_configT_type","DI-Product.category":"fake_category","DI-Relation.ownerId":"fake_owner_id","DI-Product.imageAddr1":"fake_image_addr1"} |

  @ios_ignore
  Scenario: [2003-2]用户刷新设备列表,立即更新参数为false,当数据源更新获取的新旧设备不相同时,会刷新uSDK设备列表,更新缓存设备列表成功后不会连接用户接入网关,以及发送设备列表变化通知
    Given Toolkit的连接用户接入网关的执行结果为"成功"
    When 订阅者"userA"订阅设备列表变化,立即通知标志为"FALSE"
    When 用户更新设备列表,立即更新标志为"FALSE"
    When 等待"1"秒
    Then Toolkit的更新设备列表接口被调用"1"次
    Then 设备数据源的更新设备列表接口被调用"1"次,参数立即更新为"FALSE"
    Then 设备工厂的创建设备接口被调用"2"次,参数设备列表如下:
      | Protocol   | DeviceId        | TypeId        | TypeName        | TypeCode        | Model       | ProdNo       | ParentId        | SubDevNo        | Extra   |
      | haier-usdk | fake_device_id1 | fake_type_id1 | fake_type_name1 | fake_type_code1 | fake_model1 | fake_pro_no1 | fake_parent_id1 | fake_device_id2 | {"DI-Basic.displayName":"fake_name","DI-Basic.room":"fake_room","DI-Basic.roomId":"fake_room_id","DI-Basic.bindTime":"fake_bind_time","DI-Product.brand":"fake_brand","DI-Product.bind_type":"fake_bind_type","DI-Product.configType":"fake_configT_type","DI-Product.category":"fake_category","DI-Relation.ownerId":"fake_owner_id","DI-Product.imageAddr1":"fake_image_addr1"} |
      | haier-usdk | fake_device_id2 | fake_type_id2 | fake_type_name2 | fake_type_code2 | fake_model2 | fake_pro_no2 | fake_parent_id2 | fake_device_id3 | {"DI-Basic.displayName":"fake_name","DI-Basic.room":"fake_room","DI-Basic.roomId":"fake_room_id","DI-Basic.bindTime":"fake_bind_time","DI-Product.brand":"fake_brand","DI-Product.bind_type":"fake_bind_type","DI-Product.configType":"fake_configT_type","DI-Product.category":"fake_category","DI-Relation.ownerId":"fake_owner_id","DI-Product.imageAddr1":"fake_image_addr1"} |
    Then Toolkit的连接用户接入网关接口被调用"0"次,参数网关信息为"usdk-user-id=fakeUserId,usdk-access-token=fakeAccessToken"
    Then 使用者收到更新设备列表接口的结果为"成功",更新后的设备列表如下:
      | Protocol   | DeviceId        | TypeId        | TypeName        | TypeCode        | Model       | ProdNo       | ParentId        | SubDevNo        | Extra   |
      | haier-usdk | fake_device_id1 | fake_type_id1 | fake_type_name1 | fake_type_code1 | fake_model1 | fake_pro_no1 | fake_parent_id1 | fake_device_id2 | {"DI-Basic.displayName":"fake_name","DI-Basic.room":"fake_room","DI-Basic.roomId":"fake_room_id","DI-Basic.bindTime":"fake_bind_time","DI-Product.brand":"fake_brand","DI-Product.bind_type":"fake_bind_type","DI-Product.configType":"fake_configT_type","DI-Product.category":"fake_category","DI-Relation.ownerId":"fake_owner_id","DI-Product.imageAddr1":"fake_image_addr1"} |
      | haier-usdk | fake_device_id2 | fake_type_id2 | fake_type_name2 | fake_type_code2 | fake_model2 | fake_pro_no2 | fake_parent_id2 | fake_device_id3 | {"DI-Basic.displayName":"fake_name","DI-Basic.room":"fake_room","DI-Basic.roomId":"fake_room_id","DI-Basic.bindTime":"fake_bind_time","DI-Product.brand":"fake_brand","DI-Product.bind_type":"fake_bind_type","DI-Product.configType":"fake_configT_type","DI-Product.category":"fake_category","DI-Relation.ownerId":"fake_owner_id","DI-Product.imageAddr1":"fake_image_addr1"} |
    Then 订阅者"userA"收到设备列表变化通知"0"次,变化的设备列表如下:
      | Protocol | DeviceId | TypeId | TypeName | TypeCode | Model | ProdNo | ParentId | SubDevNo | Extra |

  Scenario: [2003-3]用户刷新设备列表,立即更新参数为false,当更新缓存设备列表失败,最终刷新设备列表结果为失败
    Given 设备数据源的更新设备列表接口返回数据如下:
      | Protocol   | DeviceId        | TypeId        | TypeName        | TypeCode        | Model       | ProdNo       | ParentId        | SubDevNo        | Extra   |
    When 订阅者"userA"订阅设备列表变化,立即通知标志为"FALSE"
    When 用户更新设备列表,立即更新标志为"FALSE"
    Then Toolkit的更新设备列表接口被调用"0"次
    Then 设备数据源的更新设备列表接口被调用"1"次,参数立即更新为"FALSE"
    Then 设备工厂的创建设备接口被调用"0"次,参数设备列表如下:
      | Protocol | DeviceId        | TypeId        | TypeName        | TypeCode        | Model       | ProdNo       | ParentId        | SubDevNo        | Extra   |
    Then Toolkit的连接用户接入网关接口被调用"0"次,参数网关信息为"usdk-user-id=fakeUserId,usdk-access-token=fakeAccessToken"
    Then 使用者收到更新设备列表接口的结果为"失败",更新后的设备列表如下:
      | Protocol | DeviceId        | TypeId        | TypeName        | TypeCode        | Model       | ProdNo       | ParentId        | SubDevNo        | Extra   |
    Then 订阅者"userA"收到设备列表变化通知"0"次,变化的设备列表如下:
      | Protocol | DeviceId        | TypeId        | TypeName        | TypeCode        | Model       | ProdNo       | ParentId        | SubDevNo        | Extra   |

  #刷新设备列表，设备对象已存在，设备型号发生变化
  Scenario: [2003-4]当DeviceId为fake_device_id1的设备已存在,用户刷新设备列表,设备型号由fake_model1变为fake_model2,最终返回设备型号为fake_model2设备对象
    Given Toolkit的更新设备列表接口的执行结果为"成功"
    When 用户更新设备列表,立即更新标志为"FALSE"
    When 等待"2"秒
    Given 设备数据源的更新设备列表接口返回数据如下:
      | Protocol   | DeviceId        | TypeId        | TypeName        | TypeCode        | Model       | ProdNo       | ParentId        | SubDevNo        | Extra   |
      | haier-usdk | fake_device_id1 | fake_type_id1 | fake_type_name1 | fake_type_code1 | fake_model2 | fake_pro_no1 | fake_parent_id1 | fake_device_id2 | {"DI-Basic.displayName":"fake_name","DI-Basic.room":"fake_room","DI-Basic.roomId":"fake_room_id","DI-Basic.bindTime":"fake_bind_time","DI-Product.brand":"fake_brand","DI-Product.bind_type":"fake_bind_type","DI-Product.configType":"fake_configT_type","DI-Product.category":"fake_category","DI-Relation.ownerId":"fake_owner_id","DI-Product.imageAddr1":"fake_image_addr1"} |
    When 用户更新设备列表,立即更新标志为"FALSE"
    When 等待"2"秒
    Then 使用者收到更新设备列表接口的结果为"成功",更新后的设备列表如下:
      | Protocol   | DeviceId        | TypeId        | TypeName        | TypeCode        | Model       | ProdNo       | ParentId        | SubDevNo        | Extra   |
      | haier-usdk | fake_device_id1 | fake_type_id1 | fake_type_name1 | fake_type_code1 | fake_model2 | fake_pro_no1 | fake_parent_id1 | fake_device_id2 | {"DI-Basic.displayName":"fake_name","DI-Basic.room":"fake_room","DI-Basic.roomId":"fake_room_id","DI-Basic.bindTime":"fake_bind_time","DI-Product.brand":"fake_brand","DI-Product.bind_type":"fake_bind_type","DI-Product.configType":"fake_configT_type","DI-Product.category":"fake_category","DI-Relation.ownerId":"fake_owner_id","DI-Product.imageAddr1":"fake_image_addr1"} |

  #刷新设备列表，设备对象已存在，设备型号没有变化
  Scenario: [2003-5]当DeviceId为fake_device_id1的设备已存在,用户刷新设备列表,设备型号没有变化,但TypeName发生变化,最终返回TypeName为fake_type_name2的设备对象
    Given Toolkit的更新设备列表接口的执行结果为"成功"
    When 用户更新设备列表,立即更新标志为"FALSE"
    When 等待"1"秒
    Given 设备数据源的更新设备列表接口返回数据如下:
      | Protocol   | DeviceId        | TypeId        | TypeName        | TypeCode        | Model       | ProdNo       | ParentId        | SubDevNo        | Extra   |
      | haier-usdk | fake_device_id1 | fake_type_id1 | fake_type_name2 | fake_type_code1 | fake_model1 | fake_pro_no1 | fake_parent_id1 | fake_device_id2 | {"DI-Basic.displayName":"fake_name","DI-Basic.room":"fake_room","DI-Basic.roomId":"fake_room_id","DI-Basic.bindTime":"fake_bind_time","DI-Product.brand":"fake_brand","DI-Product.bind_type":"fake_bind_type","DI-Product.configType":"fake_configT_type","DI-Product.category":"fake_category","DI-Relation.ownerId":"fake_owner_id","DI-Product.imageAddr1":"fake_image_addr1"} |
    When 用户更新设备列表,立即更新标志为"FALSE"
    When 等待"1"秒
    Then 使用者收到更新设备列表接口的结果为"成功",更新后的设备列表如下:
      | Protocol   | DeviceId        | TypeId        | TypeName        | TypeCode        | Model       | ProdNo       | ParentId        | SubDevNo        | Extra   |
      | haier-usdk | fake_device_id1 | fake_type_id1 | fake_type_name2 | fake_type_code1 | fake_model1 | fake_pro_no1 | fake_parent_id1 | fake_device_id2 | {"DI-Basic.displayName":"fake_name","DI-Basic.room":"fake_room","DI-Basic.roomId":"fake_room_id","DI-Basic.bindTime":"fake_bind_time","DI-Product.brand":"fake_brand","DI-Product.bind_type":"fake_bind_type","DI-Product.configType":"fake_configT_type","DI-Product.category":"fake_category","DI-Relation.ownerId":"fake_owner_id","DI-Product.imageAddr1":"fake_image_addr1"} |

  #刷新设备列表，设备初始连接状态为RELEASED，连接成功后状态为PREPARED
  Scenario: [2004]用户准备Toolkit成功后,执行刷新设备列表,刷新成功后会进行设备连接,连接设备成功,设备连接状态为PREPARED
    Given Toolkit的连接工具接口的执行结果为"成功"
    When 调用设备管理器的准备Toolkit接口
    When 等待"1"秒
    Given Toolkit的更新设备列表接口的执行结果为"成功"
    Given Toolkit的连接用户接入网关的执行结果为"成功"
    Given Toolkit的连接设备接口的执行结果为"成功"
    Given Toolkit的获取设备基础信息返回数据如下
      | Protocol   | DeviceId        | TypeId        | TypeName        | TypeCode        | Model       | ProdNo       | ParentId        | SubDevNo        | Extra   |
    When 用户更新设备列表,立即更新标志为"TRUE"
    When 等待"1"秒
    Then Toolkit的连接设备接口被调用"2"次,参数设备Id为"fake_device_id1,fake_device_id2"
    Then 刷新的设备列表的设备连接状态如下:
      | Protocol   | DeviceId        | DeviceState |
      | haier-usdk | fake_device_id1 | PREPARED    |
      | haier-usdk | fake_device_id2 | PREPARED    |

  #刷新设备列表，设备初始连接状态为RELEASED，连接失败后状态仍然是RELEASED
  Scenario: [2004-2]用户准备Toolkit成功后,执行刷新设备列表,刷新成功后会进行设备连接,连接设备失败,设备连接状态为RELEASED
    Given Toolkit的连接工具接口的执行结果为"成功"
    When 调用设备管理器的准备Toolkit接口
    When 等待"1"秒
    Given Toolkit的更新设备列表接口的执行结果为"成功"
    Given Toolkit的连接用户接入网关的执行结果为"成功"
    Given Toolkit的连接设备接口的执行结果为"失败"
    When 用户更新设备列表,立即更新标志为"TRUE"
    When 等待"1"秒
    Then 刷新的设备列表的设备连接状态如下:
      | Protocol   | DeviceId        | DeviceState   |
      | haier-usdk | fake_device_id1 |  RELEASED     |
      | haier-usdk | fake_device_id2 |  RELEASED     |

  #刷新设备列表，设备初始连接状态为RELEASED，连接设备接口延时返回,设备循环连接,再次循环时连接状态为PREPARING，当延时结束后设备连接状态为PREPARED
  Scenario: [2004-3]用户准备Toolkit成功后,执行刷新设备列表,刷新成功后会进行设备连接,连接设备接口延时返回结果,会循环进行设备连接,直到连接结果返回,最终连接设备成功,设备连接状态为PREPARED
    Given Toolkit的连接工具接口的执行结果为"成功"
    When 调用设备管理器的准备Toolkit接口
    When 等待"1"秒
    Given 设备数据源的更新设备列表接口返回数据如下:
      | Protocol   | DeviceId        | TypeId        | TypeName        | TypeCode        | Model       | ProdNo       | ParentId        | SubDevNo        | Extra   |
      | haier-usdk | fake_device_id1 | fake_type_id1 | fake_type_name1 | fake_type_code1 | fake_model1 | fake_pro_no1 | fake_parent_id1 | fake_device_id2 | {"DI-Basic.displayName":"fake_name","DI-Basic.room":"fake_room","DI-Basic.roomId":"fake_room_id","DI-Basic.bindTime":"fake_bind_time","DI-Product.brand":"fake_brand","DI-Product.bind_type":"fake_bind_type","DI-Product.configType":"fake_configT_type","DI-Product.category":"fake_category","DI-Relation.ownerId":"fake_owner_id","DI-Product.imageAddr1":"fake_image_addr1"} |
    Given Toolkit的获取设备基础信息返回数据如下
      | Protocol   | DeviceId        | TypeId        | TypeName        | TypeCode        | Model       | ProdNo       | ParentId        | SubDevNo        | Extra   |
    Given Toolkit的更新设备列表接口的执行结果为"成功"
    Given Toolkit的连接用户接入网关的执行结果为"成功"
    Given Toolkit的连接设备接口等待"2"秒返回执行结果为"成功"
    When 用户更新设备列表,立即更新标志为"TRUE"
    When 等待"1"秒
    Then 协议为"haier-usdk",设备id为"fake_device_id1"的设备连接状态为"PREPARING"
    When 等待"3"秒
    Then Toolkit的连接设备接口被调用"1"次,参数设备Id为"fake_device_id1"
    Then 协议为"haier-usdk",设备id为"fake_device_id1"的设备连接状态为"PREPARED"

  #刷新设备列表，当子设备列表变化，子设备连接成功后状态为PREPARED，再次获取子设备时，子设备连接状态为PREPARED，无需再次进行子设备连接
  Scenario: [2004-4]用户准备Toolkit,执行刷新设备列表,连接设备成功,当子设备列表变化,会连接子设备,子设备状态为PREPARED,当获取子设备会再次进行子设备连接,此时子设备状态为PREPARED,无需再次进行设备连接
    Given Toolkit的连接工具接口的执行结果为"成功"
    When 调用设备管理器的准备Toolkit接口
    When 等待"1"秒
    Given 设备数据源的更新设备列表接口返回数据如下:
      | Protocol   | DeviceId     | TypeId        | TypeName        | TypeCode        | Model       | ProdNo       | ParentId        | SubDevNo         | Extra   |
      | haier-usdk | 04FA83F19E81 | fake_type_id1 | fake_type_name1 | fake_type_code1 | fake_model1 | fake_pro_no1 | fake_parent_id1 | 04FA83F19E81_1-2 | {"DI-Basic.displayName":"fake_name","DI-Basic.room":"fake_room","DI-Basic.roomId":"fake_room_id","DI-Basic.bindTime":"fake_bind_time","DI-Product.brand":"fake_brand","DI-Product.bind_type":"fake_bind_type","DI-Product.configType":"fake_configT_type","DI-Product.category":"fake_category","DI-Relation.ownerId":"fake_owner_id","DI-Product.imageAddr1":"fake_image_addr1"} |
    Given Toolkit的获取设备基础信息返回数据如下
      | Protocol   | DeviceId     | TypeId        | TypeName        | TypeCode        | Model       | ProdNo       | ParentId        | SubDevNo         | Extra   |
    Given Toolkit的更新设备列表接口的执行结果为"成功"
    Given Toolkit的连接用户接入网关的执行结果为"成功"
    Given Toolkit的连接设备接口等待"0"秒返回执行结果为"成功"
    When 用户更新设备列表,立即更新标志为"TRUE"
    When 等待"1"秒
    Then Toolkit的连接设备接口被调用"1"次,参数设备Id为"04FA83F19E81"
    Then 协议为"haier-usdk",设备id为"04FA83F19E81"的设备连接状态为"PREPARED"
    When 设置协议为"haier-usdk",设备id为"04FA83F19E81"的设备为父设备
    When 设备"04FA83F19E81"状态发生变化,具体连接状态变为"READY"
    When 设备"04FA83F19E81"状态发生变化,具体子设备变化列表如下
      | Protocol   | DeviceId         | TypeId        | TypeName        | TypeCode        | Model       | ProdNo       | ParentId     | SubDevNo | Extra   |
      | haier-usdk | 04FA83F19E81_1-2 | fake_type_id1 | fake_type_name1 | fake_type_code1 | fake_model1 | fake_pro_no1 | 04FA83F19E81 | 2        | {"DI-Basic.displayName":"fake_name","DI-Basic.room":"fake_room","DI-Basic.roomId":"fake_room_id","DI-Basic.bindTime":"fake_bind_time","DI-Product.brand":"fake_brand","DI-Product.bind_type":"fake_bind_type","DI-Product.configType":"fake_configT_type","DI-Product.category":"fake_category","DI-Relation.ownerId":"fake_owner_id","DI-Product.imageAddr1":"fake_image_addr1"} |
    When 等待"1"秒
    Then 调用设备管理器的获取设备接口,协议为"haier-usdk",设备id为"04FA83F19E81_1-2",获取的设备信息如下:
      | Protocol   | DeviceId         | TypeId        | TypeName        | TypeCode        | Model       | ProdNo       | ParentId     | SubDevNo | Extra   |
      | haier-usdk | 04FA83F19E81_1-2 | fake_type_id1 | fake_type_name1 | fake_type_code1 | fake_model1 | fake_pro_no1 | 04FA83F19E81 | 2        | {"DI-Basic.displayName":"fake_name","DI-Basic.room":"fake_room","DI-Basic.roomId":"fake_room_id","DI-Basic.bindTime":"fake_bind_time","DI-Product.brand":"fake_brand","DI-Product.bind_type":"fake_bind_type","DI-Product.configType":"fake_configT_type","DI-Product.category":"fake_category","DI-Relation.ownerId":"fake_owner_id","DI-Product.imageAddr1":"fake_image_addr1"} |
    When 等待"1"秒
    Then 协议为"haier-usdk",设备id为"04FA83F19E81_1-2"的设备连接状态为"PREPARED"

  @ios_ignore
  Scenario: [2004-5]当用户刷新设备列表时，同时执行优先准备设备fake_device_id99,fake_device_id99能优先执行设备准备
    Given Toolkit的连接工具接口的执行结果为"成功"
    When 调用设备管理器的准备Toolkit接口
    When 等待"1"秒
    Given 设备数据源的更新设备列表接口返回"100"条设备数据,设备id从"0"到"99"递增,设备数据如下:
      | Protocol   | DeviceId         | TypeId         | TypeName         | TypeCode         | Model        | ProdNo        | ParentId         | SubDevNo         | Extra   |
      | haier-usdk | fake_device_id   | fake_type_id   | fake_type_name   | fake_type_code   | fake_model   | fake_pro_no   | fake_parent_id   | fake_device_id   | {"DI-Basic.displayName":"fake_name","DI-Basic.room":"fake_room","DI-Basic.roomId":"fake_room_id","DI-Basic.bindTime":"fake_bind_time","DI-Product.brand":"fake_brand","DI-Product.bind_type":"fake_bind_type","DI-Product.configType":"fake_configT_type","DI-Product.category":"fake_category","DI-Relation.ownerId":"fake_owner_id","DI-Product.imageAddr1":"fake_image_addr1"} |
    Given Toolkit的获取设备基础信息返回数据如下
      | Protocol | DeviceId | TypeId | TypeName | TypeCode | Model | ProdNo | ParentId | SubDevNo | Extra |
    Given Toolkit的更新设备列表接口的执行结果为"成功"
    Given Toolkit的连接用户接入网关的执行结果为"成功"
    Given Toolkit的连接设备接口的执行结果为"成功"
    When 用户更新设备列表,立即更新标志为"TRUE"
    Then 协议为"haier-usdk",设备id为"fake_device_id99"的设备连接状态为"RELEASED"
    When 调用优先准备设备接口,协议为"haier-usdk",设备id为"fake_device_id99"
    When 等待"1"秒
    Then 协议为"haier-usdk",设备id为"fake_device_id99"的设备连接状态为"PREPARED"

  @ios_ignore
  Scenario Outline: [2004-6]设备id为fake_device_id99的加入到优先队列，当用户刷新设备列表时，usdk无论是否能发现优先队列里的设备,设备id为fake_device_id99最终都可以准备成功
    Given Toolkit的连接工具接口的执行结果为"成功"
    When 调用设备管理器的准备Toolkit接口
    When 等待"1"秒
    Given 设备数据源的更新设备列表接口返回"101"条设备数据,设备id从"0"到"100"递增,设备数据如下:
      | Protocol   | DeviceId       | TypeId       | TypeName       | TypeCode       | Model      | ProdNo      | ParentId       | SubDevNo       | Extra                                                                                                                                                                                                                                                                                                                                                                             |
      | haier-usdk | fake_device_id | fake_type_id | fake_type_name | fake_type_code | fake_model | fake_pro_no | fake_parent_id | fake_device_id | {"DI-Basic.displayName":"fake_name","DI-Basic.room":"fake_room","DI-Basic.roomId":"fake_room_id","DI-Basic.bindTime":"fake_bind_time","DI-Product.brand":"fake_brand","DI-Product.bind_type":"fake_bind_type","DI-Product.configType":"fake_configT_type","DI-Product.category":"fake_category","DI-Relation.ownerId":"fake_owner_id","DI-Product.imageAddr1":"fake_image_addr1"} |
    Given Toolkit的更新设备列表接口的执行结果为"成功"
    Given Toolkit的连接用户接入网关的执行结果为"成功"
    Given Toolkit的连接设备接口的执行结果为"成功"
    When 发现新设备代理方法被执行,新发现的设备列表如下:
      | Protocol   | DeviceId   | TypeId         | TypeName         | TypeCode        | Model       | ProdNo       | ParentId        | SubDevNo        | Extra                                                                                                                                                                                                                                                                                                                                                                             |
      | haier-usdk | <deviceId> | fake_type_id99 | fake_type_name99 | fake_type_code3 | fake_model3 | fake_pro_no3 | fake_parent_id3 | fake_device_id2 | {"DI-Basic.displayName":"fake_name","DI-Basic.room":"fake_room","DI-Basic.roomId":"fake_room_id","DI-Basic.bindTime":"fake_bind_time","DI-Product.brand":"fake_brand","DI-Product.bind_type":"fake_bind_type","DI-Product.configType":"fake_configT_type","DI-Product.category":"fake_category","DI-Relation.ownerId":"fake_owner_id","DI-Product.imageAddr1":"fake_image_addr1"} |
    When 用户更新设备列表,立即更新标志为"TRUE"
    When 等待"1"秒
    Then 协议为"haier-usdk",设备id为"<deviceId>"的设备连接状态为"RELEASED"
    When 调用优先准备设备接口,协议为"haier-usdk",设备id为"fake_device_id99"
    When 等待"1"秒
    Then 协议为"haier-usdk",设备id为"<deviceId>"的设备连接状态为"<State>"
    When 等待"5"秒
    Then 协议为"haier-usdk",设备id为"<deviceId>"的设备连接状态为"PREPARED"
    Examples:
      | deviceId           |  State       |
      | fake_device_id99   |  PREPARED    |
      | fake_device_id100  |  RELEASED    |

  @ios_ignore
  Scenario: [2004-7]当用户刷新设备列表时，所有设备准备完成后，usdk发现deviceId为fake_device_id99设备，在deviceId为fake_device_id99调用加入优先准备的队列后，设备依然准备成功
    Given Toolkit的连接工具接口的执行结果为"成功"
    When 调用设备管理器的准备Toolkit接口
    When 等待"1"秒
    Given 设备数据源的更新设备列表接口返回"100"条设备数据,设备id从"0"到"99"递增,设备数据如下:
      | Protocol   | DeviceId       | TypeId       | TypeName       | TypeCode       | Model      | ProdNo      | ParentId       | SubDevNo       | Extra                                                                                                                                                                                                                                                                                                                                                                             |
      | haier-usdk | fake_device_id | fake_type_id | fake_type_name | fake_type_code | fake_model | fake_pro_no | fake_parent_id | fake_device_id | {"DI-Basic.displayName":"fake_name","DI-Basic.room":"fake_room","DI-Basic.roomId":"fake_room_id","DI-Basic.bindTime":"fake_bind_time","DI-Product.brand":"fake_brand","DI-Product.bind_type":"fake_bind_type","DI-Product.configType":"fake_configT_type","DI-Product.category":"fake_category","DI-Relation.ownerId":"fake_owner_id","DI-Product.imageAddr1":"fake_image_addr1"} |
    Given Toolkit的更新设备列表接口的执行结果为"成功"
    Given Toolkit的连接用户接入网关的执行结果为"成功"
    Given Toolkit的连接设备接口的执行结果为"成功"
    When 用户更新设备列表,立即更新标志为"TRUE"
    When 等待"15"秒
    Then 协议为"haier-usdk",设备id为"fake_device_id99"的设备连接状态为"PREPARED"
    When 发现新设备代理方法被执行,新发现的设备列表如下:
      | Protocol   | DeviceId          | TypeId          | TypeName         | TypeCode        | Model       | ProdNo       | ParentId        | SubDevNo        | Extra                                                                                                                                                                                                                                                                                                                                                                             |
      | haier-usdk | fake_device_id99  | fake_type_id99 | fake_type_name99 | fake_type_code3 | fake_model3 | fake_pro_no3 | fake_parent_id3 | fake_device_id2 | {"DI-Basic.displayName":"fake_name","DI-Basic.room":"fake_room","DI-Basic.roomId":"fake_room_id","DI-Basic.bindTime":"fake_bind_time","DI-Product.brand":"fake_brand","DI-Product.bind_type":"fake_bind_type","DI-Product.configType":"fake_configT_type","DI-Product.category":"fake_category","DI-Relation.ownerId":"fake_owner_id","DI-Product.imageAddr1":"fake_image_addr1"} |
    When 调用优先准备设备接口,协议为"haier-usdk",设备id为"fake_device_id99"
    When 等待"1"秒
    Then 协议为"haier-usdk",设备id为"fake_device_id99"的设备连接状态为"PREPARED"

  @ios_ignore
  Scenario Outline: [2004-8]设备id为fake_device_id99的加入到优先队列，当用户刷新设备列表时，usdk发现优先准备队列中的设备在调用加入优先队列准备前，设备id为fake_device_id99可以准备成功，发现其他设备对fake_device_id99准备成功无影响；usdk发现优先准备队列中的设备在调用加入优先队列准备后，优先队列设备未准备成功，等在调用加入优先队列后设备准备成功
    Given Toolkit的连接工具接口的执行结果为"成功"
    When 调用设备管理器的准备Toolkit接口
    When 等待"1"秒
    Given 设备数据源的更新设备列表接口返回"100"条设备数据,设备id从"0"到"99"递增,设备数据如下:
      | Protocol   | DeviceId       | TypeId       | TypeName       | TypeCode       | Model      | ProdNo      | ParentId       | SubDevNo       | Extra                                                                                                                                                                                                                                                                                                                                                                             |
      | haier-usdk | fake_device_id | fake_type_id | fake_type_name | fake_type_code | fake_model | fake_pro_no | fake_parent_id | fake_device_id | {"DI-Basic.displayName":"fake_name","DI-Basic.room":"fake_room","DI-Basic.roomId":"fake_room_id","DI-Basic.bindTime":"fake_bind_time","DI-Product.brand":"fake_brand","DI-Product.bind_type":"fake_bind_type","DI-Product.configType":"fake_configT_type","DI-Product.category":"fake_category","DI-Relation.ownerId":"fake_owner_id","DI-Product.imageAddr1":"fake_image_addr1"} |
    Given Toolkit的更新设备列表接口的执行结果为"成功"
    Given Toolkit的连接用户接入网关的执行结果为"成功"
    Given Toolkit的连接设备接口的执行结果为"成功"
    When 用户更新设备列表,立即更新标志为"TRUE"
    When 发现新设备代理方法被执行,新发现的设备列表如下:
      | Protocol   | DeviceId   | TypeId         | TypeName         | TypeCode        | Model       | ProdNo       | ParentId        | SubDevNo        | Extra                                                                                                                                                                                                                                                                                                                                                                             |
      | haier-usdk | <deviceId1> | fake_type_id99 | fake_type_name99 | fake_type_code3 | fake_model3 | fake_pro_no3 | fake_parent_id3 | fake_device_id2 | {"DI-Basic.displayName":"fake_name","DI-Basic.room":"fake_room","DI-Basic.roomId":"fake_room_id","DI-Basic.bindTime":"fake_bind_time","DI-Product.brand":"fake_brand","DI-Product.bind_type":"fake_bind_type","DI-Product.configType":"fake_configT_type","DI-Product.category":"fake_category","DI-Relation.ownerId":"fake_owner_id","DI-Product.imageAddr1":"fake_image_addr1"} |
    When 等待"1"秒
    Then 协议为"haier-usdk",设备id为"fake_device_id99"的设备连接状态为"RELEASED"
    When 调用优先准备设备接口,协议为"haier-usdk",设备id为"fake_device_id99"
    When 等待"1"秒
    Then 协议为"haier-usdk",设备id为"fake_device_id99"的设备连接状态为"<State>"
    When 发现新设备代理方法被执行,新发现的设备列表如下:
      | Protocol   | DeviceId   | TypeId         | TypeName         | TypeCode        | Model       | ProdNo       | ParentId        | SubDevNo        | Extra                                                                                                                                                                                                                                                                                                                                                                             |
      | haier-usdk | <deviceId2> | fake_type_id101 | fake_type_name99 | fake_type_code3 | fake_model3 | fake_pro_no3 | fake_parent_id3 | fake_device_id2 | {"DI-Basic.displayName":"fake_name","DI-Basic.room":"fake_room","DI-Basic.roomId":"fake_room_id","DI-Basic.bindTime":"fake_bind_time","DI-Product.brand":"fake_brand","DI-Product.bind_type":"fake_bind_type","DI-Product.configType":"fake_configT_type","DI-Product.category":"fake_category","DI-Relation.ownerId":"fake_owner_id","DI-Product.imageAddr1":"fake_image_addr1"} |
    When 等待"1"秒
    Then 协议为"haier-usdk",设备id为"fake_device_id99"的设备连接状态为"PREPARED"
    Examples:
      | deviceId1           | deviceId2           |  State       |
      | fake_device_id99    | fake_device_id101   |  PREPARED    |
      | fake_device_id101   | fake_device_id99    |  RELEASED    |

  #刷新设备列表，设备初始连接状态为RELEASED，连接设备出现异常，连接失败设备状态仍然是RELEASED
  @ios_ignore
  Scenario: [2004-9]用户准备Toolkit成功后，执行刷新设备列表，刷新成功后会进行设备连接。连接设备抛出异常连接失败，设备状态为RELEASED。
    Given Toolkit的连接工具接口的执行结果为"成功"
    When 调用设备管理器的准备Toolkit接口
    When 等待"1"秒
    Given Toolkit的更新设备列表接口的执行结果为"成功"
    Given Toolkit的连接用户接入网关的执行结果为"成功"
    Given Toolkit连接设备失败,抛出异常
    When 用户更新设备列表,立即更新标志为"TRUE"
    When 等待"1"秒
    Then 刷新的设备列表的设备连接状态如下:
      | Protocol   | DeviceId        | DeviceState   |
      | haier-usdk | fake_device_id1 |  RELEASED     |
      | haier-usdk | fake_device_id2 |  RELEASED     |

  #获取设备
  Scenario: [2005]用户获取设备对象,deviceId为fake_device_id1的设备对象存在,获取成功返回deviceId为fake_device_id1设备对象
    Given Toolkit的更新设备列表接口的执行结果为"成功"
    When 用户更新设备列表,立即更新标志为"FALSE"
    When 等待"1"秒
    Then 调用设备管理器的获取设备接口,协议为"haier-usdk",设备id为"fake_device_id1",获取的设备信息如下:
      | Protocol   | DeviceId        | TypeId        | TypeName        | TypeCode        | Model       | ProdNo       | ParentId        | SubDevNo        | Extra   |
      | haier-usdk | fake_device_id1 | fake_type_id1 | fake_type_name1 | fake_type_code1 | fake_model1 | fake_pro_no1 | fake_parent_id1 | fake_device_id2 | {"DI-Basic.displayName":"fake_name","DI-Basic.room":"fake_room","DI-Basic.roomId":"fake_room_id","DI-Basic.bindTime":"fake_bind_time","DI-Product.brand":"fake_brand","DI-Product.bind_type":"fake_bind_type","DI-Product.configType":"fake_configT_type","DI-Product.category":"fake_category","DI-Relation.ownerId":"fake_owner_id","DI-Product.imageAddr1":"fake_image_addr1"} |

  Scenario: [2005-2]用户获取设备对象,deviceId为fake_device_id3的设备对象不存在,获取失败,返回空对象
    Given Toolkit的更新设备列表接口的执行结果为"成功"
    When 用户更新设备列表,立即更新标志为"FALSE"
    When 等待"1"秒
    Then 调用设备管理器的获取设备接口,协议为"haier-usdk",设备id为"fake_device_id3",获取的设备信息如下:
      | Protocol | DeviceId | TypeId | TypeName | TypeCode | Model | ProdNo | ParentId | SubDevNo | Extra |

  #根据过滤条件获取设备列表
  Scenario: [2006]获取设备列表,过滤条件为设备对象不为空,返回设备对象不为空的设备列表
    Given Toolkit的更新设备列表接口的执行结果为"成功"
    When 用户更新设备列表,立即更新标志为"FALSE"
    When 等待"1"秒
    Then 调用设备管理器的获取设备列表接口,参数过滤器为"NotNullFilter",获取的设备列表如下:
      | Protocol   | DeviceId        | TypeId        | TypeName        | TypeCode        | Model       | ProdNo       | ParentId        | SubDevNo        | Extra   |
      | haier-usdk | fake_device_id1 | fake_type_id1 | fake_type_name1 | fake_type_code1 | fake_model1 | fake_pro_no1 | fake_parent_id1 | fake_device_id2 | {"DI-Basic.displayName":"fake_name","DI-Basic.room":"fake_room","DI-Basic.roomId":"fake_room_id","DI-Basic.bindTime":"fake_bind_time","DI-Product.brand":"fake_brand","DI-Product.bind_type":"fake_bind_type","DI-Product.configType":"fake_configT_type","DI-Product.category":"fake_category","DI-Relation.ownerId":"fake_owner_id","DI-Product.imageAddr1":"fake_image_addr1"} |
      | haier-usdk | fake_device_id2 | fake_type_id2 | fake_type_name2 | fake_type_code2 | fake_model2 | fake_pro_no2 | fake_parent_id2 | fake_device_id3 | {"DI-Basic.displayName":"fake_name","DI-Basic.room":"fake_room","DI-Basic.roomId":"fake_room_id","DI-Basic.bindTime":"fake_bind_time","DI-Product.brand":"fake_brand","DI-Product.bind_type":"fake_bind_type","DI-Product.configType":"fake_configT_type","DI-Product.category":"fake_category","DI-Relation.ownerId":"fake_owner_id","DI-Product.imageAddr1":"fake_image_addr1"} |

  Scenario: [2006-2]获取设备列表,过滤设备类型为fake_model1或fake_model2的设备对象,返回符合过滤条件的设备对象
    Given 设备数据源的更新设备列表接口返回数据如下:
      | Protocol   | DeviceId        | TypeId        | TypeName        | TypeCode        | Model       | ProdNo       | ParentId        | SubDevNo        | Extra   |
      | haier-usdk | fake_device_id1 | fake_type_id1 | fake_type_name1 | fake_type_code1 | fake_model1 | fake_pro_no1 | fake_parent_id1 | fake_device_id2 | {"DI-Basic.displayName":"fake_name","DI-Basic.room":"fake_room","DI-Basic.roomId":"fake_room_id","DI-Basic.bindTime":"fake_bind_time","DI-Product.brand":"fake_brand","DI-Product.bind_type":"fake_bind_type","DI-Product.configType":"fake_configT_type","DI-Product.category":"fake_category","DI-Relation.ownerId":"fake_owner_id","DI-Product.imageAddr1":"fake_image_addr1"} |
      | haier-usdk | fake_device_id2 | fake_type_id2 | fake_type_name2 | fake_type_code2 | fake_model2 | fake_pro_no2 | fake_parent_id2 | fake_device_id3 | {"DI-Basic.displayName":"fake_name","DI-Basic.room":"fake_room","DI-Basic.roomId":"fake_room_id","DI-Basic.bindTime":"fake_bind_time","DI-Product.brand":"fake_brand","DI-Product.bind_type":"fake_bind_type","DI-Product.configType":"fake_configT_type","DI-Product.category":"fake_category","DI-Relation.ownerId":"fake_owner_id","DI-Product.imageAddr1":"fake_image_addr1"} |
      | haier-usdk | fake_device_id3 | fake_type_id3 | fake_type_name3 | fake_type_code3 | fake_model3 | fake_pro_no3 | fake_parent_id3 | fake_device_id4 | {"DI-Basic.displayName":"fake_name","DI-Basic.room":"fake_room","DI-Basic.roomId":"fake_room_id","DI-Basic.bindTime":"fake_bind_time","DI-Product.brand":"fake_brand","DI-Product.bind_type":"fake_bind_type","DI-Product.configType":"fake_configT_type","DI-Product.category":"fake_category","DI-Relation.ownerId":"fake_owner_id","DI-Product.imageAddr1":"fake_image_addr1"} |
    Given Toolkit的更新设备列表接口的执行结果为"成功"
    When 用户更新设备列表,立即更新标志为"FALSE"
    When 等待"1"秒
    Given 设置过滤器为"StringFilter",参数过滤集合为"fake_model1,fake_model2",过滤条件为"Model"
    Then 调用设备管理器的获取设备列表接口,参数过滤器为"StringFilter",获取的设备列表如下:
      | Protocol   | DeviceId        | TypeId        | TypeName        | TypeCode        | Model       | ProdNo       | ParentId        | SubDevNo        | Extra   |
      | haier-usdk | fake_device_id1 | fake_type_id1 | fake_type_name1 | fake_type_code1 | fake_model1 | fake_pro_no1 | fake_parent_id1 | fake_device_id2 | {"DI-Basic.displayName":"fake_name","DI-Basic.room":"fake_room","DI-Basic.roomId":"fake_room_id","DI-Basic.bindTime":"fake_bind_time","DI-Product.brand":"fake_brand","DI-Product.bind_type":"fake_bind_type","DI-Product.configType":"fake_configT_type","DI-Product.category":"fake_category","DI-Relation.ownerId":"fake_owner_id","DI-Product.imageAddr1":"fake_image_addr1"} |
      | haier-usdk | fake_device_id2 | fake_type_id2 | fake_type_name2 | fake_type_code2 | fake_model2 | fake_pro_no2 | fake_parent_id2 | fake_device_id3 | {"DI-Basic.displayName":"fake_name","DI-Basic.room":"fake_room","DI-Basic.roomId":"fake_room_id","DI-Basic.bindTime":"fake_bind_time","DI-Product.brand":"fake_brand","DI-Product.bind_type":"fake_bind_type","DI-Product.configType":"fake_configT_type","DI-Product.category":"fake_category","DI-Relation.ownerId":"fake_owner_id","DI-Product.imageAddr1":"fake_image_addr1"} |

  Scenario: [2006-3]获取设备列表,过滤设备类型为fake_model4,过滤获取设备的设备不存在,获取过滤对象失败返回空对象
    Given Toolkit的更新设备列表接口的执行结果为"成功"
    When 用户更新设备列表,立即更新标志为"FALSE"
    Given 设置过滤器为"StringFilter",参数过滤集合为"fake_model4",过滤条件为"Model"
    Then 调用设备管理器的获取设备列表接口,参数过滤器为"StringFilter",获取的设备列表如下:
      | Protocol | DeviceId | TypeId | TypeName | TypeCode | Model | ProdNo | ParentId | SubDevNo | Extra |

  Scenario: [2006-4]获取设备列表,过滤设备类型为fake_model1或fake_model2同时设备id为fake_device_id1的设备对象,返回符合过滤条件的设备对象
    Given 设备数据源的更新设备列表接口返回数据如下:
      | Protocol   | DeviceId        | TypeId        | TypeName        | TypeCode        | Model       | ProdNo       | ParentId        | SubDevNo        | Extra   |
      | haier-usdk | fake_device_id1 | fake_type_id1 | fake_type_name1 | fake_type_code1 | fake_model1 | fake_pro_no1 | fake_parent_id1 | fake_device_id2 | {"DI-Basic.displayName":"fake_name","DI-Basic.room":"fake_room","DI-Basic.roomId":"fake_room_id","DI-Basic.bindTime":"fake_bind_time","DI-Product.brand":"fake_brand","DI-Product.bind_type":"fake_bind_type","DI-Product.configType":"fake_configT_type","DI-Product.category":"fake_category","DI-Relation.ownerId":"fake_owner_id","DI-Product.imageAddr1":"fake_image_addr1"} |
      | haier-usdk | fake_device_id2 | fake_type_id2 | fake_type_name2 | fake_type_code2 | fake_model2 | fake_pro_no2 | fake_parent_id2 | fake_device_id3 | {"DI-Basic.displayName":"fake_name","DI-Basic.room":"fake_room","DI-Basic.roomId":"fake_room_id","DI-Basic.bindTime":"fake_bind_time","DI-Product.brand":"fake_brand","DI-Product.bind_type":"fake_bind_type","DI-Product.configType":"fake_configT_type","DI-Product.category":"fake_category","DI-Relation.ownerId":"fake_owner_id","DI-Product.imageAddr1":"fake_image_addr1"} |
      | haier-usdk | fake_device_id3 | fake_type_id3 | fake_type_name3 | fake_type_code3 | fake_model3 | fake_pro_no3 | fake_parent_id3 | fake_device_id4 | {"DI-Basic.displayName":"fake_name","DI-Basic.room":"fake_room","DI-Basic.roomId":"fake_room_id","DI-Basic.bindTime":"fake_bind_time","DI-Product.brand":"fake_brand","DI-Product.bind_type":"fake_bind_type","DI-Product.configType":"fake_configT_type","DI-Product.category":"fake_category","DI-Relation.ownerId":"fake_owner_id","DI-Product.imageAddr1":"fake_image_addr1"} |
    Given Toolkit的更新设备列表接口的执行结果为"成功"
    When 用户更新设备列表,立即更新标志为"FALSE"
    When 等待"1"秒
    Given 设置过滤器为"StringFilter",参数过滤集合为"fake_model1,fake_model2",过滤条件为"Model"
    Given 设置过滤器为"StringFilter",参数过滤集合为"fake_device_id1",过滤条件为"DeviceId"
    Then 调用设备管理器的获取设备列表接口,参数过滤器为"CompositeFilter",获取的设备列表如下:
      | Protocol   | DeviceId        | TypeId        | TypeName        | TypeCode        | Model       | ProdNo       | ParentId        | SubDevNo        | Extra   |
      | haier-usdk | fake_device_id1 | fake_type_id1 | fake_type_name1 | fake_type_code1 | fake_model1 | fake_pro_no1 | fake_parent_id1 | fake_device_id2 | {"DI-Basic.displayName":"fake_name","DI-Basic.room":"fake_room","DI-Basic.roomId":"fake_room_id","DI-Basic.bindTime":"fake_bind_time","DI-Product.brand":"fake_brand","DI-Product.bind_type":"fake_bind_type","DI-Product.configType":"fake_configT_type","DI-Product.category":"fake_category","DI-Relation.ownerId":"fake_owner_id","DI-Product.imageAddr1":"fake_image_addr1"} |

  #获取所有设备列表
  Scenario: [2007]执行获取所有设备列表,获取结果为成功
    Given Toolkit的更新设备列表接口的执行结果为"成功"
    When 用户更新设备列表,立即更新标志为"FALSE"
    When 等待"1"秒
    Then 调用设备管理器的获取所有设备列表接口,返回的设备列表如下:
      | Protocol   | DeviceId        | TypeId        | TypeName        | TypeCode        | Model       | ProdNo       | ParentId        | SubDevNo        | Extra   |
      | haier-usdk | fake_device_id1 | fake_type_id1 | fake_type_name1 | fake_type_code1 | fake_model1 | fake_pro_no1 | fake_parent_id1 | fake_device_id2 | {"DI-Basic.displayName":"fake_name","DI-Basic.room":"fake_room","DI-Basic.roomId":"fake_room_id","DI-Basic.bindTime":"fake_bind_time","DI-Product.brand":"fake_brand","DI-Product.bind_type":"fake_bind_type","DI-Product.configType":"fake_configT_type","DI-Product.category":"fake_category","DI-Relation.ownerId":"fake_owner_id","DI-Product.imageAddr1":"fake_image_addr1"} |
      | haier-usdk | fake_device_id2 | fake_type_id2 | fake_type_name2 | fake_type_code2 | fake_model2 | fake_pro_no2 | fake_parent_id2 | fake_device_id3 | {"DI-Basic.displayName":"fake_name","DI-Basic.room":"fake_room","DI-Basic.roomId":"fake_room_id","DI-Basic.bindTime":"fake_bind_time","DI-Product.brand":"fake_brand","DI-Product.bind_type":"fake_bind_type","DI-Product.configType":"fake_configT_type","DI-Product.category":"fake_category","DI-Relation.ownerId":"fake_owner_id","DI-Product.imageAddr1":"fake_image_addr1"} |

  Scenario: [2007-2]执行获取设备列表,缓存设备列表为空,获取的设备列表为空
    Then 调用设备管理器的获取所有设备列表接口,返回的设备列表如下:
      | Protocol | DeviceId        | TypeId        | TypeName        | TypeCode        | Model       | ProdNo       | ParentId        | SubDevNo        | Extra   |

  #清除设备列表
  Scenario: [2008]用户先准备Toolkit,更新设备列表,然后清除设备列表,Toolkit释放设备成功后移除设备,然后获取设备列表,获取的设备列表为空,则清除设备列表成功
    Given Toolkit的连接工具接口的执行结果为"成功"
    When 调用设备管理器的准备Toolkit接口
    When 等待"1"秒
    Given Toolkit的更新设备列表接口的执行结果为"成功"
    Given Toolkit的连接用户接入网关的执行结果为"成功"
    Given Toolkit的连接设备接口等待"0"秒返回执行结果为"成功"
    Given Toolkit的获取设备基础信息返回数据如下
      | Protocol   | DeviceId        | TypeId        | TypeName        | TypeCode        | Model       | ProdNo       | ParentId        | SubDevNo        | Extra   |
    When 用户更新设备列表,立即更新标志为"TRUE"
    When 等待"1"秒
    Then Toolkit的连接设备接口被调用"2"次,参数设备Id为"fake_device_id1,fake_device_id2"
    When 订阅者"userC"订阅设备列表变化,立即通知标志为"FALSE"
    When 调用设备管理器的清除设备列表接口
    Given Toolkit的释放设备接口的执行结果为"成功"
    When 等待"2"秒
    Then Toolkit的释放设备接口被调用"2"次,参数设备Id为"fake_device_id1,fake_device_id2"
    Then 刷新的设备列表的设备连接状态如下:
      | Protocol   | DeviceId        | DeviceState |
      | haier-usdk | fake_device_id1 | RELEASED    |
      | haier-usdk | fake_device_id2 | RELEASED    |
    Then 订阅者"userC"收到设备列表变化通知"1"次,变化的设备列表如下:
      | Protocol | DeviceId | TypeId | TypeName | TypeCode | Model | ProdNo | ParentId | SubDevNo | Extra |
    Then 调用设备管理器的获取所有设备列表接口,返回的设备列表如下:
      | Protocol | DeviceId | TypeId | TypeName | TypeCode | Model | ProdNo | ParentId | SubDevNo | Extra |

  Scenario: [2008-2]用户先准备Toolkit,更新设备列表,然后清除设备列表,Toolkit释放设备失败,然后获取设备列表,获取的设备列表为空
    Given Toolkit的连接工具接口的执行结果为"成功"
    When 调用设备管理器的准备Toolkit接口
    When 等待"1"秒
    Given Toolkit的更新设备列表接口的执行结果为"成功"
    Given Toolkit的连接用户接入网关的执行结果为"成功"
    Given Toolkit的连接设备接口等待"0"秒返回执行结果为"成功"
    Given Toolkit的获取设备基础信息返回数据如下
      | Protocol   | DeviceId        | TypeId        | TypeName        | TypeCode        | Model       | ProdNo       | ParentId        | SubDevNo        | Extra   |
    When 用户更新设备列表,立即更新标志为"TRUE"
    When 等待"1"秒
    Then Toolkit的连接设备接口被调用"2"次,参数设备Id为"fake_device_id1,fake_device_id2"
    When 订阅者"userC"订阅设备列表变化,立即通知标志为"FALSE"
    When 调用设备管理器的清除设备列表接口
    Given Toolkit的释放设备接口的执行结果为"失败"
    When 等待"2"秒
    Then 订阅者"userC"收到设备列表变化通知"1"次,变化的设备列表如下:
      | Protocol | DeviceId | TypeId | TypeName | TypeCode | Model | ProdNo | ParentId | SubDevNo | Extra |
    Then 调用设备管理器的获取所有设备列表接口,返回的设备列表如下:
      | Protocol   | DeviceId        | TypeId        | TypeName        | TypeCode        | Model       | ProdNo       | ParentId        | SubDevNo        | Extra   |

  @ios_ignore
  Scenario: [2008-3]用户清除设备列表,Toolkit释放设备失败抛出异常,然后获取设备列表,获取的设备列表为空
    Given Toolkit的连接工具接口的执行结果为"成功"
    When 调用设备管理器的准备Toolkit接口
    When 等待"1"秒
    Given Toolkit的更新设备列表接口的执行结果为"成功"
    Given Toolkit的连接用户接入网关的执行结果为"成功"
    Given Toolkit的连接设备接口的执行结果为"成功"
    Given Toolkit的获取设备基础信息返回数据如下
      | Protocol   | DeviceId        | TypeId        | TypeName        | TypeCode        | Model       | ProdNo       | ParentId        | SubDevNo        | Extra   |
    When 用户更新设备列表,立即更新标志为"TRUE"
    When 等待"1"秒
    When 调用设备管理器的清除设备列表接口
    Given Toolkit的释放设备接口失败,抛出异常
    When 等待"2"秒
    Then 调用设备管理器的获取所有设备列表接口,返回的设备列表如下:
      | Protocol   | DeviceId        | TypeId        | TypeName        | TypeCode        | Model       | ProdNo       | ParentId        | SubDevNo        | Extra   |

  #连接、断开用户接入网关
  Scenario: [2009]执行连接用户接入网关，usdk连接网关接口被调用
    When 调用设备管理器的连接用户接入网关接口
    When 等待"1"秒
    Then Toolkit的连接用户接入网关接口被调用"1"次,参数网关信息为"usdk-user-id=fakeUserId,usdk-access-token=fakeAccessToken"

  Scenario: [2009-3]执行连接用户断开网关，usdk断开网关接口被调用
    When 调用设备管理器的断开用户接入网关接口
    Then Toolkit的断开用户网关接口被调用"1"次

  @ios_ignore
  Scenario: [2009-4]当用户已成功接入网关，执行断开用户接入网关,断开成功后会清除用户网关参数
    Given Toolkit的连接用户接入网关的执行结果为"成功"
    Given Toolkit的断开用户接入网关的执行结果为"成功"
    When 调用设备管理器的连接用户接入网关接口
    When 等待"1"秒
    When 调用设备管理器的断开用户接入网关接口
    Then Toolkit的断开用户网关接口被调用"1"次
    Then Toolkit的连接用户接入网关接口被调用"1"次,参数网关信息为"usdk-user-id=fakeUserId,usdk-access-token=fakeAccessToken"
    Then Toolkit代理获取接入网关接口返回结果为"空对象"

  #订阅设备列表变化
  Scenario: [2010]多个用户订阅设备列表,设备列表变化,每个订阅者都能收到通知
    Given Toolkit的更新设备列表接口的执行结果为"成功"
    Given Toolkit的连接用户接入网关的执行结果为"成功"
    When 订阅者"userA"订阅设备列表变化,立即通知标志为"FALSE"
    When 订阅者"userB"订阅设备列表变化,立即通知标志为"FALSE"
    When 用户更新设备列表,立即更新标志为"TRUE"
    When 等待"1"秒
    Then 订阅者"userA"收到设备列表变化通知"1"次,变化的设备列表如下:
      | Protocol   | DeviceId        | TypeId        | TypeName        | TypeCode        | Model       | ProdNo       | ParentId        | SubDevNo        | Extra   |
      | haier-usdk | fake_device_id1 | fake_type_id1 | fake_type_name1 | fake_type_code1 | fake_model1 | fake_pro_no1 | fake_parent_id1 | fake_device_id2 | {"DI-Basic.displayName":"fake_name","DI-Basic.room":"fake_room","DI-Basic.roomId":"fake_room_id","DI-Basic.bindTime":"fake_bind_time","DI-Product.brand":"fake_brand","DI-Product.bind_type":"fake_bind_type","DI-Product.configType":"fake_configT_type","DI-Product.category":"fake_category","DI-Relation.ownerId":"fake_owner_id","DI-Product.imageAddr1":"fake_image_addr1"} |
      | haier-usdk | fake_device_id2 | fake_type_id2 | fake_type_name2 | fake_type_code2 | fake_model2 | fake_pro_no2 | fake_parent_id2 | fake_device_id3 | {"DI-Basic.displayName":"fake_name","DI-Basic.room":"fake_room","DI-Basic.roomId":"fake_room_id","DI-Basic.bindTime":"fake_bind_time","DI-Product.brand":"fake_brand","DI-Product.bind_type":"fake_bind_type","DI-Product.configType":"fake_configT_type","DI-Product.category":"fake_category","DI-Relation.ownerId":"fake_owner_id","DI-Product.imageAddr1":"fake_image_addr1"} |
    Then 订阅者"userB"收到设备列表变化通知"1"次,变化的设备列表如下:
      | Protocol   | DeviceId        | TypeId        | TypeName        | TypeCode        | Model       | ProdNo       | ParentId        | SubDevNo        | Extra   |
      | haier-usdk | fake_device_id1 | fake_type_id1 | fake_type_name1 | fake_type_code1 | fake_model1 | fake_pro_no1 | fake_parent_id1 | fake_device_id2 | {"DI-Basic.displayName":"fake_name","DI-Basic.room":"fake_room","DI-Basic.roomId":"fake_room_id","DI-Basic.bindTime":"fake_bind_time","DI-Product.brand":"fake_brand","DI-Product.bind_type":"fake_bind_type","DI-Product.configType":"fake_configT_type","DI-Product.category":"fake_category","DI-Relation.ownerId":"fake_owner_id","DI-Product.imageAddr1":"fake_image_addr1"} |
      | haier-usdk | fake_device_id2 | fake_type_id2 | fake_type_name2 | fake_type_code2 | fake_model2 | fake_pro_no2 | fake_parent_id2 | fake_device_id3 | {"DI-Basic.displayName":"fake_name","DI-Basic.room":"fake_room","DI-Basic.roomId":"fake_room_id","DI-Basic.bindTime":"fake_bind_time","DI-Product.brand":"fake_brand","DI-Product.bind_type":"fake_bind_type","DI-Product.configType":"fake_configT_type","DI-Product.category":"fake_category","DI-Relation.ownerId":"fake_owner_id","DI-Product.imageAddr1":"fake_image_addr1"} |

  Scenario: [2010-2]订阅设备列表时,可以立即获得当前的设备列表情况
    Given Toolkit的更新设备列表接口的执行结果为"成功"
    Given Toolkit的连接用户接入网关的执行结果为"成功"
    When 用户更新设备列表,立即更新标志为"TRUE"
    When 等待"1"秒
    When 订阅者"userA"订阅设备列表变化,立即通知标志为"TRUE"
    Then 订阅者"userA"收到设备列表变化通知"1"次,变化的设备列表如下:
      | Protocol   | DeviceId        | TypeId        | TypeName        | TypeCode        | Model       | ProdNo       | ParentId        | SubDevNo        | Extra   |
      | haier-usdk | fake_device_id1 | fake_type_id1 | fake_type_name1 | fake_type_code1 | fake_model1 | fake_pro_no1 | fake_parent_id1 | fake_device_id2 | {"DI-Basic.displayName":"fake_name","DI-Basic.room":"fake_room","DI-Basic.roomId":"fake_room_id","DI-Basic.bindTime":"fake_bind_time","DI-Product.brand":"fake_brand","DI-Product.bind_type":"fake_bind_type","DI-Product.configType":"fake_configT_type","DI-Product.category":"fake_category","DI-Relation.ownerId":"fake_owner_id","DI-Product.imageAddr1":"fake_image_addr1"} |
      | haier-usdk | fake_device_id2 | fake_type_id2 | fake_type_name2 | fake_type_code2 | fake_model2 | fake_pro_no2 | fake_parent_id2 | fake_device_id3 | {"DI-Basic.displayName":"fake_name","DI-Basic.room":"fake_room","DI-Basic.roomId":"fake_room_id","DI-Basic.bindTime":"fake_bind_time","DI-Product.brand":"fake_brand","DI-Product.bind_type":"fake_bind_type","DI-Product.configType":"fake_configT_type","DI-Product.category":"fake_category","DI-Relation.ownerId":"fake_owner_id","DI-Product.imageAddr1":"fake_image_addr1"} |

  #取消订阅设备列表变化
  Scenario: [2010-3]执行更新设备列表,移除设备列表监听后,不再收到设备列表变化通知
    Given Toolkit的更新设备列表接口的执行结果为"成功"
    Given Toolkit的连接用户接入网关的执行结果为"成功"
    When 订阅者"userA"订阅设备列表变化,立即通知标志为"FALSE"
    When 订阅者"userA"取消订阅设备列表变化
    When 用户更新设备列表,立即更新标志为"TRUE"
    When 等待"1"秒
    Then 订阅者"userA"收到设备列表变化通知"0"次,变化的设备列表如下:
      | Protocol | DeviceId | TypeId | TypeName | TypeCode | Model | ProdNo | ParentId | SubDevNo | Extra |

  Scenario: [2010-4]多个用户userA和userB订阅设备列表,当userB用户取消订阅，userB不会再收到设备列表变化通知，userA能收到设备列表变化通知
    Given Toolkit的更新设备列表接口的执行结果为"成功"
    Given Toolkit的连接用户接入网关的执行结果为"成功"
    When 订阅者"userA"订阅设备列表变化,立即通知标志为"FALSE"
    When 订阅者"userB"订阅设备列表变化,立即通知标志为"FALSE"
    When 订阅者"userB"取消订阅设备列表变化
    When 用户更新设备列表,立即更新标志为"TRUE"
    When 等待"1"秒
    Then 订阅者"userA"收到设备列表变化通知"1"次,变化的设备列表如下:
      | Protocol   | DeviceId        | TypeId        | TypeName        | TypeCode        | Model       | ProdNo       | ParentId        | SubDevNo        | Extra   |
      | haier-usdk | fake_device_id1 | fake_type_id1 | fake_type_name1 | fake_type_code1 | fake_model1 | fake_pro_no1 | fake_parent_id1 | fake_device_id2 | {"DI-Basic.displayName":"fake_name","DI-Basic.room":"fake_room","DI-Basic.roomId":"fake_room_id","DI-Basic.bindTime":"fake_bind_time","DI-Product.brand":"fake_brand","DI-Product.bind_type":"fake_bind_type","DI-Product.configType":"fake_configT_type","DI-Product.category":"fake_category","DI-Relation.ownerId":"fake_owner_id","DI-Product.imageAddr1":"fake_image_addr1"} |
      | haier-usdk | fake_device_id2 | fake_type_id2 | fake_type_name2 | fake_type_code2 | fake_model2 | fake_pro_no2 | fake_parent_id2 | fake_device_id3 | {"DI-Basic.displayName":"fake_name","DI-Basic.room":"fake_room","DI-Basic.roomId":"fake_room_id","DI-Basic.bindTime":"fake_bind_time","DI-Product.brand":"fake_brand","DI-Product.bind_type":"fake_bind_type","DI-Product.configType":"fake_configT_type","DI-Product.category":"fake_category","DI-Relation.ownerId":"fake_owner_id","DI-Product.imageAddr1":"fake_image_addr1"} |
    Then 订阅者"userB"收到设备列表变化通知"0"次,变化的设备列表如下:
      | Protocol | DeviceId | TypeId | TypeName | TypeCode | Model | ProdNo | ParentId | SubDevNo | Extra |

  @ios_ignore
  Scenario: [2011]多个用户订阅设备变化通知，当有新增设备时，都会收到新增设备列表通知。
    When 订阅者"userA"订阅设备变化通知
    When 订阅者"userB"订阅设备变化通知
    When 发现新设备代理方法被执行,新发现的设备列表如下:
      | Protocol   | DeviceId        | TypeId        | TypeName        | TypeCode        | Model       | ProdNo       | ParentId        | SubDevNo        | Extra   |
      | haier-usdk | fake_device_id3 | fake_type_id3 | fake_type_name3 | fake_type_code3 | fake_model3 | fake_pro_no3 | fake_parent_id3 | fake_device_id2 | {"DI-Basic.displayName":"fake_name","DI-Basic.room":"fake_room","DI-Basic.roomId":"fake_room_id","DI-Basic.bindTime":"fake_bind_time","DI-Product.brand":"fake_brand","DI-Product.bind_type":"fake_bind_type","DI-Product.configType":"fake_configT_type","DI-Product.category":"fake_category","DI-Relation.ownerId":"fake_owner_id","DI-Product.imageAddr1":"fake_image_addr1"} |
    Then 订阅者"userA"收到新发现设备列表如下:
      | Protocol   | DeviceId        | TypeId        | TypeName        | TypeCode        | Model       | ProdNo       | ParentId        | SubDevNo        | Extra   |
      | haier-usdk | fake_device_id3 | fake_type_id3 | fake_type_name3 | fake_type_code3 | fake_model3 | fake_pro_no3 | fake_parent_id3 | fake_device_id2 | {"DI-Basic.displayName":"fake_name","DI-Basic.room":"fake_room","DI-Basic.roomId":"fake_room_id","DI-Basic.bindTime":"fake_bind_time","DI-Product.brand":"fake_brand","DI-Product.bind_type":"fake_bind_type","DI-Product.configType":"fake_configT_type","DI-Product.category":"fake_category","DI-Relation.ownerId":"fake_owner_id","DI-Product.imageAddr1":"fake_image_addr1"} |
    Then 订阅者"userB"收到新发现设备列表如下:
      | Protocol   | DeviceId        | TypeId        | TypeName        | TypeCode        | Model       | ProdNo       | ParentId        | SubDevNo        | Extra   |
      | haier-usdk | fake_device_id3 | fake_type_id3 | fake_type_name3 | fake_type_code3 | fake_model3 | fake_pro_no3 | fake_parent_id3 | fake_device_id2 | {"DI-Basic.displayName":"fake_name","DI-Basic.room":"fake_room","DI-Basic.roomId":"fake_room_id","DI-Basic.bindTime":"fake_bind_time","DI-Product.brand":"fake_brand","DI-Product.bind_type":"fake_bind_type","DI-Product.configType":"fake_configT_type","DI-Product.category":"fake_category","DI-Relation.ownerId":"fake_owner_id","DI-Product.imageAddr1":"fake_image_addr1"} |

  @ios_ignore
  Scenario: [2012]多个用户订阅设备变化通知，当删除设备时，多个用户都会收到删除设备列表通知。
    When 订阅者"userA"订阅设备变化通知
    When 订阅者"userB"订阅设备变化通知
    When 删除设备代理方法被执行,删除的设备列表如下:
      | Protocol   | DeviceId        | TypeId        | TypeName        | TypeCode        | Model       | ProdNo       | ParentId        | SubDevNo        | Extra   |
      | haier-usdk | fake_device_id1 | fake_type_id1 | fake_type_name1 | fake_type_code1 | fake_model1 | fake_pro_no1 | fake_parent_id1 | fake_device_id2 | {"DI-Basic.displayName":"fake_name","DI-Basic.room":"fake_room","DI-Basic.roomId":"fake_room_id","DI-Basic.bindTime":"fake_bind_time","DI-Product.brand":"fake_brand","DI-Product.bind_type":"fake_bind_type","DI-Product.configType":"fake_configT_type","DI-Product.category":"fake_category","DI-Relation.ownerId":"fake_owner_id","DI-Product.imageAddr1":"fake_image_addr1"} |
    Then  订阅者"userA"收到删除设备列表如下:
      | Protocol   | DeviceId        | TypeId        | TypeName        | TypeCode        | Model       | ProdNo       | ParentId        | SubDevNo        | Extra   |
      | haier-usdk | fake_device_id1 | fake_type_id1 | fake_type_name1 | fake_type_code1 | fake_model1 | fake_pro_no1 | fake_parent_id1 | fake_device_id2 | {"DI-Basic.displayName":"fake_name","DI-Basic.room":"fake_room","DI-Basic.roomId":"fake_room_id","DI-Basic.bindTime":"fake_bind_time","DI-Product.brand":"fake_brand","DI-Product.bind_type":"fake_bind_type","DI-Product.configType":"fake_configT_type","DI-Product.category":"fake_category","DI-Relation.ownerId":"fake_owner_id","DI-Product.imageAddr1":"fake_image_addr1"} |
    Then  订阅者"userB"收到删除设备列表如下:
      | Protocol   | DeviceId        | TypeId        | TypeName        | TypeCode        | Model       | ProdNo       | ParentId        | SubDevNo        | Extra   |
      | haier-usdk | fake_device_id1 | fake_type_id1 | fake_type_name1 | fake_type_code1 | fake_model1 | fake_pro_no1 | fake_parent_id1 | fake_device_id2 | {"DI-Basic.displayName":"fake_name","DI-Basic.room":"fake_room","DI-Basic.roomId":"fake_room_id","DI-Basic.bindTime":"fake_bind_time","DI-Product.brand":"fake_brand","DI-Product.bind_type":"fake_bind_type","DI-Product.configType":"fake_configT_type","DI-Product.category":"fake_category","DI-Relation.ownerId":"fake_owner_id","DI-Product.imageAddr1":"fake_image_addr1"} |

@ios_ignore
  Scenario: [2013]用户订阅设备变化通知，当设备列表变化时，主动更新设备列表。
    Given Toolkit的更新设备列表接口的执行结果为"成功"
    Given Toolkit的连接用户接入网关的执行结果为"成功"
    When 订阅者"userA"订阅设备变化通知
    When 设备列表变化代理方法被执行
    When 等待"6"秒
    Then Toolkit的更新设备列表接口被调用"1"次
    Then 设备数据源的更新设备列表接口被调用"1"次,参数立即更新为"TRUE"
    Then 设备工厂的创建设备接口被调用"2"次,参数设备列表如下:
      | Protocol   | DeviceId        | TypeId        | TypeName        | TypeCode        | Model       | ProdNo       | ParentId        | SubDevNo        | Extra   |
      | haier-usdk | fake_device_id1 | fake_type_id1 | fake_type_name1 | fake_type_code1 | fake_model1 | fake_pro_no1 | fake_parent_id1 | fake_device_id2 | {"DI-Basic.displayName":"fake_name","DI-Basic.room":"fake_room","DI-Basic.roomId":"fake_room_id","DI-Basic.bindTime":"fake_bind_time","DI-Product.brand":"fake_brand","DI-Product.bind_type":"fake_bind_type","DI-Product.configType":"fake_configT_type","DI-Product.category":"fake_category","DI-Relation.ownerId":"fake_owner_id","DI-Product.imageAddr1":"fake_image_addr1"} |
      | haier-usdk | fake_device_id2 | fake_type_id2 | fake_type_name2 | fake_type_code2 | fake_model2 | fake_pro_no2 | fake_parent_id2 | fake_device_id3 | {"DI-Basic.displayName":"fake_name","DI-Basic.room":"fake_room","DI-Basic.roomId":"fake_room_id","DI-Basic.bindTime":"fake_bind_time","DI-Product.brand":"fake_brand","DI-Product.bind_type":"fake_bind_type","DI-Product.configType":"fake_configT_type","DI-Product.category":"fake_category","DI-Relation.ownerId":"fake_owner_id","DI-Product.imageAddr1":"fake_image_addr1"} |
    Then Toolkit的连接用户接入网关接口被调用"1"次,参数网关信息为"usdk-user-id=fakeUserId,usdk-access-token=fakeAccessToken"

  @android_ignore
  Scenario: [2013-1]用户订阅设备变化通知，当设备列表变化时，主动更新设备列表。
    When 设备列表变化代理方法被执行
    When 等待"6"秒
    Then Toolkit的更新设备列表接口被调用"1"次
    Then 设备数据源的更新设备列表接口被调用"1"次,参数立即更新为"TRUE"
  @ios_ignore
  Scenario: [2014]当用户快捷启动包含附件设备的设备时，主设备和附件设备都会优先准备，主设备的优先级高于附件设备，附件设备的优先级高于其它设备
    Given Toolkit的连接工具接口的执行结果为"成功"
    When 调用设备管理器的准备Toolkit接口
    When 等待"1"秒
    Given 设备数据源的更新设备列表接口返回"5"条设备数据,设备id从"0"到"4"递增,设备数据如下:
      | Protocol   | DeviceId         | TypeId         | TypeName         | TypeCode         | Model        | ProdNo        | ParentId         | SubDevNo         | Extra   |
      | haier-usdk | fake_device_id   | fake_type_id   | fake_type_name   | fake_type_code   | fake_model   | fake_pro_no   | fake_parent_id   | fake_device_id   | {"DI-Basic.displayName":"fake_name","DI-Basic.room":"fake_room","DI-Basic.roomId":"fake_room_id","DI-Basic.bindTime":"fake_bind_time","DI-Product.brand":"fake_brand","DI-Product.bind_type":"fake_bind_type","DI-Product.configType":"fake_configT_type","DI-Product.category":"fake_category","DI-Relation.ownerId":"fake_owner_id","DI-Product.imageAddr1":"fake_image_addr1"} |
    Given Toolkit的获取设备基础信息返回数据如下
      | Protocol | DeviceId | TypeId | TypeName | TypeCode | Model | ProdNo | ParentId | SubDevNo | Extra |
    Given 快捷启动的主设备id为"fake_device_id1",附件设备id为"fake_device_id2,fake_device_id3,fake_device_id4"
    Given Toolkit的更新设备列表接口的执行结果为"成功"
    Given Toolkit的连接用户接入网关的执行结果为"成功"
    Given Toolkit的连接设备接口的执行结果为"成功"
    When 用户更新设备列表,立即更新标志为"TRUE"
    When 调用优先准备设备接口,协议为"haier-usdk",设备为快捷启动的主设备和附件设备
    Then 设备准备队列的顺序为"fake_device_id1,fake_device_id2,fake_device_id3,fake_device_id4,fake_device_id0"

  @ignore
  Scenario Outline: [2015]用户将一些设备加入家庭优先准备队列数组，uSDKDeviceManager中没有这些设备。准备设备过程中，uSDKDeviceManagerDelegate的didAddDevices回调接口上报家庭优先准备队列数组中的设备，则家庭优先准备队列数组中的设备可以成功准备
    Given Toolkit的连接工具接口的执行结果为"成功"
    When 调用设备管理器的准备Toolkit接口
    When 等待"1"秒
    Given 设备数据源的更新设备列表接口返回"5"条设备数据,设备id从"0"到"4"递增,设备数据如下:
      | Protocol   | DeviceId         | TypeId         | TypeName         | TypeCode         | Model        | ProdNo        | ParentId         | SubDevNo         | Extra   |
      | haier-usdk | fake_device_id   | fake_type_id   | fake_type_name   | fake_type_code   | fake_model   | fake_pro_no   | fake_parent_id   | fake_device_id   | {"DI-Basic.displayName":"fake_name","DI-Basic.room":"fake_room","DI-Basic.roomId":"fake_room_id","DI-Basic.bindTime":"fake_bind_time","DI-Product.brand":"fake_brand","DI-Product.bind_type":"fake_bind_type","DI-Product.configType":"fake_configT_type","DI-Product.category":"fake_category","DI-Relation.ownerId":"fake_owner_id","DI-Product.imageAddr1":"fake_image_addr1"} |
    Given Toolkit的更新设备列表接口的执行结果为"成功"
    Given Toolkit的连接用户接入网关的执行结果为"成功"
    When 将如下设备加入当前家庭设备准备列表:
      | devices |
      | fake_device_id0,fake_device_id1 |
    Then 设备家庭准备列表为:
      | devices |
      | fake_device_id0,fake_device_id1 |
    Then 设备准备队列的顺序为"fake_device_id0,fake_device_id1,fake_device_id2,fake_device_id3,fake_device_id4"
    When 用户更新设备列表,立即更新标志为"TRUE"
    When uSDKDeviceManagerDelegate的didAddDevices回调接口,上报设备如下:
      | devices |
      | <usdkReportDevices> |
    When 等待"1"秒
    Then 设备家庭准备列表为:
      | devices |
      | <familyDevices> |
    Then 设备准备队列为:
      | devices |
      | <prepareDevices> |
    Examples:
      | usdkReportDevices                 | familyDevices                   | prepareDevices                                  |
      | fake_device_id0,fake_device_id1   |                                 | fake_device_id2,fake_device_id3,fake_device_id4 |
      |                                   | fake_device_id0,fake_device_id1 | fake_device_id0,fake_device_id1,fake_device_id2,fake_device_id3,fake_device_id4 |
      | fake_device_id100                 | fake_device_id0,fake_device_id1 | fake_device_id0,fake_device_id1,fake_device_id2,fake_device_id3,fake_device_id4 |
      | fake_device_id0                   | fake_device_id1                 | fake_device_id1,fake_device_id2,fake_device_id3,fake_device_id4                 |
      | fake_device_id0,fake_device_id100 | fake_device_id1                 | fake_device_id1,fake_device_id2,fake_device_id3,fake_device_id4                 |
   @android_ignore
    Scenario: [2016]将非数组类型加入家庭优先准备队列数组，则家庭优先准备队列为空
    When 将如下设备加入当前家庭设备准备列表:
      | devices |
      |         |
    Then 设备家庭准备列表为:
      | devices |
      |         |

  @ios_ignore
  Scenario Outline: [2017]用户将一些设备加入家庭优先准备队列数组，uSDKDeviceManager中没有这些设备。准备设备过程中，UpDeviceDetectListener的onFind回调接口上报家庭优先准备队列数组中的设备，则家庭优先准备队列数组中的设备可以准备成功
    Given Toolkit的连接工具接口的执行结果为"成功"
    When 调用设备管理器的准备Toolkit接口
    When 等待"1"秒
    Given 设备数据源的更新设备列表接口返回"5"条设备数据,设备id从"0"到"4"递增,设备数据如下:
      | Protocol   | DeviceId       | TypeId       | TypeName       | TypeCode       | Model      | ProdNo      | ParentId       | SubDevNo       | Extra                                                                                                                                                                                                                                                                                                                                                                             |
      | haier-usdk | fake_device_id | fake_type_id | fake_type_name | fake_type_code | fake_model | fake_pro_no | fake_parent_id | fake_device_id | {"DI-Basic.displayName":"fake_name","DI-Basic.room":"fake_room","DI-Basic.roomId":"fake_room_id","DI-Basic.bindTime":"fake_bind_time","DI-Product.brand":"fake_brand","DI-Product.bind_type":"fake_bind_type","DI-Product.configType":"fake_configT_type","DI-Product.category":"fake_category","DI-Relation.ownerId":"fake_owner_id","DI-Product.imageAddr1":"fake_image_addr1"} |
    Given Toolkit的更新设备列表接口的执行结果为"成功"
    Given Toolkit的连接用户接入网关的执行结果为"成功"
    Given Toolkit的连接设备接口的执行结果为"成功"
    When 将如下设备加入当前家庭设备准备列表:
      | devices                         |
      | fake_device_id0,fake_device_id1 |
    Then 设备家庭准备列表为:
      | devices                         |
      | fake_device_id0,fake_device_id1 |
    When 用户更新设备列表,立即更新标志为"TRUE"
    When UpDeviceDetectListener的onFind回调接口,上报设备如下:
      | devices             |
      | <usdkReportDevices> |
    When 等待"3"秒
    Then 设备家庭准备列表为:
      | devices         |
      | <familyDevices> |
    Examples:
      | usdkReportDevices                 | familyDevices                   |
      | fake_device_id0,fake_device_id1   |                                 |
      |                                   | fake_device_id0,fake_device_id1 |
      | fake_device_id100                 | fake_device_id0,fake_device_id1 |
      | fake_device_id0                   | fake_device_id1                 |
      | fake_device_id0,fake_device_id100 | fake_device_id1                 |

  @ios_ignore
  Scenario Outline: [2018]用户将设备分别加入当前家庭、普通设备列表，将会按照规则准备当前家庭设备、普通设备顺序准备设备
    Given Toolkit的连接工具接口的执行结果为"成功"
    When 调用设备管理器的准备Toolkit接口
    When 等待"1"秒
    Given 设备数据源的更新设备列表接口返回"5"条设备数据,设备id从"0"到"4"递增,设备数据如下:
      | Protocol   | DeviceId       | TypeId       | TypeName       | TypeCode       | Model      | ProdNo      | ParentId       | SubDevNo       | Extra                                                                                                                                                                                                                                                                                                                                                                             |
      | haier-usdk | fake_device_id | fake_type_id | fake_type_name | fake_type_code | fake_model | fake_pro_no | fake_parent_id | fake_device_id | {"DI-Basic.displayName":"fake_name","DI-Basic.room":"fake_room","DI-Basic.roomId":"fake_room_id","DI-Basic.bindTime":"fake_bind_time","DI-Product.brand":"fake_brand","DI-Product.bind_type":"fake_bind_type","DI-Product.configType":"fake_configT_type","DI-Product.category":"fake_category","DI-Relation.ownerId":"fake_owner_id","DI-Product.imageAddr1":"fake_image_addr1"} |
    Given Toolkit的更新设备列表接口的执行结果为"成功"
    Given Toolkit的连接用户接入网关的执行结果为"成功"
    Given Toolkit的连接设备接口的执行结果为"成功"
    Given 设备管理器创建的设备队对调用准备方法顺序进行记录
    When 将如下设备加入当前家庭设备准备列表:
      | devices         |
      | <familyDevices> |
    Then 设备家庭准备列表为:
      | devices         |
      | <familyDevices> |
    When UpDeviceDetectListener的onFind回调接口,上报设备如下:
      | devices         |
      | <familyDevices> |
    When 用户更新设备列表,立即更新标志为"TRUE"
    When 等待"3"秒
    Then 当前家庭设备开始准备早于普通设备准备
      | devices         | normalDevices   |
      | <familyDevices> | <normalDevices> |
    Examples:
      | familyDevices                   | normalDevices                                   |
      | fake_device_id3,fake_device_id4 | fake_device_id0,fake_device_id1,fake_device_id2 |
      | fake_device_id0,fake_device_id1 | fake_device_id2,fake_device_id3,fake_device_id4 |
      | fake_device_id0,fake_device_id4 | fake_device_id1,fake_device_id2,fake_device_id3 |

  @ios_ignore
  Scenario Outline: [2019]用户将设备分别加入优先准备、当前家庭、普通设备列表，将会按照规则准备优先准备设备、当前家庭设备、普通设备方式顺序准备设备
    Given Toolkit的连接工具接口的执行结果为"成功"
    When 调用设备管理器的准备Toolkit接口
    When 等待"1"秒
    Given 设备数据源的更新设备列表接口返回"5"条设备数据,设备id从"0"到"5"递增,设备数据如下:
      | Protocol   | DeviceId       | TypeId       | TypeName       | TypeCode       | Model      | ProdNo      | ParentId       | SubDevNo       | Extra                                                                                                                                                                                                                                                                                                                                                                             |
      | haier-usdk | fake_device_id | fake_type_id | fake_type_name | fake_type_code | fake_model | fake_pro_no | fake_parent_id | fake_device_id | {"DI-Basic.displayName":"fake_name","DI-Basic.room":"fake_room","DI-Basic.roomId":"fake_room_id","DI-Basic.bindTime":"fake_bind_time","DI-Product.brand":"fake_brand","DI-Product.bind_type":"fake_bind_type","DI-Product.configType":"fake_configT_type","DI-Product.category":"fake_category","DI-Relation.ownerId":"fake_owner_id","DI-Product.imageAddr1":"fake_image_addr1"} |
    Given Toolkit的更新设备列表接口的执行结果为"成功"
    Given Toolkit的连接用户接入网关的执行结果为"成功"
    Given Toolkit的连接设备接口的执行结果为"成功"
    Given 设备管理器创建的设备队对调用准备方法顺序进行记录
    Given 将如下设备加入当优先准备列表:
      | devices           |
      | <priorityDevices> |
    When 将如下设备加入当前家庭设备准备列表:
      | devices         |
      | <familyDevices> |
    Then 设备家庭准备列表为:
      | devices         |
      | <familyDevices> |
    When UpDeviceDetectListener的onFind回调接口,上报设备如下:
      | devices            |
      | <usdkReportDevice> |
    When 用户更新设备列表,立即更新标志为"TRUE"
    When 等待"3"秒
    Then 当前家庭设备开始准备早于普通设备准备
      | devices         | normalDevices   |
      | <familyDevices> | <normalDevices> |
    Then 设备按照优先准备设备、当前家庭设备、普通设备顺序进行准备设备
      | priorityDevices   | devices         | normalDevices   |
      | <priorityDevices> | <familyDevices> | <normalDevices> |

    Examples:
      | priorityDevices                 | familyDevices                   | normalDevices                   | usdkReportDevice                                                |
      | fake_device_id0,fake_device_id1 | fake_device_id2,fake_device_id3 | fake_device_id4,fake_device_id5 | fake_device_id0,fake_device_id1,fake_device_id2,fake_device_id3 |
      | fake_device_id4,fake_device_id5 | fake_device_id2,fake_device_id3 | fake_device_id0,fake_device_id1 | fake_device_id2,fake_device_id3,fake_device_id4,fake_device_id5 |
      | fake_device_id2,fake_device_id3 | fake_device_id4,fake_device_id5 | fake_device_id0,fake_device_id1 | fake_device_id2,fake_device_id3,fake_device_id4,fake_device_id5 |

  @ios_ignore
  Scenario Outline: [2020]用户将设备分别加入优先准备、当前家庭、普通设备列表，所有设备都会进行准备，并且准备成功
    Given Toolkit的连接工具接口的执行结果为"成功"
    When 调用设备管理器的准备Toolkit接口
    When 等待"1"秒
    Given 设备数据源的更新设备列表接口返回"5"条设备数据,设备id从"0"到"5"递增,设备数据如下:
      | Protocol   | DeviceId       | TypeId       | TypeName       | TypeCode       | Model      | ProdNo      | ParentId       | SubDevNo       | Extra                                                                                                                                                                                                                                                                                                                                                                             |
      | haier-usdk | fake_device_id | fake_type_id | fake_type_name | fake_type_code | fake_model | fake_pro_no | fake_parent_id | fake_device_id | {"DI-Basic.displayName":"fake_name","DI-Basic.room":"fake_room","DI-Basic.roomId":"fake_room_id","DI-Basic.bindTime":"fake_bind_time","DI-Product.brand":"fake_brand","DI-Product.bind_type":"fake_bind_type","DI-Product.configType":"fake_configT_type","DI-Product.category":"fake_category","DI-Relation.ownerId":"fake_owner_id","DI-Product.imageAddr1":"fake_image_addr1"} |
    Given Toolkit的更新设备列表接口的执行结果为"成功"
    Given Toolkit的连接用户接入网关的执行结果为"成功"
    Given Toolkit的连接设备接口的执行结果为"成功"
    Given 将如下设备加入当优先准备列表:
      | devices           |
      | <priorityDevices> |
    When 将如下设备加入当前家庭设备准备列表:
      | devices         |
      | <familyDevices> |
    Then 设备家庭准备列表为:
      | devices         |
      | <familyDevices> |
    When UpDeviceDetectListener的onFind回调接口,上报设备如下:
      | devices            |
      | <usdkReportDevice> |
    When 用户更新设备列表,立即更新标志为"TRUE"
    When 等待"3"秒
    Then 当前家庭设备开始准备早于普通设备准备
      | devices         | normalDevices   |
      | <familyDevices> | <normalDevices> |
    Then 优先准备设备、当前家庭设备、普通设备都准备完成
      | priorityDevices   | devices         | normalDevices   |
      | <priorityDevices> | <familyDevices> | <normalDevices> |

    Examples:
      | priorityDevices                 | familyDevices                   | normalDevices                   | usdkReportDevice                                                |
      | fake_device_id0,fake_device_id1 | fake_device_id2,fake_device_id3 | fake_device_id4,fake_device_id5 | fake_device_id0,fake_device_id1,fake_device_id2,fake_device_id3 |
      | fake_device_id4,fake_device_id5 | fake_device_id2,fake_device_id3 | fake_device_id0,fake_device_id1 | fake_device_id2,fake_device_id3,fake_device_id4,fake_device_id5 |
      | fake_device_id2,fake_device_id3 | fake_device_id4,fake_device_id5 | fake_device_id0,fake_device_id1 | fake_device_id2,fake_device_id3,fake_device_id4,fake_device_id5 |

  @ios_ignore
  Scenario Outline: [2021]用户将设备加入到当前家庭优先准备队列，如果传入到设备列表为空或null将不会加入到当前家庭优先准备队列
    Given Toolkit的连接工具接口的执行结果为"成功"
    When 调用设备管理器的准备Toolkit接口
    When 等待"1"秒
    Given 设备数据源的更新设备列表接口返回"5"条设备数据,设备id从"0"到"5"递增,设备数据如下:
      | Protocol   | DeviceId       | TypeId       | TypeName       | TypeCode       | Model      | ProdNo      | ParentId       | SubDevNo       | Extra                                                                                                                                                                                                                                                                                                                                                                             |
      | haier-usdk | fake_device_id | fake_type_id | fake_type_name | fake_type_code | fake_model | fake_pro_no | fake_parent_id | fake_device_id | {"DI-Basic.displayName":"fake_name","DI-Basic.room":"fake_room","DI-Basic.roomId":"fake_room_id","DI-Basic.bindTime":"fake_bind_time","DI-Product.brand":"fake_brand","DI-Product.bind_type":"fake_bind_type","DI-Product.configType":"fake_configT_type","DI-Product.category":"fake_category","DI-Relation.ownerId":"fake_owner_id","DI-Product.imageAddr1":"fake_image_addr1"} |
    Given Toolkit的更新设备列表接口的执行结果为"成功"
    Given Toolkit的连接用户接入网关的执行结果为"成功"
    Given Toolkit的连接设备接口的执行结果为"成功"
    When 将如下设备加入当前家庭设备准备列表:
      | devices         |
      | <familyDevices> |
    Then 设备家庭准备列表为:
      | devices               |
      | <familyDevicesResult> |
    Examples:
      | familyDevices | familyDevicesResult |
      | 空集合           | 空集合                 |
      | null          | 空集合                 |

  @ios_ignore
  Scenario Outline: [2022]用户将设备加入到当前家庭优先准备队列，传入的设备列表中deviceId为空的数据会被过滤，不加入到家庭优先准备队列
    Given Toolkit的连接工具接口的执行结果为"成功"
    When 调用设备管理器的准备Toolkit接口
    When 等待"1"秒
    Given 设备数据源的更新设备列表接口返回"5"条设备数据,设备id从"0"到"5"递增,设备数据如下:
      | Protocol   | DeviceId       | TypeId       | TypeName       | TypeCode       | Model      | ProdNo      | ParentId       | SubDevNo       | Extra                                                                                                                                                                                                                                                                                                                                                                             |
      | haier-usdk | fake_device_id | fake_type_id | fake_type_name | fake_type_code | fake_model | fake_pro_no | fake_parent_id | fake_device_id | {"DI-Basic.displayName":"fake_name","DI-Basic.room":"fake_room","DI-Basic.roomId":"fake_room_id","DI-Basic.bindTime":"fake_bind_time","DI-Product.brand":"fake_brand","DI-Product.bind_type":"fake_bind_type","DI-Product.configType":"fake_configT_type","DI-Product.category":"fake_category","DI-Relation.ownerId":"fake_owner_id","DI-Product.imageAddr1":"fake_image_addr1"} |
    Given Toolkit的更新设备列表接口的执行结果为"成功"
    Given Toolkit的连接用户接入网关的执行结果为"成功"
    Given Toolkit的连接设备接口的执行结果为"成功"
    When 将如下设备加入当前家庭设备准备列表:
      | devices         |
      | <familyDevices> |
    Then 设备家庭准备列表为:
      | devices               |
      | <familyDevicesResult> |
    Examples:
      | familyDevices                    | familyDevicesResult             |
      | fake_device_id0,,fake_device_id1 | fake_device_id0,fake_device_id1 |
      | ,fake_device_id0,fake_device_id1 | fake_device_id0,fake_device_id1 |
      | fake_device_id0,fake_device_id1, | fake_device_id0,fake_device_id1 |

  @ios_ignore
  Scenario Outline: [2023]在用户将设备加入到优先当前家庭准备队列前，该设备如果已经准备好将会被过滤，不加入到当前家庭准备队列
    Given Toolkit的连接工具接口的执行结果为"成功"
    When 调用设备管理器的准备Toolkit接口
    When 等待"1"秒
    Given 设备数据源的更新设备列表接口返回"5"条设备数据,设备id从"0"到"5"递增,设备数据如下:
      | Protocol   | DeviceId       | TypeId       | TypeName       | TypeCode       | Model      | ProdNo      | ParentId       | SubDevNo       | Extra                                                                                                                                                                                                                                                                                                                                                                             |
      | haier-usdk | fake_device_id | fake_type_id | fake_type_name | fake_type_code | fake_model | fake_pro_no | fake_parent_id | fake_device_id | {"DI-Basic.displayName":"fake_name","DI-Basic.room":"fake_room","DI-Basic.roomId":"fake_room_id","DI-Basic.bindTime":"fake_bind_time","DI-Product.brand":"fake_brand","DI-Product.bind_type":"fake_bind_type","DI-Product.configType":"fake_configT_type","DI-Product.category":"fake_category","DI-Relation.ownerId":"fake_owner_id","DI-Product.imageAddr1":"fake_image_addr1"} |
    Given Toolkit的更新设备列表接口的执行结果为"成功"
    Given Toolkit的连接用户接入网关的执行结果为"成功"
    Given Toolkit的连接设备接口的执行结果为"成功"
    When 用户更新设备列表,立即更新标志为"TRUE"
    When 等待"3"秒
    When 将如下设备加入当前家庭设备准备列表:
      | devices         |
      | <familyDevices> |
    Then 设备家庭准备列表为:
      | devices         |
      | <familyDevicesResult> |
    Examples:
      | familyDevices                   | familyDevicesResult |
      | fake_device_id0,fake_device_id1 | 空集合                 |
