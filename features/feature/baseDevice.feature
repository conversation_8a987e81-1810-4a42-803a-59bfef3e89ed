Feature: 设备基类逻辑
  功能范围:
  UpDevice中的所有设备类均是设备基类的子类，它们需要遵循的共同业务逻辑均体现在设备基类中，具体如下：
  - 设备相关操作下发到对应的Toolkit，具体包括查询、订阅设备状态、子类列表，执行设备各种操作
  - 支持多个订阅者，设备的状态(报警、属性、子设备列表、extra信息、基本属性、连接)变化后，会通知订阅者
  - 会缓存设备状态变化，当设备状态变化查询设备状态会查询到最新的设备状态信息
  - prepare后会自动向Toolkit下发查询当前的报警、属性、子设备的命令，以确保准备完成后是设备的最新状态
  - 当设置ui进程report开关后，将在UI线程通知订阅者

  外部依赖:
  1.Toolkit
  2.线程调度器
  3.设备工厂

  接口说明:
  1.订阅设备状态
  支持多个订阅者，当usdk上报设备的状态变化时，会通知订阅者，
  监听的状态包括设备基础信息变化，设备连接状态，子设备列表变化，属性列表变化，报警列表变化，fota状态变化，蓝牙数据和蓝牙历史数据变化。

  2.取消订阅设备状态
  取消订阅后，设备状态变化不会发送给已经取消的订阅者。

  3.订阅设备资源
  支持多个订阅者，当usdk上报设备的图片资源时，会通知所有订阅者。

  4.取消订阅设备资源
  取消订阅后，设备图片资源上报不会发送给已经取消的订阅者。

  Background:
    Given Toolkit使用"模拟的"
    Given 使用者创建"测试"设备工厂
    Given 创建"测试"设备,唯一标识ID为"uniqueId",设备信息如下:
      | Protocol   | DeviceId        | TypeId        | TypeName        | TypeCode        | Model       | ProdNo       | ParentId        | SubDevNo             | Extra                                                                                                                                                                                                                                                                                                                                                                             |
      | haier-usdk | fake_device_id1 | fake_type_id1 | fake_type_name1 | fake_type_code1 | fake_model1 | fake_pro_no1 | fake_parent_id1 | fake_device_id18-8-1 | {"DI-Basic.displayName":"fake_name","DI-Basic.room":"fake_room","DI-Basic.roomId":"fake_room_id","DI-Basic.bindTime":"fake_bind_time","DI-Product.brand":"fake_brand","DI-Product.bind_type":"fake_bind_type","DI-Product.configType":"fake_configT_type","DI-Product.category":"fake_category","DI-Relation.ownerId":"fake_owner_id","DI-Product.imageAddr1":"fake_image_addr1"} |

  Scenario: [1000]多个用户订阅了设备，设备属性状态变化后，多个用户均收到通知
    When 订阅者"userA"订阅设备"fake_device_id1"的设备状态
    When 订阅者"userB"订阅设备"fake_device_id1"的设备状态
    When 设备"fake_device_id1"状态发生变化，具体属性变化列表如下
      | name  | value |
      | attrA | 1     |
    Then 订阅者"userA"收到设备"fake_device_id1"的事件如下
      | Action                  |
      | EVENT_ATTRIBUTES_CHANGE |
    Then 订阅者"userB"收到设备"fake_device_id1"的事件如下
      | Action                  |
      | EVENT_ATTRIBUTES_CHANGE |
    Then 使用者查询设备"fake_device_id1"属性列表如下
      | name  | value |
      | attrA | 1     |

  Scenario: [1001]多个用户订阅了设备，设备报警状态变化后，多个用户均收到通知
    When 订阅者"userA"订阅设备"fake_device_id1"的设备状态
    When 订阅者"userB"订阅设备"fake_device_id1"的设备状态
    When 设备"fake_device_id1"状态发生变化,具体报警变化列表如下
      | name     | value | time  |
      | cautionA | 1     | 86400 |
    Then 订阅者"userA"收到设备"fake_device_id1"的事件如下
      | Action               |
      | EVENT_DEVICE_CAUTION |
    Then 订阅者"userB"收到设备"fake_device_id1"的事件如下
      | Action               |
      | EVENT_DEVICE_CAUTION |
    Then 使用者查询设备"fake_device_id1"报警列表如下
      | name     | value | time  |
      | cautionA | 1     | 86400 |

  Scenario: [1002]多个用户订阅了设备，设备连接状态变化后，多个用户均收到通知
    When 订阅者"userA"订阅设备"fake_device_id1"的设备状态
    When 订阅者"userB"订阅设备"fake_device_id1"的设备状态
    When 设备"fake_device_id1"状态发生变化,具体连接状态变为"CONNECTING"
    Then 订阅者"userA"收到设备"fake_device_id1"的事件如下
      | Action                    |
      | EVENT_SUB_DEV_LIST_CHANGE |
      | EVENT_CONNECTION_CHANGE   |
    Then 订阅者"userB"收到设备"fake_device_id1"的事件如下
      | Action                    |
      | EVENT_SUB_DEV_LIST_CHANGE |
      | EVENT_CONNECTION_CHANGE   |
    Then 使用者查询设备"fake_device_id1"连接状态为"CONNECTING"

  Scenario: [1003]多个用户订阅了设备，设备就绪状态变化后，如果没有子设备，不会收到子设备列表变化通知，只发送连接状态改变通知，多个用户均收到通知
    When 订阅者"userA"订阅设备"fake_device_id1"的设备状态
    When 订阅者"userB"订阅设备"fake_device_id1"的设备状态
    When 设备"fake_device_id1"状态发生变化,具体连接状态变为"READY"
    Then 订阅者"userA"收到设备"fake_device_id1"的事件如下
      | Action                  |
      | EVENT_CONNECTION_CHANGE |
    Then 订阅者"userB"收到设备"fake_device_id1"的事件如下
      | Action                  |
      | EVENT_CONNECTION_CHANGE |
    Then 使用者查询设备"fake_device_id1"连接状态为"READY"

  Scenario: [1004]多个用户订阅了设备，设备就绪状态变化后，如果有子设备，会收到子设备列表变化通知和连接状态改变通知，多个用户均收到通知
    When 订阅者"userA"订阅设备"fake_device_id1"的设备状态
    When 订阅者"userB"订阅设备"fake_device_id1"的设备状态
    When 设备"fake_device_id1"状态发生变化,具体子设备变化列表如下
      | Protocol   | DeviceId             | TypeId        | TypeName        | TypeCode        | Model       | ProdNo       | ParentId        | SubDevNo        |
      | haier-usdk | fake_device_id18-8-1 | fake_type_id1 | fake_type_name1 | fake_type_code1 | fake_model1 | fake_pro_no1 | fake_parent_id1 | fake_device_id2 |
    When 设备"fake_device_id1"状态发生变化,具体连接状态变为"READY"
    Then 订阅者"userA"收到设备"fake_device_id1"的事件如下
      | Action                    |
      | EVENT_SUB_DEV_LIST_CHANGE |
      | EVENT_CONNECTION_CHANGE   |
    Then 订阅者"userB"收到设备"fake_device_id1"的事件如下
      | Action                    |
      | EVENT_SUB_DEV_LIST_CHANGE |
      | EVENT_CONNECTION_CHANGE   |
    Then 使用者查询设备"fake_device_id1"连接状态为"READY"
    Then 使用者查询设备"fake_device_id1"子设备列表如下
      | Protocol   | DeviceId             | TypeId        | TypeName        | TypeCode        | Model       | ProdNo       | ParentId        | SubDevNo        |
      | haier-usdk | fake_device_id18-8-1 | fake_type_id1 | fake_type_name1 | fake_type_code1 | fake_model1 | fake_pro_no1 | fake_parent_id1 | fake_device_id2 |

  @ios_ignore
  Scenario: [1005]多个用户订阅了设备，设备子类列表状态变化后，多个用户均收到通知，Android子设备列表不关心连接状态
    When 订阅者"userA"订阅设备"fake_device_id1"的设备状态
    When 订阅者"userB"订阅设备"fake_device_id1"的设备状态
    When 设备"fake_device_id1"状态发生变化,具体子设备变化列表如下
      | Protocol   | DeviceId             | TypeId        | TypeName        | TypeCode        | Model       | ProdNo       | ParentId        | SubDevNo        |
      | haier-usdk | fake_device_id18-8-1 | fake_type_id1 | fake_type_name1 | fake_type_code1 | fake_model1 | fake_pro_no1 | fake_parent_id1 | fake_device_id2 |
    Then 订阅者"userA"收到设备"fake_device_id1"的事件如下
      | Action                    |
      | EVENT_SUB_DEV_LIST_CHANGE |
    Then 订阅者"userB"收到设备"fake_device_id1"的事件如下
      | Action                    |
      | EVENT_SUB_DEV_LIST_CHANGE |
    Then 使用者查询设备"fake_device_id1"子设备列表如下
      | Protocol   | DeviceId             | TypeId        | TypeName        | TypeCode        | Model       | ProdNo       | ParentId        | SubDevNo        |
      | haier-usdk | fake_device_id18-8-1 | fake_type_id1 | fake_type_name1 | fake_type_code1 | fake_model1 | fake_pro_no1 | fake_parent_id1 | fake_device_id2 |

  @android_ignore
  Scenario: [1005-2]多个用户订阅了设备，设备子类列表状态变化后，多个用户均收到通知，iOS只有当连接状态UpDeviceConnection_READY时返回子设备列表
    When 订阅者"userA"订阅设备"fake_device_id1"的设备状态
    When 订阅者"userB"订阅设备"fake_device_id1"的设备状态
    When 设备"fake_device_id1"状态发生变化,具体连接状态变为"READY"
    When 设备"fake_device_id1"状态发生变化,具体子设备变化列表如下
      | Protocol   | DeviceId             | TypeId        | TypeName        | TypeCode        | Model       | ProdNo       | ParentId        | SubDevNo        |
      | haier-usdk | fake_device_id18-8-1 | fake_type_id1 | fake_type_name1 | fake_type_code1 | fake_model1 | fake_pro_no1 | fake_parent_id1 | fake_device_id2 |
    Then 订阅者"userA"收到设备"fake_device_id1"的事件如下
      | Action                    |
      | EVENT_SUB_DEV_LIST_CHANGE |
      | EVENT_CONNECTION_CHANGE   |
    Then 订阅者"userB"收到设备"fake_device_id1"的事件如下
      | Action                    |
      | EVENT_SUB_DEV_LIST_CHANGE |
      | EVENT_CONNECTION_CHANGE   |
    Then 使用者查询设备"fake_device_id1"连接状态为"READY"
    Then 使用者查询设备"fake_device_id1"子设备列表如下
      | Protocol   | DeviceId             | TypeId        | TypeName        | TypeCode        | Model       | ProdNo       | ParentId        | SubDevNo        |
      | haier-usdk | fake_device_id18-8-1 | fake_type_id1 | fake_type_name1 | fake_type_code1 | fake_model1 | fake_pro_no1 | fake_parent_id1 | fake_device_id2 |

  Scenario: [1006]多个用户订阅了设备，设备基本信息变化后，多个用户均收到通知
    When 订阅者"userA"订阅设备"fake_device_id1"的设备状态
    When 订阅者"userB"订阅设备"fake_device_id1"的设备状态
    When 设备"fake_device_id1"状态发生变化,具体设备基本信息变化为
      | Protocol   | DeviceId        | TypeId        | TypeName         | TypeCode        | Model       | ProdNo       | ParentId        | SubDevNo             |
      | haier-usdk | fake_device_id1 | fake_type_id1 | fake_type_name11 | fake_type_code1 | fake_model1 | fake_pro_no1 | fake_parent_id1 | fake_device_id18-8-1 |
    Then 订阅者"userA"收到设备"fake_device_id1"的事件如下
      | Action                 |
      | EVENT_BASE_INFO_CHANGE |
    Then 订阅者"userB"收到设备"fake_device_id1"的事件如下
      | Action                 |
      | EVENT_BASE_INFO_CHANGE |
    Then 使用者查询设备"fake_device_id1"基本信息如下
      | Protocol   | DeviceId        | TypeId        | TypeName         | TypeCode        | Model       | ProdNo       | ParentId        | SubDevNo             |
      | haier-usdk | fake_device_id1 | fake_type_id1 | fake_type_name11 | fake_type_code1 | fake_model1 | fake_pro_no1 | fake_parent_id1 | fake_device_id18-8-1 |

  Scenario: [1007]多个用户订阅了设备，设备Fota状态变化后，多个用户均收到通知
    When 订阅者"userA"订阅设备"fake_device_id1"的设备状态
    When 订阅者"userB"订阅设备"fake_device_id1"的设备状态
    When 设备"fake_device_id1"的Fota状态发生变化
    Then 订阅者"userA"收到设备"fake_device_id1"的事件如下
      | Action                          |
      | EVENT_DEVICE_FOTA_STATUS_CHANGE |
    Then 订阅者"userB"收到设备"fake_device_id1"的事件如下
      | Action                          |
      | EVENT_DEVICE_FOTA_STATUS_CHANGE |

  Scenario: [1008]多个用户订阅了设备，设备蓝牙数据变化后，多个用户均收到通知
    When 订阅者"userA"订阅设备"fake_device_id1"的设备状态
    When 订阅者"userB"订阅设备"fake_device_id1"的设备状态
    When 设备"fake_device_id1"状态发生变化,具体蓝牙数据变化为"1"
    Then 订阅者"userA"收到设备"fake_device_id1"的事件如下
      | Action                         |
      | EVENT_DEVICE_BLE_REALTIME_DATA |
    Then 订阅者"userB"收到设备"fake_device_id1"的事件如下
      | Action                         |
      | EVENT_DEVICE_BLE_REALTIME_DATA |
    Then 使用者查询设备"fake_device_id1"蓝牙数据为"1"

  Scenario: [1009]多个用户订阅了设备，设备蓝牙历史数据变化后，多个用户均收到通知
    When 订阅者"userA"订阅设备"fake_device_id1"的设备状态
    When 订阅者"userB"订阅设备"fake_device_id1"的设备状态
    When 设备"fake_device_id1"状态发生变化,具体蓝牙历史变化如下
      | currentCount | totalCount | data |
      | 1            | 2          | 3    |
    Then 订阅者"userA"收到设备"fake_device_id1"的事件如下
      | Action                        |
      | EVENT_DEVICE_BLE_HISTORY_DATA |
    Then 订阅者"userB"收到设备"fake_device_id1"的事件如下
      | Action                        |
      | EVENT_DEVICE_BLE_HISTORY_DATA |
    Then 使用者查询设备"fake_device_id1"蓝牙历史数据为
      | currentCount | totalCount | data |
      | 1            | 2          | 3    |

  Scenario: [1010]多个用户订阅了设备资源，设备资源上传后，多个用户均收到通知
    When 订阅者"userA"订阅设备"fake_device_id1"的数据
    When 订阅者"userB"订阅设备"fake_device_id1"的数据
    When 设备"fake_device_id1"状态发生变化,具体资源数据如下
      | name  | value |
      | nameA | 1     |
    Then 订阅者"userA"收到设备"fake_device_id1"资源如下
      | name  | value |
      | nameA | 1     |
    Then 订阅者"userB"收到设备"fake_device_id1"资源如下
      | name  | value |
      | nameA | 1     |

  @ios_ignore
  Scenario: [1011]设备prepare成功后，会立即发送一次当前设备最新状态通知，iOS与Android Notify的事件有差异，Android子设备列表不关心连接状态
    Given Toolkit的连接设备接口的执行结果为"成功"
    Given Toolkit的获取设备连接状态返回"OFFLINE"
    Given Toolkit的获取设备基础信息返回数据如下
      | Protocol   | DeviceId        | TypeId        | TypeName        | TypeCode        | Model       | ProdNo       | ParentId        | SubDevNo             |
      | haier-usdk | fake_device_id1 | fake_type_id1 | fake_type_name1 | fake_type_code1 | fake_model1 | fake_pro_no1 | fake_parent_id1 | fake_device_id18-8-1 |
    Given Toolkit的获取设备属性列表返回数据如下
      | name  | value |
      | attrA | 1     |
    Given Toolkit的获取设备报警列表返回数据如下
      | name     | value | time  |
      | cautionA | 1     | 86400 |
    Given Toolkit的获取子设备列表返回数据如下
      | Protocol   | DeviceId             | TypeId        | TypeName        | TypeCode        | Model       | ProdNo       | ParentId        | SubDevNo        |
      | haier-usdk | fake_device_id18-8-1 | fake_type_id1 | fake_type_name1 | fake_type_code1 | fake_model1 | fake_pro_no1 | fake_parent_id1 | fake_device_id2 |
    When 订阅者"userA"订阅设备"fake_device_id1"的设备状态
    When 订阅者"userB"订阅设备"fake_device_id1"的设备状态
    When 使用者调用设备"fake_device_id1"的准备接口
    When 等待"1"秒
    Then 使用者收到设备"fake_device_id1"的准备接口的结果为"成功"
    Then Toolkit的获取设备基础信息接口调用"1"次,设备id为"fake_device_id1"
    Then Toolkit的获取设备连接状态接口调用"1"次,设备id为"fake_device_id1"
    Then Toolkit的获取子设备列表接口调用"1"次,设备id为"fake_device_id1"
    Then Toolkit的获取设备属性列表接口调用"1"次,设备id为"fake_device_id1"
    Then Toolkit的获取设备报警列表接口调用"1"次,设备id为"fake_device_id1"
    Then 订阅者"userA"收到设备"fake_device_id1"的事件如下
      | Action                    |
      | EVENT_DEVICE_STATE_CHANGE |
      | EVENT_DEVICE_STATE_CHANGE |
      | EVENT_BASE_INFO_CHANGE    |
      | EVENT_SUB_DEV_LIST_CHANGE |
      | EVENT_CONNECTION_CHANGE   |
      | EVENT_SUB_DEV_LIST_CHANGE |
      | EVENT_ATTRIBUTES_CHANGE   |
      | EVENT_DEVICE_CAUTION      |
    Then 订阅者"userB"收到设备"fake_device_id1"的事件如下
      | Action                    |
      | EVENT_DEVICE_STATE_CHANGE |
      | EVENT_DEVICE_STATE_CHANGE |
      | EVENT_BASE_INFO_CHANGE    |
      | EVENT_SUB_DEV_LIST_CHANGE |
      | EVENT_CONNECTION_CHANGE   |
      | EVENT_SUB_DEV_LIST_CHANGE |
      | EVENT_ATTRIBUTES_CHANGE   |
      | EVENT_DEVICE_CAUTION      |
    Then 使用者查询设备"fake_device_id1"属性列表如下
      | name  | value |
      | attrA | 1     |
    Then 使用者查询设备"fake_device_id1"报警列表如下
      | name     | value | time  |
      | cautionA | 1     | 86400 |
    Then 使用者查询设备"fake_device_id1"连接状态为"OFFLINE"
    Then 使用者查询设备"fake_device_id1"子设备列表如下
      | Protocol   | DeviceId             | TypeId        | TypeName        | TypeCode        | Model       | ProdNo       | ParentId        | SubDevNo        |
      | haier-usdk | fake_device_id18-8-1 | fake_type_id1 | fake_type_name1 | fake_type_code1 | fake_model1 | fake_pro_no1 | fake_parent_id1 | fake_device_id2 |
    Then 使用者查询设备"fake_device_id1"基本信息如下
      | Protocol   | DeviceId        | TypeId        | TypeName        | TypeCode        | Model       | ProdNo       | ParentId        | SubDevNo             |
      | haier-usdk | fake_device_id1 | fake_type_id1 | fake_type_name1 | fake_type_code1 | fake_model1 | fake_pro_no1 | fake_parent_id1 | fake_device_id18-8-1 |

  @android_ignore
  Scenario: [1011-2]设备prepare成功后，会立即发送一次当前设备最新状态通知，iOS与Android Notify的事件有差异，iOS只有当连接状态UpDeviceConnection_READY时返回子设备列表
    Given Toolkit的连接设备接口的执行结果为"成功"
    Given Toolkit的获取设备连接状态返回"OFFLINE"
    Given Toolkit的获取设备基础信息返回数据如下
      | Protocol   | DeviceId        | TypeId        | TypeName        | TypeCode        | Model       | ProdNo       | ParentId        | SubDevNo             |
      | haier-usdk | fake_device_id1 | fake_type_id1 | fake_type_name1 | fake_type_code1 | fake_model1 | fake_pro_no1 | fake_parent_id1 | fake_device_id18-8-1 |
    Given Toolkit的获取设备属性列表返回数据如下
      | name  | value |
      | attrA | 1     |
    Given Toolkit的获取设备报警列表返回数据如下
      | name     | value | time  |
      | cautionA | 1     | 86400 |
    Given Toolkit的获取子设备列表返回数据如下
      | Protocol   | DeviceId             | TypeId        | TypeName        | TypeCode        | Model       | ProdNo       | ParentId        | SubDevNo        |
      | haier-usdk | fake_device_id18-8-1 | fake_type_id1 | fake_type_name1 | fake_type_code1 | fake_model1 | fake_pro_no1 | fake_parent_id1 | fake_device_id2 |
    When 订阅者"userA"订阅设备"fake_device_id1"的设备状态
    When 订阅者"userB"订阅设备"fake_device_id1"的设备状态
    When 使用者调用设备"fake_device_id1"的准备接口
    When 等待"1"秒
    Then 使用者收到设备"fake_device_id1"的准备接口的结果为"成功"
    Then Toolkit的获取设备基础信息接口调用"1"次,设备id为"fake_device_id1"
    Then Toolkit的获取设备连接状态接口调用"1"次,设备id为"fake_device_id1"
    Then Toolkit的获取子设备列表接口调用"1"次,设备id为"fake_device_id1"
    Then Toolkit的获取设备属性列表接口调用"1"次,设备id为"fake_device_id1"
    Then Toolkit的获取设备报警列表接口调用"1"次,设备id为"fake_device_id1"
    Then 订阅者"userA"收到设备"fake_device_id1"的事件如下
      | Action                    |
      | EVENT_BASE_INFO_CHANGE    |
      | EVENT_DEVICE_STATE_CHANGE |
      | EVENT_SUB_DEV_LIST_CHANGE |
      | EVENT_ATTRIBUTES_CHANGE   |
      | EVENT_DEVICE_CAUTION      |
      | EVENT_CONNECTION_CHANGE   |
    Then 订阅者"userB"收到设备"fake_device_id1"的事件如下
      | Action                    |
      | EVENT_BASE_INFO_CHANGE    |
      | EVENT_DEVICE_STATE_CHANGE |
      | EVENT_SUB_DEV_LIST_CHANGE |
      | EVENT_ATTRIBUTES_CHANGE   |
      | EVENT_DEVICE_CAUTION      |
      | EVENT_CONNECTION_CHANGE   |
    Then 使用者查询设备"fake_device_id1"属性列表如下
      | name  | value |
      | attrA | 1     |
    Then 使用者查询设备"fake_device_id1"报警列表如下
      | name     | value | time  |
      | cautionA | 1     | 86400 |
    Then 使用者查询设备"fake_device_id1"连接状态为"OFFLINE"
    Then 使用者查询设备"fake_device_id1"子设备列表如下
      | Protocol | DeviceId | TypeId | TypeName | TypeCode | Model | ProdNo | ParentId | SubDevNo |
    Then 使用者查询设备"fake_device_id1"基本信息如下
      | Protocol   | DeviceId        | TypeId        | TypeName        | TypeCode        | Model       | ProdNo       | ParentId        | SubDevNo             |
      | haier-usdk | fake_device_id1 | fake_type_id1 | fake_type_name1 | fake_type_code1 | fake_model1 | fake_pro_no1 | fake_parent_id1 | fake_device_id18-8-1 |
