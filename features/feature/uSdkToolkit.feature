Feature: uSDK Toolkit实现

  功能范围:
  uSDK Toolkit封装了uSDK对外暴露的接口,将uSDK隔离于外部使用者。外部使用者只能通过调用uSDK Toolkit的接口，来调用uSDK提供的接口，并获得回调结果。
  uSDK Toolkit内部调用uSDK的接口有以下三种方式:
  -调用uSDK管理器提供的接口。如连接设备工具接口，断开设备工具接口。
  -调用uSDK设备管理器提供的接口。如连接远程网关接口，断开远程网关接口，刷新uSDK设备列表接口。
  -通过deviceId获得uSDKDevice对象，调用uSDKDevice对象提供的接口。如订阅资源接口，解订阅资源接口等。

  外部依赖：
  - uSDK管理器:uSDKManager
  - uSDK设备管理器:uSDKDeviceManager

  接口说明:
  1.连接设备
  与设备建立连接，连接成功后，会收到该设备的相关消息。
  -调用连接设备接口，若设备已经订阅，接口的回调结果为成功，不会再调用uSDK提供的连接接口。
  -调用连接设备接口，传入的deviceId为空，接口调用失败。
  -调用连接设备接口，传入的deviceId无相应的uSDKDevice对象，接口调用失败。
  2.断开设备连接
  不需要与设备进行交互时，断开设备连接，释放设备资源，一旦与设备断开，uSDK将不再上报该设备的任何属性消息。
  -调用断开设备连接接口，传入的deviceId为空，接口调用失败。
  -调用断开设备连接接口，传入的deviceId无相应的uSDKDevice对象，接口调用失败。
  3.订阅资源
  设备准备就绪时，通过资源名称订阅资源。
  -调用订阅资源接口，传入的deviceId或resourceName为空，接口调用失败。
  -调用订阅资源接口，传入的deviceId无相应的uSDKDevice对象，接口调用失败。
  4.解订阅资源
  与订阅资源对应，在订阅资源完成后，进行解订阅，进行资源释放。
  -调用解订阅资源接口，传入的deviceId或resourceName为空，接口调用失败。
  -调用解订阅资源接口，传入的deviceId无相应的uSDKDevice对象，接口调用失败。
  5.连接远程网关
  通过userId和accessToken连接IOT云，获得远程和智能设备交互的能力。
  -调用连接远程网关接口，传入的accessToken或userId为空，接口调用失败。
  6.断开远程网关
  与连接远程网关对应，在用户账号注销时，需要断开IOT云连接。
  7.执行指令
  与设备建立交互后，通过发送单命令或者组命令进行设备的控制。
  -调用执行命令接口，传入的deviceId或命令参数为空，接口调用失败。
  -调用执行命令接口，传入的deviceId无相应的uSDKDevice对象，接口调用失败。
  -调用执行命令接口，传入的命令参数中，组命令名称和下发命令期望列表都为空，接口调用失败。
  8.获取蓝牙历史数据
  获取蓝牙历史数据, 获取成功后，uSDK将上报蓝牙数据。
  -调用获取蓝牙历史数据接口，传入的deviceId为空，接口调用失败。
  -调用获取蓝牙历史数据接口，传入的deviceId无相应的uSDKDevice对象，接口调用失败。
  9.取消获取蓝牙历史数据
  取消获取蓝牙历史数据，取消后，uSDK将不再上报蓝牙数据。
  -调用取消获取蓝牙历史数据接口，传入的deviceId为空，接口调用失败。
  -调用取消获取蓝牙历史数据接口，传入的deviceId无相应的uSDKDevice对象，接口调用失败。
  10.获取设备是否绑定
  -调用获取设备是否绑定接口，传入的deviceId为空，接口调用失败。
  -调用获取设备是否绑定接口，传入的deviceId无相应的uSDKDevice对象，接口调用失败。
  11.获取属性列表
  获取设备所有的属性信息，包含属性名和属性值。
  -调用获取属性列表接口，传入的deviceId为空，接口调用失败。
  -调用获取属性列表接口，传入的deviceId无相应的uSDKDevice对象，接口调用失败。
  12.获取设备告警
  获取设备告警列表，包含告警名，告警值，告警的时间戳。
  -调用获取设备告警接口，传入的deviceId为空，接口调用失败。
  -调用获取设备告警接口，传入的deviceId无相应的uSDKDevice对象，接口调用失败。
  13.获取设备连接状态
  获取设备的连接状态，有五种状态类型。
  -调用获取设备连接状态接口，传入的deviceId为空，接口调用失败。
  -调用获取设备连接状态接口，传入的deviceId无相应的uSDKDevice对象，接口调用失败。
  14.获取设备信息
  获取设备基本信息，包含设备id，设备类型，设备型号，设备产品编码等信息。
  -调用获取设备信息接口，传入的deviceId为空，接口调用失败。
  -调用获取设备信息接口，传入的deviceId无相应的uSDKDevice对象，接口调用失败。
  15.获取设备列表
  获取uSDK所管理的所有智能设备。
  -调用获取设备列表接口，从uSDK获取的设备列表为空，接口调用失败。
  16.刷新uSDK设备列表
  刷新已绑定的设备列表，云平台收到请求后，会下发最新的绑定设备列表。
  17.获取网络质量
  获取网络质量信息，包含设备的链接方式，设备所连接的路由器名称，路由器信息强度，路由器内网IP，路由器网络信号质量等级。
  -调用获取网络质量接口，传入的deviceId为空，接口调用失败。
  -调用获取网络质量接口，传入的deviceId无相应的uSDKDevice对象，接口调用失败。
  18.获取Fota状态
  获取Fota升级状态和升级失败的错误信息。
  -调用获取Fota状态接口，传入的deviceId为空，接口调用失败。
  -调用获取Fota状态接口，传入的deviceId无相应的uSDKDevice对象，接口调用失败。
  19.开始Fota升级
  开始设备底板固件的升级。
  -调用开始Fota升级接口，传入的deviceId为空，接口调用失败。
  -调用开始Fota升级接口，传入的deviceId无相应的uSDKDevice对象，接口调用失败。
  20.获取Fota信息
  获取Fota信息，包含Fota的当前版本，最新版本，最新版本说明和是否需要Fota升级。
  -调用获取Fota信息接口，传入的deviceId为空，接口调用失败。
  -调用获取Fota信息接口，传入的deviceId无相应的uSDKDevice对象，接口调用失败。
  21.判断是否OTA升级
  在执行OTA升级之前，先获取是否升级，是的话再进行升级。
  -调用判断是否OTA升级接口，传入的deviceId为空，接口调用失败。
  -调用判断是否OTA升级接口，传入的deviceId无相应的uSDKDevice对象，接口调用失败。
  22.执行OTA升级
  在执行过程中，会通过回调将升级状态和升级进度给出来。
  -调用执行OTA升级接口，传入的deviceId为空，接口调用失败。
  -调用执行OTA升级接口，传入的deviceId无相应的uSDKDevice对象，接口调用失败。
  23.获取子设备列表
  获取主机下的子设备列表，包含各个子设备信息。
  -调用获取子设备列表接口，传入的deviceId为空，接口调用失败。
  -调用获取子设备列表接口，传入的deviceId无相应的uSDKDevice对象，接口调用失败。
  -调用获取子设备列表接口，获取uSDKDevice对象的子设备列表为空，接口调用失败。
  24.InFocus
  标记设备进入焦点
  -调用InFocus接口，传入的deviceId为空，接口调用失败。
  -调用InFocus接口，传入的deviceId无相应的uSDKDevice对象，接口调用失败。
  25.OutFocus
  标记设备退出焦点
  -调用OutFocus接口，传入的deviceId为空，接口调用失败。
  -调用OutFocus接口，传入的deviceId无相应的uSDKDevice对象，接口调用失败。
  26.连接设备工具
  启动uSDK,设置uSDK消息监听
  -调用连接设备工具接口，传入的appID，appKey，secretKey参数，任何一个参数为空，接口调用失败。
  27.断开设备工具
  -停止uSDK,取消uSDK消息监听
  28.通过属性名称获取属性值
  -deviceId和属性名称都不为空，可获取设备的属性值成功，否则获取失败。
  29.获取设备绑定信息
  -deviceId和accessToken都不为空，获取设备的绑定信息成功，否则获取失败。
  30.获取子设备列表
  -deviceId不为空，获取设备的子设备列表成功，否则获取失败。
  31.获取设备模块使用的配置文件版本号
  -通过deviceId获取设备的配置文件版本号，deviceId不为空时，获取成功，否则获取失败。
  32.获取设备连接网络类型
  通过deviceId获取设备的网络类型，返回设备连接网络类型。
  33.订阅资源
  设备准备就绪时，通过资源名称订阅资源。
  -调用订阅资源新接口，传入的deviceId或resourceName为空，接口调用失败。
  -调用订阅资源新接口，传入的deviceId无相应的uSDKDevice对象，接口调用失败。
  34.创建分组
  创建分组，返回组设备对象
  -调用创建分组接口，传入的deviceId为空，接口调用失败。
  -调用创建分组接口，传入的deviceId无相应的uSDKDevice对象，接口调用失败。
  35.删除组设备
  -调用删除组设备接口，传入的deviceId为空，接口调用失败。
  -调用删除组设备接口，传入的deviceId无相应的uSDKDevice对象，接口调用失败。
  36.获取组设备列表
  -调用获取组设备列表接口，传入的deviceId为空，接口调用失败。
  -调用获取组设备列表接口，传入的deviceId无相应的uSDKDevice对象，接口调用失败。
  37.获取组设备成员列表
  -调用获取组设备成员列表接口，传入的deviceId为空，接口调用失败。
  -调用获取组设备成员列表接口，传入的deviceId无相应的uSDKDevice对象，接口调用失败。
  38.是否组设备
  -调用是否组设备接口，传入的deviceId为空，接口调用失败。
  -调用是否组设备接口，传入的deviceId无相应的uSDKDevice对象，接口调用失败。
  39.向组设备中添加设备
  -调用向组设备中添加设备接口，传入的deviceId或deviceIds为空，接口调用失败。
  -调用向组设备中添加设备接口，传入的deviceId无相应的uSDKDevice对象，接口调用失败。
  40.从组设备中移除设备
  -调用从组设备中移除设备接口，传入的deviceId或deviceIds为空，接口调用失败。
  -调用从组设备中移除设备接口，传入的deviceId无相应的uSDKDevice对象，接口调用失败。
  41.获取蓝牙连接状态
  -调用获取蓝牙连接状态接口，传入的deviceId为空或者无相应的uSDKDevice对象，接口调用失败，否则接口调用成功
  -用户连接设备成功，监听设备蓝牙连接状态变化，当设备蓝牙连接状态发生变化，能收到变化通知。


  Background:
    Given 初始化WifiDeviceToolkit库,uSDK管理器为"模拟的",uSDK设备管理器为"模拟的"
    Given uSDK设备管理器,调用getDeviceWithID方法,参数deviceID为"fakeID",返回"uSDKDevice"对象

  Scenario Outline:[9000]调用订阅资源接口，传入参数deviceId和resourceName，当参数为空或uSDK中无对应的uSDKDevice对象时，资源订阅接口的回调结果为失败，否则资源订阅接口的回调结果为调用的uSDKDevice对象的接口回调结果。
    Given deviceID为"fakeID"的uSDKDevice对象的subscribeResource接口的调用结果为"<usdkResult>"
    When 调用订阅资源接口,参数deviceId为"<deviceId>", 资源名称为"<resourceName>"
    Then 订阅资源方法回调结果为"<result>"
    Then deviceID为"fakeID"的uSDKDevice对象的subscribeResource接口被调用"<callTimes>"次,参数为"<param>"
    Examples:
      | deviceId | resourceName | result  | callTimes | usdkResult | param     |
      | 空对象   | 空对象       | Fail    | 0         | Success    |           |
      | 空对象   | 空字符串     | Fail    | 0         | Success    |           |
      | 空字符串 | 空对象       | Fail    | 0         | Success    |           |
      | fakeID   | fakeName     | Success | 1         | Success    | fakeName  |
      | fakeID   | fakeName1    | Fail    | 1         | Fail       | fakeName1 |
      | fakeID1  | fakeName     | Fail    | 0         | Success    |           |
      | fakeID   | fakeName     | Fail    | 1         | Fail       | fakeName  |

  Scenario Outline:[9001]调用解订阅资源接口，传入参数deviceId和resourceName，当参数为空或uSDK中无对应的uSDKDevice对象时，解订阅资源接口的回调结果为失败，否则解订阅资源接口的回调结果为调用的uSDKDevice对象的接口回调结果。
    Given deviceID为"fakeID"的uSDKDevice对象的unSubscribeResource接口的调用结果为"<usdkResult>"
    When 调用解订阅资源接口,参数deviceId为"<deviceId>", 资源名称为"<resourceName>"
    Then 解订阅资源方法回调结果为"<result>"
    Then deviceID为"fakeID"的uSDKDevice对象的unSubscribeResource接口被调用"<callTimes>"次,参数为"<param>"
    Examples:
      | deviceId | resourceName | result  | callTimes | usdkResult | param     |
      | 空对象   | 空对象       | Fail    | 0         | Success    |           |
      | 空对象   | 空字符串     | Fail    | 0         | Success    |           |
      | 空字符串 | 空对象       | Fail    | 0         | Success    |           |
      | fakeID   | fakeName     | Success | 1         | Success    | fakeName  |
      | fakeID   | fakeName1    | Fail    | 1         | Fail       | fakeName1 |
      | fakeID1  | fakeName     | Fail    | 0         | Success    |           |
      | fakeID   | fakeName     | Fail    | 1         | Fail       | fakeName  |

  Scenario Outline:[9002]调用执行指令接口，传入参数deviceId和command，当参数为空时，执行指令接口的回调结果为失败，否则执行指令接口的回调结果为调用的uSDKDevice对象的接口回调结果。
    Given deviceID为"fakeID"的uSDKDevice对象的writeAttributeWithName接口的结果为"<singleCmdResult>"
    Given deviceID为"fakeID"的uSDKDevice对象的executeOperation接口的结果为"<groupCmdResult>"
    When 调用执行指令接口,参数deviceId为"<deviceId>",command的组命令名为"<groupName>",下发命令为"<cmdMap>"
    Then 执行指令接口的结果为"<result>"
    Then deviceID为"fakeID"的uSDKDevice对象的writeAttributeWithName接口被调用"<callSingleTimes>"次,参数为:
      | attrName   | attrValue   |
      | <attrName> | <attrValue> |
    Then deviceID为"fakeID"的uSDKDevice对象的executeOperation接口被调用"<callGroupTimes>"次,参数为:
      | groupName       | argumentList   |
      | <uSDKGroupName> | <argumentList> |
    Examples:
      | deviceId | groupName | cmdMap          | result  | callSingleTimes | callGroupTimes | singleCmdResult | groupCmdResult | attrName | attrValue | uSDKGroupName | argumentList    |
      | 空对象   | 空对象    | 空对象          | Fail    | 0               | 0              | Success         | Success        |          |           |               |                 |
      | 空对象   | 空对象    | attrA,1         | Fail    | 0               | 0              | Success         | Success        |          |           |               |                 |
      | fakeID   | 空对象    | 空对象          | Fail    | 0               | 0              | Fail            | Fail           |          |           |               |                 |
      | fakeID   | 空对象    | attrA,1         | Success | 1               | 0              | Success         | Success        | attrA    | 1         |               |                 |
      | fakeID   | G1        | attrA,1;attrB,1 | Success | 0               | 1              | Success         | Success        |          |           | G1            | attrA,1;attrB,1 |
      | fakeID   | 空对象    | attrA,1         | Fail    | 1               | 0              | Fail            | Success        | attrA    | 1         |               |                 |
      | fakeID   | G1        | attrA,1;attrB,1 | Fail    | 0               | 1              | Success         | Fail           |          |           | G1            | attrA,1;attrB,1 |

  Scenario Outline:[9003]调用连接远程网关接口，传入accessToken或者userId参数，任一参数为空时，连接远程网关接口的回调结果为失败，参数都不为空时，连接远程网关接口的回调结果为uSDK设备管理器的接口回调结果。
    Given uSDK设备管理器的setUserInfo接口的结果为"<usdkResult>"
    When 调用连接远程网关接口,参数为"<param>"
    Then 连接远程网关接口的回调结果为"<result>"
    Then uSDK设备管理器的setUserInfo接口被调用"<callTimes>"次,参数为:
      | userToken   | userID   |
      | <userToken> | <userID> |
    Examples:
      | param                                                     | result  | callTimes | usdkResult | userToken | userID     |
      |                                                           | Fail    | 0         | Success    |           |            |
      | usdk-access-token,fakeToken                               | Fail    | 0         | Success    |           |            |
      | usdk-access-userId,fakeUserId                             | Fail    | 0         | Success    |           |            |
      | usdk-access-token,fakeToken;usdk-access-userId,fakeUserId | Success | 1         | Success    | fakeToken | fakeUserId |
      | usdk-access-token,fakeToken;usdk-access-userId,fakeUserId | Fail    | 1         | Fail       | fakeToken | fakeUserId |

  Scenario Outline:[9004]调用断开远程网关接口，当uSDK执行断开远程网关的结果为成功时，回调结果为成功，否则回调结果为失败。
    Given uSDK设备管理器的setUserInfo接口的结果为"<usdkResult>"
    When 调用断开远程网关接口
    Then 断开远程网关接口回调结果为"<result>"
    Then uSDK设备管理器的setUserInfo接口被调用"1"次,参数为:
      | userToken | userID |
    Examples:
      | result  | usdkResult |
      | Fail    | Fail       |
      | Success | Success    |

  Scenario Outline:[9005]调用刷新uSDK设备列表接口，当uSDK执行刷新设备列表的结果为成功时，回调结果为成功，否则回调结果为失败。
    Given uSDK设备管理器的refreshDeviceListWithSuccess接口的结果为"<usdkResult>"
    When 调用刷新设备列表接口
    Then 刷新设备列表接口回调结果为"<result>"
    Then uSDK设备管理器的refreshDeviceListWithSuccess接口被调用"1"次
    Examples:
      | result  | usdkResult |
      | Fail    | Fail       |
      | Success | Success    |

  Scenario Outline:[9006]调用获取是否绑定接口，传入deviceId，当deviceId为空，获取是否绑定接口回调结果为失败，否则回调结果为成功。
    Given 调用uSDK设备管理器的getDeviceWithID接口,传入参数为"fakeID",返回uSDKDevice对象的isBound的值为"<uSDKIsBound>"
    When 调用获取是否绑定接口,参数deviceId为"<deviceId>"
    Then 获取是否绑定接口的结果为"<result>",回调结果中的extraData值为"<uSDKIsBound>"
    Examples:
      | deviceId | result | uSDKIsBound |
      | 空对象   | false  |             |
      | fakeID   | true   | true        |
      | fakeID   | false  | false       |

  Scenario Outline:[9007]调用传参为deviceId的uSDK Toolkit接口时，传入deviceId为空时，接口回调结果为失败，否则回调结果为调用的uSDKDevice对象的接口回调结果。
    Given deviceID为"fakeID"的uSDKDevice对象的"<usdkFunction>"接口的结果为"<usdkResult>"
    When 调用"<toolkitFunction>"接口,参数deviceId为"<deviceId>"
    Then "<toolkitFunction>"接口回调结果为"<result>"
    Then deviceID为"fakeID"的uSDKDevice对象的"<usdkFunction>"接口被调用"<callTimes>"次
    Examples:
      | deviceId | result  | callTimes | usdkFunction                     | toolkitFunction      | usdkResult |
      | 空对象   | Fail    | 0         | disconnectWithSuccess            | 断开设备             | Success    |
      | fakeID   | Success | 1         | disconnectWithSuccess            | 断开设备             | Success    |
      | fakeID   | Fail    | 1         | disconnectWithSuccess            | 断开设备             | Fail       |
      | fakeID1  | Success | 0         | disconnectWithSuccess            | 断开设备             | Success    |
      | 空对象   | Fail    | 0         | startBoardFOTAWithSuccess        | 开始Fota升级         | Success    |
      | fakeID   | Success | 1         | startBoardFOTAWithSuccess        | 开始Fota升级         | Success    |
      | fakeID   | Fail    | 1         | startBoardFOTAWithSuccess        | 开始Fota升级         | Fail       |
      | 空对象   | Fail    | 0         | fetchBLEHistoryDataSuccess       | 获取蓝牙历史数据     | Success    |
      | fakeID   | Success | 1         | fetchBLEHistoryDataSuccess       | 获取蓝牙历史数据     | Success    |
      | fakeID   | Fail    | 1         | fetchBLEHistoryDataSuccess       | 获取蓝牙历史数据     | Fail       |
      | 空对象   | Fail    | 0         | cancelFetchBLEHistoryDataSuccess | 取消获取蓝牙历史数据 | Success    |
      | fakeID   | Success | 1         | cancelFetchBLEHistoryDataSuccess | 取消获取蓝牙历史数据 | Success    |
      | fakeID   | Fail    | 1         | cancelFetchBLEHistoryDataSuccess | 取消获取蓝牙历史数据 | Fail       |

  Scenario Outline:[9008]调用判断是否OTA升级接口，传入deviceId为空时，接口回调结果为失败，否则回调结果为成功。
    Given deviceID为"fakeID"的uSDKDevice对象的isModuleNeedOTA,值为"<isNeed>"
    When 调用判断是否OTA升级接口,参数deviceId为"<deviceId>"
    Then 判断是否OTA升级接口的回调结果为"<result>",回调结果中的extraData值为"<isNeed>"
    Examples:
      | deviceId | result  | isNeed |
      | 空对象   | Fail    | 0      |
      | fakeID   | Success | 1      |

  #android中判断area和feature两个参数，不判断appID，appKey，secretKey参数
  @android_ignore
  Scenario Outline:[9009]调用连接设备工具接口，未与uSDK建立连接时,uSDK Toolkit的appID,appKey,secretKey属性,任一属性为空时,连接设备工具接口的回调结果为失败，否则连接设备工具接口的回调结果为uSDK管理器的接口回调结果。
    Given uSDK管理器的startSDKWithOptions接口的结果为"<usdkResult>"
    Given uSDK Toolkit的appID值为"<appID>",appKey值为"<appKey>",secretKey值为"<secretKey>"
    Given uSDK Toolkit与uSDK建立连接的结果是"<attachedResult>"
    When 调用连接设备工具接口
    Then 连接设备工具接口的回调结果为"<result>"
    Then uSDK管理器的startSDKWithOptions接口被调用"<callTimes>"次,参数为:
      | paraAppID   | paraAppKey   | paraSecretKey   |
      | <paraAppID> | <paraAppKey> | <paraSecretKey> |
    Examples:
      | appID     | appKey     | secretKey     | attachedResult | result  | callTimes | usdkResult | paraAppID | paraAppKey | paraSecretKey |
      | 空对象    | 空对象     | 空对象        | NO             | Fail    | 0         | Success    |           |            |               |
      | 空对象    | fakeAppKey | 空对象        | NO             | Fail    | 0         | Success    |           |            |               |
      | 空对象    | 空对象     | fakeSecretKey | NO             | Fail    | 0         | Success    |           |            |               |
      | fakeIDKey | 空对象     | 空对象        | NO             | Fail    | 0         | Success    |           |            |               |
      | fakeIDKey | fakeAppKey | fakeSecretKey | NO             | Success | 1         | Success    | fakeIDKey | fakeAppKey | fakeSecretKey |
      | 空对象    | 空对象     | 空对象        | YES            | Success | 0         | Success    |           |            |               |
      | fakeIDKey | fakeAppKey | fakeSecretKey | NO             | Fail    | 1         | Fail       | fakeIDKey | fakeAppKey | fakeSecretKey |

  @ios_ignore
  Scenario Outline:[9009-1]调用连接设备工具接口，连接uSDK成功，则连接设备工具成功，否则连接失败。
    Given uSDK管理器的startSDKWithOptions接口的结果为"<usdkResult>"
    When 调用连接设备工具接口
    Then 连接设备工具接口的回调结果为"<result>"
    Then uSDK管理器的startSDKWithOptions接口被调用"1"次
    Examples:
      | usdkResult | result  |
      | Fail       | Fail    |
      | Success    | Success |

  @ios_ignore
  Scenario:[9009-2]调用连接设备工具接口，连接uSDK成功后再次调用连接设备工具，第2次调用不会再执行uSDK的连接设备工具接口。
    Given uSDK管理器的startSDKWithOptions接口的结果为"Success"
    When 调用连接设备工具接口
    Then 连接设备工具接口的回调结果为"Success"
    When 等待"1"秒
    When 调用连接设备工具接口
    Then 连接设备工具接口的回调结果为"Success"
    Then uSDK管理器的startSDKWithOptions接口被调用"1"次

  @android_ignore
  Scenario Outline:[9010]先连接设备工具成功后，调用断开设备工具接口，当执行停止uSDK的结果为成功时，回调结果为成功，否则回调结果为失败。
    Given uSDK管理器的startSDKWithOptions接口的结果为"Success"
    Given uSDK管理器的stopSDK接口的结果为"<usdkResult>"
    Given uSDK Toolkit的appID值为"fakeIDKey",appKey值为"fakeAppKey",secretKey值为"fakeSecretKey"
    When 调用连接设备工具接口
    When 等待"1"秒
    When 调用断开设备工具接口
    Then 断开设备工具接口的回调结果为"<result>"
    Then uSDK管理器的stopSDK接口被调用"1"次
    Examples:
      | result  | usdkResult |
      | Fail    | Fail       |
      | Success | Success    |

  @ios_ignore
  Scenario Outline:[9010-1]先连接设备工具成功后，调用断开设备工具接口，当停止uSDK的结果为成功时，回调结果为成功，否则回调结果为失败。
    Given uSDK管理器的startSDKWithOptions接口的结果为"Success"
    Given uSDK管理器的stopSDK接口的结果为"<usdkResult>"
    When 调用连接设备工具接口
    When 等待"1"秒
    When 调用断开设备工具接口
    Then 断开设备工具接口的回调结果为"<result>"
    Then uSDK管理器的stopSDK接口被调用"1"次
    Examples:
      | result  | usdkResult |
      | Fail    | Fail       |
      | Success | Success    |

  @android_ignore
  Scenario:[9010-2]先连接设备工具成功后，调用2次断开设备工具接口，第一次断开设备工具成功后，第二次断开不会调用停止uSDK的接口。
    Given uSDK管理器的startSDKWithOptions接口的结果为"Success"
    Given uSDK管理器的stopSDK接口的结果为"Success"
    Given uSDK Toolkit的appID值为"fakeIDKey",appKey值为"fakeAppKey",secretKey值为"fakeSecretKey"
    When 调用连接设备工具接口
    When 等待"1"秒
    When 调用断开设备工具接口
    Then 断开设备工具接口的回调结果为"Success"
    When 等待"1"秒
    When 调用断开设备工具接口
    Then 断开设备工具接口的回调结果为"Success"
    Then uSDK管理器的stopSDK接口被调用"1"次

  @ios_ignore
  Scenario:[9010-3]先连接设备工具成功后，调用2次断开设备工具接口，第一次断开设备工具成功后，第二次断开不会调用停止uSDK的接口。
    Given uSDK管理器的startSDKWithOptions接口的结果为"Success"
    Given uSDK管理器的stopSDK接口的结果为"Success"
    When 调用连接设备工具接口
    When 等待"1"秒
    When 调用断开设备工具接口
    Then 断开设备工具接口的回调结果为"Success"
    When 等待"1"秒
    When 调用断开设备工具接口
    Then 断开设备工具接口的回调结果为"Success"
    Then uSDK管理器的stopSDK接口被调用"1"次

  #android中没有对已订阅的设备不做重复订阅的判断，不需实现uSDK中deviceId为"fakeID"的订阅设备结果为"<isSubscribed>"
  @android_ignore
  Scenario Outline:[9011]调用连接设备接口，未订阅设备,传入参数deviceId，当deviceId为空或uSDK中无对应的uSDKDevice对象时，连接设备接口的回调结果为失败，否则连接设备接口的回调结果为调用的uSDKDevice对象的接口回调结果。
    Given 初始化设备管理器
    Given deviceID为"fakeID"的uSDKDevice对象的connectNeedPropertiesWithSuccess接口的结果为"<usdkResult>"
    Given uSDK中deviceId为"fakeID"的订阅设备结果为"<isSubscribed>"
    When 调用连接设备接口,参数deviceId为"<deviceId>"
    Then 连接设备接口的回调结果为"<result>"
    Then deviceID为"fakeID"的uSDKDevice对象的connectNeedPropertiesWithSuccess接口被调用"<callTimes>"次
    Examples:
      | deviceId | isSubscribed | result  | callTimes | usdkResult |
      | 空对象   | YES          | Fail    | 0         | Success    |
      | fakeID   | NO           | Success | 1         | Success    |
      | fakeID1  | YES          | Fail    | 0         | Success    |
      | fakeID   | YES          | Success | 0         | Fail       |
      | fakeID   | NO           | Fail    | 1         | Fail       |

  @ios_ignore
  Scenario Outline:[9011-1]调用连接设备接口，当deviceId为空或连接uSDK设备接口返回失败，最终连接设备接口失败。
    Given deviceID为"fakeID"的uSDKDevice对象的connectNeedPropertiesWithSuccess接口的结果为"<usdkResult>"
    When 调用连接设备接口,参数deviceId为"<deviceId>"
    Then 连接设备接口的回调结果为"<result>"
    Then deviceID为"fakeID"的uSDKDevice对象的connectNeedPropertiesWithSuccess接口被调用"<callTimes>"次
    Examples:
      | deviceId | result | callTimes | usdkResult |
      | null     | Fail   | 0         | Success    |
      | fakeID   | Fail   | 1         | Fail       |

  @ios_ignore
  Scenario: [9011-2]调用连接设备接口，deviceId不为空，连接设备成功。
    Given deviceID为"fakeID"的uSDKDevice对象的connectNeedPropertiesWithSuccess接口的结果为"Success"
    When 调用连接设备接口,参数deviceId为"fakeID"
    Then 连接设备接口的回调结果为"Success"
    Then deviceID为"fakeID"的uSDKDevice对象的connectNeedPropertiesWithSuccess接口被调用"1"次

  Scenario:[9012]调用获取设备列表接口，当uSDK设备管理器的获取设备列表接口返回空时，回调结果为失败。
    Given uSDK设备管理器的getDeviceList接口返回的设备列表如下:
      | deviceID | type | uplusID |
    When 调用获取设备列表接口
    Then 获取设备列表接口的回调结果为"Fail"
    Then uSDK设备管理器的getDeviceList接口被调用"1"次

  Scenario:[9012-1]调用获取设备列表接口，当uSDK设备管理器的获取设备列表接口中有部分设备对象为空，返回不为空的设备对象列表。
    Given uSDK设备管理器的getDeviceList接口返回的设备列表如下:
      | deviceID  | type | uplusID |
      | deviceID1 | 0    | 010203  |
      | null      | null | null    |
      | deviceID2 | 0    | 010204  |
    When 调用获取设备列表接口
    Then 获取设备列表接口的回调结果为"Success",回调结果中的extraData值为:
      | deviceId  | typeName | typeId |
      | deviceID1 | ALL_TYPE | 010203 |
      | deviceID2 | ALL_TYPE | 010204 |
    Then uSDK设备管理器的getDeviceList接口被调用"1"次

  Scenario:[9013]调用获取设备列表接口，回调结果为uSDK中的设备列表。
    Given uSDK设备管理器的getDeviceList接口返回的设备列表如下:
      | deviceID  | type | uplusID |
      | deviceID1 | 0    | 010203  |
      | deviceID2 | 0    | 010204  |
      | deviceID3 | 0    | 010205  |
    When 调用获取设备列表接口
    Then 获取设备列表接口的回调结果为"Success",回调结果中的extraData值为:
      | deviceId  | typeName | typeId |
      | deviceID1 | ALL_TYPE | 010203 |
      | deviceID2 | ALL_TYPE | 010204 |
      | deviceID3 | ALL_TYPE | 010205 |
    Then uSDK设备管理器的getDeviceList接口被调用"1"次

  Scenario Outline:[9014]调用获取当前设备的所有同级子设备列表接口，传入参数deviceId，当deviceId为空或uSDK中无对应的uSDKDevice对象时，获取子设备列表接口的回调结果为失败，否则获取子设备列表接口的回调结果为uSDKDevice对象获取子设备列表的回调结果。
    Given deviceID为"fakeID"的uSDKDevice对象的parentDevice接口的结果为:
      | parentDevice   |
      | <parentDevice> |
    Given deviceID为"fakeID"的父设备对象的getSubDeviceList接口的结果为:
      | deviceID  | type | uplusID |
      | deviceID1 | 0    | 010203  |
      | deviceID2 | 0    | 010204  |
      | deviceID3 | 0    | 010205  |
    When 调用获取当前设备的同级子设备列表接口,参数deviceId为"<deviceId>"
    Then 获取子设备列表接口的回调结果为"<result>",回调结果的extraData值为:
      | deviceId  | typeName | typeId |
      | deviceID1 | ALL_TYPE | 010203 |
      | deviceID2 | ALL_TYPE | 010204 |
      | deviceID3 | ALL_TYPE | 010205 |
    Then deviceID为"fakeID"的uSDKDevice对象的parentDevice接口被调用"<callParentDeviceTime>"次
    Then deviceID为"fakeID"的父设备对象的getSubDeviceList接口被调用"<callSubDeviceListTime>"次
    Examples:
      | deviceId | parentDevice      | result  | callParentDeviceTime | callSubDeviceListTime |
      | 空对象   | deviceID,deviceID | Fail    | 0                    | 0                     |
      | fakeID   | deviceID,deviceID | Success | 1                    | 1                     |
      | fakeID   |                   | Fail    | 1                    | 0                     |

  Scenario:[9015]调用获取当前设备的所有同级子设备列表接口，uSDK返回的子设备列表为空，回调结果为失败。
    Given deviceID为"fakeID"的uSDKDevice对象的parentDevice接口的结果为:
      | parentDevice      |
      | deviceID,deviceID |
    Given deviceID为"fakeID"的父设备对象的getSubDeviceList接口的结果为:
      | deviceID | type | uplusID |
    When 调用获取当前设备的同级子设备列表接口,参数deviceId为"fakeID"
    Then 获取子设备列表接口的回调结果为"Fail"
    Then deviceID为"fakeID"的uSDKDevice对象的parentDevice接口被调用"1"次
    Then deviceID为"fakeID"的父设备对象的getSubDeviceList接口被调用"1"次

  Scenario Outline:[9016]调用执行OTA升级接口，传入deviceId为空时，接口回调结果为失败，否则回调结果为调用的uSDKDevice对象的接口回调结果。
    Given deviceID为"fakeID"的uSDKDevice对象的moduleOTAWithProgress接口的结果为"<usdkResult>"
    When 调用执行OTA升级接口,参数deviceId为"<deviceId>"
    Then 执行OTA升级接口的回调结果为"<result>"
    Then deviceID为"fakeID"的uSDKDevice对象的moduleOTAWithProgress接口被调用"<callTime>"次
    Examples:
      | deviceId | usdkResult | result  | callTime |
      | 空对象   | Success    | Fail    | 0        |
      | fakeID   | Success    | Success | 1        |
      | fakeID   | Fail       | Fail    | 1        |

  @ios_ignore
  Scenario Outline:[9016-1]调用执行OTA升级接口，接口回调结果为失败。
    Given deviceID为"fakeID"的uSDKDevice对象的moduleOTA接口的结果为"Fail",progressCallBack的结果为"<progressResult>",onCallback的结果为"<callBackResult>"
    When 调用执行OTA升级接口,参数deviceId为"<deviceId>"
    Then 执行OTA升级接口的回调结果为"<result>"
    Then deviceID为"fakeID"的uSDKDevice对象的moduleOTAWithProgress接口被调用"<callTime>"次
    Examples:
      | deviceId | progressResult | callBackResult | result | callTime |
      | fakeID   | Fail           | Success        | Fail   | 1        |
      | fakeID   | Success        | Fail           | Fail   | 1        |

  Scenario Outline:[9017]调用获取网络质量接口，传入deviceId为空时，接口回调结果为失败，否则回调结果为调用的uSDKDevice对象的接口回调结果。
    Given deviceID为"fakeID"的uSDKDevice对象的getNetworkQualityV2Success接口的结果为"<usdkResult>",回调结果中的uSDKNetworkQualityInfoV2对象的值为:
      | connectStatus | machineId | isOnLine | statusLastChangeTime | netType | ssid | rssi | prssi | signalLevel | ilostRatio | its | lanIP | moduleVersion |
      | 0             | mId       | true     | 0                    | wifi    | sid  | 0    | 0     | 0           | 0          | 0   | lIP   | mVersion      |
    When 调用获取网络质量接口,参数deviceId为"<deviceId>"
    Then 获取网络质量接口的回调结果为"<result>",回调结果中的DeviceNetWorkQualityInfo对象的值为:
      | connectStatus | machineId | isOnLine | statusLastChangeTime | netType | ssid | rssi | prssi | signalLevel | ilostRatio | its | lanIP | moduleVersion |
      | 0             | mId       | true     | 0                    | wifi    | sid  | 0    | 0     | 0           | 0          | 0   | lIP   | mVersion      |
    Then deviceID为"fakeID"的uSDKDevice对象的getNetworkQualityV2Success接口被调用"<callTimes>"次
    Examples:
      | deviceId | usdkResult | result  | callTimes |
      | 空对象   | Success    | Fail    | 0         |
      | fakeID   | Fail       | Fail    | 1         |
      | fakeID   | Success    | Success | 1         |

  Scenario: [9017-1]调用获取网络质量接口，uSDK获取网络质量接口回调的网络质量数据为空，获取网络质量数据失败。
    Given deviceID为"fakeID"的uSDKDevice对象的getNetworkQualityV2Success接口的结果为"Success",回调结果中的uSDKNetworkQualityInfoV2对象的值为:
      | connectStatus | machineId | isOnLine | statusLastChangeTime | netType | ssid | rssi | prssi | signalLevel | ilostRatio | its | lanIP | moduleVersion |
    When 调用获取网络质量接口,参数deviceId为"fakeID"
    Then 获取网络质量接口的回调结果为"Fail",回调结果中的DeviceNetWorkQualityInfo对象的值为:
      | connectStatus | machineId | isOnLine | statusLastChangeTime | netType | ssid | rssi | prssi | signalLevel | ilostRatio | its | lanIP | moduleVersion |
    Then deviceID为"fakeID"的uSDKDevice对象的getNetworkQualityV2Success接口被调用"1"次

  Scenario Outline:[9018]调用获取Fota状态接口，传入deviceId为空时，接口回调结果为失败，否则回调结果为调用的uSDKDevice对象的接口回调结果。
    Given deviceID为"fakeID"的uSDKDevice对象的fetchBoardFOTAStatusWithSuccess接口的结果为"<usdkResult>",回调结果中的uSDKFOTAStatusInfo对象的值为:
      | upgradeStatus | upgradeErrorInfo |
      | 0             | info             |
    When 调用获取Fota状态接口,参数deviceId为"<deviceId>"
    Then 获取Fota状态接口的回调结果为"<result>",回调结果中的DeviceFOTAStatusInfo对象的值为:
      | upgradeStatus | upgradeErrInfo |
      | 0             | info           |
    Then deviceID为"fakeID"的uSDKDevice对象的fetchBoardFOTAStatusWithSuccess接口被调用"<callTimes>"次
    Examples:
      | deviceId | usdkResult | result  | callTimes |
      | 空对象   | Success    | Fail    | 0         |
      | fakeID   | Fail       | Fail    | 1         |
      | fakeID   | Success    | Success | 1         |

  Scenario Outline:[9019]调用获取Fota信息接口，传入deviceId为空时，接口回调结果为失败，否则回调结果为调用的uSDKDevice对象的接口回调结果。
    Given deviceID为"fakeID"的uSDKDevice对象的checkBoardFOTAInfoWithSuccess接口的结果为"<usdkResult>",回调结果中的uSDKFOTAInfo对象的值为:
      | isNeedFOTA | currentVersion | newestVersion | newestVersionDescription | model | timeoutInterval |
      | false      | 1.0            | 2.0           | 2.0                      | m1    | 0               |
    When 调用获取Fota信息接口,参数deviceId为"<deviceId>"
    Then 获取Fota信息接口的回调结果为"<result>",回调结果中的DeviceFOTAInfo对象的值为:
      | isNeedFOTA | currentVersion | newestVersion | newestVersionDescription | model | timeoutInterval |
      | false      | 1.0            | 2.0           | 2.0                      | m1    | 0               |
    Then deviceID为"fakeID"的uSDKDevice对象的checkBoardFOTAInfoWithSuccess接口被调用"<callTimes>"次
    Examples:
      | deviceId | usdkResult | result  | callTimes |
      | 空对象   | Success    | Fail    | 0         |
      | fakeID   | Fail       | Fail    | 1         |
      | fakeID   | Success    | Success | 1         |

  Scenario Outline:[9020]调用获取设备信息接口，传入deviceId为空时，接口回调结果为失败，否则回调结果为成功。
    Given deviceID为"fakeID"的uSDKDevice对象信息为:
      | deviceID  | type | uplusID |
      | deviceID1 | 0    | 010203  |
    When 调用获取设备信息接口,参数deviceId为"<deviceId>"
    Then 获取设备信息接口的回调结果为"<result>",回调结果中的DeviceBaseInfo对象为:
      | deviceId  | typeName | typeId |
      | deviceID1 | ALL_TYPE | 010203 |
    Examples:
      | deviceId | result  |
      | 空对象   | Fail    |
      | fakeID   | Success |

  Scenario Outline:[9021]调用OutFocus接口，传入deviceId为空时，接口回调结果为失败，否则回调结果为成功。
    Given deviceID为"fakeID"的uSDKDevice对象的outFocus接口,返回值为"<uSDKOutFocusValue>"
    When 调用OutFocus接口,参数deviceId为"<deviceId>"
    Then 获取OutFocus接口的回调结果中的outFocus值为"<outFocusValue>"
    Then deviceID为"fakeID"的uSDKDevice对象的outFocus接口被调用"<callTimes>"次
    Examples:
      | deviceId | uSDKOutFocusValue | outFocusValue | callTimes |
      | 空对象   | true              | false         | 0         |
      | fakeID   | true              | true          | 1         |
      | fakeID   | false             | false         | 1         |

  Scenario Outline:[9022]调用InFocus接口，传入deviceId为空时，接口回调结果为失败，否则回调结果为成功。
    Given deviceID为"fakeID"的uSDKDevice对象的inFocus接口,返回值为"<uSDKInFocusValue>"
    When 调用InFocus接口,参数deviceId为"<deviceId>"
    Then 获取InFocus接口的回调结果中的inFocus值为"<inFocusValue>"
    Then deviceID为"fakeID"的uSDKDevice对象的inFocus接口被调用"<callTimes>"次
    Examples:
      | deviceId | uSDKInFocusValue | inFocusValue | callTimes |
      | 空对象   | true             | false        | 0         |
      | fakeID   | true             | true         | 1         |
      | fakeID   | false            | false        | 1         |

  Scenario Outline:[9023]调用获取属性列表接口，传入deviceId为空时，接口回调结果为失败，否则回调结果为成功。
    Given deviceID为"fakeID"的uSDKDevice对象的attributeDict接口,返回结果为:
      | key   | attrName | attrValue |
      | attr1 | attr1    | value1    |
      | attr2 | attr2    | value2    |
      | attr3 | attr3    | value3    |
    When 调用获取属性列表接口,参数deviceId为"<deviceId>"
    Then 获取属性列表接口的回调结果为"<result>",回调结果中的属性列表值为:
      | name  | value  |
      | attr1 | value1 |
      | attr2 | value2 |
      | attr3 | value3 |
    Then deviceID为"fakeID"的uSDKDevice对象的attributeDict接口被调用"<callTimes>"次
    Examples:
      | deviceId | result  | callTimes |
      | 空对象   | Fail    | 0         |
      | fakeID   | Success | 1         |

  Scenario: [9023-1]调用获取属性列表接口，uSDK返回的属性列表为空
    Given deviceID为"fakeID"的uSDKDevice对象的attributeDict接口,返回结果为:
      | key | attrName | attrValue |
    When 调用获取属性列表接口,参数deviceId为"fakeID"
    Then 获取属性列表接口的回调结果为"Success",回调结果中的属性列表值为:
      | name | value |
    Then deviceID为"fakeID"的uSDKDevice对象的attributeDict接口被调用"1"次

  Scenario Outline:[9024]调用获取设备告警接口，传入deviceId为空时，接口回调结果为失败，否则回调结果为成功。
    Given deviceID为"fakeID"的uSDKDevice对象的alarmList接口,返回结果为:
      | name   | value  | alarmTimestamp |
      | alarm1 | value1 | 2021-01-26     |
      | alarm2 | value2 | 2021-01-26     |
      | alarm3 | value3 | 2021-01-26     |
    When 调用获取设备告警接口,参数deviceId为"<deviceId>"
    Then 获取设备告警接口的回调结果为"<result>",回调结果中的属性列表值为:
      | name   | value  | time       |
      | alarm1 | value1 | 2021-01-26 |
      | alarm2 | value2 | 2021-01-26 |
      | alarm3 | value3 | 2021-01-26 |
    Then deviceID为"fakeID"的uSDKDevice对象的alarmList接口被调用"<callTimes>"次
    Examples:
      | deviceId | result  | callTimes |
      | 空对象   | Fail    | 0         |
      | fakeID   | Success | 1         |

  Scenario Outline:[9025]调用获取设备连接状态接口，传入deviceId为空时，接口回调结果为失败，否则回调结果为成功。
    Given deviceID为"fakeID"的uSDKDevice对象的state,值为"<uSDKDeviceState>"
    When 调用获取设备连接状态接口,参数deviceId为"<deviceId>"
    Then 获取设备连接状态接口的回调结果为"<result>",回调结果中的connection值为"<upDeviceState>"
    Examples:
      | deviceId | result  | uSDKDeviceState | upDeviceState |
      | 空对象   | Fail    |                 |               |
      | fakeID   | Success | 0               | 1             |
      | fakeID   | Success | 2               | 2             |
      | fakeID   | Success | 3               | 3             |
      | fakeID   | Success | 4               | 4             |
      | fakeID   | Success | 5               | 0             |

  @ios_ignore
  Scenario Outline:[9026]通过属性名称获取对应属性值，deviceId或者属性名称任意一个为空，或者获取属性值接口回调结果失败，或者deviceId不存在，获取属性值失败。
    Given deviceID为"fakeID"的uSDKDevice对象的readAttribute接口的结果为"<Result>",返回结果"<ResultValue>"
    When 通过属性名称获取属性值,参数deviceId为"<deviceId>",属性名称为"<attrName>"
    Then 获取属性列表接口的回调结果为"<Result>",回调结果中属性值为"<ExtraData>"
    Then deviceID为"fakeID"的uSDKDevice对象的readAttribute接口被调用"<callTimes>"次
    Examples:
      | deviceId | attrName     | callTimes | Result | ExtraData | ResultValue |
      | null     | fakeAttrName | 0         | Fail   | null      | null        |
      | fakeID   | null         | 0         | Fail   | null      | null        |
      | null     | null         | 0         | Fail   | null      | null        |
      | fakeID1  | fakeAttrName | 0         | Fail   | null      | null        |
      | fakeID   | fakeAttrName | 1         | Fail   | null      | null        |

  @ios_ignore
  Scenario: [9027]通过属性名称获取对应属性值，deviceId或者属性名称都不为空，接口回调结果成功。
    Given deviceID为"fakeID"的uSDKDevice对象的readAttribute接口的结果为"Success",返回结果"fakeAttrValue"
    When 通过属性名称获取属性值,参数deviceId为"fakeID",属性名称为"fakeAttrName"
    Then 获取属性列表接口的回调结果为"Success",回调结果中属性值为"fakeAttrName,fakeAttrValue"
    Then deviceID为"fakeID"的uSDKDevice对象的readAttribute接口被调用"1"次

  @ios_ignore
  Scenario Outline:[9028]获取设备绑定信息，deviceId和accessToken任意一个为空，或者绑定信息接口回调失败，或者deviceId不存在，最终获取绑定信息失败。
    Given deviceID为"fakeID"的uSDKDevice对象的getDeviceBindInfo接口的结果为"<Result>",返回结果"<ExtraData>"
    When 获取设备绑定信息,参数deviceId为"<deviceId>",accessToken为"<accessToken>"
    Then 获取设备绑定信息接口的回调结果为"<Result>",回调结果中绑定信息为"<ExtraData>"
    Then deviceID为"fakeID"的uSDKDevice对象的getDeviceBindInfo接口被调用"<callTimes>"次
    Examples:
      | deviceId | accessToken     | callTimes | Result | ExtraData |
      | null     | fakeAccessToken | 0         | Fail   | null      |
      | fakeID   | null            | 0         | Fail   | null      |
      | null     | null            | 0         | Fail   | null      |
      | fakeID1  | fakeAccessToken | 0         | Fail   | null      |
      | fakeID   | fakeAccessToken | 1         | Fail   | null      |

  @android_ignore
  Scenario Outline:[9028-1]获取设备绑定信息，deviceId和accessToken任意一个为空，或者绑定信息接口回调失败，或者deviceId不存在，最终获取绑定信息失败。
    Given  初始化设备管理器
    Given 设备代理的接入网关参数accessToken为"<accessToken>"
    Given deviceID为"fakeID"的uSDKDevice对象的getDeviceBindInfo接口的结果为"<Result>",返回结果"<ExtraData>"
    When 获取设备绑定信息,参数deviceId为"<deviceId>"
    Then 获取设备绑定信息接口的回调结果为"<Result>",回调结果中绑定信息为"<ExtraData>"
    Then deviceID为"fakeID"的uSDKDevice对象的getDeviceBindInfo接口被调用"<callTimes>"次
    Examples:
      | deviceId | accessToken     | callTimes | Result | ExtraData |
      | null     | fakeAccessToken | 0         | Fail   | null      |
      | fakeID   | null            | 0         | Fail   | null      |
      | null     | null            | 0         | Fail   | null      |
      | fakeID1  | fakeAccessToken | 0         | Fail   | null      |
      | fakeID   | fakeAccessToken | 1         | Fail   | null      |

  @ios_ignore
  Scenario: [9029]获取设备绑定信息，deviceId和accessToken都不为空，获取设备绑定信息成功。
    Given deviceID为"fakeID"的uSDKDevice对象的getDeviceBindInfo接口的结果为"Success",返回结果"fakeBindInfo"
    When 获取设备绑定信息,参数deviceId为"fakeID",accessToken为"fakeAccessToken"
    Then 获取设备绑定信息接口的回调结果为"Success",回调结果中绑定信息为"fakeBindInfo"
    Then deviceID为"fakeID"的uSDKDevice对象的getDeviceBindInfo接口被调用"1"次

  @android_ignore
  Scenario: [9029-1]获取设备绑定信息，deviceId和accessToken都不为空，获取设备绑定信息成功。
    Given  初始化设备管理器
    Given 设备代理的接入网关参数accessToken为"<accessToken>"
    Given deviceID为"fakeID"的uSDKDevice对象的getDeviceBindInfo接口的结果为"Success",返回结果"fakeBindInfo"
    When 获取设备绑定信息,参数deviceId为"fakeID"
    Then 获取设备绑定信息接口的回调结果为"Success",回调结果中绑定信息为"fakeBindInfo"
    Then deviceID为"fakeID"的uSDKDevice对象的getDeviceBindInfo接口被调用"1"次

  Scenario Outline:[9030]获取当前设备的子设备列表，deviceId为空或deviceId不存在，获取子设备列表失败。
    Given deviceID为"fakeID"的uSDKDevice对象的subDeviceList接口的结果为:
      | deviceID  | type | uplusID |
      | deviceID1 | 0    | 010203  |
      | deviceID2 | 0    | 010204  |
    When 获取当前设备的子设备列表,参数deviceId为"<deviceId>"
    Then 获取当前设备的子设备列表接口的回调结果为"<result>",回调结果的extraData值为:
      | deviceId | typeName | typeId |
    Then deviceID为"fakeID"的uSDKDevice对象的getSubDeviceList接口被调用"<callTimes>"次
    Examples:
      | deviceId | callTimes | result |
      | null     | 0         | Fail   |
      | fakeID1  | 0         | Fail   |

  Scenario: [9031]获取当前设备的子设备列表，当前设备id存在子设备列表时，获取子设备列表成功。
    Given deviceID为"fakeID"的uSDKDevice对象的subDeviceList接口的结果为:
      | deviceID  | type | uplusID |
      | deviceID1 | 0    | 010203  |
      | deviceID2 | 0    | 010204  |
    When 获取当前设备的子设备列表,参数deviceId为"fakeID"
    Then 获取当前设备的子设备列表接口的回调结果为"Success",回调结果的extraData值为:
      | deviceId  | typeName | typeId |
      | deviceID1 | ALL_TYPE | 010203 |
      | deviceID2 | ALL_TYPE | 010204 |
    Then deviceID为"fakeID"的uSDKDevice对象的getSubDeviceList接口被调用"1"次

  #usdk8.4.0中该接口已废弃，考虑是否需要删除
  Scenario Outline:[9032]获取设备模块使用的配置文件版本号，deviceId为空或uSDK不存在该设备，获取版本号失败。
    Given deviceID为"fakeID"的uSDKDevice对象的getSmartLinkSoftwareVersion接口的结果为"<ResultVersion>"
    When 获取设备模块使用的配置文件版本号,参数deviceId为"<deviceId>"
    Then 获取设备模块使用的配置文件版本号的回调结果为"<Result>",回调结果中版本信息为"<ResultVersion>"
    Then deviceID为"fakeID"的uSDKDevice对象的getSmartLinkSoftwareVersion接口被调用"<callTimes>"次
    Examples:
      | deviceId | callTimes | ResultVersion | Result |
      | null     | 0         | null          | Fail   |
      | fakeID1  | 0         | null          | Fail   |

  Scenario: [9032-1]获取设备模块使用的配置文件版本号，deviceId不为空且uSDK存在该设备，获取版本号成功。
    Given deviceID为"fakeID"的uSDKDevice对象的getSmartLinkSoftwareVersion接口的结果为"fakeVersion"
    When 获取设备模块使用的配置文件版本号,参数deviceId为"fakeID"
    Then 获取设备模块使用的配置文件版本号的回调结果为"Success",回调结果中版本信息为"fakeVersion"
    Then deviceID为"fakeID"的uSDKDevice对象的getSmartLinkSoftwareVersion接口被调用"1"次

  Scenario Outline:[9033]获取设备连接网络类型，deviceId为空或uSDK不存在该设备，获取网络类型失败。
    Given deviceID为"fakeID"的uSDKDevice对象的GetNetType接口的结果为"<ResultNetType>"
    When 获取设备连接网络类型,参数deviceId为"<deviceId>"
    Then 获取设备连接网络类型的回调结果为"<Result>",回调结果中网络类型为"<ResultNetType>"
    Then deviceID为"fakeID"的uSDKDevice对象的GetNetType接口被调用"<callTimes>"次
    Examples:
      | deviceId | callTimes | ResultNetType | Result |
      | null     | 0         | null          | Fail   |
      | fakeID1  | 0         | null          | Fail   |

  Scenario:[9033-1]获取设备连接网络类型，deviceId不为空且uSDK存在该设备，获取网络类型成功。
    Given deviceID为"fakeID"的uSDKDevice对象的GetNetType接口的结果为"fakeNetType"
    When 获取设备连接网络类型,参数deviceId为"fakeID"
    Then 获取设备连接网络类型的回调结果为"Success",回调结果中网络类型为"fakeNetType"
    Then deviceID为"fakeID"的uSDKDevice对象的GetNetType接口被调用"1"次

  Scenario: [9034]用户连接设备成功，监听设备状态变化，当设备报警信息发生变化，能收到变化通知。
    Given deviceID为"fakeID"的uSDKDevice对象的connectNeedPropertiesWithSuccess接口的结果为"Success"
    Given deviceID为"fakeID"的uSDKDevice对象信息为:
      | deviceID | type | uplusID |
      | fakeID   | 0    | 010203  |
    When 调用连接设备接口,参数deviceId为"fakeID"
    When 设备"fakeID"报警状态发生变化，变化信息如下:
      | name        | value | alarmTimestamp |
      | cautionName | 1     | 86400          |
    Then 收到设备"fakeID"的报警变化信息如下:
      | name        | value | time  |
      | cautionName | 1     | 86400 |

  Scenario: [9035]用户连接设备成功，监听设备状态变化，当设备属性发生变化，能收到变化通知。
    Given deviceID为"fakeID"的uSDKDevice对象的connectNeedPropertiesWithSuccess接口的结果为"Success"
    Given deviceID为"fakeID"的uSDKDevice对象信息为:
      | deviceID | type | uplusID |
      | fakeID   | 0    | 010203  |
    When 调用连接设备接口,参数deviceId为"fakeID"
    When 设备"fakeID"属性状态发生变化，变化信息如下:
      | name     | value     |
      | attrName | attrValue |
    Then 收到设备"fakeID"的属性信息如下:
      | name     | value     |
      | attrName | attrValue |

  Scenario: [9036]用户连接设备成功，监听设备状态变化，当设备连接状态发生变化，能收到变化通知。
    Given deviceID为"fakeID"的uSDKDevice对象的connectNeedPropertiesWithSuccess接口的结果为"Success"
    Given deviceID为"fakeID"的uSDKDevice对象信息为:
      | deviceID | type | uplusID |
      | fakeID   | 0    | 010203  |
    When 调用连接设备接口,参数deviceId为"fakeID"
    When 设备"fakeID"连接状态发生变化，连接信息为"STATUS_CONNECTED"
    Then 收到设备"fakeID"连接状态信息为"STATUS_CONNECTED"

  Scenario: [9037]用户连接设备成功，监听设备状态变化，当设备基本信息发生变化，能收到变化通知。
    Given deviceID为"fakeID"的uSDKDevice对象的connectNeedPropertiesWithSuccess接口的结果为"Success"
    Given deviceID为"fakeID"的uSDKDevice对象信息为:
      | deviceID | type | uplusID |
      | fakeID   | 0    | 010204  |
    When 调用连接设备接口,参数deviceId为"fakeID"
    When 设备"fakeID"基本信息发生变化
    Then 收到设备"fakeID"的基本信息如下:
      | deviceID | typeName | typeId |
      | fakeID   | ALL_TYPE | 010204 |

  Scenario: [9038]用户连接设备成功，监听设备状态变化，当设备的子设备列表发生变化，能收到变化通知。
    Given deviceID为"fakeID"的uSDKDevice对象的connectNeedPropertiesWithSuccess接口的结果为"Success"
    Given deviceID为"fakeID"的uSDKDevice对象信息为:
      | deviceID | type | uplusID |
      | fakeID   | 0    | 010204  |
    When 调用连接设备接口,参数deviceId为"fakeID"
    When 设备"fakeID"的子设备列表发生变化,变化信息如下:
      | deviceID | type | uplusID |
      | fakeID2  | 0    | 010205  |
    Then 收到设备"fakeID"的子设备信息如下:
      | deviceID | typeName | typeId |
      | fakeID2  | ALL_TYPE | 010205 |

  Scenario: [9039]用户连接设备成功，监听设备状态变化，当设备数据发生变化，能收到变化通知。
    Given deviceID为"fakeID"的uSDKDevice对象的connectNeedPropertiesWithSuccess接口的结果为"Success"
    Given deviceID为"fakeID"的uSDKDevice对象信息为:
      | deviceID | type | uplusID |
      | fakeID   | 0    | 010204  |
    When 调用连接设备接口,参数deviceId为"fakeID"
    When 设备"fakeID"的数据发生变化,变化信息如下:
      | name     | data |
      | fakeName | 1    |
    Then 收到设备"fakeID"的数据信息如下:
      | name     | data |
      | fakeName | 1    |

  Scenario: [9040]用户连接设备成功，监听设备状态变化，当设备底板固件升级状态发生变化，能收到变化通知。
    Given deviceID为"fakeID"的uSDKDevice对象的connectNeedPropertiesWithSuccess接口的结果为"Success"
    Given deviceID为"fakeID"的uSDKDevice对象信息为:
      | deviceID | type | uplusID |
      | fakeID   | 0    | 010204  |
    When 调用连接设备接口,参数deviceId为"fakeID"
    When 设备"fakeID"的底板固件升级状态发生变化,变化信息如下:
      | UpgradeStatus | upgradeErrInfo |
      | 1             | OK             |
    Then 收到设备"fakeID"的设备底板固件升级状态信息如下:
      | UpgradeStatus | upgradeErrInfo |
      | 1             | OK             |

  Scenario: [9041]用户连接设备成功，监听设备状态变化，当蓝牙实时数据变化，能收到变化通知。
    Given deviceID为"fakeID"的uSDKDevice对象的connectNeedPropertiesWithSuccess接口的结果为"Success"
    Given deviceID为"fakeID"的uSDKDevice对象信息为:
      | deviceID | type | uplusID |
      | fakeID   | 0    | 010204  |
    When 调用连接设备接口,参数deviceId为"fakeID"
    When 设备"fakeID"的蓝牙实时数据发生变化,变化信息为"fakeBluetoothData"
    Then 收到设备"fakeID"的蓝牙实时数据为"fakeBluetoothData"

  Scenario: [9042]用户连接设备成功，监听设备状态变化，当蓝牙历史数据变化，能收到变化通知。
    Given deviceID为"fakeID"的uSDKDevice对象的connectNeedPropertiesWithSuccess接口的结果为"Success"
    Given deviceID为"fakeID"的uSDKDevice对象信息为:
      | deviceID | type | uplusID |
      | fakeID   | 0    | 010204  |
    When 调用连接设备接口,参数deviceId为"fakeID"
    When 设备"fakeID"的蓝牙历史数据发生变化,变化信息如下:
      | currentCount | totalCount | data        |
      | 1            | 5          | historyData |
    Then 收到设备"fakeID"的蓝牙历史数据如下:
      | currentCount | totalCount | data        |
      | 1            | 5          | historyData |

  @ios_ignore
  Scenario: [9043]用户连接设备工具成功，监听设备绑定变化，当有新设备绑定时，能收到变化通知。
    Given uSDK管理器的startSDKWithOptions接口的结果为"Success"
    When 调用连接设备工具接口
    When 设备绑定代理方法被触发,设备id为"fakeID"
    Then 设备列表变化接口被调用"1"次

  @android_ignore
  Scenario: [9043-1]用户连接设备工具成功，监听设备绑定变化，当有新设备绑定时，能收到变化通知。
    Given uSDK管理器的startSDKWithOptions接口的结果为"Success"
    Given uSDK Toolkit的appID值为"fakeIDKey",appKey值为"fakeAppKey",secretKey值为"fakeSecretKey"
    When 调用连接设备工具接口
    When 设备绑定代理方法被触发,设备id为"fakeID"
    Then 设备列表变化接口被调用"1"次

  @ios_ignore
  Scenario: [9044]用户连接设备工具成功，监听设备解绑信息变化，当有设备解除绑定时，能收到变化通知。
    Given uSDK管理器的startSDKWithOptions接口的结果为"Success"
    When 调用连接设备工具接口
    When 设备解除绑定代理方法被触发,设备id为"fakeID"
    Then 设备列表变化接口被调用"1"次

  @android_ignore
  Scenario: [9044-1]用户连接设备工具成功，监听设备解绑信息变化，当有设备解除绑定时，能收到变化通知。
    Given uSDK管理器的startSDKWithOptions接口的结果为"Success"
    Given uSDK Toolkit的appID值为"fakeIDKey",appKey值为"fakeAppKey",secretKey值为"fakeSecretKey"
    When 调用连接设备工具接口
    When 设备解除绑定代理方法被触发,设备id为"fakeID"
    Then 设备列表变化接口被调用"1"次

  @ios_ignore
  Scenario: [9045]用户连接设备工具成功，监听新增设备变化，当发现新设备时，能收到变化通知。
    Given uSDK管理器的startSDKWithOptions接口的结果为"Success"
    When 调用连接设备工具接口
    When 发现新设备代理方法被触发,设备列表如下:
      | deviceID | type | uplusID |
      | fakeID2  | 0    | 010204  |
      | fakeID3  | 0    | 010205  |
    Then 收到新发现设备列表如下:
      | deviceID | typeName | typeId |
      | fakeID2  | ALL_TYPE | 010204 |
      | fakeID3  | ALL_TYPE | 010205 |

  @android_ignore
  Scenario: [9045-1]用户连接设备工具成功，监听新增设备变化，当发现新设备时，能收到变化通知。
    Given uSDK管理器的startSDKWithOptions接口的结果为"Success"
    Given uSDK Toolkit的appID值为"fakeIDKey",appKey值为"fakeAppKey",secretKey值为"fakeSecretKey"
    When 调用连接设备工具接口
    When 发现新设备代理方法被触发,设备列表如下:
      | deviceID | type | uplusID |
      | fakeID2  | 0    | 010204  |
      | fakeID3  | 0    | 010205  |
    Then 收到新发现设备列表如下:
      | deviceID | typeName | typeId |
      | fakeID2  | ALL_TYPE | 010204 |
      | fakeID3  | ALL_TYPE | 010205 |

  @ios_ignore
  Scenario: [9046]用户连接设备工具成功，监听删除设备列表变化，当删除设备列表时，能收到变化通知。
    Given uSDK管理器的startSDKWithOptions接口的结果为"Success"
    When 调用连接设备工具接口
    When 删除设备代理方法被触发,设备列表如下:
      | deviceID | type | uplusID |
      | fakeID2  | 0    | 010204  |
      | fakeID3  | 0    | 010205  |
    Then 收到新删除设备列表如下:
      | deviceID | typeName | typeId |
      | fakeID2  | ALL_TYPE | 010204 |
      | fakeID3  | ALL_TYPE | 010205 |

  @android_ignore
  Scenario: [9046-1]用户连接设备工具成功，监听删除设备列表变化，当删除设备列表时，能收到变化通知。
    Given uSDK管理器的startSDKWithOptions接口的结果为"Success"
    Given uSDK Toolkit的appID值为"fakeIDKey",appKey值为"fakeAppKey",secretKey值为"fakeSecretKey"
    When 调用连接设备工具接口
    When 删除设备代理方法被触发,设备列表如下:
      | deviceID | type | uplusID |
      | fakeID2  | 0    | 010204  |
      | fakeID3  | 0    | 010205  |
    Then 收到新删除设备列表如下:
      | deviceID | typeName | typeId |
      | fakeID2  | ALL_TYPE | 010204 |
      | fakeID3  | ALL_TYPE | 010205 |

  Scenario Outline:[9047]调用订阅资源解码接口，传入参数deviceId和resourceName，当参数为空或uSDK中无对应的uSDKDevice对象时，资源订阅接口的回调结果为失败。
    Given deviceID为"fakeID"的uSDKDevice对象的subscribeResourceWithDecode接口的回调结果为"<usdkResult>"
    When 调用订阅资源解码接口,参数deviceId为"<deviceId>", 资源名称为"<resourceName>"
    Then 订阅资源解码接口回调结果为"<result>"
    Then deviceID为"fakeID"的uSDKDevice对象的subscribeResourceWithDecode接口被调用"<callTimes>"次,参数为"<param>"
    Examples:
      | deviceId | resourceName | result | callTimes | usdkResult | param     |
      | 空对象   | 空对象       | Fail   | 0         | Success    |           |
      | 空对象   | 空字符串     | Fail   | 0         | Success    |           |
      | 空字符串 | 空对象       | Fail   | 0         | Success    |           |
      | fakeID   | fakeName1    | Fail   | 1         | Fail       | fakeName1 |
      | fakeID1  | fakeName     | Fail   | 0         | Success    |           |
      | fakeID   | fakeName     | Fail   | 1         | Fail       | fakeName  |

  Scenario:[9048]调用订阅资源解码接口，传入参数deviceId和resourceName，当参数为不为空或uSDK中对应的uSDKDevice对象存在时，资源订阅接口的回调结果为调用的uSDKDevice对象的接口回调结果。
    Given deviceID为"fakeID"的uSDKDevice对象的subscribeResourceWithDecode接口的回调结果为"Success"
    When 调用订阅资源解码接口,参数deviceId为"fakeID", 资源名称为"fakeName"
    Then 订阅资源解码接口回调结果为"Success"
    Then deviceID为"fakeID"的uSDKDevice对象的subscribeResourceWithDecode接口被调用"1"次,参数为"fakeName"

  Scenario Outline: [9049]调用创建设备组接口，传入参数deviceId，当参数为空或uSDK中无对应的uSDKDevice对象时，创建分组接口的回调结果为失败。
    Given deviceID为"fakeID"的uSDKDevice对象的createGroupWithTimeoutInterval接口的调用结果为"<usdkResult>"
    When 调用创建设备组接口,参数deviceId为"<deviceId>"
    Then 创建设备组接口回调结果为"<result>"
    Then deviceID为"fakeID"的uSDKDevice对象的createGroupWithTimeoutInterval接口被调用"<callTimes>"次
    Examples:
      | deviceId | result | callTimes | usdkResult |
      | 空对象   | Fail   | 0         | Success    |
      | 空字符串 | Fail   | 0         | Success    |
      | fakeID   | Fail   | 1         | Fail       |
      | fakeID1  | Fail   | 0         | Success    |

  Scenario: [9050]调用创建设备组接口，传入参数deviceId，当参数不为空或uSDK中对应的uSDKDevice对象存在，创建分组接口的回调结果为调用uSDKDevice对象的接口回调结果
    Given deviceID为"fakeID"的uSDKDevice对象的createGroupWithTimeoutInterval接口的调用结果为"Success"
    When 调用创建设备组接口,参数deviceId为"fakeID"
    Then 创建设备组接口回调结果为"Success"
    Then deviceID为"fakeID"的uSDKDevice对象的createGroupWithTimeoutInterval接口被调用"1"次

  Scenario Outline: [9051]调用删除组设备接口，传入参数deviceId，当参数为空或uSDK中无对应的uSDKDevice对象时，删除组设备接口的回调结果为失败。
    Given deviceID为"fakeID"的uSDKDevice对象的deleteGroupCompletionHandler接口的调用结果为"<usdkResult>"
    When 调用删除组设备接口,参数deviceId为"<deviceId>"
    Then 删除组设备接口回调结果为"<result>"
    Then deviceID为"fakeID"的uSDKDevice对象的deleteGroupCompletionHandler接口被调用"<callTimes>"次
    Examples:
      | deviceId | result | callTimes | usdkResult |
      | 空对象   | Fail   | 0         | Success    |
      | 空字符串 | Fail   | 0         | Success    |
      | fakeID   | Fail   | 1         | Fail       |
      | fakeID1  | Fail   | 0         | Success    |

  Scenario: [9052]调用删除组设备接口，传入参数deviceId，当参数不为空或uSDK中对应的uSDKDevice对象存在时，删除组设备接口的回调结果为调用uSDKDevice对象的接口回调结果
    Given deviceID为"fakeID"的uSDKDevice对象的deleteGroupCompletionHandler接口的调用结果为"Success"
    When 调用删除组设备接口,参数deviceId为"fakeID"
    Then 删除组设备接口回调结果为"Success"
    Then deviceID为"fakeID"的uSDKDevice对象的deleteGroupCompletionHandler接口被调用"1"次

  Scenario Outline: [9053]调用获取组设备列表接口，传入参数deviceId，当参数为空或uSDK中无对应的uSDKDevice对象时，获取组设备列表接口的回调结果为失败。
    Given deviceID为"fakeID"的uSDKDevice对象的fetchGroupableDeviceList接口的调用结果为"<usdkResult>",回调结果为:
      | deviceID | type | uplusID |
    When 调用获取组设备列表接口,参数deviceId为"<deviceId>"
    Then 获取组设备列表接口回调结果为"<result>",回调结果的extraData值为:
      | deviceId | typeName | typeId |
    Then deviceID为"fakeID"的uSDKDevice对象的fetchGroupableDeviceList接口被调用"<callTimes>"次
    Examples:
      | deviceId | result | callTimes | usdkResult |
      | 空对象   | Fail   | 0         | Success    |
      | 空字符串 | Fail   | 0         | Success    |
      | fakeID   | Fail   | 1         | Fail       |
      | fakeID1  | Fail   | 0         | Success    |

  Scenario: [9054]调用获取组设备列表接口，传入参数deviceId，当参数不为空或uSDK中对应的uSDKDevice对象存在时，获取组设备列表接口的回调结果为调用uSDKDevice对象的接口回调结果
    Given deviceID为"fakeID"的uSDKDevice对象的fetchGroupableDeviceList接口的调用结果为"Success",回调结果为:
      | deviceID  | type | uplusID |
      | deviceID1 | 0    | 010203  |
      | deviceID2 | 0    | 010204  |
    When 调用获取组设备列表接口,参数deviceId为"fakeID"
    Then 获取组设备列表接口回调结果为"Success",回调结果的extraData值为:
      | deviceId  | typeName | typeId |
      | deviceID1 | ALL_TYPE | 010203 |
      | deviceID2 | ALL_TYPE | 010204 |
    Then deviceID为"fakeID"的uSDKDevice对象的fetchGroupableDeviceList接口被调用"1"次

  Scenario Outline:[9055]调用是否组设备接口，传入deviceId，当deviceId为空或deviceId不存在，获取是否组设备接口回调结果为失败。
    Given deviceID为"fakeID"的uSDKDevice对象的isGroup值为"<uSDKIsGroup>"
    When 调用是否组设备接口,参数deviceId为"<deviceId>"
    Then 获取是否组设备接口的结果为"<result>",回调结果的extraData值为"<uSDKIsGroup>"
    Examples:
      | deviceId | result | uSDKIsGroup |
      | 空对象   | false  |             |
      | 空字符串 | false  |             |
      | fakeID1  | false  | false       |

  Scenario:[9056]调用是否组设备接口，传入参数deviceId，当deviceId不为空活deviceId存在，获取是否组设备接口回调结果为成功。
    Given deviceID为"fakeID"的uSDKDevice对象的isGroup值为"true"
    When 调用是否组设备接口,参数deviceId为"fakeID"
    Then 获取是否组设备接口的结果为"true",回调结果的extraData值为"true"

  Scenario Outline:[9057]获取组设备成员列表，传入参数deviceId，deviceId为空或deviceId不存在，获取组设备成员列表失败。
    Given deviceID为"fakeID"的uSDKDevice对象的groupMembers接口的结果为:
      | deviceID  | type | uplusID |
      | deviceID1 | 0    | 010203  |
      | deviceID2 | 0    | 010204  |
    When 调用获取组设备成员列表接口,参数deviceId为"<deviceId>"
    Then 获取组设备成员列表接口回调结果为"<result>",回调结果的extraData值为:
      | deviceId | typeName | typeId |
    Then deviceID为"fakeID"的uSDKDevice对象的groupMembers接口被调用"<callTimes>"次
    Examples:
      | deviceId | callTimes | result |
      | 空对象   | 0         | Fail   |
      | 空字符串 | 0         | Fail   |
      | fakeID1  | 0         | Fail   |

  Scenario: [9058]获取组设备成员列表，传入参数deviceId，当前组设备id存在成员列表时，获取组设备成员列表成功。
    Given deviceID为"fakeID"的uSDKDevice对象的groupMembers接口的结果为:
      | deviceID  | type | uplusID |
      | deviceID1 | 0    | 010203  |
      | deviceID2 | 0    | 010204  |
    When 调用获取组设备成员列表接口,参数deviceId为"fakeID"
    Then 获取组设备成员列表接口回调结果为"Success",回调结果的extraData值为:
      | deviceId  | typeName | typeId |
      | deviceID1 | ALL_TYPE | 010203 |
      | deviceID2 | ALL_TYPE | 010204 |
    Then deviceID为"fakeID"的uSDKDevice对象的groupMembers接口被调用"1"次

  Scenario Outline:[9059]向组设备中添加设备，传入参数deviceId和deviceIds，当参数为空或uSDK中无对应的uSDKDevice对象时，向组设备添加设备接口的回调结果为失败。
    Given deviceID为"fakeID"的uSDKDevice对象的addDevices接口的completionHandler调用结果为"<usdkResult>",progressNotify回调结果为"<notifyResult>"
    Given uSDK设备管理器,调用getDeviceWithID方法,参数deviceID为"fakeID1",返回"uSDKDevice"对象
    Given uSDK设备管理器,调用getDeviceWithID方法,参数deviceID为"fakeID2",返回"uSDKDevice"对象
    When 调用向组设备添加设备接口,参数deviceId为"<deviceId>",设备列表为"<devices>"
    Then 向组设备添加设备接口回调结果为"<result>"
    Then deviceID为"fakeID"的uSDKDevice对象的addDevice接口被调用"<callTimes>"次,参数为"<param>"
    Examples:
      | deviceId | devices         | result | callTimes | usdkResult | notifyResult | param           |
      | 空对象   | 空对象          | Fail   | 0         | Fail       | Success      |                 |
      | 空字符串 | 空对象          | Fail   | 0         | Fail       | Success      |                 |
      | fakeID   | fakeID1,fakeID2 | Fail   | 1         | Fail       | Success      | fakeID1,fakeID2 |
      | fakeID   | fakeID1,fakeID2 | Fail   | 1         | Fail       | Fail         | fakeID1,fakeID2 |
      | fakeID3  | fakeID1,fakeID2 | Fail   | 0         | Fail       | Fail         |                 |

  Scenario:[9060]向组设备中添加设备，传入参数deviceId和deviceIds，当参数为不为空或uSDK中有对应的uSDKDevice对象时，uSDKDevice对象的接口回调结果为空对象，向组设备添加设备接口的回调结果为空。
    Given deviceID为"fakeID"的uSDKDevice对象的addDevices接口的completionHandler调用结果为"Success",progressNotify回调结果为"Success"
    When 调用向组设备添加设备接口,参数deviceId为"fakeID",设备列表为"fakeID1,fakeID2"
    Then 向组设备添加设备接口回调结果为"Fail"
    Then deviceID为"fakeID"的uSDKDevice对象的addDevice接口被调用"0"次,参数为""

  Scenario:[9061]向组设备中添加设备，传入参数deviceId和deviceIds，当参数为不为空或uSDK中有对应的uSDKDevice对象时，向组设备添加设备接口的回调结果为调用uSDKDevice对象的接口回调结果。
    Given deviceID为"fakeID"的uSDKDevice对象的addDevices接口的completionHandler调用结果为"Success",progressNotify回调结果为"Success"
    Given uSDK设备管理器,调用getDeviceWithID方法,参数deviceID为"fakeID1",返回"uSDKDevice"对象
    Given uSDK设备管理器,调用getDeviceWithID方法,参数deviceID为"fakeID2",返回"uSDKDevice"对象
    When 调用向组设备添加设备接口,参数deviceId为"fakeID",设备列表为"fakeID1,fakeID2"
    Then 向组设备添加设备接口回调结果为"Success"
    Then deviceID为"fakeID"的uSDKDevice对象的addDevice接口被调用"1"次,参数为"fakeID1,fakeID2"

  Scenario Outline:[9062]从组设备中移除设备，传入参数deviceId和deviceIds，当参数为空或uSDK中无对应的uSDKDevice对象时，从组设备中移除设备接口的回调结果为失败。
    Given deviceID为"fakeID"的uSDKDevice对象的removeDevices接口的completionHandler调用结果为"<usdkResult>",progressNotify回调结果为"<notifyResult>"
    Given uSDK设备管理器,调用getDeviceWithID方法,参数deviceID为"fakeID1",返回"uSDKDevice"对象
    Given uSDK设备管理器,调用getDeviceWithID方法,参数deviceID为"fakeID2",返回"uSDKDevice"对象
    When 调用从组设备中移除设备接口,参数deviceId为"<deviceId>",设备列表为"<devices>"
    Then 从组设备中移除设备接口回调结果为"<result>"
    Then deviceID为"fakeID"的uSDKDevice对象的removeDevices接口被调用"<callTimes>"次,参数为"<param>"
    Examples:
      | deviceId | devices         | result | callTimes | usdkResult | notifyResult | param           |
      | 空对象   | 空对象          | Fail   | 0         | Fail       | Success      |                 |
      | 空字符串 | 空对象          | Fail   | 0         | Fail       | Success      |                 |
      | fakeID   | fakeID1,fakeID2 | Fail   | 1         | Fail       | Success      | fakeID1,fakeID2 |
      | fakeID   | fakeID1,fakeID2 | Fail   | 1         | Fail       | Fail         | fakeID1,fakeID2 |
      | fakeID3  | fakeID1,fakeID2 | Fail   | 0         | Fail       | Fail         |                 |

  Scenario:[9063]从组设备中移除设备，传入参数deviceId和deviceIds，当参数为不为空或uSDK中有对应的uSDKDevice对象时，uSDKDevice对象的接口回调结果为空对象，则从组设备中移除设备接口的回调结果为空。
    Given deviceID为"fakeID"的uSDKDevice对象的removeDevices接口的completionHandler调用结果为"Success",progressNotify回调结果为"Success"
    When 调用从组设备中移除设备接口,参数deviceId为"fakeID",设备列表为"fakeID1,fakeID2"
    Then 从组设备中移除设备接口回调结果为"Fail"
    Then deviceID为"fakeID"的uSDKDevice对象的removeDevices接口被调用"0"次,参数为""

  Scenario:[9064]从组设备中移除设备，传入参数deviceId和deviceIds，当参数为不为空或uSDK中有对应的uSDKDevice对象时，从组设备中移除设备接口的回调结果为调用uSDKDevice对象的接口回调结果。
    Given deviceID为"fakeID"的uSDKDevice对象的removeDevices接口的completionHandler调用结果为"Success",progressNotify回调结果为"Success"
    Given uSDK设备管理器,调用getDeviceWithID方法,参数deviceID为"fakeID1",返回"uSDKDevice"对象
    Given uSDK设备管理器,调用getDeviceWithID方法,参数deviceID为"fakeID2",返回"uSDKDevice"对象
    When 调用从组设备中移除设备接口,参数deviceId为"fakeID",设备列表为"fakeID1,fakeID2"
    Then 从组设备中移除设备接口回调结果为"Success"
    Then deviceID为"fakeID"的uSDKDevice对象的removeDevices接口被调用"1"次,参数为"fakeID1,fakeID2"

  Scenario: [9065]用户连接设备成功，监听解码资源数据变化，当解码资源数据发生变化，能收到变化通知。
    Given deviceID为"fakeID"的uSDKDevice对象的connectNeedPropertiesWithSuccess接口的结果为"Success"
    Given deviceID为"fakeID"的uSDKDevice对象信息为:
      | deviceID | type | uplusID |
      | fakeID   | 0    | 010204  |
    When 调用连接设备接口,参数deviceId为"fakeID"
    When 设备"fakeID"的解码资源数据发生变化,变化信息如下:
      | name     | data |
      | fakeName | 1    |
    Then 收到设备"fakeID"的资源解码数据信息如下:
      | name     | data |
      | fakeName | 1    |

  Scenario Outline: [9098]调用洗衣机Fota升级接口，当deviceId为空或者uSDK中无对应的uSDKDevice对象或者洗衣机Fota升级接口调用失败时，最终结果为失败。
    Given deviceID为"fakeID"的uSDKDevice对象的startFoTa接口的调用结果为"<usdkResult>"
    When 调用洗衣机Fota升级接口,参数traceId为 "<traceId>", 参数firmwareId为 "<firmwareId>", 参数deviceId为"<deviceId>"
    Then 洗衣机Fota升级方法回调结果为"Fail"
    Then deviceID为"fakeID"的uSDKDevice对象的startFoTa接口被调用"<callTimes>"次,参数为"<param>"
    Examples:
      | traceId     | firmwareId | deviceId | callTimes | usdkResult | param                 |
      | fakeTraceId | fakeFirId  | 空对象   | 0         | Success    |                       |
      | fakeTraceId | fakeFirId  | 空字符串 | 0         | Success    |                       |
      | fakeTraceId | fakeFirId  | fakeID   | 1         | Fail       | fakeTraceId,fakeFirId |
      | fakeTraceId | fakeFirId  | fakeID1  | 0         | Success    |                       |

  Scenario: [9099]调用洗衣机Fota升级接口，deviceId不为空且在uSdk中存在，调用洗衣机升级接口成功，最终结果返回成功。
    Given deviceID为"fakeID"的uSDKDevice对象的startFoTa接口的调用结果为"Success"
    When 调用洗衣机Fota升级接口,参数traceId为 "fakeTraceId", 参数firmwareId为 "fakeFirId", 参数deviceId为"fakeID"
    Then 洗衣机Fota升级方法回调结果为"Success"
    Then deviceID为"fakeID"的uSDKDevice对象的startFoTa接口被调用"1"次,参数为"fakeTraceId,fakeFirId"
  
  Scenario Outline: [9010]调用获取蓝牙连接状态接口，传入的deviceId为空或者无相应的uSDKDevice对象，接口调用失败，否则接口调用成功
    Given deviceID为"fakeID"的uSDKDevice对象的bleState的值为"<usdkBleState>"
    When 调用获取设备蓝牙连接状态接口,参数deviceId为"<deviceId>"
    Then 获取设备蓝牙连接状态接口的回调结果为"<result>",回调结果中的extraData值为"<bleState>"
    
    Examples:
      |  usdkBleState  |   deviceId  | result     |    bleState    |
      |    OFFLINE     |   空字符串   |   fail     |                |
      |    OFFLINE     |   空对象     |   fail     |                |
      |    OFFLINE     |   fakeID1   |   fail     |                |
      |    OFFLINE     |   fakeID    |   success  |   OFFLINE      |
      |    DISCONNECTED|   fakeID    |   success  |   DISCONNECTED |
      |    CONNECTING  |   fakeID    |   success  |   CONNECTING   |
      |    CONNECTED   |   fakeID    |   success  |   CONNECTED    |
      |    READY       |   fakeID    |   success  |   READY        |


  Scenario Outline:[9011]用户连接设备成功，监听设备蓝牙连接状态变化，当设备蓝牙状态发生变化，能收到变化通知。
    Given deviceID为"fakeID"的uSDKDevice对象的connectNeedPropertiesWithSuccess接口的结果为"Success"
    Given deviceID为"fakeID"的uSDKDevice对象信息为:
      | deviceID | type | uplusID |
      | fakeID   | 0    | 010203  |
    When 调用连接设备接口,参数deviceId为"fakeID"
    When 设备"fakeID"baseinfo发生变化，其中蓝牙连接状态为"<bleState>"
    Then 收到设备"fakeID"蓝牙连接状态为"<bleState>" 
    Examples: 
    |  bleState   |    
    |  OFFLINE     |   
    |  DISCONNECTED  |   
    |  CONNECTING  |   
    |  CONNECTED  |   
    |   READY  |   