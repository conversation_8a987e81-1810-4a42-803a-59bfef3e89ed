Feature: 历史Bug雷区

  记录了在UpDevice上线后发生的线上问题的还原用例

  Background:
    Given 初始化设备管理器
    Given 设备数据源的更新设备列表接口返回数据如下:
      | Protocol   | DeviceId        | TypeId        | TypeName        | TypeCode        | Model       | ProdNo       | ParentId        | SubDevNo        | Extra                                                                                                                                                                                                                                                                                                                                                                             |
      | haier-usdk | fake_device_id1 | fake_type_id1 | fake_type_name1 | fake_type_code1 | fake_model1 | fake_pro_no1 | fake_parent_id1 | fake_device_id2 | {"DI-Basic.displayName":"fake_name","DI-Basic.room":"fake_room","DI-Basic.roomId":"fake_room_id","DI-Basic.bindTime":"fake_bind_time","DI-Product.brand":"fake_brand","DI-Product.bind_type":"fake_bind_type","DI-Product.configType":"fake_configT_type","DI-Product.category":"fake_category","DI-Relation.ownerId":"fake_owner_id","DI-Product.imageAddr1":"fake_image_addr1"} |
      | haier-usdk | fake_device_id2 | fake_type_id2 | fake_type_name2 | fake_type_code2 | fake_model2 | fake_pro_no2 | fake_parent_id2 | fake_device_id3 | {"DI-Basic.displayName":"fake_name","DI-Basic.room":"fake_room","DI-Basic.roomId":"fake_room_id","DI-Basic.bindTime":"fake_bind_time","DI-Product.brand":"fake_brand","DI-Product.bind_type":"fake_bind_type","DI-Product.configType":"fake_configT_type","DI-Product.category":"fake_category","DI-Relation.ownerId":"fake_owner_id","DI-Product.imageAddr1":"fake_image_addr1"} |
    Given 设备代理的接入网关参数为"usdk-user-id=fakeUserId,usdk-access-token=fakeAccessToken"

  ### 1.准备队列一直循环，用户登出，清理设备列表，新加入准备队列的设备有可能不会进入准备队列进行设备准备。导致设备一直显示为离线。bug:ZHIJIAAPP-26381
  @ignore
  Scenario:[4001]准备队列一直循环，用户登出，清理设备列表，新加入准备队列的设备正常执行准备，设备状态为PREPARED
    Given Toolkit的获取设备基础信息返回数据如下
      | Protocol | DeviceId | TypeId | TypeName | TypeCode | Model | ProdNo | ParentId | SubDevNo | Extra |
    Given Toolkit的获取子设备列表返回数据如下
      | Protocol   | DeviceId             | TypeId        | TypeName        | TypeCode        | Model       | ProdNo       | ParentId        | SubDevNo        |
      | haier-usdk | fake_device_id18-8-1 | fake_type_id1 | fake_type_name1 | fake_type_code1 | fake_model1 | fake_pro_no1 | fake_parent_id1 | fake_device_id2 |
    Given Toolkit的连接工具接口的执行结果为"成功"
    When 调用设备管理器的准备Toolkit接口
    Given Toolkit的更新设备列表接口的执行结果为"成功"
    Given Toolkit的连接用户接入网关的执行结果为"成功"
    Given Toolkit的连接设备接口的执行结果为"失败"
    When 用户更新设备列表,立即更新标志为"TRUE"
    Given Toolkit的释放设备接口的执行结果为"成功"
    When 调用设备管理器的清除设备列表接口
    Given 设备数据源的更新设备列表接口返回数据如下:
      | Protocol   | DeviceId        | TypeId        | TypeName        | TypeCode        | Model       | ProdNo       | ParentId        | SubDevNo        | Extra                                                                                                                                                                                                                                                                                                                                                                             |
      | haier-usdk | fake_device_id3 | fake_type_id1 | fake_type_name1 | fake_type_code1 | fake_model1 | fake_pro_no1 | fake_parent_id1 | fake_device_id2 | {"DI-Basic.displayName":"fake_name","DI-Basic.room":"fake_room","DI-Basic.roomId":"fake_room_id","DI-Basic.bindTime":"fake_bind_time","DI-Product.brand":"fake_brand","DI-Product.bind_type":"fake_bind_type","DI-Product.configType":"fake_configT_type","DI-Product.category":"fake_category","DI-Relation.ownerId":"fake_owner_id","DI-Product.imageAddr1":"fake_image_addr1"} |
    Given Toolkit的连接设备接口的执行结果为"成功"
    When 用户更新设备列表,立即更新标志为"TRUE"
    When 等待"1"秒
    Then 协议为"haier-usdk",设备id为"fake_device_id3"的设备连接状态为"PREPARED"


