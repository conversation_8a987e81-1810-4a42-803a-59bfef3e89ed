# 单元测试设计说明

## 背景
智家APP是用户通过手机控制海尔物联网器的唯一入口，网器控制功能是智家APP最重要的功能板块。
针对智家APP中的网器相关功能，控制信道的创建、维护、命令下发等基础功能由IOT团队的uSDK封装。uSDK仅聚焦在设备信道的稳定性、实时性方面，无法满足智家APP的上层业务诉求，具体比如：
- 依赖外部提供设备列表数据
- 无设备列表变化通知
- 设备实例和设备列表只支持单订阅

为了降低上层业务开发网器控制功能的代码量和业务复杂度抽离了Updevice组件

## 业务功能说明
组件主要提供了以下几个方面的能力：
### 根据设备列表通过工厂类创建设备子类
上层业务在使用设备控制功能时将设备控制逻辑封装到基于设备子类中，通过注册工厂类完成自定义设备子类创建逻辑。支持注册多个工厂类，组件会遍历注册的工厂类直到完成子类创建过程。未找到合适的工厂类时会使用默认工厂类完成设备子类创建。
### 统一的设备子类准备机制
上层业务在使用设备控制功能时将设备控制逻辑封装到基于设备子类中，通过设备子类的函数封装隔离了设备控制中组命令组装补全等设备控制逻辑细节，因此设备子类从构造、准备到能够正常控制设备需要比较复杂的数据准备过程。Updevice组件通过提供标准设备基类方式约定并实现了完整的设备子类准备机制。
### 逻辑引擎设备子类
由于逻辑引擎属于智家APP的默认网器控制子类，因此在组件中提供了默认的逻辑引擎子类方法以及工厂类。
### uSDK Toolkit实现
组件中将设备控制sdk抽象为ToolKit，具体设备控制行为均通过抽象的Toolkit接口。组件中默认实现了基于uSDK的ToolKit实现。
### 兼容设备子类
用于适配updevice1.0版本的设备基类，预计即将在12月底将上层单品设备接入下线；不再单独进行测试；
### 洗衣机设备子类
目前洗衣机使用的是产业自己的自适配接入方案，为了卡片也能正常工作，需要在洗衣机设备子类中下来相关型号配置文件；为了业务的聚合性，将原先位于网器库的设备子类逻辑下沉到组件中
### 设备列表与标准设备基类
提供设备列表查询、订阅、更新等标准能力；提供了标准设备基类完成通用的设备控制操作与多订阅机制。

## 通用术语
- 设备数据源 - 设备管理器初始化入参，为设备管理器提供设备信息相关数据来源的代理实现对象。
- Toolkit - 抽象封装物联设备控制信道的相关操作，比如连接设备、查询设备状态、下发控制命令等；
- 设备工厂类 - 根据设备相关信息完成设备子类创建
- 设备子类 - 继承设备基类封装了具体的设备控制逻辑的子类
- 设备状态变化 - 由usdk上报设备相关状态变化，包括设备基础信息变化，设备连接状态变化，子设备列表变化，属性列表变化，报警列表变化，fota状态变化，蓝牙数据和蓝牙历史数据变化
- 设备资源 - 由usdk上报设备图片资源数据
- 空对象 - iOS指nil,Android指null
- 空字符串- 双端均指""长度为0的字符串对象

### 订阅者收到设备状态变化通知事件如下
- 已连接                 EVENT_ATTACHED
- 基础信息变化            EVENT_BASE_INFO_CHANGE
- 设备准备和释放状态变化    EVENT_DEVICE_STATE_CHANGE
- 子设备列表变化          EVENT_SUB_DEV_LIST_CHANGE
- 属性列表变化            EVENT_ATTRIBUTES_CHANGE
- 设备告警               EVENT_DEVICE_CAUTION
- 连接状态变化            EVENT_CONNECTION_CHANGE
- FOTA状态变化           EVENT_DEVICE_FOTA_STATUS_CHANGE
- BLE实时数据            EVENT_DEVICE_BLE_REALTIME_DATA
- BLE历史数据            EVENT_DEVICE_BLE_HISTORY_DATA

### Feature中通用Background如下
```
    Background:
    Given Toolkit使用"模拟的"
    Given 使用者创建"测试"设备工厂
    Given 初始化"测试"设备,唯一标识ID为"uniqueId",设备信息如下:
        | Protocol   | DeviceId        | TypeId        | TypeName        | TypeCode        | Model       | ProdNo       | ParentId        | SubDevNo             | Extra   |
        | haier-usdk | fake_device_id1 | fake_type_id1 | fake_type_name1 | fake_type_code1 | fake_model1 | fake_pro_no1 | fake_parent_id1 | fake_device_id18-8-1 | a=1,b=2 |

```
